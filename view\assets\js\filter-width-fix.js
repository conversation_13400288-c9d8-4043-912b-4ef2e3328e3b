/**
 * This script ensures that the filter card and schedule container have exactly the same width
 */
document.addEventListener('DOMContentLoaded', function() {
    // Function to create a wrapper around filter card and schedule container if it doesn't exist
    function createWrapper() {
        const scheduleContainer = document.querySelector('.schedule-container');
        const filterCard = document.querySelector('.card.animate-fade-in.mb-4');

        if (scheduleContainer && filterCard && !document.querySelector('.timetable-components-wrapper')) {
            // Get the parent container
            const parentContainer = document.querySelector('.timetable-display-container');

            if (parentContainer) {
                // Create a wrapper div
                const wrapper = document.createElement('div');
                wrapper.className = 'timetable-components-wrapper';
                wrapper.style.width = '100%';
                wrapper.style.maxWidth = '95%';
                wrapper.style.margin = '0 auto';

                // Move the filter card and schedule container into the wrapper
                parentContainer.insertBefore(wrapper, filterCard);
                wrapper.appendChild(filterCard);
                wrapper.appendChild(scheduleContainer);

                console.log('Created wrapper for timetable components');

                // Add media queries via JavaScript
                if (window.matchMedia('(min-width: 1200px)').matches) {
                    wrapper.style.maxWidth = '90%';
                }

                if (window.matchMedia('(min-width: 1600px)').matches) {
                    wrapper.style.maxWidth = '85%';
                }
            }
        }
    }

    // Function to match the filter card width to the schedule container width
    function matchFilterWidth() {
        // Try to create wrapper first
        createWrapper();

        const scheduleContainer = document.querySelector('.schedule-container');
        const filterCard = document.querySelector('.card.animate-fade-in.mb-4');

        if (scheduleContainer && filterCard) {
            // Get the computed width of the schedule container
            const scheduleWidth = window.getComputedStyle(scheduleContainer).width;

            // Set the filter card width to match exactly
            filterCard.style.width = scheduleWidth;

            // Store the width as a CSS variable for potential use in calculations
            document.documentElement.style.setProperty('--schedule-width', scheduleWidth);

            console.log('Filter width adjusted to match schedule: ' + scheduleWidth);

            // Also ensure the table inside the schedule container is full width
            const timetableWrapper = document.querySelector('.timetable-wrapper');
            if (timetableWrapper) {
                timetableWrapper.style.width = '100%';
            }

            // Ensure all time columns have equal width
            const timeHeaders = document.querySelectorAll('.time-header');
            if (timeHeaders.length > 0) {
                const columnWidth = `${100 / timeHeaders.length}%`;
                timeHeaders.forEach(header => {
                    header.style.width = columnWidth;
                });
            }
        }
    }

    // Run on page load
    matchFilterWidth();

    // Run on window resize
    window.addEventListener('resize', matchFilterWidth);

    // Run after a short delay to ensure all elements are fully rendered
    setTimeout(matchFilterWidth, 500);

    // Run again after a longer delay to catch any dynamic content changes
    setTimeout(matchFilterWidth, 1000);
});