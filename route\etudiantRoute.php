<?php
require_once "../controller/etudiantController.php";

// Désactiver l'affichage des erreurs pour éviter de renvoyer du HTML
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Définir le type de contenu comme JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

// Capturer toutes les erreurs
try {
    $method = $_SERVER['REQUEST_METHOD'];

    // Utiliser un switch pour traiter les différentes méthodes HTTP
    switch ($method) {
        case 'GET':
            // Vérifier si un CNE est passé dans l'URL
            if (isset($_GET['cne'])) {
                // Route pour obtenir un étudiant par son CNE
                getEtudiantByCNEAPI($_GET['cne']);
            } else {
                // Route pour obtenir tous les étudiants
                getAllEtudiantsAPI();
            }
            break;

        case 'POST':
            // Route pour créer un nouvel étudiant
            createEtudiantAPI();
            break;

        case 'PUT':
            // Vérifier si un CNE est passé dans l'URL pour mettre à jour un étudiant
            if (isset($_GET['cne'])) {
                // Route pour mettre à jour un étudiant par son CNE
                updateEtudiantAPI($_GET['cne']);
            } else {
                jsonResponse(['error' => 'CNE requis pour la mise à jour'], 400);
            }
            break;

        case 'DELETE':
            // Vérifier si un CNE est passé dans l'URL pour supprimer un étudiant
            if (isset($_GET['cne'])) {
                // Route pour supprimer un étudiant par son CNE
                deleteEtudiantAPI($_GET['cne']);
            } else {
                jsonResponse(['error' => 'CNE requis pour la suppression'], 400);
            }
            break;

        default:
            // Si la méthode n'est pas supportée, renvoyer une erreur
            jsonResponse(['error' => 'Méthode HTTP non autorisée'], 405);
            break;
    }
} catch (Exception $e) {
    // Journaliser l'erreur
    error_log("Erreur dans etudiantRoute.php: " . $e->getMessage());

    // Renvoyer une réponse JSON avec l'erreur
    jsonResponse(['error' => 'Erreur serveur: ' . $e->getMessage()], 500);
}
?>