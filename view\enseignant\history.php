<?php
/**
 * Vue pour la page d'historique des enseignants
 *
 * Cette page affiche l'historique complet des activités d'enseignement
 * d'un enseignant à travers les années universitaires.
 */

// Inclure la vérification d'authentification pour les enseignants
require_once '../includes/auth_check_enseignant.php';

// Récupérer l'ID de l'enseignant depuis la session
$teacherId = $_SESSION['user']['teacher_id'] ?? null;

// Récupérer le nom complet de l'enseignant
$teacherFullName = ($_SESSION['user']['prenom'] ?? '') . ' ' . ($_SESSION['user']['nom'] ?? '');
$departmentName = $_SESSION['user']['department_name'] ?? 'Non spécifié';
$specialtyName = $_SESSION['user']['specialty_name'] ?? 'Non spécifié';

// Titre de la page
$pageTitle = "Historique d'Enseignement";
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/dashboard-style.css" rel="stylesheet">
    <link href="../assets/css/teacher.css" rel="stylesheet">

    <style>
        /* Enhanced styles for history page with consistent design patterns */
        .year-filter-container {
            background: linear-gradient(135deg, var(--pastel-blue) 0%, var(--pastel-purple) 100%);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        #yearFilter {
            background: white;
            border: 2px solid transparent;
            border-radius: 12px;
            color: #333;
            padding: 12px 16px;
            transition: all 0.3s ease;
            font-weight: 500;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        #yearFilter:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }

        #yearFilter:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }

        .form-label {
            color: #333;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .history-stats-card {
            background: linear-gradient(135deg, #cfeaf4, #e8c4fd 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 10px 0;
            text-align: center;
            transition: transform 0.3s ease;
            border: none;
            box-shadow: var(--card-shadow);
        }

        .history-stats-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--hover-shadow);
        }

        .history-stats-card h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .history-stats-card p {
            color: #555;
            font-weight: 500;
            margin: 0;
        }

        .history-stats-card i {
            color: #667eea;
            margin-bottom: 15px;
        }

        .module-card {
            background: linear-gradient(135deg, var(--pastel-orange) 0%, var(--pastel-yellow) 100%);
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #ff8c42;
            transition: all 0.3s ease;
            box-shadow: var(--card-shadow);
        }

        .module-card:hover {
            transform: translateX(8px);
            box-shadow: var(--hover-shadow);
        }

        .ue-badge {
            background: linear-gradient(135deg, var(--pastel-teal) 0%, var(--pastel-green) 100%);
            color: #333;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            margin: 3px;
            display: inline-block;
            font-weight: 500;
        }

        .grade-history-item {
            background: linear-gradient(135deg, var(--pastel-pink) 0%, var(--pastel-yellow) 100%);
            border-radius: 12px;
            padding: 18px;
            margin: 12px 0;
            border-left: 4px solid #e91e63;
            transition: all 0.3s ease;
            box-shadow: var(--card-shadow);
        }

        .grade-history-item:hover {
            transform: translateX(5px);
            box-shadow: var(--hover-shadow);
        }

        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }

        .no-data {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .no-data i {
            color: var(--pastel-purple);
        }

        .download-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .timeline-item {
            position: relative;
            padding-left: 35px;
            margin-bottom: 25px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: 12px;
            top: 8px;
            width: 12px;
            height: 12px;
            background: #667eea;
            border-radius: 50%;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }

        .timeline-item::after {
            content: '';
            position: absolute;
            left: 17px;
            top: 20px;
            width: 2px;
            height: calc(100% + 5px);
            background: linear-gradient(to bottom, #667eea, var(--pastel-blue));
        }

        .timeline-item:last-child::after {
            display: none;
        }

        /* Chart container styles */
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(0,0,0,0.05);
        }

        .chart-title {
            color: #333;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
        }

        /* Observation styles */
        .observation-item {
            background: linear-gradient(135deg, rgba(248, 187, 208, 0.3) 0%, rgba(181, 126, 220, 0.3) 100%);
            border-radius: 8px;
            padding: 10px 12px;
            margin: 6px 0;
            transition: all 0.3s ease;
            box-shadow: 0 1px 4px rgba(0,0,0,0.08);
            font-size: 0.85rem;
            border: 1px solid rgba(248, 187, 208, 0.2);
        }

        .observation-item:hover {
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.12);
            background: linear-gradient(135deg, rgb(125 122 255 / 30%) 0%, rgb(255 168 250 / 30%) 100%);
        }

        .observation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .observation-title {
            display: flex;
            align-items: center;
            font-size: 0.8rem;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .observation-date {
            font-size: 0.75rem;
            color: #666;
            margin-left: auto;
            margin-right: 8px;
        }

        .observation-content {
            background: rgba(255, 255, 255, 0.7);
            border-radius: 4px;
            padding: 8px 10px;
            margin: 6px 0;
            font-size: 0.8rem;
            line-height: 1.3;
            color: #444;
        }

        .observation-footer {
            font-size: 0.75rem;
            color: #666;
            margin-top: 6px;
        }

        .observation-item .badge {
            font-size: 0.65rem;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 500;
        }

        /* Scrollable containers for cards */
        .scrollable-card-body {
            max-height: 400px;
            overflow-y: auto;
            padding-right: 10px;
        }

        /* Custom scrollbar styling */
        .scrollable-card-body::-webkit-scrollbar {
            width: 6px;
        }

        .scrollable-card-body::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .scrollable-card-body::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 3px;
        }

        .scrollable-card-body::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        /* Card styling to match chef history */
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
        }

        .card-header {
            border-bottom: 1px solid rgba(0,0,0,0.1);
            border-radius: 12px 12px 0 0 !important;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .year-selector {
                margin: 5px;
                padding: 10px 15px;
            }

            .history-stats-card {
                margin: 8px 0;
                padding: 20px;
            }

            .timeline-item {
                padding-left: 25px;
            }

            .observation-item {
                padding: 10px 12px;
                margin: 6px 0;
                font-size: 0.85rem;
            }

            .observation-content {
                padding: 8px 10px;
                font-size: 0.8rem;
            }

            .observation-footer {
                font-size: 0.75rem;
            }

            .card-body {
                max-height: 300px !important;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4">
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="page-title">
                            <i class="fas fa-history me-3"></i>
                            Historique d'Enseignement
                        </h1>
                        <p class="text-muted mb-0">
                            <strong><?php echo htmlspecialchars($teacherFullName); ?></strong> -

                            <?php echo htmlspecialchars($specialtyName); ?>
                        </p>
                    </div>
                    <div>
                        <button class="btn download-btn" id="downloadFullReport">
                            <i class="fas fa-download me-2"></i>
                            Rapport Complet
                        </button>
                    </div>
                </div>

                <!-- Academic Years Filter -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card dashboard-card">
                            <div class="card-body p-4">
                                <h5 class="card-title mb-3">
                                    <i class="fas fa-calendar-alt me-2 text-primary"></i>
                                    Sélectionner une Année Universitaire
                                </h5>
                                <div class="row">
                                    <div class="col-md-6 col-lg-4">
                                        <div class="form-group">
                                            <label for="yearFilter" class="form-label">
                                                <i class="fas fa-filter me-1"></i>
                                                Année Universitaire
                                            </label>
                                            <select id="yearFilter" class="form-select" onchange="handleYearChange()">
                                                <option value="">
                                                    <i class="fas fa-spinner fa-spin"></i>
                                                    Chargement...
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics Overview -->
                <div class="row mb-4" id="statsSection" style="display: none;">
                    <div class="col-12">
                        <div class="card dashboard-card">
                            <div class="card-body p-4">
                                <h5 class="card-title mb-4">
                                    <i class="fas fa-chart-bar me-2 text-primary"></i>
                                    Statistiques de l'Année <span id="selectedYearDisplay" class="text-primary"></span>
                                </h5>
                                <div class="row" id="statsContainer">
                                    <!-- Statistics will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Répartition des Types d'Enseignement Section -->
                <div id="teachingTypeSection" style="display: none;">
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card dashboard-card">
                                <div class="card-body">
                                    <h6 class="chart-title">
                                        <i class="fas fa-chart-pie me-2 text-primary"></i>
                                        Répartition des Types d'Enseignement
                                    </h6>
                                    <div class="chart-container">
                                        <canvas id="teachingTypeChart" width="400" height="300"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Évolution de la Charge d'Enseignement Section -->
                <div id="workloadSection" style="display: none;">
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card dashboard-card">
                                <div class="card-body">
                                    <h6 class="chart-title">
                                        <i class="fas fa-chart-line me-2 text-primary"></i>
                                        Évolution de la Charge d'Enseignement
                                    </h6>
                                    <div class="chart-container">
                                        <canvas id="workloadChart" width="400" height="300"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Side by Side Section -->
                <div id="chartsSection" style="display: none;">
                    <div class="row mb-4">
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chart-pie me-2"></i>
                                        Répartition des Types d'Enseignement
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="teachingTypeChart2" width="400" height="300"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chart-line me-2"></i>
                                        Évolution de la Charge d'Enseignement
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="workloadChart2" width="400" height="300"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Module Distribution Section -->
                <div id="moduleDistributionSection" style="display: none;">
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card dashboard-card">
                                <div class="card-body">
                                    <h6 class="chart-title">
                                        <i class="fas fa-chart-bar me-2 text-primary"></i>
                                        Modules par Filière et Niveau
                                    </h6>
                                    <div class="chart-container">
                                        <canvas id="moduleDistributionChart" width="800" height="400"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Unités d'Enseignement Assignées Section -->
                <div id="ueAssignmentsSection" style="display: none;">
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card dashboard-card card-modules">
                                <div class="card-body p-4">
                                    <h5 class="card-title mb-3">
                                        <i class="fas fa-chalkboard-teacher me-2 text-primary"></i>
                                        Unités d'Enseignement Assignées
                                    </h5>
                                    <div id="ueAssignments">
                                        <div class="loading-spinner">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Chargement...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modules Enseignés Section -->
                <div id="modulesSection" style="display: none;">
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card dashboard-card card-preferences">
                                <div class="card-body p-4">
                                    <h5 class="card-title mb-3">
                                        <i class="fas fa-book me-2 text-primary"></i>
                                        Modules Enseignés
                                    </h5>
                                    <div id="moduleDetails">
                                        <div class="loading-spinner">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Chargement...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Content Area - Side by Side -->
                <div id="mainContent" style="display: none;">
                    <div class="row mb-4">
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chalkboard-teacher me-2"></i>
                                        Unités d'Enseignement Assignées
                                    </h5>
                                </div>
                                <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                                    <div id="ueAssignments2">
                                        <div class="loading-spinner">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Chargement...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-book me-2"></i>
                                        Modules Enseignés
                                    </h5>
                                </div>
                                <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                                    <div id="moduleDetails2">
                                        <div class="loading-spinner">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Chargement...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Grade History and Administrative Observations Side by Side -->
                <div class="row" id="gradeHistoryAndObservationsSection" style="display: none;">
                  <div class="row mb-4"> 
                    <!-- Grade History -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">
                                    <i class="fas fa-clipboard-list me-2"></i>
                                    Historique des Notes Uploadées
                                </h5>
                            </div>
                            <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                                <div id="gradeHistory">
                                    <div class="loading-spinner">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Chargement...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Administrative Observations -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">
                                    <i class="fas fa-sticky-note me-2"></i>
                                    Observations Administratives
                                </h5>
                            </div>
                            <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                                <div id="adminNotes">
                                    <div class="no-data">
                                        <i class="fas fa-info-circle fa-3x mb-3 text-muted"></i>
                                        <p>Aucune observation administrative pour cette année.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                  </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/sidebar.js"></script>
    <script>
        // Pass PHP variables to JavaScript
        const teacherId = <?php echo json_encode($teacherId); ?>;
        const teacherName = <?php echo json_encode($teacherFullName); ?>;
    </script>
    <script src="../assets/js/teacher_history.js"></script>
</body>
</html>
