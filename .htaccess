# Enable CORS
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With"

    # <PERSON>le preflight requests
    RewriteEngine On
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]
</IfModule>

# Handle 404 errors
# Utiliser une variable d'environnement pour le chemin de base
# Cette ligne sera remplacée dynamiquement par le script d'installation
ErrorDocument 404 /Projet-Web/index.php

# Enable rewriting
<IfModule mod_rewrite.c>
    RewriteEngine On
    # Cette ligne sera remplacée dynamiquement par le script d'installation
    RewriteBase /Projet-Web/

    # Redirect to index.php if file or directory doesn't exist
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ index.php [L,QSA]
</IfModule>
