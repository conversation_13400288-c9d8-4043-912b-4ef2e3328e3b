<?php
require_once "../model/specialiteModel.php";
require_once "../utils/response.php";

// Handle the request based on the HTTP method
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        if (isset($_GET['id'])) {
            getSpecialiteByIdAPI($_GET['id']);
        } else {
            getAllSpecialitesAPI();
        }
        break;

    case 'POST':
        createSpecialiteAPI();
        break;

    case 'PUT':
        if (isset($_GET['id'])) {
            updateSpecialiteAPI($_GET['id']);
        } else {
            jsonResponse(['error' => 'ID requis pour la mise à jour'], 400);
        }
        break;

    case 'DELETE':
        if (isset($_GET['id'])) {
            deleteSpecialiteAPI($_GET['id']);
        } else {
            jsonResponse(['error' => 'ID requis pour la suppression'], 400);
        }
        break;

    default:
        jsonResponse(['error' => 'Méthode HTTP non autorisée'], 405);
        break;
}

// API Functions
function getAllSpecialitesAPI() {
    $specialites = getAllSpecialites();
    if (isset($specialites['error'])) {
        jsonResponse(['error' => $specialites['error']], 404);
    }
    jsonResponse(['data' => $specialites], 200);
}

function getSpecialiteByIdAPI($id) {
    $specialite = getSpecialiteById($id);
    if (isset($specialite['error'])) {
        jsonResponse(['error' => $specialite['error']], 404);
    }
    jsonResponse(['data' => $specialite], 200);
}

function createSpecialiteAPI() {
    $json = file_get_contents("php://input");
    $data = json_decode($json, true);

    if (!$data) {
        jsonResponse(['error' => 'Données JSON invalides'], 400);
        return;
    }

    $result = createSpecialite($data);
    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 500);
    } else {
        jsonResponse(['message' => 'Spécialité créée avec succès'], 201);
    }
}

function updateSpecialiteAPI($id) {
    $json = file_get_contents("php://input");
    $data = json_decode($json, true);

    if (!$data) {
        jsonResponse(['error' => 'Données JSON invalides'], 400);
        return;
    }

    $result = updateSpecialite($id, $data);
    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 500);
    } else {
        jsonResponse(['message' => 'Spécialité mise à jour avec succès'], 200);
    }
}

function deleteSpecialiteAPI($id) {
    $result = deleteSpecialite($id);
    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 500);
    } else {
        jsonResponse(['message' => 'Spécialité supprimée avec succès'], 200);
    }
}
