<?php
// Include necessary files
require_once "../controller/uePreferencesController.php";

// Set headers for JSON response
header('Content-Type: application/json');

// Handle CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Handle different HTTP methods
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        // Handle GET requests
        if (isset($_GET['action'])) {
            $action = $_GET['action'];

            switch ($action) {
                case 'getModulesAndUEsByTeacherSpecialty':
                    if (!isset($_GET['teacher_id'])) {
                        jsonResponse(['error' => 'Teacher ID is required'], 400);
                    }
                    getModulesAndUEsByTeacherSpecialtyAPI($_GET['teacher_id']);
                    break;

                case 'getTeacherUePreferences':
                    if (!isset($_GET['teacher_id'])) {
                        jsonResponse(['error' => 'Teacher ID is required'], 400);
                    }
                    getTeacherUePreferencesAPI($_GET['teacher_id']);
                    break;

                default:
                    jsonResponse(['error' => 'Invalid action'], 400);
                    break;
            }
        } else {
            jsonResponse(['error' => 'Action is required'], 400);
        }
        break;

    case 'POST':
        // Handle POST requests
        if (isset($_GET['action']) && $_GET['action'] === 'saveMultipleUePreferences') {
            saveMultipleUePreferencesAPI();
        } else {
            saveUePreferenceAPI();
        }
        break;

    case 'DELETE':
        // Handle DELETE requests
        if (!isset($_GET['preference_id'])) {
            jsonResponse(['error' => 'Preference ID is required'], 400);
        }
        deleteUePreferenceAPI($_GET['preference_id']);
        break;

    default:
        jsonResponse(['error' => 'Method not allowed'], 405);
        break;
}
?>
