/**
 * Import Grades Controller
 *
 * This file handles the client-side functionality for the import_grades.php page.
 * It manages the cascading filters and file upload functionality.
 */

$(document).ready(function() {
    // Initialize variables to store selected values
    let selectedTeacher = null;
    let selectedLevel = null;
    let selectedSemester = null;
    let selectedModule = null;
    let selectedSession = null;
    let selectedTeacherName = '';
    let selectedModuleName = '';

    // Load teachers when the page loads
    loadTeachers();

    // Event listeners for filter changes
    $('#teacher-filter').on('change', function() {
        selectedTeacher = $(this).val();
        if (selectedTeacher) {
            selectedTeacherName = $('#teacher-filter option:selected').text();
            loadLevels(selectedTeacher);
            $('#level-filter').prop('disabled', false);
            $('#semester-filter').prop('disabled', true).val('');
            $('#module-filter').prop('disabled', true).val('');
            $('#session-filter').prop('disabled', true).val('');
            selectedLevel = null;
            selectedSemester = null;
            selectedModule = null;
            selectedSession = null;
            hideModuleInfo();
        } else {
            $('#level-filter').prop('disabled', true).val('');
            $('#semester-filter').prop('disabled', true).val('');
            $('#module-filter').prop('disabled', true).val('');
            $('#session-filter').prop('disabled', true).val('');
            selectedTeacherName = '';
            hideModuleInfo();
        }
    });

    $('#level-filter').on('change', function() {
        selectedLevel = $(this).val();
        if (selectedLevel) {
            loadSemesters(selectedLevel);
            $('#semester-filter').prop('disabled', false);
            $('#module-filter').prop('disabled', true).val('');
            $('#session-filter').prop('disabled', true).val('');
            selectedSemester = null;
            selectedModule = null;
            selectedSession = null;
            hideModuleInfo();
        } else {
            $('#semester-filter').prop('disabled', true).val('');
            $('#module-filter').prop('disabled', true).val('');
            $('#session-filter').prop('disabled', true).val('');
            hideModuleInfo();
        }
    });

    $('#semester-filter').on('change', function() {
        selectedSemester = $(this).val();
        if (selectedSemester && selectedTeacher && selectedLevel) {
            loadModules(selectedTeacher, selectedLevel, selectedSemester);
            $('#module-filter').prop('disabled', false);
            $('#session-filter').prop('disabled', true).val('');
            selectedModule = null;
            selectedSession = null;
            hideModuleInfo();
        } else {
            $('#module-filter').prop('disabled', true).val('');
            $('#session-filter').prop('disabled', true).val('');
            hideModuleInfo();
        }
    });

    $('#module-filter').on('change', function() {
        selectedModule = $(this).val();
        if (selectedModule) {
            selectedModuleName = $('#module-filter option:selected').text();
            $('#session-filter').prop('disabled', false);
            selectedSession = null;
            hideModuleInfo();
        } else {
            $('#session-filter').prop('disabled', true).val('');
            selectedModuleName = '';
            hideModuleInfo();
        }
    });

    $('#session-filter').on('change', function() {
        selectedSession = $(this).val();
        console.log('Session sélectionnée:', selectedSession);

        if (selectedSession) {
            // Show module info
            showModuleInfo(selectedModuleName, selectedTeacherName);
            // Enable the display PDFs button
            $('#display-pdfs-btn').prop('disabled', false);
        } else {
            hideModuleInfo();
            $('#display-pdfs-btn').prop('disabled', true);
        }
    });

    // Event listener for the display PDFs button
    $('#display-pdfs-btn').on('click', function() {
        if (validateFilters()) {
            loadPdfGrades();
        }
    });

    // File upload handling
    $('#browse-button').on('click', function() {
        $('#file-upload').click();
    });

    $('#file-upload').on('change', function() {
        const file = this.files[0];
        if (file) {
            if (validateFileSelection()) {
                handleFileUpload(file);
            }
        }
    });

    // Drag and drop functionality
    const uploadArea = document.getElementById('upload-area');

    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('border-primary');
    });

    uploadArea.addEventListener('dragleave', function() {
        uploadArea.classList.remove('border-primary');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('border-primary');

        const file = e.dataTransfer.files[0];
        if (file) {
            if (validateFileSelection()) {
                handleFileUpload(file);
            }
        }
    });

    // Functions to load filter options
    function loadTeachers() {
        $.ajax({
            url: '../../controller/importGradesController.php',
            type: 'GET',
            data: { action: 'getTeachers' },
            dataType: 'json',
            success: function(response) {
                if (response.data) {
                    const teacherSelect = $('#teacher-filter');
                    teacherSelect.empty();
                    teacherSelect.append('<option value="">Sélectionner un enseignant</option>');

                    response.data.forEach(function(teacher) {
                        teacherSelect.append(`<option value="${teacher.id_enseignant}">${teacher.nom} ${teacher.prenom}</option>`);
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading teachers:', error);
                showError('Erreur lors du chargement des enseignants.');
            }
        });
    }

    function loadLevels(teacherId) {
        $.ajax({
            url: '../../controller/importGradesController.php',
            type: 'GET',
            data: {
                action: 'getLevels',
                teacherId: teacherId
            },
            dataType: 'json',
            success: function(response) {
                if (response.data) {
                    const levelSelect = $('#level-filter');
                    levelSelect.empty();
                    levelSelect.append('<option value="">Sélectionner un niveau</option>');

                    response.data.forEach(function(level) {
                        levelSelect.append(`<option value="${level.id_niveau}">${level.niveau}</option>`);
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading levels:', error);
                showError('Erreur lors du chargement des niveaux.');
            }
        });
    }

    function loadSemesters(levelId) {
        $.ajax({
            url: '../../controller/importGradesController.php',
            type: 'GET',
            data: {
                action: 'getSemesters',
                levelId: levelId
            },
            dataType: 'json',
            success: function(response) {
                if (response.data) {
                    const semesterSelect = $('#semester-filter');
                    semesterSelect.empty();
                    semesterSelect.append('<option value="">Sélectionner un semestre</option>');

                    response.data.forEach(function(semester) {
                        semesterSelect.append(`<option value="${semester.id}">${semester.nom}</option>`);
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading semesters:', error);
                showError('Erreur lors du chargement des semestres.');
            }
        });
    }

    function loadModules(teacherId, levelId, semester) {
        console.log('Loading modules for teacher ID:', teacherId, 'level ID:', levelId, 'semester:', semester);
        $.ajax({
            url: '../../controller/importGradesController.php',
            type: 'GET',
            data: {
                action: 'getModules',
                teacherId: teacherId,
                levelId: levelId,
                semester: semester
            },
            dataType: 'json',
            success: function(response) {
                console.log('Modules response:', response);
                if (response.data) {
                    const moduleSelect = $('#module-filter');
                    moduleSelect.empty();
                    moduleSelect.append('<option value="">Sélectionner un module</option>');

                    response.data.forEach(function(module) {
                        // Check if module has id and nom properties
                        const id = module.id || '';
                        const nom = module.nom || 'Unknown Module';
                        console.log('Module:', module);
                        moduleSelect.append(`<option value="${id}">${nom}</option>`);
                    });
                } else {
                    console.warn('No modules found or invalid response format:', response);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading modules:', error);
                console.error('XHR status:', status);
                console.error('XHR response:', xhr.responseText);
                showError('Erreur lors du chargement des modules.');
            }
        });
    }

    // Function to validate filters
    function validateFilters() {
        // Check if all filters are selected
        if (!selectedTeacher || !selectedLevel || !selectedSemester || !selectedModule || !selectedSession) {
            showError('Veuillez sélectionner tous les filtres avant de continuer.');
            return false;
        }
        return true;
    }

    // Functions to handle file upload
    function validateFileSelection() {
        return validateFilters();
    }

    // Function to load PDF grades based on filters
    function loadPdfGrades() {
        // Show the PDF display section and loading indicator
        $('#pdf-display-section').show();
        $('#pdf-loading').show();
        $('#pdf-list').empty();
        $('#no-pdfs-message').hide();

        // Prepare the data for the request
        const requestData = {
            action: 'getPdfGrades',
            teacherId: selectedTeacher,
            moduleId: selectedModule,
            levelId: selectedLevel,
            semesterId: selectedSemester,
            session: selectedSession
        };

        console.log('Loading PDF grades with filters:', requestData);
        console.log('Session value:', selectedSession);
        console.log('Session type:', typeof selectedSession);

        // Make the AJAX request
        $.ajax({
            url: '../../controller/pdfGradesController.php',
            type: 'GET',
            data: requestData,
            dataType: 'json',
            success: function(response) {
                $('#pdf-loading').hide();

                if (response.success && response.data && response.data.length > 0) {
                    console.log('PDF grades found:', response.data);
                    displayPdfGrades(response.data);
                } else {
                    $('#no-pdfs-message').show();
                    console.log('No PDF grades found or error:', response);
                }
            },
            error: function(xhr, status, error) {
                $('#pdf-loading').hide();
                $('#no-pdfs-message').show();
                console.error('Error loading PDF grades:', error);
                console.error('XHR status:', status);
                console.error('XHR response:', xhr.responseText);
                showError('Erreur lors du chargement des PDFs.');
            }
        });
    }

    // Function to display PDF grades
    function displayPdfGrades(pdfGrades) {
        const pdfList = $('#pdf-list');
        pdfList.empty();

        // Ajouter une animation d'entrée progressive pour les cartes PDF
        pdfGrades.forEach(function(pdf, index) {
            // Log des données du PDF pour débogage
            console.log('PDF data:', pdf);

            // Create the PDF card with enhanced design
            const pdfCard = $(`
                <div class="pdf-card" style="opacity: 0; transform: translateY(20px);">
                    <div class="pdf-icon">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="pdf-info">
                        <div class="pdf-title">Notes du module: ${selectedModuleName}</div>
                        <div class="pdf-meta">
                            <div><i class="fas fa-user-tie me-2"></i>Enseignant: ${selectedTeacherName}</div>
                            <div><i class="fas fa-calendar-alt me-2"></i>Session: ${selectedSession}</div>
                            <div><i class="fas fa-file me-2"></i>Fichier: ${pdf.file_path}</div>
                        </div>
                    </div>
                    <div class="pdf-actions">
                        <button class="pdf-view-btn" data-file="${pdf.file_path}">
                            <i class="fas fa-eye me-1"></i> Voir
                        </button>
                        <button class="pdf-download-btn" data-file="${pdf.file_path}">
                            <i class="fas fa-download me-1"></i> Télécharger
                        </button>
                    </div>
                </div>
            `);

            pdfList.append(pdfCard);

            // Animer l'entrée de chaque carte avec un délai progressif
            setTimeout(function() {
                pdfCard.animate({
                    opacity: 1,
                    transform: 'translateY(0)'
                }, 500);
            }, 100 * index); // Délai progressif pour chaque carte
        });

        // Add event listeners for the view and download buttons
        $('.pdf-view-btn').on('click', function() {
            const filePath = $(this).data('file');
            viewPdf(filePath);
        });

        $('.pdf-download-btn').on('click', function() {
            const filePath = $(this).data('file');
            downloadPdf(filePath);
        });
    }

    // Function to view a PDF
    function viewPdf(filePath) {
        console.log('Viewing PDF:', filePath);

        // Ajouter un timestamp pour éviter les problèmes de cache
        const timestamp = new Date().getTime();
        const pdfUrl = `../../uploads/pdfs/${filePath}?t=${timestamp}`;
        console.log('PDF URL with timestamp:', pdfUrl);

        // Vérifier d'abord si le fichier existe en utilisant notre API
        $.ajax({
            url: '../../controller/pdfGradesController.php',
            type: 'GET',
            data: {
                action: 'checkPdfExists',
                filePath: filePath
            },
            dataType: 'json',
            success: function(response) {
                if (response.success && response.exists) {
                    // Le fichier existe, l'ouvrir dans un nouvel onglet
                    window.open(pdfUrl, '_blank');
                } else {
                    // Le fichier n'existe pas
                    console.error('Le fichier PDF n\'existe pas sur le serveur:', pdfUrl);
                    showError(`Erreur: Le fichier ${filePath} n'existe pas sur le serveur.`);
                }
            },
            error: function(error) {
                console.error('Erreur lors de la vérification du fichier:', error);
                showError('Erreur lors de l\'ouverture du fichier. Veuillez réessayer.');
            }
        });
    }

    // Function to download a PDF
    function downloadPdf(filePath) {
        console.log('Downloading PDF:', filePath);

        // Ajouter un timestamp pour éviter les problèmes de cache et de duplication
        const timestamp = new Date().getTime();
        const pdfUrl = `../../uploads/pdfs/${filePath}?t=${timestamp}`;
        console.log('PDF URL with timestamp:', pdfUrl);

        // Vérifier d'abord si le fichier existe en utilisant notre API
        $.ajax({
            url: '../../controller/pdfGradesController.php',
            type: 'GET',
            data: {
                action: 'checkPdfExists',
                filePath: filePath
            },
            dataType: 'json',
            success: function(response) {
                if (response.success && response.exists) {
                    // Le fichier existe, procéder au téléchargement
                    const a = document.createElement('a');

                    // Utiliser un nom de fichier modifié pour éviter les doublons dans le navigateur
                    const fileNameParts = filePath.split('.');
                    const extension = fileNameParts.pop();
                    const fileName = fileNameParts.join('.') + '_' + timestamp + '.' + extension;

                    a.href = pdfUrl;
                    a.download = fileName;
                    a.target = '_blank'; // Ouvrir dans un nouvel onglet
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);

                    // Afficher un message de succès
                    showSuccess(`Le fichier a été téléchargé avec succès.`);
                } else {
                    // Le fichier n'existe pas
                    console.error('Le fichier PDF n\'existe pas sur le serveur:', pdfUrl);
                    showError(`Erreur: Le fichier ${filePath} n'existe pas sur le serveur.`);
                }
            },
            error: function(error) {
                console.error('Erreur lors de la vérification du fichier:', error);
                showError('Erreur lors du téléchargement du fichier. Veuillez réessayer.');
            }
        });
    }

    function handleFileUpload(file) {
        // Check file type
        if (file.type !== 'application/pdf') {
            showError('Seuls les fichiers PDF sont acceptés.');
            return;
        }

        // Show loading spinner
        $('#loading-spinner').show();
        hideAlerts();

        // Create form data
        const formData = new FormData();
        formData.append('gradeFile', file);
        formData.append('teacherId', selectedTeacher);
        formData.append('moduleId', selectedModule);
        formData.append('levelId', selectedLevel);
        formData.append('semester', selectedSemester);
        formData.append('session', selectedSession);

        // Send the file to the server
        $.ajax({
            url: '../../controller/importGradesController.php?action=importGrades',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                $('#loading-spinner').hide();
                if (response.success) {
                    showSuccess('Le fichier a été importé avec succès.');
                    // Reset file input
                    $('#file-upload').val('');
                } else {
                    showError(response.error || 'Une erreur s\'est produite lors de l\'importation du fichier.');
                }
            },
            error: function(jqXHR) {
                $('#loading-spinner').hide();
                console.error('Error uploading file:', jqXHR.responseText);
                showError('Erreur lors de l\'envoi du fichier au serveur.');
            }
        });
    }

    // Helper functions for UI
    function showModuleInfo(moduleName, teacherName) {
        $('#module-name').text(moduleName);
        $('#teacher-name').text(teacherName);
        $('#module-info').show();
    }

    function hideModuleInfo() {
        $('#module-info').hide();
    }

    function showSuccess(message) {
        // Mettre à jour le message (en préservant la structure HTML)
        $('#success-alert p.mb-0').text(message);

        // Afficher l'alerte avec animation
        $('#success-alert').css('opacity', '0').show().animate({
            opacity: 1
        }, 300);

        $('#error-alert').hide();

        // Faire disparaître le message après 1 seconde
        setTimeout(function() {
            $('#success-alert').animate({
                opacity: 0
            }, 500, function() {
                $(this).hide();
            });
        }, 1000);
    }

    function showError(message) {
        // Mettre à jour le message
        $('#error-message').text(message);

        // Afficher l'alerte avec animation
        $('#error-alert').css('opacity', '0').show().animate({
            opacity: 1
        }, 300);

        $('#success-alert').hide();

        // Faire disparaître le message après 3 secondes (plus long pour les erreurs)
        setTimeout(function() {
            $('#error-alert').animate({
                opacity: 0
            }, 500, function() {
                $(this).hide();
            });
        }, 3000);
    }

    function hideAlerts() {
        $('#success-alert').hide();
        $('#error-alert').hide();
    }
});
