<?php
require_once __DIR__ . "/../model/clubsModel.php";
require_once __DIR__ . "/../utils/response.php";

/**
 * Get all clubs for the admin page
 */
function getAllClubsAPI() {
    $clubs = getAllClubs();

    if (isset($clubs['error'])) {
        jsonResponse(['success' => false, 'error' => $clubs['error']], 500);
    }

    jsonResponse(['success' => true, 'data' => $clubs]);
}

/**
 * Get all clubs with pagination
 */
function getAllClubsPaginatedAPI() {
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $perPage = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 3;

    // Ensure valid values
    $page = max(1, $page);
    $perPage = max(1, min(20, $perPage)); // Limit between 1 and 20

    $result = getAllClubsPaginated($page, $perPage);

    if (isset($result['error'])) {
        jsonResponse(['success' => false, 'error' => $result['error']], 500);
    }

    jsonResponse(['success' => true, 'data' => $result['clubs'], 'pagination' => $result['pagination']]);
}

/**
 * Get a club by ID
 *
 * @param int $id_club Club ID
 */
function getClubByIdAPI($id_club) {
    $club = getClubById($id_club);

    if (isset($club['error'])) {
        jsonResponse(['success' => false, 'error' => $club['error']], 500);
    }

    if (!$club) {
        jsonResponse(['success' => false, 'error' => 'Club not found'], 404);
    }

    jsonResponse(['success' => true, 'data' => $club]);
}

/**
 * Create a new club
 */
function createClubAPI() {
    // Get JSON data from request body
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);

    if (!$data) {
        jsonResponse(['success' => false, 'error' => 'Invalid JSON data'], 400);
    }

    // Validate required fields
    $requiredFields = ['nom', 'description', 'date_creation'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            jsonResponse(['success' => false, 'error' => "Field '$field' is required"], 400);
        }
    }

    // Create the club
    $result = createClub($data);

    if (isset($result['error'])) {
        jsonResponse(['success' => false, 'error' => $result['error']], 500);
    }

    jsonResponse(['success' => true, 'message' => 'Club created successfully', 'id_club' => $result['id_club']], 201);
}

/**
 * Update a club
 *
 * @param int $id_club Club ID
 */
function updateClubAPI($id_club) {
    // Check if club exists
    $club = getClubById($id_club);

    if (isset($club['error'])) {
        jsonResponse(['success' => false, 'error' => $club['error']], 500);
    }

    if (!$club) {
        jsonResponse(['success' => false, 'error' => 'Club not found'], 404);
    }

    // Get JSON data from request body
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);

    if (!$data) {
        jsonResponse(['success' => false, 'error' => 'Invalid JSON data'], 400);
    }

    // Validate required fields
    $requiredFields = ['nom', 'description', 'date_creation'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            jsonResponse(['success' => false, 'error' => "Field '$field' is required"], 400);
        }
    }

    // Update the club
    $result = updateClub($id_club, $data);

    if (isset($result['error'])) {
        jsonResponse(['success' => false, 'error' => $result['error']], 500);
    }

    jsonResponse(['success' => true, 'message' => 'Club updated successfully']);
}

/**
 * Delete a club
 *
 * @param int $id_club Club ID
 */
function deleteClubAPI($id_club) {
    // Check if club exists
    $club = getClubById($id_club);

    if (isset($club['error'])) {
        jsonResponse(['success' => false, 'error' => $club['error']], 500);
    }

    if (!$club) {
        jsonResponse(['success' => false, 'error' => 'Club not found'], 404);
    }

    // Delete the club
    $result = deleteClub($id_club);

    if (isset($result['error'])) {
        jsonResponse(['success' => false, 'error' => $result['error']], 500);
    }

    jsonResponse(['success' => true, 'message' => 'Club deleted successfully']);
}

/**
 * Get all students for dropdown selection
 */
function getAllStudentsForDropdownAPI() {
    $students = getAllStudentsForDropdown();

    if (isset($students['error'])) {
        jsonResponse(['success' => false, 'error' => $students['error']], 500);
    }

    jsonResponse(['success' => true, 'data' => $students]);
}



/**
 * Get all programme items for a specific club
 *
 * @param int $id_club Club ID
 */
function getClubProgrammeAPI($id_club) {
    // Check if club exists
    $club = getClubById($id_club);

    if (isset($club['error'])) {
        jsonResponse(['success' => false, 'error' => $club['error']], 500);
    }

    if (!$club) {
        jsonResponse(['success' => false, 'error' => 'Club not found'], 404);
    }

    // Get programme for the club
    $programmes = getClubProgramme($id_club);

    if (isset($programmes['error'])) {
        jsonResponse(['success' => false, 'error' => $programmes['error']], 500);
    }

    jsonResponse(['success' => true, 'data' => $programmes]);
}

/**
 * Add a new programme item to a club
 */
function addClubProgrammeAPI() {
    // Get JSON data from request body
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);

    if (!$data) {
        jsonResponse(['success' => false, 'error' => 'Invalid JSON data'], 400);
    }

    // Validate required fields
    $requiredFields = ['id_club', 'titre', 'description', 'date_debut', 'date_fin', 'lieu', 'type'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            jsonResponse(['success' => false, 'error' => "Field '$field' is required"], 400);
        }
    }

    // Check if club exists
    $club = getClubById($data['id_club']);

    if (isset($club['error'])) {
        jsonResponse(['success' => false, 'error' => $club['error']], 500);
    }

    if (!$club) {
        jsonResponse(['success' => false, 'error' => 'Club not found'], 404);
    }

    // Set default status if not provided
    if (!isset($data['statut']) || empty($data['statut'])) {
        $data['statut'] = 'planifie';
    }

    // Add the programme item
    $result = addClubProgramme($data);

    if (isset($result['error'])) {
        jsonResponse(['success' => false, 'error' => $result['error']], 500);
    }

    jsonResponse(['success' => true, 'message' => 'Programme added successfully', 'id_programme' => $result['id_programme']], 201);
}

/**
 * Delete a club programme item
 *
 * @param int $id_programme Programme ID
 */
function deleteClubProgrammeAPI($id_programme) {
    // Delete the programme item
    $result = deleteClubProgramme($id_programme);

    if (isset($result['error'])) {
        jsonResponse(['success' => false, 'error' => $result['error']], 500);
    }

    jsonResponse(['success' => true, 'message' => 'Programme deleted successfully']);
}