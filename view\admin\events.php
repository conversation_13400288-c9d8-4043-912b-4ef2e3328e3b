<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UniAdmin - Gestion des Événements</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Flatpickr (pour les sélecteurs de date/heure) -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard-style.css">
    <link rel="stylesheet" href="../assets/css/header-fix.css">
    <link rel="stylesheet" href="../assets/css/events-style.css">
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="page-title">Gestion des Événements</h1>
                    <button type="button" class="btn btn-primary" id="addEventBtn">
                        <i class="fas fa-plus-circle me-2"></i>Ajouter un événement
                    </button>
                </div>

                <!-- Filtres -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="categoryFilter" class="form-label">Catégorie</label>
                                <select class="form-select" id="categoryFilter">
                                    <option value="">Toutes les catégories</option>
                                    <option value="Administrative">Administrative</option>
                                    <option value="Academic">Academic</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="dateFilter" class="form-label">Date</label>
                                <input type="text" class="form-control" id="dateFilter" placeholder="Sélectionner une date">
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="button" class="btn btn-primary w-100" id="applyFiltersBtn">
                                    <i class="fas fa-filter me-2"></i>Appliquer les filtres
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Liste des événements -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="eventsTable">
                                <thead>
                                    <tr>
                                        <th>Titre</th>
                                        <th>Catégorie</th>
                                        <th>Date</th>
                                        <th>Heure</th>
                                        <th>Lieu</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="eventsTableBody">
                                    <!-- Les événements seront chargés dynamiquement ici -->
                                    <tr>
                                        <td colspan="6" class="text-center">Chargement des événements...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <div class="text-muted" id="eventCount">
                                Affichage de <span id="displayedCount">0</span> sur <span id="totalCount">0</span> événements
                            </div>
                            <nav aria-label="Page navigation">
                                <ul class="pagination" id="eventsPagination">
                                    <!-- La pagination sera générée dynamiquement -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour ajouter/modifier un événement -->
    <div class="modal fade" id="eventModal" tabindex="-1" aria-labelledby="eventModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="eventModalLabel">Ajouter un événement</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="eventForm">
                        <input type="hidden" id="eventId" name="eventId">

                        <div class="mb-3">
                            <label for="eventTitle" class="form-label">Titre <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="eventTitle" name="title" required>
                        </div>

                        <div class="mb-3">
                            <label for="eventCategory" class="form-label">Catégorie <span class="text-danger">*</span></label>
                            <select class="form-select" id="eventCategory" name="category" required>
                                <option value="">Sélectionner une catégorie</option>
                                <option value="Administrative">Administrative</option>
                                <option value="Academic">Academic</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="eventDate" class="form-label">Date <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="eventDate" name="event_date" required>
                            </div>
                            <div class="col-md-6">
                                <label for="eventTime" class="form-label">Heure <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="eventTime" name="event_time" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="eventLocation" class="form-label">Lieu <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="eventLocation" name="location" required>
                        </div>

                        <div class="mb-3">
                            <label for="eventDescription" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="eventDescription" name="description" rows="4" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Images</label>
                            <div class="input-group mb-3">
                                <input type="file" class="form-control" id="imageFile" accept="image/*">
                                <button class="btn btn-outline-secondary" type="button" id="addImageBtn">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            <small class="form-text text-muted">Formats acceptés: JPG, PNG, GIF. Taille max: 5 MB</small>
                            <div id="imagesList" class="d-flex flex-wrap gap-2 mt-2">
                                <!-- Les images seront ajoutées ici -->
                            </div>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="sendNotification" name="send_notification" value="1" checked>
                            <label class="form-check-label" for="sendNotification">
                                Envoyer une notification
                                <i class="fas fa-info-circle ms-1 text-primary" data-bs-toggle="tooltip" data-bs-placement="top" title="Si cette option est cochée, une notification sera envoyée pour annoncer cet événement avec tous ses détails (titre, date, heure, lieu, description)."></i>
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" id="saveEventBtn">Enregistrer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de confirmation de suppression -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteConfirmModalLabel">Confirmer la suppression</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Êtes-vous sûr de vouloir supprimer cet événement ? Cette action est irréversible.</p>
                    <p class="fw-bold" id="deleteEventTitle"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Supprimer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de visualisation d'événement -->
    <div class="modal fade" id="viewEventModal" tabindex="-1" aria-labelledby="viewEventModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewEventModalLabel">Détails de l'événement</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="event-details">
                        <h2 id="viewEventTitle" class="mb-3"></h2>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <p><i class="fas fa-tag me-2"></i> <span id="viewEventCategory"></span></p>
                                <p><i class="fas fa-map-marker-alt me-2"></i> <span id="viewEventLocation"></span></p>
                            </div>
                            <div class="col-md-6">
                                <p><i class="fas fa-calendar-alt me-2"></i> <span id="viewEventDate"></span></p>
                                <p><i class="fas fa-clock me-2"></i> <span id="viewEventTime"></span></p>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h5>Description</h5>
                            <p id="viewEventDescription"></p>
                        </div>

                        <div id="viewEventImages" class="mb-4">
                            <h5>Images</h5>
                            <div class="event-images d-flex flex-wrap gap-3">
                                <!-- Les images seront ajoutées ici -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    <button type="button" class="btn btn-primary" id="editEventBtn">Modifier</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://npmcdn.com/flatpickr/dist/l10n/fr.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/sidebar.js"></script>
    <script src="../assets/js/events-admin.js"></script>
</body>
</html>