-- Script pour modifier la table ue_preferences en supprimant la colonne preference_level

-- Vérifier si la table existe
SET @table_exists = 0;
SELECT COUNT(*) INTO @table_exists FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'ue_preferences';

-- Si la table existe, supprimer la colonne preference_level
SET @alter_statement = IF(@table_exists > 0, 
    'ALTER TABLE ue_preferences DROP COLUMN preference_level',
    'SELECT "Table ue_preferences does not exist"');

PREPARE stmt FROM @alter_statement;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
