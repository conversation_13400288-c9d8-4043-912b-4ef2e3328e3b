<?php
require_once "../config/db.php";

/**
 * Get all available semesters from the semestre table
 *
 * @param int|null $niveauId Optional niveau ID to filter semesters
 * @return array Array of semesters
 */
function getAllSemestres($niveauId = null) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAllSemestres");
        return ["error" => "Database connection error"];
    }

    // Check if the semestre table exists
    $checkTable = mysqli_query($conn, "SHOW TABLES LIKE 'semestre'");

    if (mysqli_num_rows($checkTable) > 0) {
        // If the semestre table exists, use it
        $sql = "SELECT * FROM semestre";

        // Add filter by niveau if provided
        if ($niveauId !== null) {
            $niveauId = mysqli_real_escape_string($conn, $niveauId);
            $sql .= " WHERE niveau_id = '$niveauId'";
        }

        $sql .= " ORDER BY id";

        error_log("Using semestre table with SQL: " . $sql);
    } else {
        // Fallback to using the seance table if semestre table doesn't exist
        $sql = "SELECT DISTINCT semestre FROM seance";

        // Add filter by niveau if provided
        if ($niveauId !== null) {
            $niveauId = mysqli_real_escape_string($conn, $niveauId);
            $sql .= " WHERE id_niveau = '$niveauId'";
        }

        $sql .= " ORDER BY semestre";

        error_log("Using seance table with SQL: " . $sql);
    }

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getAllSemestres: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching semesters: " . $error];
    }

    $semestres = [];
    while ($row = mysqli_fetch_assoc($result)) {
        // Standardize the response format
        if (isset($row['id'])) {
            // From semestre table
            $semestres[] = [
                'id' => $row['id'],
                'nom' => $row['nom'],
                'niveau_id' => $row['niveau_id'] ?? null
            ];
        } else {
            // From seance table
            $semestres[] = [
                'id' => null,
                'nom' => $row['semestre'],
                'niveau_id' => $niveauId
            ];
        }
    }

    mysqli_close($conn);
    return $semestres;
}

/**
 * Get semesters by niveau ID
 *
 * @param int $niveauId The niveau ID
 * @return array Array of semesters
 */
function getSemestresByNiveau($niveauId) {
    return getAllSemestres($niveauId);
}

/**
 * Get semesters by filiere and level
 *
 * @param int $filiereId The filiere ID
 * @param int $levelId The level ID within the filiere
 * @return array Array of semesters
 */
function getSemestresByFiliereAndLevel($filiereId, $levelId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getSemestresByFiliereAndLevel");
        return ["error" => "Database connection error"];
    }

    // First, get the niveau ID based on filiere and level
    $filiereId = mysqli_real_escape_string($conn, $filiereId);
    $levelId = mysqli_real_escape_string($conn, $levelId);

    // Get the cycle_id from the filiere
    $sqlFiliere = "SELECT id_cycle FROM filiere WHERE id_filiere = '$filiereId'";
    $resultFiliere = mysqli_query($conn, $sqlFiliere);

    if (!$resultFiliere || mysqli_num_rows($resultFiliere) == 0) {
        $error = mysqli_error($conn);
        error_log("Error getting cycle_id for filiere $filiereId: " . $error);
        mysqli_close($conn);
        return ["error" => "Error getting cycle_id for filiere: " . $error];
    }

    $filiere = mysqli_fetch_assoc($resultFiliere);
    $cycleId = $filiere['id_cycle'];

    // Get the niveau ID based on cycle and level
    $sqlNiveau = "SELECT id FROM niveaux WHERE cycle_id = '$cycleId' AND id = '$levelId'";
    $resultNiveau = mysqli_query($conn, $sqlNiveau);

    if (!$resultNiveau || mysqli_num_rows($resultNiveau) == 0) {
        $error = mysqli_error($conn);
        error_log("Error getting niveau ID for cycle $cycleId and level $levelId: " . $error);
        mysqli_close($conn);
        return ["error" => "Error getting niveau ID: " . $error];
    }

    $niveau = mysqli_fetch_assoc($resultNiveau);
    $niveauId = $niveau['id'];

    mysqli_close($conn);

    // Now get the semesters for this niveau
    return getAllSemestres($niveauId);
}
?>