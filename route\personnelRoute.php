<?php
require_once "../controller/personnelController.php";
require_once "../utils/response.php";

// Set headers for API responses
header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Allow-Headers: Content-Type");

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            if (isset($_GET['search']) && !empty($_GET['search'])) {
                // Search for personnel by name
                searchPersonnelAPI($_GET['search']);
            } else {
                // Get all personnel
                getAllPersonnelAPI();
            }
            break;

        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
            break;
    }
} catch (Exception $e) {
    jsonResponse(['error' => 'Server error: ' . $e->getMessage()], 500);
}
?>