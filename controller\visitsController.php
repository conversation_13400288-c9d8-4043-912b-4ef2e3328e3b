<?php
require_once __DIR__ . '/../model/visitsModel.php';
require_once __DIR__ . '/../utils/response.php';

/**
 * Gère l'enregistrement d'une visite
 */
function handleRecordVisit($data) {
    $userType = isset($data['user_type']) ? $data['user_type'] : 'anonymous';
    $pageVisited = isset($data['page_visited']) ? $data['page_visited'] : 'dashboard';

    $success = recordVisit($userType, $pageVisited);

    if ($success) {
        jsonResponse(['success' => true, 'message' => 'Visite enregistrée avec succès']);
    } else {
        jsonResponse(['success' => false, 'message' => 'Erreur lors de l\'enregistrement de la visite'], 500);
    }
}

/**
 * Récupère les statistiques de visite
 */
function handleGetVisitStats() {
    $days = isset($_GET['days']) ? intval($_GET['days']) : 30;
    $userType = isset($_GET['user_type']) ? $_GET['user_type'] : 'all';
    $pageVisited = isset($_GET['page_visited']) ? $_GET['page_visited'] : 'all';

    // Ajouter des logs pour le débogage
    error_log("handleGetVisitStats - Paramètres: days=$days, userType=$userType, pageVisited=$pageVisited");

    $visitStats = getVisitStats($days, $userType, $pageVisited);

    // Formater les données pour le graphique
    $labels = [];
    $data = [];

    foreach ($visitStats as $stat) {
        $date = new DateTime($stat['visit_date']);
        $labels[] = $date->format('d M');
        $data[] = intval($stat['total_visits']);
    }

    // Si nous n'avons pas assez de données, compléter avec des zéros
    if (count($labels) < $days) {
        $currentDate = new DateTime();
        for ($i = $days - count($labels); $i > 0; $i--) {
            $date = clone $currentDate;
            $date->modify("-$i days");
            array_unshift($labels, $date->format('d M'));
            array_unshift($data, 0);
        }
    }

    // Ajouter des logs pour le débogage
    error_log("handleGetVisitStats - Nombre de labels: " . count($labels));
    error_log("handleGetVisitStats - Nombre de données: " . count($data));

    $response = [
        'success' => true,
        'message' => 'Statistiques récupérées avec succès',
        'labels' => $labels,
        'data' => $data
    ];

    // Ajouter des logs pour le débogage
    error_log("handleGetVisitStats - Réponse JSON: " . json_encode($response));

    jsonResponse($response);
}

/**
 * Récupère les statistiques globales
 */
function handleGetGlobalStats() {
    $globalStats = getGlobalStats();

    jsonResponse([
        'success' => true,
        'message' => 'Statistiques globales récupérées avec succès',
        'data' => $globalStats
    ]);
}
?>
