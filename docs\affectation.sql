-- Script pour créer la table affectation si elle n'existe pas déjà

CREATE TABLE IF NOT EXISTS `affectation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_enseignant` int(11) NOT NULL,
  `id_ue` int(11) NOT NULL,
  `date_affectation` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `annee_academique` varchar(9) COLLATE utf8mb4_general_ci NOT NULL,
  `statut` enum('acceptee','rejetee') COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'acceptee',
  `commentaire` text COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_enseignant_ue_annee` (`id_enseignant`, `id_ue`, `annee_academique`),
  KEY `fk_affectation_enseignant` (`id_enseignant`),
  KEY `fk_affectation_ue` (`id_ue`),
  CONSTRAINT `fk_affectation_enseignant` FOREIGN KEY (`id_enseignant`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_affectation_ue` FOREIGN KEY (`id_ue`) REFERENCES `uniteenseignement` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
