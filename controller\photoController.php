<?php
require_once __DIR__ . "/../model/adminModel.php";
require_once __DIR__ . "/../utils/response.php";

function updateAdminPhotoAPI($cni) {
    if (empty($cni) || !preg_match('/^[A-Z0-9]+$/', $cni)) {
        jsonResponse(['error' => 'CNI invalide ou manquant'], 400);
    }

    // Vérifier si l'admin existe
    $existingAdmin = getAdminByCNI($cni);
    if (!$existingAdmin) {
        jsonResponse(['error' => 'Administrateur non trouvé'], 404);
    }

    // Vérifier si une image a été envoyée
    if (!isset($_FILES['profileImage']) || $_FILES['profileImage']['error'] !== UPLOAD_ERR_OK) {
        jsonResponse(['error' => 'Aucune image n\'a été envoyée ou erreur lors de l\'upload'], 400);
    }

    $uploadDir = __DIR__ . '/../view/assets/img/profile/';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    // Supprimer l'ancienne photo si elle existe
    if (!empty($existingAdmin['photo_url'])) {
        $oldPhotoPath = __DIR__ . '/../view/' . $existingAdmin['photo_url'];
        if (file_exists($oldPhotoPath)) {
            unlink($oldPhotoPath);
        }
    }

    $imageFile = $_FILES['profileImage'];
    $imageFileType = strtolower(pathinfo($imageFile['name'], PATHINFO_EXTENSION));

    // Vérifier le type de fichier
    if (!in_array($imageFileType, ['jpg', 'jpeg', 'png', 'gif'])) {
        jsonResponse(['error' => 'Seuls les fichiers JPG, JPEG, PNG & GIF sont autorisés'], 400);
    }

    // Générer un nom de fichier unique
    $newFileName = $cni . '_' . time() . '.' . $imageFileType;
    $targetFile = $uploadDir . $newFileName;

    if (move_uploaded_file($imageFile['tmp_name'], $targetFile)) {
        // Mettre à jour le chemin de la photo dans la base de données
        $photoUrl = 'assets/img/profile/' . $newFileName;
        $data = ['photo_url' => $photoUrl];

        if (updateAdmin($cni, $data)) {
            jsonResponse([
                'success' => true,
                'photo_url' => $photoUrl,
                'message' => 'Photo de profil mise à jour avec succès'
            ]);
        } else {
            // Supprimer le fichier si la mise à jour de la base de données a échoué
            unlink($targetFile);
            jsonResponse(['error' => 'Erreur lors de la mise à jour de la photo dans la base de données'], 500);
        }
    } else {
        jsonResponse(['error' => 'Erreur lors de l\'upload de l\'image'], 500);
    }
}

function deleteAdminPhotoAPI($cni) {
    if (empty($cni) || !preg_match('/^[A-Z0-9]+$/', $cni)) {
        jsonResponse(['error' => 'CNI invalide ou manquant'], 400);
    }

    // Vérifier si l'admin existe
    $existingAdmin = getAdminByCNI($cni);
    if (!$existingAdmin) {
        jsonResponse(['error' => 'Administrateur non trouvé'], 404);
    }

    // Vérifier si l'admin a une photo
    if (empty($existingAdmin['photo_url'])) {
        jsonResponse(['success' => true, 'message' => 'Aucune photo à supprimer']);
    }

    // Supprimer le fichier physique si possible
    $photoPath = __DIR__ . '/../view/' . $existingAdmin['photo_url'];
    if (file_exists($photoPath) && !empty($existingAdmin['photo_url'])) {
        unlink($photoPath);
    }

    // Mettre à jour la base de données
    $data = ['photo_url' => ''];
    if (updateAdmin($cni, $data)) {
        jsonResponse([
            'success' => true,
            'message' => 'Photo de profil supprimée avec succès'
        ]);
    } else {
        jsonResponse(['error' => 'Erreur lors de la suppression de la photo dans la base de données'], 500);
    }
}
?>
