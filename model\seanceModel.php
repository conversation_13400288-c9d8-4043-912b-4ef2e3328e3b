<?php
require_once "../config/db.php";

// Get all seances
function getAllSeances() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAllSeances");
        return ["error" => "Database connection error"];
    }

    $sql = "SELECT s.*, m.nom_module, e.nom as prof_nom, e.prenom as prof_prenom, sa.nom_salle
            FROM seance s
            LEFT JOIN module m ON s.id_module = m.id_module
            LEFT JOIN enseignant e ON s.id_enseignant = e.id_enseignant
            LEFT JOIN salle sa ON s.id_salle = sa.id_salle
            ORDER BY s.day ASC, s.heure_debut ASC";

    error_log("SQL query for getAllSeances: $sql");
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error executing query in getAllSeances: $error");
        mysqli_close($conn);
        return ["error" => "Error fetching seances: " . $error];
    }

    $seances = [];
    while ($row = mysqli_fetch_assoc($result)) {
        // Make sure we have both 'jour' and 'day' fields for compatibility
        if (isset($row['day']) && !isset($row['jour'])) {
            $row['jour'] = $row['day'];
        } else if (isset($row['jour']) && !isset($row['day'])) {
            $row['day'] = $row['jour'];
        }

        // Make sure we have both 'type' and 'type_seance' fields for compatibility
        if (isset($row['type']) && !isset($row['type_seance'])) {
            $row['type_seance'] = $row['type'];
        } else if (isset($row['type_seance']) && !isset($row['type'])) {
            $row['type'] = $row['type_seance'];
        }

        $seances[] = $row;
    }

    error_log("Found " . count($seances) . " seances in total");
    mysqli_close($conn);
    return $seances;
}

// Get seances by filters
function getSeancesByFilters($filiere = null, $niveau = null, $groupe = null, $semestre = null, $semaine = null) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    // Set charset to UTF-8
    mysqli_set_charset($conn, "utf8mb4");

    // Debug log
    error_log("getSeancesByFilters called with: filiere=$filiere, niveau=$niveau, groupe=$groupe, semestre=$semestre, semaine=$semaine");

    $sql = "SELECT s.*, m.nom_module, e.nom as prof_nom, e.prenom as prof_prenom, sa.nom_salle
            FROM seance s
            LEFT JOIN module m ON s.id_module = m.id_module
            LEFT JOIN enseignant e ON s.id_enseignant = e.id_enseignant
            LEFT JOIN salle sa ON s.id_salle = sa.id_salle
            WHERE 1=1";

    error_log("Initial SQL query: $sql");

    $params = [];

    // Level (niveau) is a required filter for all class types
    if ($niveau) {
        $niveau = mysqli_real_escape_string($conn, $niveau);
        $sql .= " AND s.id_niveau = '$niveau'";
        error_log("Filtering by niveau: $niveau");
    } else {
        error_log("Warning: No niveau filter provided");
        // Return empty result if niveau is not provided
        mysqli_close($conn);
        return [];
    }

    // Check if we're dealing with an AP class (niveau + groupe) or a cycle class (filiere + niveau + semestre)
    $isAPClass = ($groupe && $groupe !== 'all');
    $isCycleClass = ($filiere && $filiere !== 'none' && $semestre);

    error_log("Class type detection: isAPClass=$isAPClass, isCycleClass=$isCycleClass");

    if ($isAPClass) {
        // This is an AP class (niveau + groupe)
        $groupe = mysqli_real_escape_string($conn, $groupe);
        $sql .= " AND s.id_groupe = '$groupe'";
        error_log("Filtering for AP class with groupe: $groupe");
    } else if ($isCycleClass) {
        // This is a cycle class (filiere + niveau + semestre)
        $filiere = mysqli_real_escape_string($conn, $filiere);
        $semestre = mysqli_real_escape_string($conn, $semestre);

        // Make the semestre filter case-insensitive
        $sql .= " AND s.id_filiere = '$filiere'";
        $sql .= " AND LOWER(s.semestre) = LOWER('$semestre')";

        error_log("Filtering for cycle class with filiere: $filiere, semestre: $semestre");
    } else {
        // Not enough filters to identify a specific class
        error_log("Warning: Not enough filters to identify a specific class");

        // If semestre is provided, use it as a filter
        if ($semestre) {
            $semestre = mysqli_real_escape_string($conn, $semestre);
            $sql .= " AND LOWER(s.semestre) = LOWER('$semestre')";
            error_log("Filtering by semestre: $semestre");
        }

        // If filiere is provided, use it as a filter
        if ($filiere && $filiere !== 'none') {
            $filiere = mysqli_real_escape_string($conn, $filiere);
            $sql .= " AND s.id_filiere = '$filiere'";
            error_log("Filtering by filiere: $filiere");
        }
    }

    // Week is optional - if provided, show only sessions for this week
    if ($semaine) {
        $semaine = mysqli_real_escape_string($conn, $semaine);
        $sql .= " AND s.semaine = '$semaine'";
        error_log("Filtering by semaine: $semaine");
    }

    // Order by day and time for consistent display
    $sql .= " ORDER BY s.day ASC, s.heure_debut ASC";

    error_log("FINAL SQL query for getSeancesByFilters: $sql");
    $result = mysqli_query($conn, $sql);

    // Log the number of rows returned
    if ($result) {
        $num_rows = mysqli_num_rows($result);
        error_log("Query returned $num_rows rows");
    } else {
        error_log("Query failed to execute");
    }

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error executing query in getSeancesByFilters: $error");
        mysqli_close($conn);
        return ["error" => "Error fetching seances: " . $error];
    }

    $seances = [];
    while ($row = mysqli_fetch_assoc($result)) {
        // Ensure all string values are properly encoded
        foreach ($row as $key => $value) {
            if (is_string($value)) {
                $row[$key] = mb_convert_encoding($value, 'UTF-8', 'UTF-8');
            }
        }

        // Make sure we have both 'jour' and 'day' fields for compatibility
        if (isset($row['day']) && !isset($row['jour'])) {
            $row['jour'] = $row['day'];
        } else if (isset($row['jour']) && !isset($row['day'])) {
            $row['day'] = $row['jour'];
        }

        // Make sure we have both 'type' and 'type_seance' fields for compatibility
        if (isset($row['type']) && !isset($row['type_seance'])) {
            $row['type_seance'] = $row['type'];
        } else if (isset($row['type_seance']) && !isset($row['type'])) {
            $row['type'] = $row['type_seance'];
        }

        // Ensure time formats are consistent
        if (isset($row['heure_debut'])) {
            // Standardize time format to "08h" format
            $row['heure_debut'] = standardizeTimeFormat($row['heure_debut']);
        }

        if (isset($row['heure_fin'])) {
            // Standardize time format to "10h" format
            $row['heure_fin'] = standardizeTimeFormat($row['heure_fin']);
        }

        // Map time to standard time slots for better compatibility
        if (isset($row['heure_debut']) && isset($row['heure_fin'])) {
            $standardTimeSlot = mapToStandardTimeSlot($row['heure_debut'], $row['heure_fin']);
            if ($standardTimeSlot) {
                $row['standard_time_slot'] = $standardTimeSlot;

                // Extract start and end hours from the standard time slot
                list($start, $end) = explode('-', $standardTimeSlot);
                $row['standard_heure_debut'] = $start;
                $row['standard_heure_fin'] = $end;
            }
        }

        $seances[] = $row;
    }

    error_log("Found " . count($seances) . " seances matching the filters");
    mysqli_close($conn);
    return $seances;
}

// Get seance by ID
function getSeanceById($id) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getSeanceById");
        return ["error" => "Database connection error"];
    }

    $id = mysqli_real_escape_string($conn, $id);
    error_log("Getting seance with ID: $id");

    $sql = "SELECT s.*, m.nom_module, e.nom as prof_nom, e.prenom as prof_prenom, sa.nom_salle
            FROM seance s
            LEFT JOIN module m ON s.id_module = m.id_module
            LEFT JOIN enseignant e ON s.id_enseignant = e.id_enseignant
            LEFT JOIN salle sa ON s.id_salle = sa.id_salle
            WHERE s.id_seance = '$id'";

    error_log("SQL query for getSeanceById: $sql");
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error executing query in getSeanceById: $error");
        mysqli_close($conn);
        return ["error" => "Error fetching seance: " . $error];
    }

    $seance = mysqli_fetch_assoc($result);

    if (!$seance) {
        error_log("Seance not found with ID: $id");
        mysqli_close($conn);
        return ["error" => "Seance not found"];
    }

    // Make sure we have both 'jour' and 'day' fields for compatibility
    if (isset($seance['day']) && !isset($seance['jour'])) {
        $seance['jour'] = $seance['day'];
    } else if (isset($seance['jour']) && !isset($seance['day'])) {
        $seance['day'] = $seance['jour'];
    }

    // Make sure we have both 'type' and 'type_seance' fields for compatibility
    if (isset($seance['type']) && !isset($seance['type_seance'])) {
        $seance['type_seance'] = $seance['type'];
    } else if (isset($seance['type_seance']) && !isset($seance['type'])) {
        $seance['type'] = $seance['type_seance'];
    }

    error_log("Seance found: " . print_r($seance, true));
    mysqli_close($conn);
    return $seance;
}

// Add a new seance
function addSeance($data) {
    error_log("addSeance function called with data: " . print_r($data, true));

    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in addSeance");
        return ["error" => "Database connection error"];
    }

    // Validate required fields
    $requiredFields = ['id_module', 'id_enseignant', 'id_salle', 'type_seance', 'jour', 'heure_debut', 'heure_fin', 'id_niveau', 'semestre'];

    // Map 'jour' to 'day' and 'type_seance' to 'type' for database compatibility
    if (isset($data['jour'])) {
        $data['day'] = $data['jour'];
    }
    if (isset($data['type_seance'])) {
        $data['type'] = $data['type_seance'];
    }
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            error_log("Missing required field in addSeance: $field");
            return ["error" => "Missing required field: $field"];
        }
    }

    // Sanitize inputs
    $id_module = mysqli_real_escape_string($conn, $data['id_module']);
    $id_enseignant = mysqli_real_escape_string($conn, $data['id_enseignant']);
    $id_salle = mysqli_real_escape_string($conn, $data['id_salle']);
    $type_seance = mysqli_real_escape_string($conn, $data['type_seance']);
    $type = mysqli_real_escape_string($conn, $data['type']); // Use the mapped type value

    // Get the day field (mapped from jour)
    $jour = mysqli_real_escape_string($conn, $data['jour']);

    $heure_debut = mysqli_real_escape_string($conn, $data['heure_debut']);
    $heure_fin = mysqli_real_escape_string($conn, $data['heure_fin']);
    $id_niveau = mysqli_real_escape_string($conn, $data['id_niveau']);
    $semestre = mysqli_real_escape_string($conn, $data['semestre']);

    error_log("Sanitized inputs: module=$id_module, enseignant=$id_enseignant, salle=$id_salle, type=$type_seance, jour=$jour, debut=$heure_debut, fin=$heure_fin, niveau=$id_niveau, semestre=$semestre");

    // Debug log for teacher ID
    error_log("Teacher ID details: value=$id_enseignant, length=" . strlen($id_enseignant));

    // Check if the teacher exists
    $checkTeacherSql = "SELECT * FROM enseignant WHERE id_enseignant = '$id_enseignant'";
    error_log("Checking if teacher exists: $checkTeacherSql");
    $checkTeacherResult = mysqli_query($conn, $checkTeacherSql);

    if (!$checkTeacherResult || mysqli_num_rows($checkTeacherResult) === 0) {
        error_log("Teacher not found with id_enseignant: $id_enseignant");
        mysqli_close($conn);
        return ["error" => "Teacher not found with ID: $id_enseignant"];
    } else {
        error_log("Teacher found with id_enseignant: $id_enseignant");
    }

    // Optional fields
    $id_filiere = isset($data['id_filiere']) && !empty($data['id_filiere']) ? mysqli_real_escape_string($conn, $data['id_filiere']) : 'NULL';
    $id_groupe = isset($data['id_groupe']) && !empty($data['id_groupe']) ? mysqli_real_escape_string($conn, $data['id_groupe']) : 'NULL';
    $semaine = isset($data['semaine']) && !empty($data['semaine']) ? mysqli_real_escape_string($conn, $data['semaine']) : 'NULL';

    error_log("Optional fields: filiere=$id_filiere, groupe=$id_groupe, semaine=$semaine");

    // Check for conflicts - use the day variable for consistency
    $conflicts = checkConflicts($conn, $jour, $heure_debut, $heure_fin, $id_salle, $id_enseignant);
    if ($conflicts) {
        error_log("Conflict detected in addSeance: $conflicts");
        mysqli_close($conn);
        return ["error" => "Conflict detected: " . $conflicts];
    }

    // Build the SQL query - use day as the column name and type instead of type_seance
    $sql = "INSERT INTO seance (id_module, id_enseignant, id_salle, type, day, heure_debut, heure_fin, id_niveau, semestre";

    if ($id_filiere !== 'NULL') {
        $sql .= ", id_filiere";
    }

    if ($id_groupe !== 'NULL') {
        $sql .= ", id_groupe";
    }

    if ($semaine !== 'NULL') {
        $sql .= ", semaine";
    }

    $sql .= ") VALUES ('$id_module', '$id_enseignant', '$id_salle', '$type', '$jour', '$heure_debut', '$heure_fin', '$id_niveau', '$semestre'";

    if ($id_filiere !== 'NULL') {
        $sql .= ", '$id_filiere'";
    }

    if ($id_groupe !== 'NULL') {
        $sql .= ", '$id_groupe'";
    }

    if ($semaine !== 'NULL') {
        $sql .= ", '$semaine'";
    }

    $sql .= ")";

    error_log("SQL query for addSeance: $sql");

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error executing query in addSeance: $error");
        mysqli_close($conn);
        return ["error" => "Error adding seance: " . $error];
    }

    $id = mysqli_insert_id($conn);
    error_log("Seance added successfully with ID: $id");
    mysqli_close($conn);

    return ["id" => $id, "message" => "Seance added successfully"];
}

// Update a seance
function updateSeance($data) {
    error_log("updateSeance function called with data: " . print_r($data, true));

    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in updateSeance");
        return ["error" => "Database connection error"];
    }

    // Validate required fields
    $requiredFields = ['id_seance', 'id_module', 'id_enseignant', 'id_salle', 'type_seance', 'jour', 'heure_debut', 'heure_fin'];

    // Map 'jour' to 'day' and 'type_seance' to 'type' for database compatibility
    if (isset($data['jour']) && !isset($data['day'])) {
        $data['day'] = $data['jour'];
    }
    if (isset($data['type_seance']) && !isset($data['type'])) {
        $data['type'] = $data['type_seance'];
    }
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            error_log("Missing required field in updateSeance: $field");
            return ["error" => "Missing required field: $field"];
        }
    }

    // Sanitize inputs
    $id_seance = mysqli_real_escape_string($conn, $data['id_seance']);
    $id_module = mysqli_real_escape_string($conn, $data['id_module']);
    $id_enseignant = mysqli_real_escape_string($conn, $data['id_enseignant']);
    $id_salle = mysqli_real_escape_string($conn, $data['id_salle']);
    $type_seance = mysqli_real_escape_string($conn, $data['type_seance']);
    $type = mysqli_real_escape_string($conn, $data['type']); // Use the mapped type value

    // Get the day field (mapped from jour)
    $jour = mysqli_real_escape_string($conn, $data['jour']);

    $heure_debut = mysqli_real_escape_string($conn, $data['heure_debut']);
    $heure_fin = mysqli_real_escape_string($conn, $data['heure_fin']);

    error_log("Sanitized inputs for update: id=$id_seance, module=$id_module, enseignant=$id_enseignant, salle=$id_salle, type=$type_seance, jour=$jour, debut=$heure_debut, fin=$heure_fin");

    // Check if the seance exists
    $checkSql = "SELECT * FROM seance WHERE id_seance = '$id_seance'";
    error_log("Checking if seance exists: $checkSql");
    $checkResult = mysqli_query($conn, $checkSql);

    if (!$checkResult || mysqli_num_rows($checkResult) === 0) {
        error_log("Seance not found with ID: $id_seance");
        mysqli_close($conn);
        return ["error" => "Seance not found"];
    }

    // Check for conflicts (excluding the current seance)
    $conflicts = checkConflicts($conn, $jour, $heure_debut, $heure_fin, $id_salle, $id_enseignant, $id_seance);
    if ($conflicts) {
        error_log("Conflict detected in updateSeance: $conflicts");
        mysqli_close($conn);
        return ["error" => "Conflict detected: " . $conflicts];
    }

    // Build the SQL query - use day as the column name and type instead of type_seance
    $sql = "UPDATE seance SET
            id_module = '$id_module',
            id_enseignant = '$id_enseignant',
            id_salle = '$id_salle',
            type = '$type',
            day = '$jour',
            heure_debut = '$heure_debut',
            heure_fin = '$heure_fin'
            WHERE id_seance = '$id_seance'";

    error_log("SQL query for updateSeance: $sql");

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error executing query in updateSeance: $error");
        mysqli_close($conn);
        return ["error" => "Error updating seance: " . $error];
    }

    error_log("Seance updated successfully with ID: $id_seance");
    mysqli_close($conn);
    return ["id" => $id_seance, "message" => "Seance updated successfully"];
}

// Delete a seance
function deleteSeance($id) {
    error_log("deleteSeance function called with ID: $id");

    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in deleteSeance");
        return ["error" => "Database connection error"];
    }

    $id = mysqli_real_escape_string($conn, $id);
    error_log("Sanitized ID for delete: $id");

    // Check if the seance exists
    $checkSql = "SELECT * FROM seance WHERE id_seance = '$id'";
    error_log("Checking if seance exists: $checkSql");
    $checkResult = mysqli_query($conn, $checkSql);

    if (!$checkResult) {
        $error = mysqli_error($conn);
        error_log("Error checking if seance exists: $error");
        mysqli_close($conn);
        return ["error" => "Error checking seance: $error"];
    }

    if (mysqli_num_rows($checkResult) === 0) {
        error_log("Seance not found with ID: $id");
        mysqli_close($conn);
        return ["error" => "Seance not found"];
    }

    $sql = "DELETE FROM seance WHERE id_seance = '$id'";
    error_log("SQL query for deleteSeance: $sql");
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error executing query in deleteSeance: $error");
        mysqli_close($conn);
        return ["error" => "Error deleting seance: " . $error];
    }

    error_log("Seance deleted successfully with ID: $id");
    mysqli_close($conn);
    return ["message" => "Seance deleted successfully"];
}

// Function to standardize time format to "08h" format
function standardizeTimeFormat($time) {
    // If time is already in "08h" format, return it
    if (preg_match('/^\d+h$/', $time)) {
        return $time;
    }

    // If time is in "08:00" format, convert to "08h"
    if (preg_match('/^(\d+):00$/', $time, $matches)) {
        return $matches[1] . 'h';
    }

    // If time is in "08:30" format, convert to "08h30"
    if (preg_match('/^(\d+):(\d+)$/', $time, $matches) && $matches[2] != '00') {
        return $matches[1] . 'h' . $matches[2];
    }

    // Default: return the original time
    return $time;
}

// Function to map time to standard time slots
function mapToStandardTimeSlot($startTime, $endTime) {
    // Extract hours from start time
    $startHour = null;
    if (preg_match('/^(\d+)h/', $startTime, $matches)) {
        $startHour = (int)$matches[1];
    } else if (preg_match('/^(\d+):/', $startTime, $matches)) {
        $startHour = (int)$matches[1];
    } else if (is_numeric($startTime)) {
        $startHour = (int)$startTime;
    }

    // Extract hours from end time
    $endHour = null;
    if (preg_match('/^(\d+)h/', $endTime, $matches)) {
        $endHour = (int)$matches[1];
    } else if (preg_match('/^(\d+):/', $endTime, $matches)) {
        $endHour = (int)$matches[1];
    } else if (is_numeric($endTime)) {
        $endHour = (int)$endTime;
    }

    // If we couldn't extract hours, return null
    if ($startHour === null || $endHour === null) {
        return null;
    }

    // Log the extracted hours for debugging
    error_log("Extracted hours: start=$startHour, end=$endHour");

    // Map to standard time slots based on start hour
    // This ensures sessions are placed in the correct time slot
    if ($startHour == 8) {
        return '08h-10h';
    } else if ($startHour == 10) {
        return '10h-12h';
    } else if ($startHour == 14) {
        return '14h-16h';
    } else if ($startHour == 16) {
        return '16h-18h';
    }

    // If no standard time slot matches, return null
    return null;
}

// Check for conflicts (room or teacher at the same time)
function checkConflicts($conn, $jour, $heure_debut, $heure_fin, $id_salle, $id_enseignant, $exclude_id = null) {
    error_log("Checking conflicts: jour=$jour, heure_debut=$heure_debut, heure_fin=$heure_fin, salle=$id_salle, enseignant=$id_enseignant, exclude_id=" . ($exclude_id ? $exclude_id : "none"));

    // Check for room conflicts - use day as the column name
    $roomSql = "SELECT * FROM seance
                WHERE day = '$jour'
                AND ((heure_debut <= '$heure_debut' AND heure_fin > '$heure_debut')
                    OR (heure_debut < '$heure_fin' AND heure_fin >= '$heure_fin')
                    OR (heure_debut >= '$heure_debut' AND heure_fin <= '$heure_fin'))
                AND id_salle = '$id_salle'";

    if ($exclude_id) {
        $roomSql .= " AND id_seance != '$exclude_id'";
    }

    error_log("Room conflict check SQL: $roomSql");
    $roomResult = mysqli_query($conn, $roomSql);

    if (!$roomResult) {
        $error = mysqli_error($conn);
        error_log("Error checking room conflicts: $error");
        return "Error checking room conflicts: $error";
    }

    if (mysqli_num_rows($roomResult) > 0) {
        $conflictingRoom = mysqli_fetch_assoc($roomResult);
        error_log("Room conflict detected: " . print_r($conflictingRoom, true));
        return "Room is already booked at this time (Seance ID: " . $conflictingRoom['id_seance'] . ")";
    }

    // Check for teacher conflicts - use day as the column name
    $teacherSql = "SELECT * FROM seance
                  WHERE day = '$jour'
                  AND ((heure_debut <= '$heure_debut' AND heure_fin > '$heure_debut')
                      OR (heure_debut < '$heure_fin' AND heure_fin >= '$heure_fin')
                      OR (heure_debut >= '$heure_debut' AND heure_fin <= '$heure_fin'))
                  AND id_enseignant = '$id_enseignant'";

    if ($exclude_id) {
        $teacherSql .= " AND id_seance != '$exclude_id'";
    }

    error_log("Teacher conflict check SQL: $teacherSql");
    $teacherResult = mysqli_query($conn, $teacherSql);

    if (!$teacherResult) {
        $error = mysqli_error($conn);
        error_log("Error checking teacher conflicts: $error");
        return "Error checking teacher conflicts: $error";
    }

    if (mysqli_num_rows($teacherResult) > 0) {
        $conflictingTeacher = mysqli_fetch_assoc($teacherResult);
        error_log("Teacher conflict detected: " . print_r($conflictingTeacher, true));
        return "Teacher is already booked at this time (Seance ID: " . $conflictingTeacher['id_seance'] . ")";
    }

    error_log("No conflicts found");
    return null;
}
?>