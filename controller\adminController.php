<?php
require_once __DIR__ . "/../model/adminModel.php";
require_once __DIR__ . "/../utils/response.php";

function getAllAdminsAPI() {
    try {
        $admins = getAllAdmins();
        if (empty($admins)) {
            jsonResponse(['success' => true, 'message' => 'Aucun administrateur trouvé'], 204);
        }
        // Masquer les informations sensibles pour chaque admin
        foreach ($admins as &$admin) {
            unset($admin['password']);
        }
        jsonResponse(['success' => true, 'admins' => $admins]);
    } catch (Exception $e) {
        error_log('Erreur lors de la récupération des administrateurs: ' . $e->getMessage());
        jsonResponse(['success' => false, 'error' => 'Erreur lors de la récupération des administrateurs'], 500);
    }
}

function getAdminByCNIAPI($cni) {
    if (empty($cni) || !preg_match('/^[A-Z0-9]+$/', $cni)) {
        jsonResponse(['error' => 'CNI invalide ou manquant'], 400);
    }

    try {
        $admin = getAdminByCNI($cni);
        if ($admin) {
            // Masquer les informations sensibles dans la réponse
            unset($admin['password']);
            jsonResponse(['success' => true, 'admin' => $admin]);
        } else {
            jsonResponse(['success' => false, 'error' => 'Administrateur non trouvé'], 404);
        }
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to fetch admin'], 500);
    }
}

function createAdminAPI() {
    $data = json_decode(file_get_contents("php://input"), true);
    if (!$data) {
        jsonResponse(['error' => 'Données JSON invalides ou manquantes'], 400);
    }

    // Validate required fields
    $required_fields = ['CNI', 'nom', 'prenom', 'email', 'tele', 'date_naissance',
                       'lieu_naissance', 'sexe', 'ville', 'pays', 'date_debut_travail'];

    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            jsonResponse(['error' => "Le champ '$field' est obligatoire"], 400);
        }
    }

    // Validate email format and uniqueness
    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        jsonResponse(['error' => 'Format d\'email invalide'], 400);
    }

    // Vérifier si l'email est déjà utilisé
    $conn = getConnection();
    $stmt = mysqli_prepare($conn, "SELECT COUNT(*) FROM admin WHERE email = ?");
    mysqli_stmt_bind_param($stmt, "s", $data['email']);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $count = mysqli_fetch_row($result)[0];
    mysqli_close($conn);

    if ($count > 0) {
        jsonResponse(['error' => 'Cet email est déjà utilisé par un autre administrateur'], 400);
    }

    // Validate sexe value
    if (!in_array($data['sexe'], ['masculin', 'féminin'])) {
        jsonResponse(['error' => 'Le sexe doit être "masculin" ou "féminin"'], 400);
    }

    // Validate date formats and logic
    $date_naissance = strtotime($data['date_naissance']);
    $date_debut_travail = strtotime($data['date_debut_travail']);
    $now = time();

    if (!$date_naissance || !$date_debut_travail) {
        jsonResponse(['error' => 'Format de date invalide'], 400);
    }

    if ($date_naissance > $now) {
        jsonResponse(['error' => 'La date de naissance ne peut pas être dans le futur'], 400);
    }

    if ($date_debut_travail < $date_naissance) {
        jsonResponse(['error' => 'La date de début de travail ne peut pas être antérieure à la date de naissance'], 400);
    }

    // Validate photo_url format and file type if provided
    if (isset($data['photo_url']) && !empty($data['photo_url'])) {
        if (!filter_var($data['photo_url'], FILTER_VALIDATE_URL)) {
            if (!file_exists($data['photo_url'])) {
                jsonResponse(['error' => 'Le fichier photo n\'existe pas'], 400);
            }
            // Vérifier le type de fichier
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mime_type = finfo_file($finfo, $data['photo_url']);
            finfo_close($finfo);

            if (!in_array($mime_type, $allowed_types)) {
                jsonResponse(['error' => 'Type de fichier non autorisé. Utilisez JPG, PNG ou GIF'], 400);
            }
        }
    }

    try {
        if (createAdmin($data)) {
            jsonResponse(['message' => 'Admin créé avec succès'], 201);
        }
        jsonResponse(['error' => 'Échec de la création de l\'admin'], 500);
    } catch (Exception $e) {
        jsonResponse(['error' => 'Erreur serveur lors de la création de l\'admin'], 500);
    }
}

function updateAdminAPI($cni) {
    if (empty($cni) || !preg_match('/^[A-Z0-9]+$/', $cni)) {
        jsonResponse(['error' => 'CNI invalide ou manquant'], 400);
    }

    // Vérifier si l'admin existe avant la mise à jour
    $existingAdmin = getAdminByCNI($cni);
    if (!$existingAdmin) {
        jsonResponse(['error' => 'Administrateur non trouvé'], 404);
    }

    // Récupérer les données JSON du formulaire
    $data = json_decode(file_get_contents('php://input'), true);

    if (!$data) {
        jsonResponse(['error' => 'Données JSON invalides ou manquantes'], 400);
    }

    // Gérer l'upload de l'image de profil via photoController
    if (isset($_FILES['profileImage'])) {
        require_once __DIR__ . '/photoController.php';
        updateAdminPhotoAPI($cni);
        return;
    }
    if (empty($data)) {
        jsonResponse(['error' => 'Aucune donnée fournie pour la mise à jour'], 400);
    }

    // Validate email format and uniqueness if provided
    if (isset($data['email'])) {
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            jsonResponse(['error' => 'Format d\'email invalide'], 400);
        }
        // Vérifier si l'email est déjà utilisé par un autre admin
        if ($data['email'] !== $existingAdmin['email']) {
            $conn = getConnection();
            $stmt = mysqli_prepare($conn, "SELECT COUNT(*) FROM admin WHERE email = ? AND CNI != ?");
            mysqli_stmt_bind_param($stmt, "ss", $data['email'], $cni);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $count = mysqli_fetch_row($result)[0];
            mysqli_close($conn);

            if ($count > 0) {
                jsonResponse(['error' => 'Cet email est déjà utilisé par un autre administrateur'], 400);
            }
        }
    }

    // Validate sexe value if provided
    if (isset($data['sexe'])) {
        // Journaliser la valeur reçue pour le sexe
        error_log("Valeur reçue pour le sexe: " . $data['sexe']);

        if (!in_array($data['sexe'], ['masculin', 'féminin'])) {
            jsonResponse(['error' => 'Le sexe doit être "masculin" ou "féminin". Valeur reçue: ' . $data['sexe']], 400);
        }
    }

    // Validate date formats if provided
    if (isset($data['date_naissance']) && !strtotime($data['date_naissance'])) {
        jsonResponse(['error' => 'Format de date de naissance invalide'], 400);
    }
    if (isset($data['date_debut_travail']) && !strtotime($data['date_debut_travail'])) {
        jsonResponse(['error' => 'Format de date de début de travail invalide'], 400);
    }

    // Validate photo_url format and file type if provided
    if (isset($data['photo_url']) && !empty($data['photo_url'])) {
        if (!filter_var($data['photo_url'], FILTER_VALIDATE_URL)) {
            if (!file_exists($data['photo_url'])) {
                jsonResponse(['error' => 'Le fichier photo n\'existe pas'], 400);
            }
            // Vérifier le type de fichier
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mime_type = finfo_file($finfo, $data['photo_url']);
            finfo_close($finfo);

            if (!in_array($mime_type, $allowed_types)) {
                jsonResponse(['error' => 'Type de fichier non autorisé. Utilisez JPG, PNG ou GIF'], 400);
            }
        }
    }

    try {
        // Journaliser les données avant la mise à jour
        error_log("Données avant mise à jour: " . json_encode($data));

        $result = updateAdmin($cni, $data);
        error_log("Résultat de updateAdmin: " . ($result ? 'true' : 'false'));

        if ($result) {
            // Récupérer les données mises à jour de l'administrateur
            $updatedAdmin = getAdminByCNI($cni);
            if ($updatedAdmin) {
                // Masquer les informations sensibles
                unset($updatedAdmin['password']);
                jsonResponse([
                    'success' => true,
                    'admin' => $updatedAdmin,
                    'message' => 'Admin mis à jour avec succès'
                ]);
            }
        }
        jsonResponse(['success' => false, 'error' => 'Admin non trouvé ou aucune modification effectuée'], 404);
    } catch (Exception $e) {
        error_log("Exception lors de la mise à jour: " . $e->getMessage());
        jsonResponse(['success' => false, 'error' => 'Erreur serveur lors de la mise à jour de l\'admin: ' . $e->getMessage()], 500);
    }
}

function deleteAdminAPI($cni) {
    if (empty($cni) || !preg_match('/^[A-Z0-9]+$/', $cni)) {
        jsonResponse(['error' => 'CNI invalide ou manquant'], 400);
    }

    // Vérifier si l'admin existe avant la suppression
    $existingAdmin = getAdminByCNI($cni);
    if (!$existingAdmin) {
        jsonResponse(['error' => 'Administrateur non trouvé'], 404);
    }

    try {
        if (deleteAdmin($cni)) {
            jsonResponse(['message' => 'Administrateur supprimé avec succès']);
        }
        jsonResponse(['error' => 'Échec de la suppression de l\'administrateur'], 500);
    } catch (Exception $e) {
        jsonResponse(['error' => 'Erreur serveur lors de la suppression de l\'administrateur'], 500);
    }
}
?>
