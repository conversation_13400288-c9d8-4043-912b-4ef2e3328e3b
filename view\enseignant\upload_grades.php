<?php
// Include the authentication check for teachers
require_once '../includes/auth_check_enseignant.php';
// Récupérer les informations de l'enseignant depuis la session
$userName = $_SESSION['user']['username'] ?? 'Enseignant';
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$departmentName = $_SESSION['user']['department_name'] ?? 'Non spécifié';
$specialtyName = $_SESSION['user']['specialty_name'] ?? 'Non spécifié';
$teacherId = $_SESSION['user']['teacher_id'] ?? null;

// Récupérer l'année académique actuelle
$academicYear = "2024/2025"; // À remplacer par une valeur dynamique si nécessaire

// Récupérer le coordinateur de filière
require_once '../../model/filiereModel.php';
require_once '../../model/enseignantModel.php';
$coordinateur = "Non spécifié";

// Fonction pour récupérer le coordinateur d'une filière
function getCoordinateurFiliere($filiereId) {
    $conn = getConnection();
    if (!$conn) {
        return "Non spécifié";
    }

    // Sécuriser l'entrée
    $filiereId = mysqli_real_escape_string($conn, $filiereId);

    // Vérifier les colonnes possibles pour le coordinateur
    $possibleColumns = ['id_coordinateur', 'coordinateur_id'];
    $columnToUse = null;

    foreach ($possibleColumns as $column) {
        $checkColumn = mysqli_query($conn, "SHOW COLUMNS FROM filiere LIKE '$column'");
        if ($checkColumn && mysqli_num_rows($checkColumn) > 0) {
            $columnToUse = $column;
            break;
        }
    }

    if (!$columnToUse) {
        // Si aucune colonne spécifique pour coordinateur n'est trouvée, utiliser id_chef_filiere
        $columnToUse = 'id_chef_filiere';
    }

    // Récupérer l'ID du coordinateur
    $query = "SELECT $columnToUse FROM filiere WHERE id_filiere = '$filiereId'";
    $result = mysqli_query($conn, $query);

    if ($result && mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $coordinateurId = $row[$columnToUse];

        if ($coordinateurId) {
            // Récupérer les informations de l'enseignant qui est coordinateur
            $enseignantQuery = "SELECT nom, prenom FROM enseignant WHERE id_enseignant = '$coordinateurId'";
            $enseignantResult = mysqli_query($conn, $enseignantQuery);

            if ($enseignantResult && mysqli_num_rows($enseignantResult) > 0) {
                $enseignantRow = mysqli_fetch_assoc($enseignantResult);
                mysqli_close($conn);
                return $enseignantRow['prenom'] . ' ' . $enseignantRow['nom'];
            }
        }
    }

    mysqli_close($conn);
    return "Non spécifié";
}

// Récupérer le coordinateur pour la filière actuelle (si disponible)
if (isset($_SESSION['user']['filiere_id'])) {
    $filiereId = $_SESSION['user']['filiere_id'];
    $coordinateur = getCoordinateurFiliere($filiereId);
}

// Récupérer le cycle et le niveau
$cycle = "Cycle Ingénieur"; // Valeur par défaut
$niveau = "GI1"; // Valeur par défaut
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Grades - Teacher Dashboard</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard-style.css">
    <!-- CSS personnalisé pour la page import_grades - appliqué également à upload_grades -->
    <link rel="stylesheet" href="../assets/css/import_grades_custom.css">

    <!-- Styles spécifiques pour la page upload_grades -->
    <style>
        /* Styles supplémentaires pour les filtres */
        .filter-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            border: none;
            animation: slideIn 0.5s ease-in-out;
        }

        .filter-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
            display: block;
        }

        .filter-select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background-color: white;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .filter-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            outline: none;
        }

        .form-text {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }

        #module-info {
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
            border-color: #bee5eb;
        }

        #module-info strong {
            color: #495057;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        #module-info div:not(.row) {
            color: #007bff;
            font-weight: 600;
            font-size: 14px;
            margin-top: 2px;
        }

        .btn-lg {
            padding: 12px 30px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-lg:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        #loading-status {
            padding: 40px;
            background-color: #f8f9fa;
            border-radius: 10px;
            border: 2px dashed #dee2e6;
        }

        #loading-status .spinner-border {
            width: 3rem;
            height: 3rem;
        }

        /* Animation pour la transition en douceur */
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .animate-fade-in {
            animation: slideIn 0.5s ease-in-out;
        }
    </style>
</head>
<body>
    <!-- Toast Container for Notifications -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3" id="toast-container">
        <!-- Toasts will be added here dynamically -->
    </div>

    <!-- Status Message Area -->
    <div id="status-message-container" class="position-fixed top-0 start-50 translate-middle-x p-3" style="z-index: 1050; display: none;">
        <div id="status-message" class="alert alert-info text-center">
            <!-- Status messages will be displayed here -->
        </div>
    </div>

    <div class="dashboard-container">
        <!-- Sidebar -->
        <?php include_once '../includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Navbar -->
            <?php include_once '../includes/header.php'; ?>

            <!-- Page Content -->
            <div class="container-fluid p-4">
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>Saisie des Notes</h2>
                </div>

                <!-- Module Selection Card -->
                <div class="card filter-card animate-fade-in mb-4">
                    <div class="card-header d-flex align-items-center">
                        <div class="filter-icon me-2">
                            <i class="bi bi-book text-primary"></i>
                        </div>
                        <div class="card-title mb-0 fw-bold">
                            Sélection du Module et Session
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="filter-group mb-3">
                                    <label class="filter-label" for="module">
                                        <i class="bi bi-book me-1"></i>Module
                                    </label>
                                    <select class="filter-select" id="module" required>
                                        <option value="" disabled selected>Chargement des modules...</option>
                                        <!-- Modules will be loaded here -->
                                    </select>
                                    <div class="form-text">Sélectionnez le module pour lequel vous souhaitez saisir les notes</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="filter-group mb-3">
                                    <label class="filter-label" for="session">
                                        <i class="bi bi-calendar-event me-1"></i>Session
                                    </label>
                                    <select class="filter-select" id="session" required>
                                        <option value="" disabled selected>Sélectionnez une session</option>
                                        <option value="normale">Session Normale</option>
                                        <option value="rattrapage">Session de Rattrapage</option>
                                    </select>
                                    <div class="form-text">Choisissez la session d'examen</div>
                                </div>
                            </div>
                        </div>

                        <!-- Module Information Display -->
                        <div id="module-info" class="alert alert-info" style="display: none;">
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>Module:</strong>
                                    <div id="selected-module-name">-</div>
                                </div>
                                <div class="col-md-3">
                                    <strong>Filière:</strong>
                                    <div id="selected-filiere-name">-</div>
                                </div>
                                <div class="col-md-3">
                                    <strong>Niveau:</strong>
                                    <div id="selected-niveau-name">-</div>
                                </div>
                                <div class="col-md-3">
                                    <strong>Semestre:</strong>
                                    <div id="selected-semestre-name">-</div>
                                </div>
                            </div>
                        </div>

                        <div class="button-container text-center mt-3">
                            <button type="button" class="btn btn-primary btn-lg" id="display-button" disabled>
                                <i class="bi bi-search me-2"></i>Afficher les Étudiants
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Loading Status -->
                <div id="loading-status" class="text-center" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2">Chargement des modules...</p>
                </div>


            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/sidebar.js"></script>

    <!-- Initialize teacher ID for JavaScript -->
    <script>
        // Pass the teacher ID from PHP to JavaScript
        window.currentTeacherId = <?php echo json_encode($teacherId); ?>;
        console.log('Teacher ID from PHP:', window.currentTeacherId);
    </script>

    <script src="../assets/js/upload_grades_simple.js"></script>
</body>
</html>