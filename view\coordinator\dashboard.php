<?php
// Vérifier l'authentification
require_once '../includes/auth_check_coordinateur.php';

// Récupérer les informations du coordinateur depuis la session
$userName = $_SESSION['user']['username'] ?? 'Coordinateur';
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$filiereName = $_SESSION['user']['filiere_name'] ?? 'Non spécifié';

// Construire le nom complet
$fullName = $prenom . ' ' . $nom;
if (trim($fullName) === '') {
    $fullName = $userName;
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coordinateur - Tableau de Bord</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard-style.css">
    <link rel="stylesheet" href="../assets/css/header-fix.css">
</head>
<body>
    <div class="dashboard-container">
        <?php
        include '../includes/sidebar.php';

        // Inclure le modèle des visites et enregistrer la visite du dashboard
        require_once '../../model/visitsModel.php';
        recordVisit('coordinateur', 'dashboard');
        ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>
            <div class="container-fluid p-4">
                <h1 class="page-title">Tableau de Bord - Coordinateur</h1>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="text-muted">Bienvenue, <strong><?php echo htmlspecialchars($fullName); ?></strong>. Vous êtes coordinateur de la filière <strong><?php echo htmlspecialchars($filiereName); ?></strong>.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <div class="d-inline-block me-2">
                            <span class="badge bg-primary rounded-pill">
                                <?php echo date('d M Y'); ?>
                            </span>
                        </div>
                        <div class="d-inline-block me-2">
                            <span class="badge bg-info rounded-pill">
                                <i class="fas fa-clock me-1"></i> <span id="current-time"></span>
                            </span>
                        </div>
                        <div class="d-inline-block">
                            <span class="badge bg-secondary rounded-pill">
                                <i class="fas fa-user me-1"></i> Coordinateur
                            </span>
                        </div>
                    </div>
                </div>

                <div class="row g-4">
                    <!-- Carte des Modules de la Filière -->
                    <div class="col-md-6 col-lg-4 animate-fade-in delay-1">
                        <a href="listerUEfiliere.php" class="text-decoration-none">
                            <div class="card dashboard-card card-modules h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="card-icon icon-modules">
                                            <i class="fas fa-book"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Modules de la Filière</h5>
                                            <div class="small text-muted">Gestion des modules</div>
                                        </div>
                                    </div>
                                    <ul class="dashboard-list">
                                        <!-- Données statiques pour les modules de la filière -->
                                        <li><span><i class="fas fa-book-open me-2 text-primary"></i>Programmation Avancée</span></li>
                                        <li><span><i class="fas fa-book-open me-2 text-primary"></i>Réseaux Informatiques</span></li>
                                        <li><span><i class="fas fa-book-open me-2 text-primary"></i>Systèmes d'Information</span></li>
                                    </ul>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte des Enseignants Vacataires -->
                    <div class="col-md-6 col-lg-4 animate-fade-in delay-2">
                        <a href="create_adjuncts.php" class="text-decoration-none">
                            <div class="card dashboard-card card-adjuncts h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="card-icon icon-adjuncts">
                                            <i class="fas fa-user-tie"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Vacataires</h5>
                                            <div class="small text-muted">Gestion des vacataires</div>
                                        </div>
                                    </div>
                                    <div class="text-center py-4">
                                        <p>Gérez les comptes des enseignants vacataires et assignez-leur des modules.</p>
                                        <button class="btn btn-outline-primary mt-2">Gérer les vacataires</button>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte des Descriptifs de Cours -->
                    <div class="col-md-6 col-lg-4 animate-fade-in delay-3">
                        <a href="descriptif.php" class="text-decoration-none">
                            <div class="card dashboard-card card-descriptions h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="card-icon icon-descriptions">
                                            <i class="fas fa-file-alt"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Descriptifs de Cours</h5>
                                            <div class="small text-muted">Gestion des descriptifs</div>
                                        </div>
                                    </div>
                                    <div class="text-center py-4">
                                        <p>Créez et importez les descriptifs de cours pour les modules de la filière.</p>
                                        <button class="btn btn-outline-primary mt-2">Gérer les descriptifs</button>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Groupes TD/TP -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-4">
                        <a href="define_groups.php" class="text-decoration-none">
                            <div class="card dashboard-card card-groups square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-groups mb-3">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <h5 class="card-title">Groupes TD/TP</h5>
                                    <div class="small text-muted">Définir les groupes</div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Emploi du Temps -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-5">
                        <a href="upload_timetables.php" class="text-decoration-none">
                            <div class="card dashboard-card card-schedule square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-schedule mb-3">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <h5 class="card-title">Emploi du Temps</h5>
                                    <div class="small text-muted">Gérer les horaires</div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Affectations -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-6">
                        <a href="ListerAffUECord.php" class="text-decoration-none">
                            <div class="card dashboard-card card-assignments square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-assignments mb-3">
                                        <i class="fas fa-tasks"></i>
                                    </div>
                                    <h5 class="card-title">Affectations</h5>
                                    <div class="small text-muted">Voir les affectations</div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Communication -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-7">
                        <a href="messages.php" class="text-decoration-none">
                            <div class="card dashboard-card card-messages square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-messages mb-3">
                                        <i class="fas fa-comments"></i>
                                    </div>
                                    <h5 class="card-title">Messages</h5>
                                    <div class="small text-muted">Communiquer avec l'équipe</div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Dashboard Scripts -->
    <script src="../assets/js/dashboard-dynamic.js"></script>
    <script src="../assets/js/dashboard-notifications.js"></script>
    <!-- Notifications JS -->
    <script src="../assets/js/notifications.js"></script>
    <script src="../assets/js/sidebar.js"></script>
</body>
</html>