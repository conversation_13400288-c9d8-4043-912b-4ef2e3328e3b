<?php
require_once __DIR__ . '/../config/db.php';

function createEvent($title, $description, $category, $event_date, $event_time, $location, $send_notification = 1) {
    try {
        $conn = getConnection();
        $stmt = $conn->prepare("INSERT INTO events (title, description, category, event_date, event_time, location, send_notification, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
        $stmt->bind_param("ssssssi", $title, $description, $category, $event_date, $event_time, $location, $send_notification);

        if (!$stmt->execute()) {
            error_log("Erreur lors de la création de l'événement: " . $stmt->error);
            return false;
        }

        $event_id = $conn->insert_id;

        // Si l'option d'envoi de notification est activée, créer une notification
        if ($send_notification == 1) {
            sendEventNotification($event_id, $title, $description, $event_date, $event_time, $location);
        }

        return $event_id;
    } catch (Exception $e) {
        error_log("Exception dans createEvent: " . $e->getMessage());
        return false;
    }
}

function addEventImage($event_id, $photo_url) {
    try {
        $conn = getConnection();
        $stmt = $conn->prepare("INSERT INTO events_image (id_event, photo_url) VALUES (?, ?)");
        $stmt->bind_param("is", $event_id, $photo_url);

        return $stmt->execute();
    } catch (Exception $e) {
        error_log("Exception dans addEventImage: " . $e->getMessage());
        return false;
    }
}

function getEvents($category = null) {
    try {
        $conn = getConnection();
        $query = "SELECT e.*, GROUP_CONCAT(ei.photo_url) as images
                 FROM events e
                 LEFT JOIN events_image ei ON e.id_event = ei.id_event";

        if ($category) {
            $query .= " WHERE e.category = ?";
        }

        $query .= " GROUP BY e.id_event ORDER BY e.event_date, e.event_time";

        $stmt = $conn->prepare($query);

        if ($category) {
            $stmt->bind_param("s", $category);
        }

        if (!$stmt->execute()) {
            error_log("Erreur lors de la récupération des événements: " . $stmt->error);
            return [];
        }

        $result = $stmt->get_result();
        $events = [];

        while ($row = $result->fetch_assoc()) {
            if ($row['images']) {
                $row['images'] = explode(',', $row['images']);
            } else {
                $row['images'] = [];
            }
            $events[] = $row;
        }

        return $events;
    } catch (Exception $e) {
        error_log("Exception dans getEvents: " . $e->getMessage());
        return [];
    }
}

function getEventById($event_id) {
    try {
        $conn = getConnection();
        $stmt = $conn->prepare("SELECT e.*, GROUP_CONCAT(ei.photo_url) as images
                              FROM events e
                              LEFT JOIN events_image ei ON e.id_event = ei.id_event
                              WHERE e.id_event = ?
                              GROUP BY e.id_event");
        $stmt->bind_param("i", $event_id);

        if (!$stmt->execute()) {
            error_log("Erreur lors de la récupération de l'événement: " . $stmt->error);
            return null;
        }

        $result = $stmt->get_result();
        $event = $result->fetch_assoc();

        if ($event && $event['images']) {
            $event['images'] = explode(',', $event['images']);
        } else if ($event) {
            $event['images'] = [];
        }

        return $event;
    } catch (Exception $e) {
        error_log("Exception dans getEventById: " . $e->getMessage());
        return null;
    }
}

function updateEvent($event_id, $title, $description, $category, $event_date, $event_time, $location, $send_notification) {
    try {
        $conn = getConnection();
        $stmt = $conn->prepare("UPDATE events
                              SET title = ?, description = ?, category = ?,
                                  event_date = ?, event_time = ?, location = ?,
                                  send_notification = ?
                              WHERE id_event = ?");
        $stmt->bind_param("ssssssis", $title, $description, $category,
                         $event_date, $event_time, $location,
                         $send_notification, $event_id);

        $result = $stmt->execute();

        // Si l'option d'envoi de notification est activée, créer une notification
        if ($result && $send_notification == 1) {
            sendEventNotification($event_id, $title, $description, $event_date, $event_time, $location);
        }

        return $result;
    } catch (Exception $e) {
        error_log("Exception dans updateEvent: " . $e->getMessage());
        return false;
    }
}

function deleteEvent($event_id) {
    try {
        $conn = getConnection();
        // Supprimer d'abord les images associées
        $stmt = $conn->prepare("DELETE FROM events_image WHERE id_event = ?");
        $stmt->bind_param("i", $event_id);
        $stmt->execute();

        // Puis supprimer l'événement
        $stmt = $conn->prepare("DELETE FROM events WHERE id_event = ?");
        $stmt->bind_param("i", $event_id);

        return $stmt->execute();
    } catch (Exception $e) {
        error_log("Exception dans deleteEvent: " . $e->getMessage());
        return false;
    }
}

function deleteEventImage($event_id, $photo_url) {
    try {
        $conn = getConnection();
        $stmt = $conn->prepare("DELETE FROM events_image WHERE id_event = ? AND photo_url = ?");
        $stmt->bind_param("is", $event_id, $photo_url);

        return $stmt->execute();
    } catch (Exception $e) {
        error_log("Exception dans deleteEventImage: " . $e->getMessage());
        return false;
    }
}

/**
 * Envoie une notification concernant un événement
 *
 * @param int $event_id ID de l'événement
 * @param string $title Titre de l'événement
 * @param string $description Description de l'événement
 * @param string $event_date Date de l'événement
 * @param string $location Lieu de l'événement
 * @param string $action Action effectuée (non utilisé, gardé pour compatibilité)
 * @return bool Succès ou échec de l'envoi de la notification
 */
function sendEventNotification($event_id, $title, $description, $event_date, $event_time = null, $location = '', $action = '') {
    try {
        // Inclure le modèle des notifications
        require_once __DIR__ . '/notificationsModel.php';

        // Récupérer l'heure de l'événement si elle n'est pas fournie
        if ($event_time === null) {
            $conn = getConnection();
            $stmt = $conn->prepare("SELECT event_time FROM events WHERE id_event = ?");
            $stmt->bind_param("i", $event_id);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($row = $result->fetch_assoc()) {
                $event_time = $row['event_time'];
            }
        }

        // Formater la date et l'heure pour l'affichage
        $formatted_date = date('d/m/Y', strtotime($event_date));
        $formatted_time = $event_time ? date('H:i', strtotime($event_time)) : '';

        // Créer le titre de la notification
        $notification_title = "Événement : $title";

        // Créer le message de la notification
        $notification_message = "Événement : $title\n";
        $notification_message .= "Date : $formatted_date\n";

        if ($formatted_time) {
            $notification_message .= "Heure : $formatted_time\n";
        }

        if ($location) {
            $notification_message .= "Lieu : $location\n";
        }

        $notification_message .= "\nDescription :\n";

        // Ajouter la description complète ou un extrait si elle est très longue
        if (strlen($description) > 300) {
            $notification_message .= substr($description, 0, 297) . '...';
        } else {
            $notification_message .= $description;
        }

        // Créer un lien vers l'événement
        $media_url = "view/admin/events.php?view=$event_id";

        // Créer la notification
        $result = createNotification(
            $notification_title,
            $notification_message,
            $media_url,
            null, // Pas de fichier attaché
            'event' // Type de notification
        );

        if ($result) {
            error_log("Notification d'événement créée avec succès pour l'événement #$event_id");
        } else {
            error_log("Échec de la création de la notification pour l'événement #$event_id");
        }

        return $result;
    } catch (Exception $e) {
        error_log("Exception dans sendEventNotification: " . $e->getMessage());
        return false;
    }
}
?>