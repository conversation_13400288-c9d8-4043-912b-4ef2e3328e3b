<?php
/**
 * <PERSON>chier de vérification d'authentification pour les chefs de département
 *
 * Ce fichier vérifie si l'utilisateur est authentifié et a le rôle de chef de département.
 * Il doit être inclus au début de chaque page réservée aux chefs de département.
 */

// Inclure le fichier de constantes
require_once __DIR__ . '/../../config/constants.php';

// Inclure les fonctions d'authentification
require_once __DIR__ . '/../../utils/auth.php';

// Démarrer la session si elle n'est pas déjà démarrée
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Vérifier si l'utilisateur est connecté
if (!isLoggedIn()) {
    // Sauvegarder l'URL actuelle pour redirection après connexion
    $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];

    // Rediriger vers la page de connexion
    header('Location: ' . BASE_URL . '/index.php');
    exit;
}

// Vérifier l'expiration de la session (30 minutes)
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > 1800)) {
    // Détruire la session
    session_destroy();

    // Rediriger vers la page de connexion
    header('Location: ' . BASE_URL . '/index.php?expired=1');
    exit;
}

// Vérifier si l'IP a changé
if (isset($_SESSION['ip']) && $_SESSION['ip'] !== $_SERVER['REMOTE_ADDR']) {
    // Détruire la session
    session_destroy();

    // Rediriger vers la page de connexion
    header('Location: ' . BASE_URL . '/index.php?security=1');
    exit;
}

// Mettre à jour le timestamp de dernière activité
$_SESSION['last_activity'] = time();

// Fonction pour vérifier si l'utilisateur est un chef de département
if (!function_exists('isDepartmentHead')) {
    function isDepartmentHead() {
        return isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'chef de departement';
    }
}

// Vérifier si l'utilisateur est un chef de département ou un administrateur
if (!isDepartmentHead() && !isAdmin()) {
    // Rediriger vers la page d'erreur
    header('Location: ' . BASE_URL . '/view/unauthorized.php');
    exit;
}

// Si l'utilisateur est un chef de département, vérifier que son department_id est disponible
if (isDepartmentHead()) {
    // Inclure le modèle d'authentification pour accéder à la fonction getDepartmentHeadDepartment
    require_once __DIR__ . '/../../model/authModel.php';

    // Vérifier si le department_id est déjà dans la session
    if (!isset($_SESSION['user']['department_id'])) {
        // Récupérer le department_id
        $departmentId = getDepartmentHeadDepartment($_SESSION['user']['username']);

        // Si aucun department_id n'est trouvé, rediriger vers une page d'erreur
        if (!$departmentId) {
            // Enregistrer un message d'erreur dans la session
            $_SESSION['error_message'] = "Vous n'êtes pas associé à un département en tant que chef. Veuillez contacter l'administrateur.";

            // Rediriger vers une page d'erreur
            header('Location: ' . BASE_URL . '/view/error.php');
            exit;
        }

        // Stocker le department_id dans la session
        $_SESSION['user']['department_id'] = $departmentId;
    }
}
?>
