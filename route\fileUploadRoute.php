<?php
// Désactiver le cache
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

// Définir le type de contenu
header('Content-Type: application/json; charset=utf-8');

// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Fonction pour gérer les erreurs et renvoyer une réponse JSON
function handleError($message, $code = 500) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'error' => $message
    ]);
    exit;
}

// Inclure les fichiers nécessaires
try {
    require_once '../config/db.php';
} catch (Exception $e) {
    handleError('Erreur lors du chargement des fichiers requis: ' . $e->getMessage());
}

// Vérifier si la requête est une requête POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérifier si un fichier a été uploadé
    try {
        if (!isset($_FILES['file'])) {
            handleError('Aucun fichier n\'a été envoyé', 400);
        }

        if ($_FILES['file']['error'] !== UPLOAD_ERR_OK) {
            $errorMessages = [
                UPLOAD_ERR_INI_SIZE => 'Le fichier dépasse la taille maximale autorisée par PHP',
                UPLOAD_ERR_FORM_SIZE => 'Le fichier dépasse la taille maximale autorisée par le formulaire',
                UPLOAD_ERR_PARTIAL => 'Le fichier n\'a été que partiellement téléchargé',
                UPLOAD_ERR_NO_FILE => 'Aucun fichier n\'a été téléchargé',
                UPLOAD_ERR_NO_TMP_DIR => 'Dossier temporaire manquant',
                UPLOAD_ERR_CANT_WRITE => 'Échec de l\'écriture du fichier sur le disque',
                UPLOAD_ERR_EXTENSION => 'Une extension PHP a arrêté le téléchargement du fichier'
            ];

            $errorMessage = isset($errorMessages[$_FILES['file']['error']])
                ? $errorMessages[$_FILES['file']['error']]
                : 'Erreur inconnue lors du téléchargement';

            handleError($errorMessage, 400);
        }

        // Créer le dossier uploads/messages s'il n'existe pas
        $uploadDir = '../uploads/messages/';
        if (!file_exists($uploadDir)) {
            if (!mkdir($uploadDir, 0777, true)) {
                handleError('Impossible de créer le dossier de destination');
            }
        }

        // Vérifier que le dossier est accessible en écriture
        if (!is_writable($uploadDir)) {
            handleError('Le dossier de destination n\'est pas accessible en écriture');
        }

        // Générer un nom de fichier unique
        $fileName = uniqid() . '_' . basename($_FILES['file']['name']);
        $uploadPath = $uploadDir . $fileName;

        // Déplacer le fichier uploadé vers le dossier uploads/messages
        if (!move_uploaded_file($_FILES['file']['tmp_name'], $uploadPath)) {
            handleError('Échec du déplacement du fichier téléchargé');
        }

        // Chemin relatif pour stocker dans la base de données
        $relativePath = 'uploads/messages/' . $fileName;

        // Ajouter des logs pour le débogage
        error_log("File uploaded successfully: " . $uploadPath);
        error_log("Relative path for database: " . $relativePath);

        // Retourner le chemin du fichier
        echo json_encode([
            'success' => true,
            'file_path' => $relativePath,
            'file_name' => basename($_FILES['file']['name'])
        ]);
    } catch (Exception $e) {
        handleError('Erreur lors du téléchargement du fichier: ' . $e->getMessage());
    }
} else {
    // Méthode non autorisée
    handleError('Méthode non autorisée', 405);
}
