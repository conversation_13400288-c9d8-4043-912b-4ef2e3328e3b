<?php
require_once __DIR__ . '/../config/db.php';

/**
 * Vérifie si la table pdf_grades existe et la crée si nécessaire
 *
 * @return bool True si la table existe ou a été créée avec succès, False sinon
 */
function ensurePdfGradesTableExists() {
    $conn = getConnection();
    if (!$conn) {
        error_log("Database connection error in ensurePdfGradesTableExists");
        return false;
    }

    // Vérifier si la table existe
    $checkTableQuery = "SHOW TABLES LIKE 'pdf_grades'";
    $tableExists = mysqli_query($conn, $checkTableQuery);

    if (mysqli_num_rows($tableExists) == 0) {
        // La table n'existe pas, la créer
        $createTableQuery = "CREATE TABLE pdf_grades (
            id INT AUTO_INCREMENT PRIMARY KEY,
            id_filiere INT NOT NULL,
            id_enseignant INT NOT NULL,
            id_module INT NOT NULL,
            id_niveau INT NOT NULL,
            id_semestre INT NOT NULL,
            session VARCHAR(50) NOT NULL,
            file_path VARCHAR(255) NOT NULL,
            upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX (id_filiere),
            INDEX (id_enseignant),
            INDEX (id_module),
            INDEX (id_niveau)
        )";

        if (!mysqli_query($conn, $createTableQuery)) {
            $error = mysqli_error($conn);
            error_log("Error creating pdf_grades table: " . $error);
            mysqli_close($conn);
            return false;
        }

        error_log("Table pdf_grades created successfully");
    }

    mysqli_close($conn);
    return true;
}

/**
 * Enregistre un nouveau PDF de notes dans la base de données
 *
 * @param int $id_filiere ID de la filière
 * @param int $id_enseignant ID de l'enseignant
 * @param int $id_module ID du module
 * @param int $id_niveau ID du niveau
 * @param int $id_semestre ID du semestre
 * @param string $session Session
 * @param string $filename Nom du fichier PDF (sera enregistré dans la colonne file_path)
 * @return array Résultat de l'opération
 */
function savePdfGrade($id_filiere, $id_enseignant, $id_module, $id_niveau, $id_semestre, $session, $filename) {
    // S'assurer que la table existe
    if (!ensurePdfGradesTableExists()) {
        return ["error" => "Failed to ensure pdf_grades table exists"];
    }

    $conn = getConnection();
    if (!$conn) {
        error_log("Database connection error in savePdfGrade");
        return ["error" => "Database connection error"];
    }

    // Échapper les valeurs pour éviter les injections SQL
    $id_filiere = mysqli_real_escape_string($conn, $id_filiere);
    $id_enseignant = mysqli_real_escape_string($conn, $id_enseignant);
    $id_module = mysqli_real_escape_string($conn, $id_module);
    $id_niveau = mysqli_real_escape_string($conn, $id_niveau);
    $id_semestre = mysqli_real_escape_string($conn, $id_semestre);
    $session = mysqli_real_escape_string($conn, $session);
    $filename = mysqli_real_escape_string($conn, $filename);

    // Insérer le nouvel enregistrement
    $query = "INSERT INTO pdf_grades (id_filiere, id_enseignant, id_module, id_niveau, id_semestre, session, file_path)
              VALUES ('$id_filiere', '$id_enseignant', '$id_module', '$id_niveau', '$id_semestre', '$session', '$filename')";

    error_log("Executing query: " . $query);

    if (!mysqli_query($conn, $query)) {
        $error = mysqli_error($conn);
        error_log("Error inserting pdf_grade: " . $error);
        mysqli_close($conn);
        return ["error" => "Error saving PDF record: " . $error];
    }

    $id = mysqli_insert_id($conn);
    mysqli_close($conn);

    return ["id" => $id];
}

/**
 * Récupère les PDF de notes en fonction des filtres
 *
 * @param int $id_filiere ID de la filière (optionnel)
 * @param int $id_enseignant ID de l'enseignant (optionnel)
 * @param int $id_module ID du module (optionnel)
 * @param int $id_niveau ID du niveau (optionnel)
 * @param int $id_semestre ID du semestre (optionnel)
 * @param string $session Session (optionnel)
 * @return array Liste des PDF de notes
 */
function getPdfGrades($id_filiere = null, $id_enseignant = null, $id_module = null, $id_niveau = null, $id_semestre = null, $session = null) {
    // S'assurer que la table existe
    if (!ensurePdfGradesTableExists()) {
        return ["error" => "Failed to ensure pdf_grades table exists"];
    }

    $conn = getConnection();
    if (!$conn) {
        error_log("Database connection error in getPdfGrades");
        return ["error" => "Database connection error"];
    }

    // Construire la requête avec les filtres
    $query = "SELECT * FROM pdf_grades WHERE 1=1";

    if ($id_filiere !== null) {
        $id_filiere = mysqli_real_escape_string($conn, $id_filiere);
        $query .= " AND id_filiere = '$id_filiere'";
    }

    if ($id_enseignant !== null) {
        $id_enseignant = mysqli_real_escape_string($conn, $id_enseignant);
        $query .= " AND id_enseignant = '$id_enseignant'";
    }

    if ($id_module !== null) {
        $id_module = mysqli_real_escape_string($conn, $id_module);
        $query .= " AND id_module = '$id_module'";
    }

    if ($id_niveau !== null) {
        $id_niveau = mysqli_real_escape_string($conn, $id_niveau);
        $query .= " AND id_niveau = '$id_niveau'";
    }

    if ($id_semestre !== null) {
        $id_semestre = mysqli_real_escape_string($conn, $id_semestre);
        $query .= " AND id_semestre = '$id_semestre'";
    }

    if ($session !== null) {
        $session = mysqli_real_escape_string($conn, $session);
        // Utiliser LOWER pour rendre la comparaison insensible à la casse
        $query .= " AND LOWER(session) = LOWER('$session')";

        // Log pour débogage
        error_log("Filtrage par session: '$session'");
    }

    // Utiliser id pour le tri (par ordre décroissant)
    $query .= " ORDER BY id DESC";

    // Log complet de la requête pour débogage
    error_log("Query for getPdfGrades: " . $query);

    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error fetching pdf_grades: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching PDF records: " . $error];
    }

    $pdfGrades = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $pdfGrades[] = $row;
    }

    mysqli_close($conn);
    return $pdfGrades;
}
?>
