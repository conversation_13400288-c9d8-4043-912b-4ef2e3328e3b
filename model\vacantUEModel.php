<?php
require_once __DIR__ . "/../config/db.php";

/**
 * Create the ue_vacantes table if it doesn't exist
 *
 * @return bool True on success, false on failure
 */
function createUeVacantesTable() {
    $conn = getConnection();
    
    if (!$conn) {
        error_log("Database connection error in createUeVacantesTable");
        return false;
    }
    
    $sql = "CREATE TABLE IF NOT EXISTS ue_vacantes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        ue_id INT NOT NULL,
        department_id INT NOT NULL,
        academic_year VARCHAR(9) NOT NULL,
        is_vacant BOOLEAN NOT NULL DEFAULT FALSE,
        marked_by INT NOT NULL,
        marked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        comments TEXT,
        UNIQUE KEY unique_ue_dept_year (ue_id, department_id, academic_year),
        FOREIGN KEY (ue_id) REFERENCES uniteenseignement(id) ON DELETE CASCADE,
        FOREIGN KEY (marked_by) REFERENCES enseignant(id_enseignant) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    $result = mysqli_query($conn, $sql);
    
    if (!$result) {
        error_log("Error creating ue_vacantes table: " . mysqli_error($conn));
        mysqli_close($conn);
        return false;
    }
    
    mysqli_close($conn);
    return true;
}

/**
 * Get current academic year
 *
 * @return string Current academic year in format YYYY-YYYY
 */
function getCurrentAcademicYear() {
    $currentMonth = date('n'); // 1-12
    $currentYear = date('Y');
    
    // If we're in the second half of the calendar year (August-December),
    // the academic year is currentYear/currentYear+1
    if ($currentMonth >= 8) {
        return $currentYear . '-' . ($currentYear + 1);
    }
    // If we're in the first half of the calendar year (January-July),
    // the academic year is currentYear-1/currentYear
    else {
        return ($currentYear - 1) . '-' . $currentYear;
    }
}

/**
 * Get all unassigned teaching units for a department
 *
 * @param int $departmentId The department ID
 * @param string $academicYear The academic year (optional, defaults to current)
 * @return array Array of unassigned teaching units or error
 */
function getUnassignedTeachingUnits($departmentId, $academicYear = null) {
    $conn = getConnection();
    
    if (!$conn) {
        error_log("Database connection error in getUnassignedTeachingUnits");
        return ["error" => "Database connection error"];
    }
    
    // Ensure ue_vacantes table exists
    createUeVacantesTable();
    
    if (!$academicYear) {
        $academicYear = getCurrentAcademicYear();
    }
    
    $departmentId = mysqli_real_escape_string($conn, $departmentId);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);
    
    // Get all teaching units from the department that are NOT assigned for the current academic year
    $sql = "SELECT DISTINCT ue.id as ue_id, ue.type as ue_type, ue.volume_horaire, ue.nb_groupes,
            m.id as module_id, m.nom as module_name, m.volume_total,
            f.nom_filiere, f.id_filiere, n.nom as niveau, s.nom as nom_specialite, sem.nom as semestre,
            uv.is_vacant, uv.comments, uv.marked_at,
            CONCAT(e.prenom, ' ', e.nom) as marked_by_name
            FROM uniteenseignement ue
            LEFT JOIN module m ON ue.module_id = m.id
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveaux n ON m.id_niveau = n.id
            LEFT JOIN specialite s ON m.specialite_id = s.id
            LEFT JOIN semestre sem ON m.id_semestre = sem.id
            LEFT JOIN affectation a ON ue.id = a.unite_enseignement_id AND a.annee_academique = '$academicYear'
            LEFT JOIN ue_vacantes uv ON ue.id = uv.ue_id AND uv.department_id = '$departmentId' AND uv.academic_year = '$academicYear'
            LEFT JOIN enseignant e ON uv.marked_by = e.id_enseignant
            WHERE f.id_dep = '$departmentId'
            AND a.id IS NULL
            ORDER BY f.nom_filiere, n.nom, sem.nom, m.nom, ue.type";
    
    $result = mysqli_query($conn, $sql);
    
    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getUnassignedTeachingUnits: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching unassigned teaching units: " . $error];
    }
    
    $unassignedUnits = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $unassignedUnits[] = $row;
    }
    
    mysqli_close($conn);
    return $unassignedUnits;
}

/**
 * Mark a teaching unit as vacant or not vacant
 *
 * @param int $ueId The teaching unit ID
 * @param int $departmentId The department ID
 * @param bool $isVacant Whether the unit is vacant
 * @param int $markedBy The ID of the person marking it
 * @param string $comments Optional comments
 * @param string $academicYear The academic year (optional, defaults to current)
 * @return array Success or error message
 */
function markTeachingUnitVacancy($ueId, $departmentId, $isVacant, $markedBy, $comments = null, $academicYear = null) {
    $conn = getConnection();
    
    if (!$conn) {
        error_log("Database connection error in markTeachingUnitVacancy");
        return ["error" => "Database connection error"];
    }
    
    // Ensure ue_vacantes table exists
    createUeVacantesTable();
    
    if (!$academicYear) {
        $academicYear = getCurrentAcademicYear();
    }
    
    $ueId = mysqli_real_escape_string($conn, $ueId);
    $departmentId = mysqli_real_escape_string($conn, $departmentId);
    $markedBy = mysqli_real_escape_string($conn, $markedBy);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);
    $isVacant = $isVacant ? 1 : 0;
    $comments = $comments ? mysqli_real_escape_string($conn, $comments) : null;
    
    // Check if record already exists
    $checkSql = "SELECT id FROM ue_vacantes 
                 WHERE ue_id = '$ueId' AND department_id = '$departmentId' AND academic_year = '$academicYear'";
    $checkResult = mysqli_query($conn, $checkSql);
    
    if (!$checkResult) {
        $error = mysqli_error($conn);
        error_log("Error checking existing vacancy record: " . $error);
        mysqli_close($conn);
        return ["error" => "Error checking existing record: " . $error];
    }
    
    if (mysqli_num_rows($checkResult) > 0) {
        // Update existing record
        $updateSql = "UPDATE ue_vacantes 
                      SET is_vacant = '$isVacant', marked_by = '$markedBy', marked_at = CURRENT_TIMESTAMP";
        
        if ($comments !== null) {
            $updateSql .= ", comments = '$comments'";
        }
        
        $updateSql .= " WHERE ue_id = '$ueId' AND department_id = '$departmentId' AND academic_year = '$academicYear'";
        
        $result = mysqli_query($conn, $updateSql);
        
        if (!$result) {
            $error = mysqli_error($conn);
            error_log("Error updating vacancy record: " . $error);
            mysqli_close($conn);
            return ["error" => "Error updating vacancy record: " . $error];
        }
    } else {
        // Insert new record
        $insertSql = "INSERT INTO ue_vacantes (ue_id, department_id, academic_year, is_vacant, marked_by";
        
        if ($comments !== null) {
            $insertSql .= ", comments";
        }
        
        $insertSql .= ") VALUES ('$ueId', '$departmentId', '$academicYear', '$isVacant', '$markedBy'";
        
        if ($comments !== null) {
            $insertSql .= ", '$comments'";
        }
        
        $insertSql .= ")";
        
        $result = mysqli_query($conn, $insertSql);
        
        if (!$result) {
            $error = mysqli_error($conn);
            error_log("Error inserting vacancy record: " . $error);
            mysqli_close($conn);
            return ["error" => "Error inserting vacancy record: " . $error];
        }
    }
    
    mysqli_close($conn);
    return ["success" => "Teaching unit vacancy status updated successfully"];
}

/**
 * Get department information by ID
 *
 * @param int $departmentId The department ID
 * @return array|null Department information or null if not found
 */
function getDepartmentById($departmentId) {
    $conn = getConnection();
    
    if (!$conn) {
        error_log("Database connection error in getDepartmentById");
        return null;
    }
    
    $departmentId = mysqli_real_escape_string($conn, $departmentId);
    
    $sql = "SELECT * FROM departement WHERE id_departement = '$departmentId'";
    $result = mysqli_query($conn, $sql);
    
    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getDepartmentById: " . $error);
        mysqli_close($conn);
        return null;
    }
    
    $department = null;
    if (mysqli_num_rows($result) > 0) {
        $department = mysqli_fetch_assoc($result);
    }
    
    mysqli_close($conn);
    return $department;
}

?>
