<?php
require_once __DIR__ . '/../config/db.php';

function checkDatabase() {
    $conn = getConnection();
    
    // Vérifier les tables existantes
    $tables = [];
    $result = $conn->query("SHOW TABLES");
    while ($row = $result->fetch_row()) {
        $tables[] = $row[0];
    }
    
    echo "<h2>Tables existantes</h2>";
    echo "<pre>";
    print_r($tables);
    echo "</pre>";
    
    // Vérifier la structure de la table messages
    if (in_array('messages', $tables)) {
        echo "<h2>Structure de la table messages</h2>";
        $result = $conn->query("DESCRIBE messages");
        echo "<pre>";
        while ($row = $result->fetch_assoc()) {
            print_r($row);
        }
        echo "</pre>";
        
        // Vérifier les données de la table messages
        echo "<h2>Données de la table messages</h2>";
        $result = $conn->query("SELECT * FROM messages");
        echo "<pre>";
        while ($row = $result->fetch_assoc()) {
            print_r($row);
        }
        echo "</pre>";
    }
    
    // Vérifier la structure de la table notifications
    if (in_array('notifications', $tables)) {
        echo "<h2>Structure de la table notifications</h2>";
        $result = $conn->query("DESCRIBE notifications");
        echo "<pre>";
        while ($row = $result->fetch_assoc()) {
            print_r($row);
        }
        echo "</pre>";
        
        // Vérifier les données de la table notifications
        echo "<h2>Données de la table notifications</h2>";
        $result = $conn->query("SELECT * FROM notifications");
        echo "<pre>";
        while ($row = $result->fetch_assoc()) {
            print_r($row);
        }
        echo "</pre>";
    }
    
    // Vérifier la structure de la table events
    if (in_array('events', $tables)) {
        echo "<h2>Structure de la table events</h2>";
        $result = $conn->query("DESCRIBE events");
        echo "<pre>";
        while ($row = $result->fetch_assoc()) {
            print_r($row);
        }
        echo "</pre>";
        
        // Vérifier les données de la table events
        echo "<h2>Données de la table events</h2>";
        $result = $conn->query("SELECT * FROM events");
        echo "<pre>";
        while ($row = $result->fetch_assoc()) {
            print_r($row);
        }
        echo "</pre>";
    }
}

// Exécuter la fonction
checkDatabase();
?>
