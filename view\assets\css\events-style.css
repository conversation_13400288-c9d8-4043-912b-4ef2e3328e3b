/* Styles pour la page de gestion des événements */

/* Styles généraux */
.page-title {
    color: #4a90e2;
    font-weight: 600;
    margin-bottom: 0;
}

/* Styles pour les cartes */
.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: none;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.card-body {
    padding: 1.5rem;
}

/* Styles pour les boutons */
.btn-primary {
    background-color: #4a90e2;
    border-color: #4a90e2;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #3a7bc8;
    border-color: #3a7bc8;
    transform: translateY(-2px);
}

.btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
    transition: all 0.3s ease;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    color: white;
}

/* Styles pour le tableau */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    color: #495057;
    border-top: none;
    background-color: #f8f9fa;
}

.table td {
    vertical-align: middle;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Styles pour les actions */
.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 5px;
    transition: all 0.3s ease;
}

.action-btn:hover {
    transform: translateY(-2px);
}

.view-btn {
    background-color: #4a90e2;
    color: white;
}

.view-btn:hover {
    background-color: #3a7bc8;
}

.edit-btn {
    background-color: #ffc107;
    color: white;
}

.edit-btn:hover {
    background-color: #e0a800;
}

.delete-btn {
    background-color: #dc3545;
    color: white;
}

.delete-btn:hover {
    background-color: #c82333;
}

/* Styles pour la pagination */
.pagination {
    margin-bottom: 0;
}

.page-link {
    color: #4a90e2;
    border-radius: 5px;
    margin: 0 2px;
}

.page-item.active .page-link {
    background-color: #4a90e2;
    border-color: #4a90e2;
}

/* Styles pour les modals */
.modal-content {
    border-radius: 10px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

/* Styles pour les formulaires */
.form-label {
    font-weight: 600;
    color: #495057;
}

.form-control, .form-select {
    border-radius: 5px;
    border: 1px solid #ced4da;
    padding: 0.5rem 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

/* Styles pour les images */
.image-preview {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
}

.image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-preview .remove-image {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 20px;
    height: 20px;
    background-color: rgba(220, 53, 69, 0.8);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 10px;
    transition: all 0.3s ease;
}

.image-preview .remove-image:hover {
    background-color: rgba(220, 53, 69, 1);
    transform: scale(1.1);
}

/* Styles pour l'indicateur de chargement */
.image-preview.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
}

/* Styles pour les images uploadées */
#imagesList {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

/* Styles pour le bouton d'upload */
#addImageBtn {
    height: 38px;
}

/* Styles pour la visualisation des événements */
.event-details h2 {
    color: #4a90e2;
    font-weight: 600;
}

.event-details p {
    margin-bottom: 0.5rem;
}

.event-details i {
    color: #6c757d;
}

.event-images img {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.event-images img:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Styles pour les filtres */
#applyFiltersBtn {
    height: 38px;
}

/* Styles pour les animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

/* Styles responsifs */
@media (max-width: 768px) {
    .action-btn {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }

    .event-images img {
        width: 120px;
        height: 120px;
    }
}

@media (max-width: 576px) {
    .table-responsive {
        border: none;
    }

    .event-images img {
        width: 100px;
        height: 100px;
    }
}
