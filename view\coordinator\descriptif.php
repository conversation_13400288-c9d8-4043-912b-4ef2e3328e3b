<?php
// Include the authentication check for coordinators
require_once '../includes/auth_check_coordinateur.php';

// Debug information
error_log("[DEBUG] Coordinator session info in descriptif.php:");
error_log("[DEBUG] Username: " . ($_SESSION['user']['username'] ?? 'not set'));
error_log("[DEBUG] Role: " . ($_SESSION['user']['role'] ?? 'not set'));
error_log("[DEBUG] Filiere ID: " . ($_SESSION['user']['filiere_id'] ?? 'not set'));
error_log("[DEBUG] Filiere Name: " . ($_SESSION['user']['filiere_name'] ?? 'not set'));
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teaching Units Management - UniAdmin</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/descriptif.css">
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4">
                <!-- Page Header with Icon -->
                <div class="descriptif-header">
                    <h1><i class="bi bi-journal-text me-2"></i>Teaching Units Creation</h1>
                </div>

                <!-- Alert Container -->
                <div id="alert-container"></div>

                <!-- Action Buttons at Top Right -->
                <div class="d-flex justify-content-end mb-4">
                    <button id="add-module-btn" class="btn btn-add-module me-2">
                        <i class="bi bi-plus-circle me-2"></i> Add New Module
                    </button>
                    <button id="import-modules-btn" class="btn btn-import-module">
                        <i class="bi bi-upload me-2"></i> Import Modules
                    </button>
                </div>

                <!-- Direct Module Creation Form -->
                <div class="direct-module-form-container">
                    <div class="card shadow-sm">
                        <div class="card-header bg-white">
                            <h5 class="mb-0"><i class="bi bi-journal-plus me-2 text-primary"></i>Create New Module</h5>
                        </div>
                        <div class="card-body">
                            <form id="direct-module-form">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="direct-module-nom" class="form-label">Module Name</label>
                                        <input type="text" id="direct-module-nom" name="nom" class="form-control" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="direct-module-volume-total" class="form-label">Total Volume (hours)</label>
                                        <input type="number" id="direct-module-volume-total" name="volume_total" class="form-control" min="1" required>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="direct-module-specialite" class="form-label">Specialite</label>
                                        <select id="direct-module-specialite" name="specialite_id" class="form-select" required>
                                            <option value="">Select Specialite</option>
                                            <!-- Will be populated dynamically -->
                                        </select>
                                        <!-- Hidden input for filiere_id -->
                                        <input type="hidden" id="direct-module-filiere" name="filiere_id" value="<?php echo $_SESSION['user']['filiere_id']; ?>">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="direct-module-niveau" class="form-label">Niveau</label>
                                        <select id="direct-module-niveau" name="id_niveau" class="form-select" required>
                                            <option value="">Select Niveau</option>
                                            <!-- Will be populated dynamically -->
                                        </select>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="direct-module-semestre" class="form-label">Semestre</label>
                                        <select id="direct-module-semestre" name="id_semestre" class="form-select" required>
                                            <option value="">Select Semestre</option>
                                            <!-- Will be populated dynamically -->
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Teaching Unit Types</label>
                                        <div class="d-flex">
                                            <div class="form-check me-3">
                                                <input class="form-check-input" type="checkbox" id="direct-module-is-cours" name="is_cours">
                                                <label class="form-check-label" for="direct-module-is-cours">Cours</label>
                                            </div>
                                            <div class="form-check me-3">
                                                <input class="form-check-input" type="checkbox" id="direct-module-is-td" name="is_td">
                                                <label class="form-check-label" for="direct-module-is-td">TD</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="direct-module-is-tp" name="is_tp">
                                                <label class="form-check-label" for="direct-module-is-tp">TP</label>
                                            </div>
                                        </div>

                                        <!-- Container for direct unit forms -->
                                        <div id="direct-unit-forms-container" class="mt-3"></div>
                                    </div>
                                </div>

                                <div class="text-end">
                                    <button type="button" id="direct-save-module-btn" class="btn btn-primary">
                                        <i class="bi bi-save me-1"></i> Save Module
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- View All Modules Button -->
                <div class="view-button-container text-center mt-4">
                    <a href="listerUEfiliere.php" class="btn btn-view-all btn-lg">
                        <i class="bi bi-eye me-2"></i> View All Modules
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Module Modal -->
    <div class="modal fade" id="add-module-modal" tabindex="-1" aria-labelledby="addModuleModalLabel">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addModuleModalLabel">Add New Module</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="add-module-form">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="module-nom" class="form-label">Module Name</label>
                                <input type="text" id="module-nom" name="nom" class="form-control" required>
                            </div>
                            <div class="col-md-6">
                                <label for="module-volume-total" class="form-label">Total Volume (hours)</label>
                                <input type="number" id="module-volume-total" name="volume_total" class="form-control" min="1" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="module-specialite" class="form-label">Specialite</label>
                                <select id="module-specialite" name="specialite_id" class="form-select" required>
                                    <option value="">Select Specialite</option>
                                    <!-- Will be populated dynamically -->
                                </select>
                                <!-- Hidden input for filiere_id -->
                                <input type="hidden" id="module-filiere" name="filiere_id" value="<?php echo $_SESSION['user']['filiere_id']; ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="module-niveau" class="form-label">Niveau</label>
                                <select id="module-niveau" name="id_niveau" class="form-select" required>
                                    <option value="">Select Niveau</option>
                                    <!-- Will be populated dynamically -->
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="module-semestre" class="form-label">Semestre</label>
                                <select id="module-semestre" name="id_semestre" class="form-select" required>
                                    <option value="">Select Semestre</option>
                                    <!-- Will be populated dynamically -->
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Teaching Unit Types</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="module-is-cours" name="is_cours">
                                <label class="form-check-label" for="module-is-cours">
                                    Cours
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="module-is-td" name="is_td">
                                <label class="form-check-label" for="module-is-td">
                                    TD (Travaux Dirigés)
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="module-is-tp" name="is_tp">
                                <label class="form-check-label" for="module-is-tp">
                                    TP (Travaux Pratiques)
                                </label>
                            </div>
                        </div>

                        <div id="unit-forms-container" class="mt-3">
                            <!-- Unit type forms will be added here dynamically -->
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" id="save-module-btn" class="btn btn-primary">Save Module</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Unit Modal -->
    <div class="modal fade" id="add-unit-modal" tabindex="-1" aria-labelledby="addUnitModalLabel">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addUnitModalLabel">Add Teaching Unit</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="add-unit-form">
                        <input type="hidden" id="unit-module-id" name="module_id">

                        <div class="mb-3">
                            <label for="unit-type" class="form-label">Unit Type</label>
                            <select id="unit-type" name="type" class="form-select" required>
                                <option value="">Select Type</option>
                                <option value="Cours">Cours</option>
                                <option value="TD">TD (Travaux Dirigés)</option>
                                <option value="TP">TP (Travaux Pratiques)</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="unit-volume-horaire" class="form-label">Volume Horaire (hours)</label>
                            <input type="number" id="unit-volume-horaire" name="volume_horaire" class="form-control" min="1" value="1" required>
                            <div class="form-text">Enter the total number of hours for this teaching unit.</div>
                        </div>

                        <!-- Hidden input for nb_groupes, always set to 1 -->
                        <input type="hidden" id="unit-nb-groupes" name="nb_groupes" value="1">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" id="save-unit-btn" class="btn btn-primary">Save Unit</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Module Modal -->
    <div class="modal fade" id="edit-module-modal" tabindex="-1" aria-labelledby="editModuleModalLabel">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModuleModalLabel">Edit Module</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="edit-module-form">
                        <input type="hidden" id="edit-module-id" name="id">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit-module-nom" class="form-label">Module Name</label>
                                <input type="text" id="edit-module-nom" name="nom" class="form-control" required>
                            </div>
                            <div class="col-md-6">
                                <label for="edit-module-volume-total" class="form-label">Total Volume (hours)</label>
                                <input type="number" id="edit-module-volume-total" name="volume_total" class="form-control" min="1" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit-module-specialite" class="form-label">Specialite</label>
                                <select id="edit-module-specialite" name="specialite_id" class="form-select" required>
                                    <option value="">Select Specialite</option>
                                    <!-- Will be populated dynamically -->
                                </select>
                                <!-- Hidden input for filiere_id -->
                                <input type="hidden" id="edit-module-filiere" name="filiere_id" value="<?php echo $_SESSION['user']['filiere_id']; ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="edit-module-niveau" class="form-label">Niveau</label>
                                <select id="edit-module-niveau" name="id_niveau" class="form-select" required>
                                    <option value="">Select Niveau</option>
                                    <!-- Will be populated dynamically -->
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit-module-semestre" class="form-label">Semestre</label>
                                <select id="edit-module-semestre" name="id_semestre" class="form-select" required>
                                    <option value="">Select Semestre</option>
                                    <!-- Will be populated dynamically -->
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Teaching Unit Types</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit-module-is-cours" name="is_cours">
                                <label class="form-check-label" for="edit-module-is-cours">
                                    Cours
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit-module-is-td" name="is_td">
                                <label class="form-check-label" for="edit-module-is-td">
                                    TD (Travaux Dirigés)
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit-module-is-tp" name="is_tp">
                                <label class="form-check-label" for="edit-module-is-tp">
                                    TP (Travaux Pratiques)
                                </label>
                            </div>
                        </div>

                        <!-- Container for edit unit forms -->
                        <div id="edit-unit-forms-container" class="mt-3">
                            <!-- Unit type forms will be added here dynamically -->
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" id="update-module-btn" class="btn btn-primary">Update Module</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Unit Modal -->
    <div class="modal fade" id="edit-unit-modal" tabindex="-1" aria-labelledby="editUnitModalLabel">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editUnitModalLabel">Edit Teaching Unit</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="edit-unit-form">
                        <input type="hidden" id="edit-unit-id" name="id">
                        <input type="hidden" id="edit-unit-module-id" name="module_id">

                        <div class="mb-3">
                            <label for="edit-unit-type" class="form-label">Unit Type</label>
                            <select id="edit-unit-type" name="type" class="form-select" required>
                                <option value="">Select Type</option>
                                <option value="Cours">Cours</option>
                                <option value="TD">TD (Travaux Dirigés)</option>
                                <option value="TP">TP (Travaux Pratiques)</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="edit-unit-volume-horaire" class="form-label">Volume Horaire (hours)</label>
                            <input type="number" id="edit-unit-volume-horaire" name="volume_horaire" class="form-control" min="1" required>
                            <div class="form-text">Enter the total number of hours for this teaching unit.</div>
                        </div>

                        <!-- Hidden input for nb_groupes, always set to 1 -->
                        <input type="hidden" id="edit-unit-nb-groupes" name="nb_groupes" value="1">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" id="update-unit-btn" class="btn btn-primary">Update Unit</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Import Modules Modal -->
    <div class="modal fade" id="import-modules-modal" tabindex="-1" aria-labelledby="importModulesModalLabel">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="importModulesModalLabel">Import Modules</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="import-modules-form" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="modules-file" class="form-label">Select CSV or Excel File</label>
                            <input type="file" id="modules-file" name="modules_file" class="form-control" accept=".csv,.xlsx,.xls" required>
                            <div class="form-text">
                                File should contain columns for module name, volume, specialite, filiere, niveau, semestre, and unit types.
                            </div>
                        </div>
                        <div id="import-modules-note" class="alert alert-info" style="display: none;">
                            <!-- This will be populated dynamically for coordinators -->
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="has-header" name="has_header" checked>
                                <label class="form-check-label" for="has-header">
                                    File has header row
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" id="import-modules-submit-btn" class="btn btn-primary">Import</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/sidebar.js"></script>
    <script src="../assets/js/descriptif.js"></script>
</body>
</html>