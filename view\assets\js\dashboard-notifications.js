document.addEventListener("DOMContentLoaded", function () {
    const notificationsContainer = document.querySelector('.card-notifications .dashboard-list');

    if (!notificationsContainer) {
        console.error("Container de notifications non trouvé");
        return;
    }

    // Afficher un message de chargement
    notificationsContainer.innerHTML = '<li><span>Chargement des notifications...</span></li>';

    // Chemin vers la route des notifications
    const basePath = "../../route/notificationsRoute.php";

    // Fonction pour formater la date
    function formatDate(dateStr) {
        const date = new Date(dateStr);
        const day = date.getDate().toString().padStart(2, '0');
        const month = date.toLocaleString('fr-FR', { month: 'short' });
        return `${day} ${month}`;
    }

    // Récupérer les notifications récentes
    fetch(`${basePath}?action=getAll`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (!data || data.length === 0) {
                notificationsContainer.innerHTML = '<li><span>Aucune notification</span></li>';
                return;
            }

            // Trier les notifications par date (les plus récentes d'abord)
            const sortedNotifications = data.sort((a, b) => {
                const dateA = new Date(a.created_at);
                const dateB = new Date(b.created_at);
                return dateB - dateA;
            });

            // Prendre les 3 dernières notifications
            const recentNotifications = sortedNotifications.slice(0, 3);

            // Afficher les notifications
            notificationsContainer.innerHTML = '';
            recentNotifications.forEach(notification => {
                const formattedDate = formatDate(notification.created_at);
                const li = document.createElement('li');

                // Créer la structure HTML qui correspond à celle du dashboard
                const iconType = getNotificationIcon(notification.type || 'default');
                li.innerHTML = `
                    <span><i class="fas ${iconType} me-2 text-warning"></i>${notification.title}</span>
                    <span class="notification-badge">${notification.is_read ? 'Lu' : 'Nouveau'}</span>
                `;

                notificationsContainer.appendChild(li);
            });

            // Fonction pour obtenir l'icône correspondant au type de notification
            function getNotificationIcon(type) {
                const icons = {
                    system: 'fa-gear',
                    update: 'fa-rotate-right',
                    message: 'fa-envelope',
                    assignment: 'fa-clipboard-list',
                    meeting: 'fa-calendar-check',
                    event: 'fa-calendar-day',
                    default: 'fa-info-circle'
                };
                return icons[type] || icons.default;
            }
        })
        .catch(error => {
            console.error('Erreur lors du chargement des notifications:', error);
            notificationsContainer.innerHTML = '<li><span><i class="fas fa-exclamation-circle me-2 text-danger"></i>Erreur lors du chargement des notifications</span></li>';
        });
});