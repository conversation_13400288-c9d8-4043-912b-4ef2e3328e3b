<?php
require_once __DIR__ . "/../utils/response.php";
require_once __DIR__ . "/../controller/adminController.php";
require_once __DIR__ . "/../controller/photoController.php";

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    setJsonHeaders();
    http_response_code(200);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];
$cni = isset($_GET['cni']) ? $_GET['cni'] : null;

try {
    switch ($method) {
        case 'GET':
            if ($cni) {
                getAdminByCNIAPI($cni);
            } else {
                getAllAdminsAPI();
            }
            break;

        case 'POST':
            if (isset($_GET['action']) && $_GET['action'] === 'updatePhoto' && $cni) {
                updateAdminPhotoAPI($cni);
            } else {
                createAdminAPI();
            }
            break;

        case 'PUT':
            if ($cni) {
                updateAdminAPI($cni);
            } else {
                jsonResponse(['error' => 'Le paramètre CNI est requis'], 400);
            }
            break;

        case 'DELETE':
            if ($cni) {
                if (isset($_GET['action']) && $_GET['action'] === 'deletePhoto') {
                    deleteAdminPhotoAPI($cni);
                } else {
                    deleteAdminAPI($cni);
                }
            } else {
                jsonResponse(['error' => 'Le paramètre CNI est requis'], 400);
            }
            break;

        default:
            jsonResponse(['error' => 'Méthode non autorisée'], 405);
            break;
    }
} catch (Exception $e) {
    jsonResponse(['error' => 'Erreur interne du serveur'], 500);
}
?>
