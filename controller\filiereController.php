<?php
require_once "../model/filiereModel.php";
require_once "../utils/response.php";
require_once "../model/enseignantModel.php";
require_once "../config/db.php";

// Démarrer la session si elle n'est pas déjà démarrée
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Vérifier si une action est spécifiée
if (isset($_GET['action'])) {
    $action = $_GET['action'];

    switch ($action) {
        case 'getCoordinateur':
            getCoordinateur();
            break;

        case 'getAllFilieres':
            getAllFilieresAPI();
            break;

        case 'getFiliereById':
            if (isset($_GET['id'])) {
                getFiliereByIdAPI($_GET['id']);
            } else {
                echo json_encode(['success' => false, 'error' => 'ID de filière non spécifié']);
            }
            break;

        case 'createFiliere':
            createFiliereAPI();
            break;

        case 'updateFiliere':
            if (isset($_GET['id'])) {
                updateFiliereAPI($_GET['id']);
            } else {
                echo json_encode(['success' => false, 'error' => 'ID de filière non spécifié']);
            }
            break;

        case 'deleteFiliere':
            if (isset($_GET['id'])) {
                deleteFiliereAPI($_GET['id']);
            } else {
                echo json_encode(['success' => false, 'error' => 'ID de filière non spécifié']);
            }
            break;

        default:
            echo json_encode(['success' => false, 'error' => 'Action non reconnue']);
            break;
    }
}

// Get all filieres
function getAllFilieresAPI() {
    $filieres = getAllFilieres();

    if (isset($filieres['error'])) {
        jsonResponse(['error' => $filieres['error']], 404);
    }

    jsonResponse(['data' => $filieres], 200);
}

// Get filiere by ID
function getFiliereByIdAPI($id) {
    $filiere = getFiliereById($id);

    if (isset($filiere['error'])) {
        jsonResponse(['error' => $filiere['error']], 404);
    }

    jsonResponse(['data' => $filiere], 200);
}

// Create new filiere
function createFiliereAPI() {
    $json = file_get_contents("php://input");
    error_log("Received JSON: " . $json);
    $data = json_decode($json, true);

    if (!$data) {
        error_log("JSON decode error: " . json_last_error_msg());
        jsonResponse(['error' => 'Invalid JSON data'], 400);
    }

    $requiredFields = ['nom_filiere', 'id_chef_filiere', 'id_dep', 'niveau'];

    foreach ($requiredFields as $field) {
        if (empty($data[$field])) {
            jsonResponse(['error' => "Field $field is required"], 400);
        }
    }

    $result = insertFiliere(
        $data['nom_filiere'],
        $data['id_chef_filiere'],
        $data['id_dep'],
        $data['niveau']
    );

    if ($result) {
        jsonResponse(['message' => 'Filiere created successfully'], 201);
    } else {
        jsonResponse(['error' => 'Error creating filiere'], 500);
    }
}

// Update filiere
function updateFiliereAPI($id) {
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);

    if (!$data) {
        jsonResponse(['error' => 'Invalid JSON data'], 400);
    }

    $requiredFields = ['nom_filiere', 'id_chef_filiere', 'id_dep', 'niveau'];

    foreach ($requiredFields as $field) {
        if (empty($data[$field])) {
            jsonResponse(['error' => "Field $field is required"], 400);
        }
    }

    $result = updateFiliere(
        $id,
        $data['nom_filiere'],
        $data['id_chef_filiere'],
        $data['id_dep'],
        $data['niveau']
    );

    if ($result) {
        jsonResponse(['message' => 'Filiere updated successfully'], 200);
    } else {
        jsonResponse(['error' => 'Error updating filiere'], 500);
    }
}

// Delete filiere
function deleteFiliereAPI($id) {
    $filiere = getFiliereById($id);

    if (isset($filiere['error'])) {
        jsonResponse(['error' => $filiere['error']], 404);
    }

    $result = deleteFiliere($id);

    if ($result) {
        jsonResponse(['message' => 'Filiere deleted successfully'], 200);
    } else {
        jsonResponse(['error' => 'Error deleting filiere'], 500);
    }
}

/**
 * Récupère le coordinateur d'une filière
 */
function getCoordinateur() {
    // Vérifier si l'ID de la filière est spécifié
    if (!isset($_GET['filiereId'])) {
        echo json_encode(['success' => false, 'error' => 'ID de filière non spécifié']);
        return;
    }

    $filiereId = $_GET['filiereId'];

    // Connexion à la base de données
    $conn = getConnection();
    if (!$conn) {
        echo json_encode(['success' => false, 'error' => 'Erreur de connexion à la base de données']);
        return;
    }

    // Sécuriser l'entrée
    $filiereId = mysqli_real_escape_string($conn, $filiereId);

    // Vérifier les colonnes possibles pour le coordinateur
    $possibleColumns = ['id_coordinateur', 'coordinateur_id'];
    $columnToUse = null;

    foreach ($possibleColumns as $column) {
        $checkColumn = mysqli_query($conn, "SHOW COLUMNS FROM filiere LIKE '$column'");
        if ($checkColumn && mysqli_num_rows($checkColumn) > 0) {
            $columnToUse = $column;
            break;
        }
    }

    if (!$columnToUse) {
        // Si aucune colonne spécifique pour coordinateur n'est trouvée, utiliser id_chef_filiere
        $columnToUse = 'id_chef_filiere';
    }

    // Récupérer l'ID du coordinateur
    $query = "SELECT $columnToUse FROM filiere WHERE id_filiere = '$filiereId'";
    $result = mysqli_query($conn, $query);

    if ($result && mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $coordinateurId = $row[$columnToUse];

        if ($coordinateurId) {
            // Récupérer les informations de l'enseignant qui est coordinateur
            $enseignantQuery = "SELECT nom, prenom FROM enseignant WHERE id_enseignant = '$coordinateurId'";
            $enseignantResult = mysqli_query($conn, $enseignantQuery);

            if ($enseignantResult && mysqli_num_rows($enseignantResult) > 0) {
                $enseignantRow = mysqli_fetch_assoc($enseignantResult);
                $coordinateur = $enseignantRow['prenom'] . ' ' . $enseignantRow['nom'];

                mysqli_close($conn);
                echo json_encode(['success' => true, 'coordinateur' => $coordinateur]);
                return;
            }
        }
    }

    mysqli_close($conn);
    echo json_encode(['success' => false, 'coordinateur' => 'Non spécifié']);
}