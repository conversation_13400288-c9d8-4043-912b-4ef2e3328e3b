<?php
// Include the authentication check for teachers
require_once '../includes/auth_check_enseignant.php';

// Récupérer les informations de l'enseignant depuis la session
$userName = $_SESSION['user']['username'] ?? 'Enseignant';
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$departmentName = $_SESSION['user']['department_name'] ?? 'Non spécifié';
$specialtyName = $_SESSION['user']['specialty_name'] ?? 'Non spécifié';
$teacherId = $_SESSION['user']['teacher_id'] ?? null;

// Récupérer les paramètres de l'URL
$filiereId = isset($_GET['filiere']) ? trim(urldecode($_GET['filiere'])) : '';
$niveauId = isset($_GET['niveau']) ? trim(urldecode($_GET['niveau'])) : '';
$semestre = isset($_GET['semestre']) ? trim(urldecode($_GET['semestre'])) : '';
$session = isset($_GET['session']) ? trim(urldecode($_GET['session'])) : '';
$moduleId = isset($_GET['module']) ? trim(urldecode($_GET['module'])) : '';

// Récupérer les noms des filtres sélectionnés
$filiereName = isset($_GET['filiere_name']) ? trim(urldecode($_GET['filiere_name'])) : '';
$niveauName = isset($_GET['niveau_name']) ? trim(urldecode($_GET['niveau_name'])) : '';
$moduleName = isset($_GET['module_name']) ? trim(urldecode($_GET['module_name'])) : '';
$semestreName = isset($_GET['semestre_name']) ? trim(urldecode($_GET['semestre_name'])) : $semestre; // Utiliser l'ID comme fallback

// Log des paramètres bruts pour débogage
error_log("Paramètres URL bruts: filiere=" . (isset($_GET['filiere']) ? $_GET['filiere'] : 'non défini'));
error_log("Paramètres URL bruts: niveau=" . (isset($_GET['niveau']) ? $_GET['niveau'] : 'non défini'));
error_log("Paramètres URL bruts: semestre=" . (isset($_GET['semestre']) ? $_GET['semestre'] : 'non défini'));
error_log("Paramètres URL bruts: session=" . (isset($_GET['session']) ? $_GET['session'] : 'non défini'));
error_log("Paramètres URL bruts: module=" . (isset($_GET['module']) ? $_GET['module'] : 'non défini'));

// Log des paramètres décodés pour débogage
error_log("Paramètres URL décodés: filiereId=$filiereId, niveauId=$niveauId, semestre=$semestre, session=$session, moduleId=$moduleId");
error_log("Noms des filtres: filiereName=$filiereName, niveauName=$niveauName, moduleName=$moduleName");

// Vérifier si les paramètres sont vides et afficher un message d'erreur
$parametresManquants = [];
if (empty($filiereId)) $parametresManquants[] = 'filiere';
if (empty($niveauId)) $parametresManquants[] = 'niveau';
if (empty($semestre)) $parametresManquants[] = 'semestre';
if (empty($session)) $parametresManquants[] = 'session';
if (empty($moduleId)) $parametresManquants[] = 'module';

if (!empty($parametresManquants)) {
    error_log("ATTENTION: Paramètres manquants: " . implode(', ', $parametresManquants));
}

// Définir les variables JavaScript pour les paramètres
echo "<script>
    // Paramètres de filtrage
    var urlFiliereId = " . json_encode($filiereId) . ";
    var urlNiveauId = " . json_encode($niveauId) . ";
    var urlSemestre = " . json_encode($semestre) . ";
    var urlSession = " . json_encode($session) . ";
    var urlModuleId = " . json_encode($moduleId) . ";

    // Noms des filtres
    var urlFiliereName = " . json_encode($filiereName) . ";
    var urlNiveauName = " . json_encode($niveauName) . ";
    var urlModuleName = " . json_encode($moduleName) . ";
    var urlSemestreName = " . json_encode($semestreName) . ";

    // Log des paramètres pour débogage
    console.log('Paramètres URL définis dans PHP:', {
        filiereId: urlFiliereId,
        niveauId: urlNiveauId,
        semestre: urlSemestre,
        session: urlSession,
        moduleId: urlModuleId,
        filiereName: urlFiliereName,
        niveauName: urlNiveauName,
        moduleName: urlModuleName,
        semestreName: urlSemestreName
    });

    // Vérifier si les paramètres sont valides
    var parametresValides = urlFiliereId && urlNiveauId && urlSemestre && urlSession && urlModuleId;
    console.log('Paramètres valides:', parametresValides);

    // Ajouter l'ID de l'enseignant pour l'envoi au coordinateur
    var teacherId = " . json_encode($teacherId) . ";
    console.log('ID de l\'enseignant:', teacherId);
</script>";

// Année académique actuelle (à remplacer par une valeur dynamique si nécessaire)
$academicYear = "2024/2025";

// Récupérer le cycle depuis la base de données
require_once '../../model/cycleModel.php';
require_once '../../model/niveauModel.php';
require_once '../../model/filiereModel.php';

// Déterminer le cycle en fonction du niveau
$cycle = "Cycle Ingénieur"; // Valeur par défaut

if (!empty($niveauId)) {
    // Récupérer le niveau pour obtenir le cycle_id
    $niveauInfo = getNiveauById($niveauId);

    if ($niveauInfo && isset($niveauInfo['cycle_id'])) {
        $cycleId = $niveauInfo['cycle_id'];
        $cycleInfo = getCycleById($cycleId);

        if ($cycleInfo && isset($cycleInfo['nom'])) {
            $cycle = $cycleInfo['nom'];
        }
    }
} else if (!empty($filiereId)) {
    // Si pas de niveau mais une filière, récupérer le cycle depuis la filière
    $filiereInfo = getFiliereById($filiereId);

    if ($filiereInfo && isset($filiereInfo['id_cycle'])) {
        $cycleId = $filiereInfo['id_cycle'];
        $cycleInfo = getCycleById($cycleId);

        if ($cycleInfo && isset($cycleInfo['nom'])) {
            $cycle = $cycleInfo['nom'];
        }
    }
}

// Récupérer les informations du module, de l'enseignant et du chef de département
require_once '../../model/moduleModel.php';
require_once '../../model/departementModel.php';

// Add error handling for the module retrieval
try {
    $moduleInfo = getModuleById($moduleId);

    // Log the module info for debugging
    error_log("Module info: " . print_r($moduleInfo, true));

    $enseignantModule = "$prenom $nom"; // Enseignant connecté
    $coordinateur = "Non spécifié"; // Coordinateur de filière (à récupérer)

    if ($moduleInfo && isset($moduleInfo['enseignant']) && !empty($moduleInfo['enseignant'])) {
        $enseignantModule = $moduleInfo['enseignant'];
    }

    // Récupérer le coordinateur de filière si on a l'ID de la filière
    if (!empty($filiereId)) {
        // Fonction pour récupérer le coordinateur d'une filière
        function getCoordinateurFiliere($filiereId) {
            $conn = getConnection();
            if (!$conn) {
                return "Non spécifié";
            }

            // Sécuriser l'entrée
            $filiereId = mysqli_real_escape_string($conn, $filiereId);

            // Vérifier les colonnes possibles pour le coordinateur
            $possibleColumns = ['id_coordinateur', 'coordinateur_id'];
            $columnToUse = null;

            foreach ($possibleColumns as $column) {
                $checkColumn = mysqli_query($conn, "SHOW COLUMNS FROM filiere LIKE '$column'");
                if ($checkColumn && mysqli_num_rows($checkColumn) > 0) {
                    $columnToUse = $column;
                    break;
                }
            }

            if (!$columnToUse) {
                // Si aucune colonne spécifique pour coordinateur n'est trouvée, utiliser id_chef_filiere
                $columnToUse = 'id_chef_filiere';
            }

            // Récupérer l'ID du coordinateur
            $query = "SELECT $columnToUse FROM filiere WHERE id_filiere = '$filiereId'";
            $result = mysqli_query($conn, $query);

            if ($result && mysqli_num_rows($result) > 0) {
                $row = mysqli_fetch_assoc($result);
                $coordinateurId = $row[$columnToUse];

                if ($coordinateurId) {
                    // Récupérer les informations de l'enseignant qui est coordinateur
                    $enseignantQuery = "SELECT nom, prenom FROM enseignant WHERE id_enseignant = '$coordinateurId'";
                    $enseignantResult = mysqli_query($conn, $enseignantQuery);

                    if ($enseignantResult && mysqli_num_rows($enseignantResult) > 0) {
                        $enseignantRow = mysqli_fetch_assoc($enseignantResult);
                        mysqli_close($conn);
                        return $enseignantRow['prenom'] . ' ' . $enseignantRow['nom'];
                    }
                }
            }

            mysqli_close($conn);
            return "Non spécifié";
        }

        // Récupérer le coordinateur pour la filière actuelle
        $coordinateur = getCoordinateurFiliere($filiereId);
    }
} catch (Exception $e) {
    // Log the error
    error_log("Error retrieving module info: " . $e->getMessage());

    // Set default values
    $enseignantModule = "$prenom $nom";
    $coordinateur = "Non spécifié";
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notes des Étudiants - Tableau de bord Enseignant</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/student_grades.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <?php include_once '../includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Navbar -->
            <?php include_once '../includes/header.php'; ?>

            <!-- Page Content -->
            <div class="container-fluid p-4">
                <!-- Compact Module Information Header -->
                <div class="compact-header mb-3">
                    <div class="header-top">
                        <img src="../assets/img/logo.png" alt="Logo" class="compact-logo">
                        <div class="header-titles">
                            <h2 class="university-name">Université Abdelmalek Essaadi</h2>
                            <p class="university-school">École Nationale des Sciences Appliquées d'Al Hoceima</p>
                        </div>
                        <div class="academic-year">
                            <span class="year-label">AU:</span>
                            <span class="year-value"><?php echo $academicYear; ?></span>
                        </div>
                    </div>

                    <div class="module-info-container">
                        <div class="module-main-info">
                            <div class="module-name">
                                <i class="bi bi-book me-1"></i>
                                <span id="module-value"><?php echo $moduleName; ?></span>
                            </div>
                            <div class="module-details">
                                <div class="detail-item">
                                    <span class="detail-label">Filière:</span>
                                    <span class="detail-value" id="filiere-value"><?php echo $filiereName; ?></span>
                                </div>
                                <div class="detail-separator">|</div>
                                <div class="detail-item">
                                    <span class="detail-label">Niveau:</span>
                                    <span class="detail-value" id="niveau-value"><?php echo $niveauName; ?></span>
                                </div>
                                <div class="detail-separator">|</div>
                                <div class="detail-item">
                                    <span class="detail-label">Sem:</span>
                                    <span class="detail-value" id="semestre-value"><?php echo $semestreName; ?></span>
                                </div>
                                <div class="detail-separator">|</div>
                                <div class="detail-item">
                                    <span class="detail-label">Session:</span>
                                    <span class="detail-value" id="session-value"><?php echo ucfirst($session); ?></span>
                                </div>
                            </div>
                        </div>

                        <div class="personnel-info">
                            <div class="personnel-item">
                                <i class="bi bi-person-video3 me-1"></i>
                                <span class="personnel-label">Prof:</span>
                                <span class="personnel-value"><?php echo $enseignantModule; ?></span>
                            </div>
                            <div class="personnel-item">
                                <i class="bi bi-person-badge me-1"></i>
                                <span class="personnel-label">Coordinateur:</span>
                                <span class="personnel-value"><?php echo $coordinateur; ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Students Grades Table -->
                <div class="grades-container">
                    <div class="grades-header">
                        <div class="grades-title">
                            <i class="bi bi-list-check me-2"></i>
                            Liste des Notes
                        </div>
                        <div class="grades-actions">
                            <div class="search-box">
                                <input type="text" id="search-input" placeholder="Rechercher par nom, prénom...">
                                <i class="bi bi-search"></i>
                            </div>
                            <!-- <button id="save-all-grades" class="btn btn-success me-2">
                                <i class="bi bi-save me-2"></i>Enregistrer toutes les notes
                            </button> -->
                            <button id="download-pdf" class="btn btn-primary me-2">
                                <i class="bi bi-file-earmark-pdf me-2"></i>Télécharger PDF
                            </button>
                            <button id="send-to-coordinator" class="btn btn-info">
                                <i class="bi bi-send me-2"></i>Export Grades
                            </button>
                        </div>
                    </div>

                    <div class="grades-description">
                        Liste complète des notes des étudiants pour ce module.
                    </div>

                    <!-- Top Performances -->
                    <div class="top-performances">
                        <div class="top-title">
                            <i class="bi bi-star-fill text-warning me-2"></i>
                            Top Performances
                        </div>
                        <div class="top-students">
                            <div class="top-student">
                                <div class="student-rank">1</div>
                                <div class="student-info">
                                    <div class="student-name">Ahmed Alaoui</div>
                                    <div class="student-grade">19.5</div>
                                </div>
                            </div>
                            <div class="top-student">
                                <div class="student-rank">2</div>
                                <div class="student-info">
                                    <div class="student-name">Sara Bennani</div>
                                    <div class="student-grade">18.75</div>
                                </div>
                            </div>
                            <div class="top-student">
                                <div class="student-rank">3</div>
                                <div class="student-info">
                                    <div class="student-name">Karim Tazi</div>
                                    <div class="student-grade">18.25</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Grades Table -->
                    <div class="table-responsive">
                        <table class="table grades-table" id="grades-table">
                            <thead>
                                <tr>
                                    <th>N°</th>
                                    <th>CNE</th>
                                    <th>Nom</th>
                                    <th>Prénom</th>
                                    <th>Moyenne</th>
                                    <th>V/R</th>
                                </tr>
                            </thead>
                            <tbody id="students-list">
                                <!-- Will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- No Students Message -->
                    <div class="no-students-message" id="no-students-message" style="display: none;">
                        Aucune note trouvée pour les filtres sélectionnés
                    </div>

                    <!-- Pagination -->
                    <div class="pagination-container">
                        <div class="pagination">
                            <button class="pagination-btn" id="prev-page" disabled>
                                <i class="bi bi-chevron-left"></i> Précédent
                            </button>
                            <div class="pagination-pages" id="pagination-pages">
                                <span class="page-number active">1</span>
                            </div>
                            <button class="pagination-btn" id="next-page">
                                Suivant <i class="bi bi-chevron-right"></i>
                            </button>
                        </div>
                        <div class="total-students" id="total-students">
                            Total: 0 étudiants
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- html2pdf.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/sidebar.js"></script>
    <script src="../assets/js/student_grades.js"></script>
</body>
</html>