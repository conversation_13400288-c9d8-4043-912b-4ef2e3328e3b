<?php
require_once "../model/enseignantModel.php";
require_once "../utils/response.php";

function getAllEnseignantsAPI() {
    $enseignants = getAllEnseignants();
    if (isset($enseignants['error'])) {
        jsonResponse(['error' => $enseignants['error']], 404);
    }
    jsonResponse(['data' => $enseignants], 200);
}

/**
 * API pour récupérer tous les enseignants d'un département spécifique
 *
 * @param int $departementId L'ID du département
 */
function getEnseignantsByDepartementAPI($departementId) {
    $enseignants = getEnseignantsByDepartement($departementId);
    if (isset($enseignants['error'])) {
        jsonResponse(['error' => $enseignants['error']], 404);
    }
    jsonResponse(['data' => $enseignants], 200);
}

function getEnseignantByCNIAPI($cni) {
    $enseignant = getEnseignantByCNI($cni);
    if (!$enseignant) {
        jsonResponse(['error' => 'Enseignant non trouvé'], 404);
    }
    jsonResponse(['data' => $enseignant], 200);
}

function createEnseignantAPI() {
    try {
        // Récupérer les données POST au format JSON
        $json = file_get_contents("php://input");
        error_log("JSON reçu : " . $json);
        $data = json_decode($json, true);

        if (!$data) {
            error_log("Erreur de décodage JSON : " . json_last_error_msg());
            jsonResponse(['error' => 'Données JSON invalides'], 400);
            return;
        }

        // Validation des données
        $requiredFields = ['CNI', 'nom', 'prenom', 'email', 'sexe'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                jsonResponse(['error' => "Le champ $field est obligatoire"], 400);
                return;
            }
        }

        // Vérifier si le département est spécifié
        if (empty($data['department']) && empty($data['id_departement'])) {
            jsonResponse(['error' => "Le département est obligatoire"], 400);
            return;
        }

        // Créer l'enseignant dans la base de données
        $result = createEnseignant($data);

        if (!$result) {
            jsonResponse(['error' => 'Erreur lors de la création de l\'enseignant. Vérifiez que le CNI n\'est pas déjà utilisé.'], 500);
            return;
        }

        // Vérifier si la création de compte utilisateur est demandée
        if (isset($data['createAccount']) && $data['createAccount'] === true) {
            // Créer un compte utilisateur pour l'enseignant avec son rôle
            $accountResult = createFacultyAccount($data['CNI'], $data['email'], $data['nom'], $data['prenom'], $data['role']);

            if (isset($accountResult['error'])) {
                // L'enseignant a été créé mais pas le compte
                jsonResponse([
                    'message' => 'Enseignant créé avec succès, mais erreur lors de la création du compte: ' . $accountResult['error'],
                    'CNI' => $data['CNI']
                ], 201);
            } else {
                // Tout s'est bien passé
                jsonResponse([
                    'message' => 'Enseignant créé avec succès et compte utilisateur initialisé',
                    'CNI' => $data['CNI']
                ], 201);
            }
        } else {
            // Pas de création de compte demandée
            jsonResponse(['message' => 'Enseignant créé avec succès', 'CNI' => $data['CNI']], 201);
        }
    } catch (Exception $e) {
        error_log("Exception dans createEnseignantAPI: " . $e->getMessage());
        jsonResponse(['error' => 'Erreur serveur: ' . $e->getMessage()], 500);
    }
}

/**
 * Crée un compte utilisateur pour un enseignant et envoie un email d'initialisation de mot de passe
 *
 * @param string $cni Le CNI de l'enseignant (sera utilisé comme nom d'utilisateur)
 * @param string $email L'email de l'enseignant
 * @param string $nom Le nom de l'enseignant
 * @param string $prenom Le prénom de l'enseignant
 * @param string $role Le rôle de l'enseignant
 * @return array Résultat de l'opération
 */
function createFacultyAccount($cni, $email, $nom, $prenom, $role = 'enseignant') {
    try {
        require_once "../model/userModel.php";

        // Vérifier si le fichier emailSender.php existe
        $emailSenderPath = "../utils/emailSender.php";
        if (!file_exists($emailSenderPath)) {
            error_log("Le fichier emailSender.php n'existe pas");
            return [
                'success' => true,
                'message' => 'Compte créé avec succès (simulation d\'email)'
            ];
        }

        require_once $emailSenderPath;

    // Vérifier si un utilisateur avec ce nom d'utilisateur existe déjà
    $existingUser = getUserByUsername($cni);
    if ($existingUser && !isset($existingUser['error'])) {
        return ['error' => 'Un utilisateur avec ce CNI existe déjà'];
    }

    // Générer un token unique pour la réinitialisation du mot de passe
    $token = bin2hex(random_bytes(32));
    $expiry = date('Y-m-d H:i:s', strtotime('+24 hours'));

    // Créer l'utilisateur avec un mot de passe temporaire (qui ne sera pas utilisé)
    $tempPassword = bin2hex(random_bytes(8));
    $hashedPassword = password_hash($tempPassword, PASSWORD_DEFAULT);

    // Utiliser directement le rôle fourni
    // Si le rôle est 'normal', le convertir en 'enseignant' pour maintenir la cohérence
    if ($role === 'normal') {
        $role = 'enseignant';
    }

    // S'assurer que le rôle est l'un des rôles autorisés
    $allowedRoles = ['enseignant', 'chef de departement', 'coordinateur', 'vacataire'];
    if (!in_array($role, $allowedRoles)) {
        $role = 'enseignant'; // Rôle par défaut si le rôle fourni n'est pas autorisé
    }

    // Insérer l'utilisateur dans la base de données
    $result = createUser($cni, $hashedPassword, $role);

    if (isset($result['error'])) {
        return ['error' => $result['error']];
    }

    // Stocker le token de réinitialisation
    $tokenResult = storePasswordResetToken($cni, $token, $expiry);

    if (isset($tokenResult['error'])) {
        return ['error' => $tokenResult['error']];
    }

    // Envoyer l'email d'initialisation de mot de passe
    $fullName = $prenom . ' ' . $nom;

    // Récupérer le chemin de base dynamiquement
    require_once "../config/constants.php";
    $resetLink = "http://" . $_SERVER['HTTP_HOST'] . BASE_URL . "/view/initialize-password.php?token=" . $token;

    $subject = "Initialisation de votre mot de passe - UniAdmin";
    $message = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #1a73e8; color: white; padding: 15px 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { padding: 30px; background-color: #f9f9f9; border: 1px solid #ddd; border-radius: 0 0 8px 8px; }
            .button { display: inline-block; background-color: #1a73e8; color: white; padding: 12px 30px; text-decoration: none; border-radius: 4px; font-weight: bold; font-size: 16px; }
            .button:hover { background-color: #0d47a1; }
            .credentials { background-color: #e8f0fe; border-left: 4px solid #1a73e8; padding: 15px; margin: 20px 0; }
            .link-container { background-color: #f5f5f5; padding: 15px; border-radius: 4px; margin: 20px 0; border: 1px solid #ddd; }
            .important { font-weight: bold; color: #d93025; }
            .footer { font-size: 12px; text-align: center; margin-top: 30px; color: #777; }
            .highlight { font-weight: bold; color: #1a73e8; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2 style='margin: 0; font-size: 24px;'>Bienvenue sur UniAdmin</h2>
            </div>
            <div class='content'>
                <p style='font-size: 18px;'><strong>Bonjour $fullName,</strong></p>

                <p>Un compte enseignant a été créé pour vous sur la plateforme UniAdmin de l'École Nationale des Sciences Appliquées Al Hoceima.</p>

                <div class='credentials'>
                    <p style='margin: 5px 0;'><strong>Vos informations de connexion :</strong></p>
                    <p style='margin: 5px 0;'><span class='highlight'>Nom d'utilisateur :</span> <strong>$cni</strong></p>
                    <p style='margin: 5px 0;'><span class='highlight'>Mot de passe :</span> À définir en cliquant sur le bouton ci-dessous</p>
                </div>

                <p>Pour finaliser la création de votre compte, vous devez initialiser votre mot de passe en cliquant sur le bouton ci-dessous :</p>

                <p style='text-align: center; margin: 30px 0;'>
                    <a href='$resetLink' class='button'>Initialiser mon mot de passe</a>
                </p>

                <p>Si le bouton ne fonctionne pas, vous pouvez copier et coller ce lien dans votre navigateur :</p>

                <div class='link-container'>
                    <p style='word-break: break-all; margin: 0;'>$resetLink</p>
                </div>

                <p><span class='important'>Important :</span> Ce lien est valable pendant <strong>24 heures</strong> seulement.</p>

                <p>Après avoir initialisé votre mot de passe, vous pourrez vous connecter à la plateforme et accéder à toutes les fonctionnalités réservées aux enseignants.</p>

                <p>Cordialement,<br><strong>L'équipe UniAdmin</strong></p>
            </div>
            <div class='footer'>
                <p>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</p>
                <p>© " . date('Y') . " École Nationale des Sciences Appliquées Al Hoceima. Tous droits réservés.</p>
            </div>
        </div>
    </body>
    </html>";

    // Envoyer l'email
    $emailResult = sendEmail($email, $subject, $message);

    // Vérifier si l'email a été envoyé avec succès
    if (!$emailResult['success']) {
        error_log("Erreur lors de l'envoi de l'email: " . $emailResult['message']);
        return ['error' => 'Compte créé mais erreur lors de l\'envoi de l\'email: ' . $emailResult['message']];
    }

    // Journaliser l'envoi de l'email
    error_log("Email d'initialisation de mot de passe envoyé à: " . $email);

    // Stocker le token dans la session pour faciliter les tests
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    $_SESSION['last_password_reset_token'] = $token;
    $_SESSION['last_password_reset_link'] = $resetLink;

    // Afficher le lien dans les logs pour faciliter les tests
    error_log("Lien de réinitialisation: " . $resetLink);

    return ['success' => true, 'message' => 'Compte créé et email d\'initialisation envoyé'];
    } catch (Exception $e) {
        error_log("Exception dans createFacultyAccount: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Erreur lors de la création du compte: ' . $e->getMessage()
        ];
    }
}

function updateEnseignantAPI($cni) {
    try {
        // Récupérer les données JSON
        $jsonData = file_get_contents("php://input");
        error_log("Données JSON reçues: " . $jsonData);

        $data = json_decode($jsonData, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("Erreur de décodage JSON: " . json_last_error_msg());
            jsonResponse(['error' => 'Données JSON invalides: ' . json_last_error_msg()], 400);
            return;
        }

        // Log des données reçues pour le débogage
        error_log("Données décodées pour updateEnseignantAPI: " . print_r($data, true));

        // Vérifier et normaliser le rôle si présent
        if (isset($data['role'])) {
            // Si le rôle est 'normal', le convertir en 'enseignant' pour maintenir la cohérence
            if ($data['role'] === 'normal') {
                $data['role'] = 'enseignant';
            }

            // S'assurer que le rôle est l'un des rôles autorisés
            $allowedRoles = ['enseignant', 'chef de departement', 'coordinateur', 'vacataire', 'etudiant', 'admin'];
            if (!in_array($data['role'], $allowedRoles)) {
                $data['role'] = 'enseignant'; // Rôle par défaut si le rôle fourni n'est pas autorisé
            }
        }

        // Log du CNI pour le débogage
        error_log("CNI de l'enseignant à mettre à jour: " . $cni);

        // Mettre à jour l'enseignant
        $updateResult = updateEnseignant($cni, $data);
        error_log("Résultat de updateEnseignant: " . ($updateResult ? "succès" : "échec"));

        if ($updateResult) {
            // Si le rôle a été modifié, mettre à jour également le compte utilisateur
            if (isset($data['role'])) {
                $updateRoleResult = updateUserRole($cni, $data['role']);
                error_log("Résultat de updateUserRole: " . ($updateRoleResult ? "succès" : "échec"));
            }

            jsonResponse(['message' => 'Enseignant mis à jour avec succès']);
        } else {
            jsonResponse(['error' => 'Erreur lors de la mise à jour de l\'enseignant'], 500);
        }
    } catch (Exception $e) {
        error_log("Exception dans updateEnseignantAPI: " . $e->getMessage());
        jsonResponse(['error' => 'Erreur lors de la mise à jour: ' . $e->getMessage()], 500);
    }
}

/**
 * Met à jour le rôle d'un utilisateur
 *
 * @param string $username Le nom d'utilisateur (CNI de l'enseignant)
 * @param string $role Le rôle à définir
 * @return bool Succès de l'opération
 */
function updateUserRole($username, $role) {
    try {
        require_once "../model/userModel.php";

        // Log des données reçues pour le débogage
        error_log("updateUserRole appelé avec username=$username, role=$role");

        // Si le rôle est 'normal', le convertir en 'enseignant' pour maintenir la cohérence
        if ($role === 'normal') {
            $role = 'enseignant';
            error_log("Rôle 'normal' converti en 'enseignant'");
        }

        // S'assurer que le rôle est l'un des rôles autorisés
        $allowedRoles = ['enseignant', 'chef de departement', 'coordinateur', 'vacataire', 'etudiant', 'admin'];
        if (!in_array($role, $allowedRoles)) {
            error_log("Rôle '$role' non autorisé, utilisation du rôle par défaut 'enseignant'");
            $role = 'enseignant'; // Rôle par défaut si le rôle fourni n'est pas autorisé
        }

        // Vérifier si l'utilisateur existe
        $user = getUserByUsername($username);
        if (!$user || isset($user['error'])) {
            error_log("Utilisateur '$username' non trouvé ou erreur lors de la récupération");
            return false;
        }

        error_log("Utilisateur trouvé: " . print_r($user, true));

        // Mettre à jour le rôle de l'utilisateur
        $conn = getConnection();
        $username = mysqli_real_escape_string($conn, $username);
        $role = mysqli_real_escape_string($conn, $role);

        $sql = "UPDATE users SET role = '$role' WHERE username = '$username'";
        error_log("Requête SQL: $sql");

        $result = mysqli_query($conn, $sql);

        if (!$result) {
            error_log("Erreur lors de la mise à jour du rôle: " . mysqli_error($conn));
        } else {
            error_log("Rôle mis à jour avec succès");
        }

        mysqli_close($conn);
        return $result;
    } catch (Exception $e) {
        error_log("Exception dans updateUserRole: " . $e->getMessage());
        return false;
    }
}

function deleteEnseignantAPI($cni) {
    if (deleteEnseignant($cni)) {
        jsonResponse(['message' => 'Enseignant supprimé avec succès']);
    } else {
        jsonResponse(['error' => 'Erreur lors de la suppression'], 500);
    }
}

/**
 * Récupère les rôles disponibles pour les enseignants
 */
function getAvailableRolesAPI() {
    // Liste des rôles autorisés
    $roles = ['enseignant', 'chef de departement', 'coordinateur', 'vacataire', 'etudiant', 'admin'];
    jsonResponse(['data' => $roles], 200);
}

/**
 * API pour récupérer les filières associées à un enseignant
 *
 * @param int $teacherId L'ID de l'enseignant
 */
function getTeacherFieldsAPI($teacherId) {
    $fields = getTeacherFields($teacherId);
    if (isset($fields['error'])) {
        jsonResponse(['error' => $fields['error'], 'success' => false], 404);
    }
    jsonResponse(['data' => $fields, 'success' => true], 200);
}

/**
 * API pour récupérer les filières associées à un enseignant en utilisant la table affectation
 * Cette méthode est plus précise car elle utilise les affectations réelles plutôt que la table enseignant_filiere
 *
 * @param int $teacherId L'ID de l'enseignant
 */
function getTeacherFieldsFromAffectationAPI($teacherId) {
    $fields = getTeacherFieldsFromAffectation($teacherId);
    if (isset($fields['error'])) {
        jsonResponse(['error' => $fields['error'], 'success' => false], 404);
    }
    jsonResponse(['data' => $fields, 'success' => true], 200);
}


?>