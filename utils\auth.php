<?php
/**
 * Fonctions d'authentification et d'autorisation
 */

// Démarrer la session si elle n'est pas déjà démarrée
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Vérifie si l'utilisateur est connecté
 *
 * @return bool True si l'utilisateur est connecté, false sinon
 */
function isLoggedIn() {
    return isset($_SESSION['user']) && !empty($_SESSION['user']);
}

/**
 * Vérifie si l'utilisateur est un administrateur
 *
 * @return bool True si l'utilisateur est un administrateur, false sinon
 */
function isAdmin() {
    return isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'admin';
}

/**
 * Vérifie si l'utilisateur est un enseignant
 *
 * @return bool True si l'utilisateur est un enseignant, false sinon
 */
function isTeacher() {
    return isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'enseignant';
}

/**
 * Vérifie si l'utilisateur est un étudiant
 *
 * @return bool True si l'utilisateur est un étudiant, false sinon
 */
function isStudent() {
    return isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'etudiant';
}

/**
 * Vérifie si l'utilisateur est un vacataire
 *
 * @return bool True si l'utilisateur est un vacataire, false sinon
 */
function isVacataire() {
    return isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'vacataire';
}

/**
 * Vérifie si l'utilisateur a un rôle spécifique
 *
 * @param string|array $roles Un rôle ou un tableau de rôles à vérifier
 * @return bool True si l'utilisateur a l'un des rôles spécifiés, false sinon
 */
function hasRole($roles) {
    if (!isset($_SESSION['user']['role'])) {
        return false;
    }

    if (is_array($roles)) {
        return in_array($_SESSION['user']['role'], $roles);
    }

    return $_SESSION['user']['role'] === $roles;
}

/**
 * Redirige l'utilisateur vers la page de connexion s'il n'est pas connecté
 *
 * @param string $redirectUrl URL de redirection après la connexion (optionnel)
 */
function requireLogin($redirectUrl = '') {
    if (!isLoggedIn()) {
        if (!empty($redirectUrl)) {
            $_SESSION['redirect_after_login'] = $redirectUrl;
        }
        header('Location: /view/login.php');
        exit;
    }
}

/**
 * Redirige l'utilisateur vers la page d'accueil s'il n'a pas le rôle requis
 *
 * @param string|array $roles Un rôle ou un tableau de rôles autorisés
 */
function requireRole($roles) {
    requireLogin();

    if (!hasRole($roles)) {
        header('Location: /view/unauthorized.php');
        exit;
    }
}

/**
 * Déconnecte l'utilisateur en détruisant la session
 */
function logout() {
    // Détruire toutes les données de session
    $_SESSION = array();

    // Détruire le cookie de session si nécessaire
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }

    // Détruire la session
    session_destroy();
}

/**
 * Génère un jeton CSRF pour protéger les formulaires
 *
 * @return string Jeton CSRF
 */
function generateCsrfToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Vérifie si le jeton CSRF est valide
 *
 * @param string $token Jeton CSRF à vérifier
 * @return bool True si le jeton est valide, false sinon
 */
function verifyCsrfToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Pour le débogage, créer une session admin si nécessaire
if (!isLoggedIn() && defined('DEBUG_MODE') && DEBUG_MODE === true) {
    $_SESSION['user'] = [
        'id_user' => 1,
        'username' => 'admin',
        'role' => 'admin',
        'name' => 'Admin User'
    ];
}
?>
