<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';

// Check if user is logged in and is admin
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Personnel de l'ENSA Al-Hoceima - UniAdmin</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/personnel.css">
</head>
<body>
    <div class="dashboard-container">
        <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 include '../includes/header.php'; ?>

            <div class="container-fluid p-4">
                <div class="personnel-container">
                    <!-- Page Title with gradient header -->
                    <div class="personnel-header">
                        <h1>Personnel</h1>
                        <p class="subtitle">Gérer et rechercher le personnel </p>
                    </div>

                    <!-- Search Section -->
                    <div class="search-container">
                        <h5 class="search-title">Saisir le nom ou une partie du nom <span class="text-danger">(*)</span></h5>
                        <div class="row g-3">
                            <div class="col-md-8 position-relative">
                                <i class="bi bi-search search-icon"></i>
                                <input type="text" id="searchInput" class="form-control search-input" placeholder="Rechercher...">
                            </div>
                            <div class="col-md-4 d-flex gap-2">
                                <button id="searchBtn" class="btn search-btn flex-grow-1">Rechercher</button>
                                <button id="cancelBtn" class="btn cancel-btn d-none">Annuler</button>
                            </div>
                        </div>
                    </div>

                    <!-- Results Count -->
                    <div id="resultsCount" class="results-count">
                        Nombre d'éléments trouvés : <span id="countValue">0</span>
                    </div>

                    <!-- Personnel Table -->
                    <div class="personnel-table">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Nom</th>
                                        <th>Prénom</th>
                                        <th>Email académique</th>
                                        <th>Photo</th>
                                        <th>Rôle</th>
                                    </tr>
                                </thead>
                                <tbody id="personnelTableBody">
                                    <!-- Table content will be dynamically populated -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/personnel.js"></script>
    <script src="../assets/js/sidebar.js"></script>


</body>
</html>