<?php
// Vérifier l'authentification
require_once '../includes/auth_check_chef.php';

// Get the department head's ID and department ID from the session
$chefId = $_SESSION['user']['teacher_id'] ?? null;
$departmentId = $_SESSION['user']['department_id'] ?? null;

// Get department head's name from session
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$fullName = trim($prenom . ' ' . $nom);
if (empty($fullName)) {
    $fullName = $_SESSION['user']['username'] ?? 'Chef de département';
}

// Get department name from session
$departmentName = $_SESSION['user']['department_name'] ?? 'Non spécifié';

// Page configuration
$pageTitle = "Analyse Historique de la Charge de Travail";
$currentPage = "workload_history";
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Main Sidebar CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">

    <style>
        /* Override main content styles for proper sidebar integration */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            background-color: #f8f9fa;
            width: calc(100% - 280px);
            transition: margin-left 0.3s ease;
        }

        .page-title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 1rem;
            font-size: 1.75rem;
        }

        /* Responsive styles for mobile */
        @media (max-width: 991.98px) {
            .main-content {
                margin-left: 0;
                width: 100%;
            }
        }

        /* Ensure sidebar positioning is correct */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 1030;
        }
        .history-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }

        .year-comparison {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .comparison-item {
            text-align: center;
            padding: 1rem;
        }

        .comparison-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .comparison-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .trend-indicator {
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }

        .trend-up {
            color: #28a745;
        }

        .trend-down {
            color: #dc3545;
        }

        .trend-stable {
            color: #6c757d;
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin-top: 1rem;
        }

        .no-data {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }

        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }
    </style>
</head>
<body>
    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <div class="d-flex">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4">
                    <!-- Page Header -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h1 class="page-title">
                                <i class="fas fa-chart-area me-3"></i>
                                Analyse Historique de la Charge de Travail
                            </h1>
                            <p class="text-muted mb-0">
                                Évolution de la charge de travail du département <strong><?php echo htmlspecialchars($departmentName); ?></strong> au fil des années académiques
                            </p>
                        </div>
                    </div>

                    <!-- Year Comparison -->
                    <div class="year-comparison" id="yearComparison">
                        <div class="loading-spinner">
                            <div class="spinner-border text-light" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Section -->
                    <div class="row">
                        <!-- Department Trends -->
                        <div class="col-lg-8">
                            <div class="history-card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chart-line me-2"></i>
                                        Évolution de la Charge de Travail du Département
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="departmentTrendsChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Workload Distribution -->
                        <div class="col-lg-4">
                            <div class="history-card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chart-pie me-2"></i>
                                        Répartition par Type (Dernière Année)
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container" style="height: 300px;">
                                        <canvas id="distributionChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Teacher Evolution -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="history-card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-users me-2"></i>
                                        Évolution du Nombre d'Enseignants et Charge Moyenne
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="teacherEvolutionChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/sidebar.js"></script>

    <script>
        // Global variables
        let departmentTrendsChart = null;
        let distributionChart = null;
        let teacherEvolutionChart = null;
        let historicalData = [];
        const departmentId = <?php echo $departmentId ? $departmentId : 'null'; ?>;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Check if department ID is available
            if (departmentId === null) {
                showErrorMessage();
                return;
            }

            loadHistoricalData();
        });

        // Load historical data
        async function loadHistoricalData() {
            try {
                // Get available academic years
                const yearsResponse = await fetch('../../controller/workloadController.php?action=getAvailableAcademicYears');
                const yearsData = await yearsResponse.json();

                if (yearsData.success) {
                    const years = yearsData.data.slice(0, 5); // Last 5 years
                    historicalData = [];

                    for (const year of years) {
                        const yearResponse = await fetch(`../../controller/workloadController.php?action=getDepartmentWorkloadSummary&department_id=${departmentId}&academic_year=${year}`);
                        const yearData = await yearResponse.json();

                        if (yearData.success) {
                            historicalData.push({
                                year: year,
                                ...yearData.data
                            });
                        }
                    }

                    // Display data
                    displayYearComparison();
                    createDepartmentTrendsChart();
                    createDistributionChart();
                    createTeacherEvolutionChart();
                }
            } catch (error) {
                console.error('Error loading historical data:', error);
                showErrorMessage();
            }
        }

        // Display year comparison
        function displayYearComparison() {
            const container = document.getElementById('yearComparison');

            if (historicalData.length < 2) {
                container.innerHTML = `
                    <div class="text-center">
                        <h5>Données insuffisantes</h5>
                        <p>Au moins 2 années académiques sont nécessaires pour la comparaison</p>
                    </div>
                `;
                return;
            }

            const currentYear = historicalData[0];
            const previousYear = historicalData[1];

            const totalHoursTrend = calculateTrend(currentYear.total_hours, previousYear.total_hours);
            const teachersTrend = calculateTrend(currentYear.total_teachers, previousYear.total_teachers);
            const averageTrend = calculateTrend(currentYear.average_hours, previousYear.average_hours);

            container.innerHTML = `
                <div class="row">
                    <div class="col-md-3">
                        <div class="comparison-item">
                            <div class="comparison-value">${currentYear.total_hours}h</div>
                            <div class="comparison-label">Heures Totales</div>
                            <div class="trend-indicator ${totalHoursTrend.class}">
                                <i class="fas ${totalHoursTrend.icon}"></i>
                                ${totalHoursTrend.text}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="comparison-item">
                            <div class="comparison-value">${currentYear.total_teachers}</div>
                            <div class="comparison-label">Enseignants</div>
                            <div class="trend-indicator ${teachersTrend.class}">
                                <i class="fas ${teachersTrend.icon}"></i>
                                ${teachersTrend.text}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="comparison-item">
                            <div class="comparison-value">${currentYear.average_hours}h</div>
                            <div class="comparison-label">Moyenne/Enseignant</div>
                            <div class="trend-indicator ${averageTrend.class}">
                                <i class="fas ${averageTrend.icon}"></i>
                                ${averageTrend.text}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="comparison-item">
                            <div class="comparison-value">${currentYear.total_assignments}</div>
                            <div class="comparison-label">Affectations</div>
                            <div class="trend-indicator">
                                <i class="fas fa-info-circle"></i>
                                ${currentYear.year}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Calculate trend between two values
        function calculateTrend(current, previous) {
            const diff = current - previous;
            const percentage = previous > 0 ? Math.round((diff / previous) * 100) : 0;

            if (diff > 0) {
                return {
                    class: 'trend-up',
                    icon: 'fa-arrow-up',
                    text: `+${percentage}% vs année précédente`
                };
            } else if (diff < 0) {
                return {
                    class: 'trend-down',
                    icon: 'fa-arrow-down',
                    text: `${percentage}% vs année précédente`
                };
            } else {
                return {
                    class: 'trend-stable',
                    icon: 'fa-minus',
                    text: 'Stable vs année précédente'
                };
            }
        }

        // Create department trends chart
        function createDepartmentTrendsChart() {
            const ctx = document.getElementById('departmentTrendsChart').getContext('2d');

            if (departmentTrendsChart) {
                departmentTrendsChart.destroy();
            }

            departmentTrendsChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: historicalData.map(d => d.year),
                    datasets: [{
                        label: 'Heures Totales',
                        data: historicalData.map(d => d.total_hours),
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return 'Total: ' + context.parsed.y + 'h';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Heures'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Année Académique'
                            }
                        }
                    }
                }
            });
        }

        // Create distribution chart for latest year
        function createDistributionChart() {
            if (historicalData.length === 0) return;

            const ctx = document.getElementById('distributionChart').getContext('2d');
            const latestYear = historicalData[0];

            if (distributionChart) {
                distributionChart.destroy();
            }

            // Get detailed data for latest year
            loadLatestYearDistribution(latestYear.year);
        }

        // Load distribution data for latest year
        async function loadLatestYearDistribution(year) {
            try {
                const response = await fetch(`../../controller/workloadController.php?action=getDepartmentWorkloadByYear&department_id=${departmentId}&academic_year=${year}`);
                const data = await response.json();

                if (data.success) {
                    const teachers = data.data;
                    const coursTotal = teachers.reduce((sum, t) => sum + parseInt(t.cours_hours), 0);
                    const tdTotal = teachers.reduce((sum, t) => sum + parseInt(t.td_hours), 0);
                    const tpTotal = teachers.reduce((sum, t) => sum + parseInt(t.tp_hours), 0);

                    const ctx = document.getElementById('distributionChart').getContext('2d');

                    distributionChart = new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            labels: ['Cours', 'TD', 'TP'],
                            datasets: [{
                                data: [coursTotal, tdTotal, tpTotal],
                                backgroundColor: [
                                    '#667eea',
                                    '#764ba2',
                                    '#f093fb'
                                ],
                                borderWidth: 0
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                    labels: {
                                        padding: 20,
                                        usePointStyle: true
                                    }
                                },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return context.label + ': ' + context.parsed + 'h';
                                        }
                                    }
                                }
                            }
                        }
                    });
                }
            } catch (error) {
                console.error('Error loading distribution data:', error);
            }
        }

        // Create teacher evolution chart
        function createTeacherEvolutionChart() {
            const ctx = document.getElementById('teacherEvolutionChart').getContext('2d');

            if (teacherEvolutionChart) {
                teacherEvolutionChart.destroy();
            }

            teacherEvolutionChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: historicalData.map(d => d.year),
                    datasets: [{
                        label: 'Nombre d\'Enseignants',
                        data: historicalData.map(d => d.total_teachers),
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.4,
                        yAxisID: 'y'
                    }, {
                        label: 'Charge Moyenne (h)',
                        data: historicalData.map(d => d.average_hours),
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: 'Année Académique'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Nombre d\'Enseignants'
                            },
                            beginAtZero: true
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Heures Moyennes'
                            },
                            beginAtZero: true,
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }

        // Show error message
        function showErrorMessage() {
            document.getElementById('yearComparison').innerHTML = `
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                    <h5>Erreur</h5>
                    <p>Impossible de charger les données historiques</p>
                </div>
            `;
        }
    </script>
</body>
</html>
