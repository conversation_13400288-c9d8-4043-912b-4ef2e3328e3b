<?php
require_once "../controller/defineGrpController.php";

// Set proper content type and CORS headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            // Get modules by filiere and niveau
            if (isset($_GET['filiere_id']) && isset($_GET['niveau_id'])) {
                getModulesByFiliereAndNiveauAPI($_GET['filiere_id'], $_GET['niveau_id']);
            }
            // Get modules by filiere and cycle
            else if (isset($_GET['filiere_id']) && isset($_GET['cycle_id'])) {
                getModulesByFiliereAndCycleAPI($_GET['filiere_id'], $_GET['cycle_id']);
            }
            // Get modules by filiere
            else if (isset($_GET['filiere_id'])) {
                getModulesByFiliereAPI($_GET['filiere_id']);
            }
            // Get niveaux by filiere
            else if (isset($_GET['niveaux_by_filiere'])) {
                getNiveauxByFiliereAPI($_GET['niveaux_by_filiere']);
            }
            // Get cycles by filiere
            else if (isset($_GET['cycles_by_filiere'])) {
                getCyclesByFiliereAPI($_GET['cycles_by_filiere']);
            }
            // Get niveaux grouped by cycle for a filiere
            else if (isset($_GET['niveaux_by_cycle'])) {
                getNiveauxGroupedByCycleAPI($_GET['niveaux_by_cycle']);
            }
            // Invalid request
            else {
                jsonResponse(['error' => 'Missing required parameters'], 400);
            }
            break;

        case 'PUT':
            // Update a single module's groups
            if (isset($_GET['module_id'])) {
                $data = json_decode(file_get_contents('php://input'), true);
                updateModuleGroupsAPI($_GET['module_id'], $data);
            }
            // Update all modules in a cycle
            else if (isset($_GET['filiere_id']) && isset($_GET['cycle_id'])) {
                $data = json_decode(file_get_contents('php://input'), true);
                updateModuleGroupsByCycleAPI($_GET['filiere_id'], $_GET['cycle_id'], $data);
            }
            // Batch update multiple modules' groups
            else if (isset($_GET['batch'])) {
                $data = json_decode(file_get_contents('php://input'), true);
                updateModulesGroupsBatchAPI($data);
            }
            // Invalid request
            else {
                jsonResponse(['error' => 'Missing required parameters'], 400);
            }
            break;

        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
            break;
    }
} catch (Exception $e) {
    error_log("Error in defineGrpRoute: " . $e->getMessage());
    jsonResponse(['error' => 'Server error: ' . $e->getMessage()], 500);
}