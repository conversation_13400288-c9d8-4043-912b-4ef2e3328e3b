<?php
// En-têtes communs pour les réponses JSON
function setJsonHeaders() {
    header("Content-Type: application/json; charset=UTF-8");
    header("Access-Control-Allow-Origin: *");
    header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
    header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");
    header("Access-Control-Max-Age: 3600");
}

// Réponse JSON standardisée
function jsonResponse($data, $statusCode = 200) {
    // Désactiver l'affichage des erreurs
    $displayErrors = ini_get('display_errors');
    ini_set('display_errors', 0);

    try {
        // Make sure no output has been sent before
        if (!headers_sent()) {
            setJsonHeaders();
            http_response_code($statusCode);
        } else {
            error_log("Headers already sent in jsonResponse");
        }

        // S'assurer que la réponse est encodée correctement
        $jsonData = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        if ($jsonData === false) {
            // En cas d'erreur d'encodage JSON, enregistrer l'erreur et renvoyer une réponse d'erreur générique
            error_log("Erreur d'encodage JSON: " . json_last_error_msg());
            echo json_encode(['success' => false, 'error' => 'Erreur serveur: ' . json_last_error_msg()]);
        } else {
            echo $jsonData;
        }
    } catch (Exception $e) {
        // En cas d'exception, enregistrer l'erreur et renvoyer une réponse d'erreur générique
        error_log("Exception dans jsonResponse: " . $e->getMessage());
        if (!headers_sent()) {
            http_response_code(500);
        }
        echo json_encode(['success' => false, 'error' => 'Erreur serveur: ' . $e->getMessage()]);
    }

    // Restaurer le paramètre d'affichage des erreurs
    ini_set('display_errors', $displayErrors);

    exit();
}

?>