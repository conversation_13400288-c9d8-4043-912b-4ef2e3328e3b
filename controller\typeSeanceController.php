<?php
require_once "../model/typeSeanceModel.php";
require_once "../utils/response.php";

// Get all session types
function getAllTypeSeancesAPI() {
    $types = getAllTypeSeances();

    if (isset($types['error'])) {
        jsonResponse(['error' => $types['error']], 404);
    }

    // Log the types for debugging
    error_log("Retrieved " . count($types) . " session types");

    jsonResponse(['data' => $types], 200);
}

// Get session type by ID
function getTypeSeanceByIdAPI($id) {
    $type = getTypeSeanceById($id);

    if (isset($type['error'])) {
        jsonResponse(['error' => $type['error']], 404);
    }

    jsonResponse(['data' => $type], 200);
}
?>