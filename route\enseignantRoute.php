<?php
require_once "../controller/enseignantController.php";
require_once "../utils/response.php";

// Désactiver l'affichage des erreurs pour éviter de renvoyer du HTML
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Définir le type de contenu comme JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

// Capturer toutes les erreurs
try {
    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'GET':
            if (isset($_GET['cni'])) {
                getEnseignantByCNIAPI($_GET['cni']);
            } elseif (isset($_GET['departement'])) {
                getEnseignantsByDepartementAPI($_GET['departement']);
            } elseif (isset($_GET['roles'])) {
                getAvailableRolesAPI();
            } elseif (isset($_GET['action']) && $_GET['action'] === 'getTeacherFields' && isset($_GET['id'])) {
                getTeacherFieldsAPI($_GET['id']);
            } elseif (isset($_GET['action']) && $_GET['action'] === 'getTeacherFieldsFromAffectation' && isset($_GET['id'])) {
                getTeacherFieldsFromAffectationAPI($_GET['id']);
            } else {
                getAllEnseignantsAPI();
            }
            break;

        case 'POST':
            createEnseignantAPI();
            break;

        case 'PUT':
            if (isset($_GET['cni'])) {
                updateEnseignantAPI($_GET['cni']);
            } else {
                jsonResponse(['error' => 'CNI requis pour la mise à jour'], 400);
            }
            break;

        case 'DELETE':
            if (isset($_GET['cni'])) {
                deleteEnseignantAPI($_GET['cni']);
            } else {
                jsonResponse(['error' => 'CNI requis pour la suppression'], 400);
            }
            break;

        default:
            jsonResponse(['error' => 'Méthode HTTP non autorisée'], 405);
            break;
    }
} catch (Exception $e) {
    // Journaliser l'erreur
    error_log("Erreur dans enseignantRoute.php: " . $e->getMessage());

    // Renvoyer une réponse JSON avec l'erreur
    jsonResponse(['error' => 'Erreur serveur: ' . $e->getMessage()], 500);
}
?>