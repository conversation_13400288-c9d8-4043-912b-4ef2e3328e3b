<?php
require_once "../config/db.php";

 //get All etudiant
function getAllEtudiants() {
    $conn = getConnection();
    $sql = "SELECT e.*, f.nom_filiere, n.nom as niveau FROM etudiant e LEFT JOIN filiere f ON e.id_filiere = f.id_filiere LEFT JOIN niveaux n ON e.id_niveau = n.id";
    $result = mysqli_query($conn, $sql);

    $etudiants = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $etudiants[] = $row;
    }

    mysqli_close($conn);


    return $etudiants;
}


 //get etudiant by CNE
function getEtudiantByCNE($cne) {
    $conn = getConnection(); // Connexion à la base de données

    if (!$conn) {
        return ["error" => "Erreur de connexion à la base de données"];
    }

    // Sécuriser le CNE contre les injections SQL
    $cne = mysqli_real_escape_string($conn, $cne);

    $sql = "SELECT e.*, f.nom_filiere, n.nom as niveau FROM etudiant e LEFT JOIN filiere f ON e.id_filiere = f.id_filiere LEFT JOIN niveaux n ON e.id_niveau = n.id WHERE e.CNE = '$cne'";
    $result = mysqli_query($conn, $sql);

    if ($result && mysqli_num_rows($result) > 0) {
        $etudiant = mysqli_fetch_assoc($result);
        mysqli_free_result($result);
        mysqli_close($conn);
        return $etudiant;
    } else {
        mysqli_close($conn);
        return ["error" => "Aucun étudiant trouvé avec ce CNE"];
    }
}

//insert etudiant
function insertEtudiant($CNE, $nom, $prenom, $email, $tele, $sexe, $pays, $ville, $date_naissance, $lieu_naissance, $coordonne_parental, $id_filiere, $date_inscription, $id_niveau) {
    $conn = getConnection();

    // Échapper toutes les variables pour prévenir les injections SQL
    $CNE = mysqli_real_escape_string($conn, $CNE);
    $nom = mysqli_real_escape_string($conn, $nom);
    $prenom = mysqli_real_escape_string($conn, $prenom);
    $email = mysqli_real_escape_string($conn, $email);
    $tele = mysqli_real_escape_string($conn, $tele);
    $sexe = mysqli_real_escape_string($conn, $sexe);
    $pays = mysqli_real_escape_string($conn, $pays);
    $ville = mysqli_real_escape_string($conn, $ville);
    $date_naissance = mysqli_real_escape_string($conn, $date_naissance);
    $lieu_naissance = mysqli_real_escape_string($conn, $lieu_naissance);
    $coordonne_parental = mysqli_real_escape_string($conn, $coordonne_parental);
    $id_filiere = mysqli_real_escape_string($conn, $id_filiere);
    $date_inscription = mysqli_real_escape_string($conn, $date_inscription);
    $id_niveau = mysqli_real_escape_string($conn, $id_niveau);

    // Vérifier si le niveau appartient au cycle de la filière
    $check_sql = "SELECT COUNT(*) as count FROM niveaux n
                  JOIN filiere f ON n.cycle_id = f.id_cycle
                  WHERE n.id = '$id_niveau' AND f.id_filiere = '$id_filiere'";
    $check_result = mysqli_query($conn, $check_sql);
    $row = mysqli_fetch_assoc($check_result);

    if ($row['count'] == 0) {
        mysqli_close($conn);
        return ['error' => 'Le niveau sélectionné ne correspond pas au cycle de la filière choisie'];
    }

    $sql = "INSERT INTO etudiant (CNE, nom, prenom, email, tele, sexe, pays, ville, date_naissance, lieu_naissance, coordonne_parental, id_filiere, date_inscription, id_niveau)
            VALUES ('$CNE', '$nom', '$prenom', '$email', '$tele', '$sexe', '$pays', '$ville', '$date_naissance', '$lieu_naissance', '$coordonne_parental', '$id_filiere', '$date_inscription', '$id_niveau')";

    if (!mysqli_query($conn, $sql)) {
        $error = mysqli_error($conn);
        mysqli_close($conn);
        return ['error' => 'Erreur lors de l\'insertion : ' . $error];
    }
    mysqli_close($conn);
    return ['success' => true];
}

//update etudiant's details
function updateEtudiant($CNE, $nom, $prenom, $email, $tele, $sexe, $pays, $ville, $date_naissance, $lieu_naissance, $coordonne_parental, $id_filiere, $date_inscription, $id_niveau) {
    $conn = getConnection();

    // Échapper toutes les variables pour prévenir les injections SQL
    $CNE = mysqli_real_escape_string($conn, $CNE);
    $nom = mysqli_real_escape_string($conn, $nom);
    $prenom = mysqli_real_escape_string($conn, $prenom);
    $email = mysqli_real_escape_string($conn, $email);
    $tele = mysqli_real_escape_string($conn, $tele);
    $sexe = mysqli_real_escape_string($conn, $sexe);
    $pays = mysqli_real_escape_string($conn, $pays);
    $ville = mysqli_real_escape_string($conn, $ville);
    $date_naissance = mysqli_real_escape_string($conn, $date_naissance);
    $lieu_naissance = mysqli_real_escape_string($conn, $lieu_naissance);
    $coordonne_parental = mysqli_real_escape_string($conn, $coordonne_parental);
    $id_filiere = mysqli_real_escape_string($conn, $id_filiere);
    $date_inscription = mysqli_real_escape_string($conn, $date_inscription);
    $id_niveau = mysqli_real_escape_string($conn, $id_niveau);

    // Mettre à jour les données de l'étudiant
    $sql = "UPDATE etudiant
            SET nom = '$nom', prenom = '$prenom', email = '$email', tele = '$tele', sexe = '$sexe',
                pays = '$pays', ville = '$ville', date_naissance = '$date_naissance',
                lieu_naissance = '$lieu_naissance', coordonne_parental = '$coordonne_parental',
                id_filiere = '$id_filiere', date_inscription = '$date_inscription', id_niveau = '$id_niveau'
            WHERE CNE = '$CNE'";

    if (!mysqli_query($conn, $sql)) {
        $error = mysqli_error($conn);
        mysqli_close($conn);
        return ['error' => 'Erreur lors de l\'insertion : ' . $error];
    }
    mysqli_close($conn);
    return ['success' => true];
}


//delete etudiant
function deleteEtudiantByCNE($CNE) {
    $conn = getConnection();
    $CNE = mysqli_real_escape_string($conn, $CNE);

    $sql = "DELETE FROM etudiant WHERE CNE = '$CNE'";
    $result = mysqli_query($conn, $sql);

    mysqli_close($conn);
    return $result;
}
?>
