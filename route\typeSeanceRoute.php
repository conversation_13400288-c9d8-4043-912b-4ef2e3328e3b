<?php
// Ensure no output before headers
ob_start();

require_once "../controller/typeSeanceController.php";

// Set proper content type and CORS headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        if (isset($_GET['id'])) {
            getTypeSeanceByIdAPI($_GET['id']);
        } else {
            getAllTypeSeancesAPI();
        }
        break;
        
    default:
        jsonResponse(['error' => 'Method not allowed'], 405);
        break;
}
?>