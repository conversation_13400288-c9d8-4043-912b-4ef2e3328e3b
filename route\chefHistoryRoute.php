<?php
/**
 * Chef History Route
 *
 * This file handles API requests for chef de département history functionality.
 */

// Include required files
require_once __DIR__ . '/../controller/chefHistoryController.php';
require_once __DIR__ . '/../utils/response.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if the user is authenticated and is a department head
function isChefDepartement() {
    return isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'chef de departement';
}

// Check if the user is an admin
function isAdmin() {
    return isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'admin';
}

// Verify authentication
if (!isChefDepartement() && !isAdmin()) {
    jsonResponse(['error' => 'Unauthorized access. Chef de département or admin access required.'], 401);
    exit;
}

// Set content type to JSON
header('Content-Type: application/json');

// Handle CORS if needed
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    http_response_code(200);
    exit;
}

// Handle GET requests
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    if (!isset($_GET['action'])) {
        jsonResponse(['error' => 'Action parameter is required'], 400);
        exit;
    }

    $action = $_GET['action'];

    // Get chef ID from session or parameter
    $chefId = $_GET['chef_id'] ?? $_SESSION['user']['teacher_id'] ?? null;

    if (!$chefId) {
        jsonResponse(['error' => 'Chef ID is required'], 400);
        exit;
    }

    switch ($action) {
        case 'getAcademicYears':
            getChefAcademicYearsAPI($chefId);
            break;

        case 'getStatistics':
            if (!isset($_GET['academic_year'])) {
                jsonResponse(['error' => 'Academic year is required'], 400);
                exit;
            }
            getDepartmentStatisticsAPI($chefId, $_GET['academic_year']);
            break;

        case 'getAssignments':
            if (!isset($_GET['academic_year'])) {
                jsonResponse(['error' => 'Academic year is required'], 400);
                exit;
            }
            getDepartmentAssignmentsAPI($chefId, $_GET['academic_year']);
            break;

        case 'getProfessorChoices':
            if (!isset($_GET['academic_year'])) {
                jsonResponse(['error' => 'Academic year is required'], 400);
                exit;
            }
            getDepartmentProfessorChoicesAPI($chefId, $_GET['academic_year']);
            break;

        case 'getWorkload':
            if (!isset($_GET['academic_year'])) {
                jsonResponse(['error' => 'Academic year is required'], 400);
                exit;
            }
            getDepartmentWorkloadAPI($chefId, $_GET['academic_year']);
            break;

        case 'getObservations':
            if (!isset($_GET['academic_year'])) {
                jsonResponse(['error' => 'Academic year is required'], 400);
                exit;
            }
            getDepartmentObservationsAPI($chefId, $_GET['academic_year']);
            break;

        case 'getModules':
            if (!isset($_GET['academic_year'])) {
                jsonResponse(['error' => 'Academic year is required'], 400);
                exit;
            }
            getDepartmentModulesAPI($chefId, $_GET['academic_year']);
            break;

        case 'getCompleteHistory':
            if (!isset($_GET['academic_year'])) {
                jsonResponse(['error' => 'Academic year is required'], 400);
                exit;
            }
            getCompleteChefHistoryAPI($chefId, $_GET['academic_year']);
            break;

        case 'getDepartmentInfo':
            getDepartmentInfoAPI($chefId);
            break;

        default:
            jsonResponse(['error' => 'Invalid action: ' . $action], 400);
            break;
    }
} else {
    jsonResponse(['error' => 'Method not allowed. Only GET requests are supported.'], 405);
}
?>
