# Dynamic Workload Tracking System

## Overview

The Dynamic Workload Tracking System replaces the previous single-column approach with a comprehensive solution that preserves historical data and supports multiple academic years. This system calculates workload dynamically from the `affectation` table, ensuring data accuracy and historical preservation.

## Key Features

### 1. **Dynamic Calculation**
- Workload is calculated in real-time from the `affectation` table
- No dependency on the `charge_horaire_accomplie` column for calculations
- Supports filtering by academic year for historical analysis

### 2. **Historical Data Preservation**
- Complete workload history is maintained across all academic years
- No data loss when transitioning between academic years
- Historical comparison and trend analysis capabilities

### 3. **Multi-Year Support**
- View workload data for any academic year
- Compare workload across different years
- Track teacher workload evolution over time

### 4. **Department Head Dashboard**
- Comprehensive workload overview for department management
- Real-time statistics and visualizations
- Teacher-specific workload details and history

## Database Structure

### Core Tables

#### `affectation` Table
- Stores teacher assignments to teaching units (UE)
- Includes `annee_academique` field for year-based filtering
- Links teachers (`professeur_id`) to teaching units (`unite_enseignement_id`)

#### `uniteenseignement` Table
- Contains teaching unit details including `volume_horaire`
- Linked to modules and provides workload calculation basis

#### `enseignant` Table
- Teacher information and department assignments
- `charge_horaire_accomplie` column maintained for backward compatibility

## New Functions

### Model Functions (`affectationModel.php`)

#### `calculateTeacherWorkloadByYear($teacherId, $academicYear)`
- Calculates total workload for a specific teacher and academic year
- Returns integer hours or error array
- Used for real-time workload calculations

#### `getTeacherWorkloadHistory($teacherId)`
- Retrieves complete workload history for a teacher across all years
- Returns array with breakdown by year and UE type (Cours, TD, TP)
- Includes assignment counts and detailed statistics

#### `getDepartmentWorkloadByYear($departmentId, $academicYear)`
- Gets workload overview for all teachers in a department for specific year
- Returns comprehensive data including role-based statistics
- Used for department management and reporting

### Controller Functions (`workloadController.php`)

#### API Endpoints
- `getTeacherWorkloadByYear`: Get teacher workload for specific year
- `getTeacherWorkloadHistory`: Get complete teacher workload history
- `getDepartmentWorkloadByYear`: Get department workload overview
- `getAvailableAcademicYears`: Get list of available academic years
- `getDepartmentWorkloadSummary`: Get statistical summary for department

## User Interface

### 1. **Workload Overview Page** (`workload_overview.php`)
- Real-time department workload statistics
- Teacher workload list with progress indicators
- Workload distribution charts (Cours, TD, TP)
- Historical comparison charts
- Academic year selector for filtering

### 2. **Teacher Workload Details** (`teacher_workload_details.php`)
- Individual teacher workload analysis
- Complete workload history with charts
- Year-by-year breakdown of assignments
- Visual trend analysis

### 3. **Workload History** (`workload_history.php`)
- Department-wide historical analysis
- Year-over-year comparison with trend indicators
- Evolution charts for total hours and teacher counts
- Distribution analysis across multiple years

## Benefits

### 1. **Data Accuracy**
- Real-time calculations ensure accuracy
- No manual workload updates required
- Automatic synchronization with assignments

### 2. **Historical Preservation**
- Complete workload history maintained
- No data loss during year transitions
- Long-term trend analysis capabilities

### 3. **Administrative Efficiency**
- Comprehensive dashboard for department heads
- Automated reporting and statistics
- Visual analytics for better decision making

### 4. **Scalability**
- Supports unlimited academic years
- Efficient database queries with proper indexing
- Modular design for easy extension

## Migration from Legacy System

### Backward Compatibility
- Legacy `charge_horaire_accomplie` column maintained
- Existing functions continue to work
- Gradual migration path available

### Data Integrity
- Dynamic calculations validated against existing data
- Workload balance validation functions included
- Error handling and logging for troubleshooting

## Usage Examples

### For Department Heads
```php
// Get current year workload overview
$workload = getDepartmentWorkloadByYear($departmentId, '2024-2025');

// Get teacher workload history
$history = getTeacherWorkloadHistory($teacherId);

// Calculate specific year workload
$hours = calculateTeacherWorkloadByYear($teacherId, '2023-2024');
```

### API Usage
```javascript
// Get department workload summary
fetch('../../controller/workloadController.php?action=getDepartmentWorkloadSummary&department_id=1&academic_year=2024-2025')

// Get teacher workload history
fetch('../../controller/workloadController.php?action=getTeacherWorkloadHistory&teacher_id=5')

// Get available academic years
fetch('../../controller/workloadController.php?action=getAvailableAcademicYears')
```

## Performance Considerations

### Database Optimization
- Proper indexing on `affectation` table
- Efficient JOIN operations for workload calculations
- Caching strategies for frequently accessed data

### Query Efficiency
- Year-based filtering reduces data processing
- Aggregated calculations minimize database load
- Optimized SQL queries for large datasets

## Future Enhancements

### Planned Features
- Workload prediction based on historical trends
- Automated workload balancing recommendations
- Integration with academic planning systems
- Export capabilities for external reporting

### Extensibility
- Plugin architecture for custom calculations
- API extensions for third-party integrations
- Configurable workload thresholds and alerts

## Conclusion

The Dynamic Workload Tracking System provides a robust, scalable solution for managing teacher workloads while preserving historical data and supporting comprehensive analysis. The system maintains backward compatibility while offering significant improvements in accuracy, functionality, and user experience.
