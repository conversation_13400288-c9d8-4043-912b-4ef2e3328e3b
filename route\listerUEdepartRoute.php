<?php
/**
 * Liste UE Departement Route
 *
 * This file handles API requests for department modules functionality.
 */

// Include required files
require_once __DIR__ . '/../controller/listerUEdepartController.php';
require_once __DIR__ . '/../utils/response.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if the user is authenticated and is a department head
function isDepartmentHead() {
    return isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'chef de departement';
}

// Check if the user is an admin
function isAdmin() {
    return isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'admin';
}

// Check authentication
if (!isDepartmentHead() && !isAdmin()) {
    jsonResponse(['error' => 'Unauthorized access'], 401);
    exit;
}

// Log the request for debugging
error_log("listerUEdepartRoute.php accessed with action: " . (isset($_GET['action']) ? $_GET['action'] : 'none'));
error_log("Session data: " . json_encode($_SESSION));

// Get the action from the request
$action = isset($_GET['action']) ? $_GET['action'] : '';

switch ($action) {
    case 'get_department_id':
        // Return the department ID from the session
        if (isset($_SESSION['user']['department_id'])) {
            jsonResponse(['department_id' => $_SESSION['user']['department_id']]);
        } else {
            jsonResponse(['error' => 'Department ID not found in session'], 404);
        }
        break;

    case 'get_modules':
        error_log("Processing get_modules action");

        // Get the department ID from the request or session
        $departmentId = isset($_GET['department_id']) ? $_GET['department_id'] : null;
        error_log("Department ID from request: " . ($departmentId ?? 'null'));

        // If no department ID provided, try to get it from the session
        if (!$departmentId && isset($_SESSION['user']['department_id'])) {
            $departmentId = $_SESSION['user']['department_id'];
            error_log("Department ID from session: " . $departmentId);
        }

        // If still no department ID, return an error
        if (!$departmentId) {
            error_log("No department ID found in request or session");
            jsonResponse(['error' => 'Department ID not provided'], 400);
            break;
        }

        error_log("Calling getModulesByDepartement with department ID: " . $departmentId);

        // Get modules for this department
        $modules = getDepartmentModules($departmentId);

        error_log("Result from getDepartmentModules: " . json_encode($modules));

        // Check if there was an error
        if (isset($modules['error'])) {
            error_log("Error in getDepartmentModules: " . $modules['error']);
            jsonResponse(['error' => $modules['error']], 500);
            break;
        }

        // Return the modules
        error_log("Returning " . (is_array($modules) ? count($modules) : 'non-array') . " modules");
        jsonResponse(['success' => true, 'data' => $modules]);
        break;

    case 'get_units':
        // Get the module ID from the request
        $moduleId = isset($_GET['module_id']) ? $_GET['module_id'] : null;

        // If no module ID provided, return an error
        if (!$moduleId) {
            jsonResponse(['error' => 'Module ID not provided'], 400);
            break;
        }

        // Get units for this module
        $units = getModuleUnits($moduleId);

        // Check if there was an error
        if (isset($units['error'])) {
            jsonResponse(['error' => $units['error']], 500);
            break;
        }

        // Return the units
        jsonResponse(['success' => true, 'data' => $units]);
        break;

    default:
        // Invalid action
        jsonResponse(['error' => 'Invalid action'], 400);
        break;
}

// Using the jsonResponse function from utils/response.php
?>
