<?php
require_once "../model/cycleModel.php";
require_once "../utils/response.php";

/**
 * Get all cycles API endpoint
 */
function getAllCyclesAPI() {
    $cycles = getAllCycles();
    
    if (isset($cycles['error'])) {
        jsonResponse(['error' => $cycles['error']], 404);
    }
    
    jsonResponse(['data' => $cycles], 200);
}

/**
 * Get cycle by ID API endpoint
 * 
 * @param int $id Cycle ID
 */
function getCycleByIdAPI($id) {
    $cycle = getCycleById($id);
    
    if (isset($cycle['error'])) {
        jsonResponse(['error' => $cycle['error']], 404);
    }
    
    jsonResponse(['data' => $cycle], 200);
}
?>
