<?php
require_once __DIR__ . "/../config/db.php";

/**
 * Get all notes
 *
 * @return array Array of notes
 */
function getAllNotes() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAllNotes");
        return ["error" => "Database connection error"];
    }

    $sql = "SELECT n.*, e.nom as etudiant_nom, e.prenom as etudiant_prenom, m.nom as module_nom, niv.niveau
            FROM note n
            LEFT JOIN etudiant e ON n.id_etudiant = e.id_etudiant
            LEFT JOIN module m ON n.id_module = m.id
            LEFT JOIN niveau niv ON n.id_niveau = niv.id_niveau
            ORDER BY n.date_saisie DESC";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getAllNotes: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching notes: " . $error];
    }

    $notes = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $notes[] = $row;
    }

    mysqli_close($conn);
    return $notes;
}

/**
 * Get students by filters
 *
 * @param int|null $filiereId The field ID (optional)
 * @param int|null $niveauId The level ID (optional)
 * @param string|null $semestre The semester (optional)
 * @param string|null $session The session (optional)
 * @param int $moduleId The module ID
 * @return array Array of students with their grades
 */
function getStudentsByFilters($filiereId = null, $niveauId = null, $semestre = null, $session = null, $moduleId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getStudentsByFilters");
        return ["error" => "Database connection error"];
    }

    // Sanitize inputs
    $moduleId = mysqli_real_escape_string($conn, $moduleId);
    $filiereId = $filiereId ? mysqli_real_escape_string($conn, $filiereId) : null;
    $niveauId = $niveauId ? mysqli_real_escape_string($conn, $niveauId) : null;
    $semestre = $semestre ? mysqli_real_escape_string($conn, $semestre) : null;
    $session = $session ? mysqli_real_escape_string($conn, $session) : null;

    // Vérifier si les paramètres de filière et niveau sont définis
    if (!$filiereId || !$niveauId) {
        error_log("Missing required parameters: filiereId=$filiereId, niveauId=$niveauId");
        return [];
    }

    // Log the parameters for debugging
    error_log("getStudentsByFilters parameters: moduleId=$moduleId, niveauId=$niveauId, filiereId=$filiereId, semestre=$semestre, session=$session");

    try {
        // Use the new implementation that first checks the note table, then adds missing students from etudiant table
        return getFilteredStudents($filiereId, $niveauId, $semestre, $session, $moduleId, $conn);
    } catch (Exception $e) {
        error_log("Exception in getStudentsByFilters: " . $e->getMessage());
        if ($conn) {
            mysqli_close($conn);
        }
        return ["error" => $e->getMessage()];
    }
}

/**
 * Get filtered students with improved logic
 *
 * This function first retrieves students who already have grades from the note table,
 * then adds students from the etudiant table who match the filters but don't have grades yet.
 *
 * @param int|null $filiereId The field ID
 * @param int|null $niveauId The level ID
 * @param string|null $semestre The semester
 * @param string|null $session The session
 * @param int $moduleId The module ID
 * @param mysqli $conn Database connection (optional, will create one if not provided)
 * @return array Array of students with their grades
 */
function getFilteredStudents($filiereId, $niveauId, $semestre, $session, $moduleId, $conn = null) {
    // Create connection if not provided
    $closeConn = false;
    if (!$conn) {
        $conn = getConnection();
        $closeConn = true;

        if (!$conn) {
            error_log("Database connection error in getFilteredStudents");
            return ["error" => "Database connection error"];
        }
    }

    try {
        // Initialize arrays to store results
        $studentsWithGrades = [];
        $studentsWithoutGrades = [];
        $processedStudentIds = [];

        // Debug the input parameters
        error_log("getFilteredStudents parameters: moduleId=$moduleId, niveauId=$niveauId, filiereId=$filiereId, semestre=$semestre, session=$session");

        // STEP 1: First, get students who already have grades in the note table
        $notesQuery = "
            SELECT
                n.id_etudiant,
                n.valeur,
                n.id_note,
                e.CNE,
                e.nom,
                e.prenom
            FROM
                note n
            JOIN
                etudiant e ON n.id_etudiant = e.id_etudiant
            WHERE
                n.id_module = '$moduleId'
                AND n.id_filiere = '$filiereId'
                AND n.id_niveau = '$niveauId'
                " . ($semestre ? "AND n.semestre = '$semestre'" : "") . "
                " . ($session ? "AND n.session = '$session'" : "") . "
            ORDER BY
                e.nom, e.prenom
        ";

        // Execute a test query to check if there are any records in the note table matching these filters
        $testQuery = "
            SELECT COUNT(*) as count
            FROM note
            WHERE
                id_module = '$moduleId'
                AND id_filiere = '$filiereId'
                AND id_niveau = '$niveauId'
                " . ($semestre ? "AND semestre = '$semestre'" : "") . "
                " . ($session ? "AND session = '$session'" : "") . "
        ";

        $testResult = mysqli_query($conn, $testQuery);
        if ($testResult) {
            $countRow = mysqli_fetch_assoc($testResult);
            error_log("Number of matching records in note table: " . $countRow['count']);
        }

        error_log("SQL query for students with grades: $notesQuery");
        $notesResult = mysqli_query($conn, $notesQuery);

        if (!$notesResult) {
            $error = mysqli_error($conn);
            error_log("Error fetching students with grades: " . $error);
            throw new Exception("Error fetching students with grades: " . $error);
        }

        // Process students with grades
        while ($student = mysqli_fetch_assoc($notesResult)) {
            // Debug raw data from database
            error_log("Raw student data from note table: " . print_r($student, true));

            // Ensure the grade value is properly formatted
            if (isset($student['valeur']) && $student['valeur'] !== null && $student['valeur'] !== '') {
                // Convert to float and format to 2 decimal places for consistency
                $student['valeur'] = number_format((float)$student['valeur'], 2, '.', '');
                $student['has_grade'] = true;
                error_log("Student {$student['nom']} {$student['prenom']} has grade: {$student['valeur']}");
            } else {
                // If valeur is null or empty, set has_grade to false
                $student['valeur'] = '';
                $student['has_grade'] = false;
                error_log("Student {$student['nom']} {$student['prenom']} has empty grade value");
            }

            $studentsWithGrades[] = $student;

            // Keep track of processed student IDs to avoid duplicates
            $processedStudentIds[] = $student['id_etudiant'];
        }

        error_log("Found " . count($studentsWithGrades) . " students with grades from note table");

        // STEP 2: Get additional students from etudiant table who match the filters but don't have grades yet
        if (!empty($processedStudentIds)) {
            $excludeIds = implode(',', $processedStudentIds);
            $additionalStudentsQuery = "
                SELECT
                    id_etudiant,
                    CNE,
                    nom,
                    prenom
                FROM
                    etudiant
                WHERE
                    id_filiere = '$filiereId'
                    AND id_niveau = '$niveauId'
                    AND id_etudiant NOT IN ($excludeIds)
                ORDER BY
                    nom, prenom
            ";
        } else {
            // If no students with grades were found, get all students matching the filters
            $additionalStudentsQuery = "
                SELECT
                    id_etudiant,
                    CNE,
                    nom,
                    prenom
                FROM
                    etudiant
                WHERE
                    id_filiere = '$filiereId'
                    AND id_niveau = '$niveauId'
                ORDER BY
                    nom, prenom
            ";
        }

        error_log("SQL query for additional students: $additionalStudentsQuery");
        $additionalStudentsResult = mysqli_query($conn, $additionalStudentsQuery);

        if (!$additionalStudentsResult) {
            $error = mysqli_error($conn);
            error_log("Error fetching additional students: " . $error);
            throw new Exception("Error fetching additional students: " . $error);
        }

        // Process additional students without grades
        while ($student = mysqli_fetch_assoc($additionalStudentsResult)) {
            // Initialize with empty grade values
            $student['valeur'] = '';
            $student['id_note'] = null;
            $student['has_grade'] = false;

            $studentsWithoutGrades[] = $student;
        }

        error_log("Found " . count($studentsWithoutGrades) . " additional students without grades from etudiant table");

        // STEP 3: Combine both sets of results, with students with grades appearing first
        $combinedResults = array_merge($studentsWithGrades, $studentsWithoutGrades);

        error_log("Total students after combining: " . count($combinedResults));

        // Debug the first few results to verify data
        for ($i = 0; $i < min(5, count($combinedResults)); $i++) {
            $student = $combinedResults[$i];
            error_log("Result $i: id_etudiant={$student['id_etudiant']}, nom={$student['nom']}, valeur={$student['valeur']}, has_grade={$student['has_grade']}");
        }

        // Close connection if we created it
        if ($closeConn) {
            mysqli_close($conn);
        }

        return $combinedResults;

    } catch (Exception $e) {
        error_log("Exception in getFilteredStudents: " . $e->getMessage());
        if ($conn && $closeConn) {
            mysqli_close($conn);
        }
        return ["error" => $e->getMessage()];
    }
}

/**
 * Save grades for multiple students
 *
 * @param array $grades Array of grade data
 * @return bool|array True on success, error array on failure
 */
function saveGrades($grades) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in saveGrades");
        return ["error" => "Database connection error"];
    }

    // Start transaction
    mysqli_begin_transaction($conn);

    try {
        foreach ($grades as $grade) {
            // Validate required fields
            if (!isset($grade['id_etudiant']) || !isset($grade['id_module']) ||
                !isset($grade['id_niveau']) || !isset($grade['id_filiere']) ||
                !isset($grade['semestre']) || !isset($grade['session']) ||
                !isset($grade['valeur'])) {
                throw new Exception("Missing required fields in grade data");
            }

            // Sanitize inputs
            $id_etudiant = mysqli_real_escape_string($conn, $grade['id_etudiant']);
            $id_module = mysqli_real_escape_string($conn, $grade['id_module']);
            $id_niveau = mysqli_real_escape_string($conn, $grade['id_niveau']);
            $id_filiere = mysqli_real_escape_string($conn, $grade['id_filiere']);
            $semestre = mysqli_real_escape_string($conn, $grade['semestre']);
            $session = mysqli_real_escape_string($conn, $grade['session']);
            $valeur = (float) $grade['valeur'];

            // Log the grade data for debugging
            error_log("Processing grade: student=$id_etudiant, module=$id_module, niveau=$id_niveau, filiere=$id_filiere, semestre=$semestre, session=$session, valeur=$valeur");

            // Check if a note already exists for this student and module
            $checkSql = "SELECT id_note FROM note
                        WHERE id_etudiant = '$id_etudiant'
                        AND id_module = '$id_module'
                        AND id_niveau = '$id_niveau'
                        AND semestre = '$semestre'
                        AND session = '$session'";

            error_log("Check SQL in saveGrades: $checkSql");
            $checkResult = mysqli_query($conn, $checkSql);

            if (!$checkResult) {
                $error = mysqli_error($conn);
                error_log("Error checking existing note: " . $error);
                throw new Exception("Error checking existing note: " . $error);
            }

            if (mysqli_num_rows($checkResult) > 0) {
                // Update existing note
                $row = mysqli_fetch_assoc($checkResult);
                $id_note = $row['id_note'];

                $updateSql = "UPDATE note SET valeur = '$valeur', date_saisie = NOW()
                            WHERE id_note = '$id_note'";

                error_log("Update SQL in saveGrades: $updateSql");
                $updateResult = mysqli_query($conn, $updateSql);

                if (!$updateResult) {
                    $error = mysqli_error($conn);
                    error_log("Error updating note: " . $error);
                    throw new Exception("Error updating note: " . $error);
                }

                error_log("Note updated successfully for student ID: $id_etudiant, module ID: $id_module");
            } else {
                // Before inserting, check if the table has AUTO_INCREMENT for id_note
                $tableInfoSql = "SHOW COLUMNS FROM note WHERE Field = 'id_note'";
                $tableInfoResult = mysqli_query($conn, $tableInfoSql);

                if (!$tableInfoResult) {
                    $error = mysqli_error($conn);
                    error_log("Error checking table structure in saveGrades: " . $error);
                    throw new Exception("Error checking table structure: " . $error);
                }

                $columnInfo = mysqli_fetch_assoc($tableInfoResult);
                $hasAutoIncrement = (strpos($columnInfo['Extra'], 'auto_increment') !== false);

                error_log("Note table id_note column info in saveGrades: " . print_r($columnInfo, true));
                error_log("Has AUTO_INCREMENT: " . ($hasAutoIncrement ? 'Yes' : 'No'));

                if (!$hasAutoIncrement) {
                    // If id_note doesn't have AUTO_INCREMENT, find the max id_note and increment it
                    $maxIdSql = "SELECT MAX(id_note) as max_id FROM note";
                    $maxIdResult = mysqli_query($conn, $maxIdSql);

                    if (!$maxIdResult) {
                        $error = mysqli_error($conn);
                        error_log("Error getting max id_note in saveGrades: " . $error);
                        throw new Exception("Error getting max id_note: " . $error);
                    }

                    $maxIdRow = mysqli_fetch_assoc($maxIdResult);
                    $nextId = ($maxIdRow['max_id'] ?? 0) + 1;

                    error_log("Next id_note to use in saveGrades: $nextId");

                    // Insert with explicit id_note
                    $insertSql = "INSERT INTO note (id_note, id_etudiant, id_module, id_niveau, id_filiere, semestre, session, valeur, date_saisie)
                                VALUES ('$nextId', '$id_etudiant', '$id_module', '$id_niveau', '$id_filiere', '$semestre', '$session', '$valeur', NOW())";
                } else {
                    // If id_note has AUTO_INCREMENT, let MySQL handle it
                    $insertSql = "INSERT INTO note (id_etudiant, id_module, id_niveau, id_filiere, semestre, session, valeur, date_saisie)
                                VALUES ('$id_etudiant', '$id_module', '$id_niveau', '$id_filiere', '$semestre', '$session', '$valeur', NOW())";
                }

                error_log("Insert SQL in saveGrades: $insertSql");
                $insertResult = mysqli_query($conn, $insertSql);

                if (!$insertResult) {
                    $error = mysqli_error($conn);
                    error_log("Error inserting note in saveGrades: " . $error);
                    throw new Exception("Error inserting note: " . $error);
                }

                error_log("New note inserted successfully for student ID: $id_etudiant, module ID: $id_module");
            }
        }

        // Commit transaction
        mysqli_commit($conn);
        mysqli_close($conn);
        return true;
    } catch (Exception $e) {
        // Rollback transaction on error
        if ($conn) {
            mysqli_rollback($conn);
            mysqli_close($conn);
        }
        error_log("Error in saveGrades: " . $e->getMessage());
        return ["error" => "Error saving grades: " . $e->getMessage()];
    }
}

/**
 * Create a new note
 *
 * @param array $data Note data
 * @return bool|array True on success, error array on failure
 */
function createNote($data) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in createNote");
        return ["error" => "Database connection error"];
    }

    try {
        // Start transaction
        mysqli_begin_transaction($conn);

        // First, check if a record with these criteria already exists
        $id_etudiant = mysqli_real_escape_string($conn, $data['id_etudiant']);
        $id_module = mysqli_real_escape_string($conn, $data['id_module']);
        $id_niveau = mysqli_real_escape_string($conn, $data['id_niveau']);
        $semestre = mysqli_real_escape_string($conn, $data['semestre']);
        $session = mysqli_real_escape_string($conn, $data['session']);

        $checkSql = "SELECT id_note FROM note
                    WHERE id_etudiant = '$id_etudiant'
                    AND id_module = '$id_module'
                    AND id_niveau = '$id_niveau'
                    AND semestre = '$semestre'
                    AND session = '$session'";

        error_log("Check SQL in createNote: $checkSql");
        $checkResult = mysqli_query($conn, $checkSql);

        if (!$checkResult) {
            $error = mysqli_error($conn);
            error_log("Error checking note existence in createNote: " . $error);
            throw new Exception("Error checking note existence: " . $error);
        }

        if (mysqli_num_rows($checkResult) > 0) {
            // Note already exists, update it instead
            $row = mysqli_fetch_assoc($checkResult);
            $id_note = $row['id_note'];
            $valeur = (float) $data['valeur'];

            $updateSql = "UPDATE note SET valeur = '$valeur', date_saisie = NOW()
                        WHERE id_note = '$id_note'";

            error_log("Update SQL in createNote: $updateSql");
            $updateResult = mysqli_query($conn, $updateSql);

            if (!$updateResult) {
                $error = mysqli_error($conn);
                error_log("Error updating note in createNote: " . $error);
                throw new Exception("Error updating note: " . $error);
            }

            error_log("Note updated successfully in createNote for student ID: $id_etudiant, module ID: $id_module");

            // Commit transaction
            mysqli_commit($conn);
            mysqli_close($conn);
            return ["id" => $id_note];
        }

        // Before inserting, check if the table has AUTO_INCREMENT for id_note
        $tableInfoSql = "SHOW COLUMNS FROM note WHERE Field = 'id_note'";
        $tableInfoResult = mysqli_query($conn, $tableInfoSql);

        if (!$tableInfoResult) {
            $error = mysqli_error($conn);
            error_log("Error checking table structure in createNote: " . $error);
            throw new Exception("Error checking table structure: " . $error);
        }

        $columnInfo = mysqli_fetch_assoc($tableInfoResult);
        $hasAutoIncrement = (strpos($columnInfo['Extra'], 'auto_increment') !== false);

        error_log("Note table id_note column info in createNote: " . print_r($columnInfo, true));
        error_log("Has AUTO_INCREMENT: " . ($hasAutoIncrement ? 'Yes' : 'No'));

        if (!$hasAutoIncrement) {
            // If id_note doesn't have AUTO_INCREMENT, find the max id_note and increment it
            $maxIdSql = "SELECT MAX(id_note) as max_id FROM note";
            $maxIdResult = mysqli_query($conn, $maxIdSql);

            if (!$maxIdResult) {
                $error = mysqli_error($conn);
                error_log("Error getting max id_note in createNote: " . $error);
                throw new Exception("Error getting max id_note: " . $error);
            }

            $maxIdRow = mysqli_fetch_assoc($maxIdResult);
            $nextId = ($maxIdRow['max_id'] ?? 0) + 1;

            error_log("Next id_note to use in createNote: $nextId");

            // Insert with explicit id_note
            $id_filiere = mysqli_real_escape_string($conn, $data['id_filiere']);
            $valeur = (float) $data['valeur'];

            $insertSql = "INSERT INTO note (id_note, id_etudiant, id_module, id_niveau, id_filiere, semestre, session, valeur, date_saisie)
                        VALUES ('$nextId', '$id_etudiant', '$id_module', '$id_niveau', '$id_filiere', '$semestre', '$session', '$valeur', NOW())";

            error_log("Insert SQL in createNote: $insertSql");
            $insertResult = mysqli_query($conn, $insertSql);

            if (!$insertResult) {
                $error = mysqli_error($conn);
                error_log("Error inserting note in createNote: " . $error);
                throw new Exception("Error inserting note: " . $error);
            }

            error_log("New note inserted successfully in createNote with ID: $nextId");

            // Commit transaction
            mysqli_commit($conn);
            mysqli_close($conn);
            return ["id" => $nextId];
        } else {
            // If id_note has AUTO_INCREMENT, use prepared statement
            $query = "INSERT INTO note (id_etudiant, id_module, id_niveau, id_filiere, semestre, session, valeur, date_saisie)
                    VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";

            $stmt = mysqli_prepare($conn, $query);

            if (!$stmt) {
                $error = mysqli_error($conn);
                error_log("Error preparing statement in createNote: " . $error);
                throw new Exception("Error preparing statement: " . $error);
            }

            // Bind parameters
            mysqli_stmt_bind_param(
                $stmt,
                "iiiissd",
                $data['id_etudiant'],
                $data['id_module'],
                $data['id_niveau'],
                $data['id_filiere'],
                $data['semestre'],
                $data['session'],
                $data['valeur']
            );

            // Execute the statement
            $result = mysqli_stmt_execute($stmt);

            if (!$result) {
                $error = mysqli_stmt_error($stmt);
                error_log("Error executing statement in createNote: " . $error);
                mysqli_stmt_close($stmt);
                throw new Exception("Error creating note: " . $error);
            }

            // Get the ID of the newly created note
            $id = mysqli_insert_id($conn);

            error_log("New note inserted successfully in createNote with auto-increment ID: $id");

            mysqli_stmt_close($stmt);

            // Commit transaction
            mysqli_commit($conn);
            mysqli_close($conn);
            return ["id" => $id];
        }
    } catch (Exception $e) {
        // Rollback transaction on error
        if ($conn) {
            mysqli_rollback($conn);
            mysqli_close($conn);
        }
        error_log("Exception in createNote: " . $e->getMessage());
        return ["error" => $e->getMessage()];
    }
}

/**
 * Update a note
 *
 * @param int $id Note ID
 * @param array $data Note data
 * @return bool|array True on success, error array on failure
 */
function updateNote($id, $data) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in updateNote");
        return ["error" => "Database connection error"];
    }

    // Prepare the query
    $query = "UPDATE note
              SET id_etudiant = ?, id_module = ?, id_niveau = ?, semestre = ?, session = ?, valeur = ?, date_saisie = NOW()
              WHERE id_note = ?";

    $stmt = mysqli_prepare($conn, $query);

    if (!$stmt) {
        $error = mysqli_error($conn);
        error_log("Error preparing statement in updateNote: " . $error);
        mysqli_close($conn);
        return ["error" => "Error preparing statement: " . $error];
    }

    // Bind parameters
    mysqli_stmt_bind_param(
        $stmt,
        "iiissdi",
        $data['id_etudiant'],
        $data['id_module'],
        $data['id_niveau'],
        $data['semestre'],
        $data['session'],
        $data['valeur'],
        $id
    );

    // Execute the statement
    $result = mysqli_stmt_execute($stmt);

    if (!$result) {
        $error = mysqli_stmt_error($stmt);
        error_log("Error executing statement in updateNote: " . $error);
        mysqli_stmt_close($stmt);
        mysqli_close($conn);
        return ["error" => "Error updating note: " . $error];
    }

    mysqli_stmt_close($stmt);
    mysqli_close($conn);

    return true;
}

/**
 * Update a student's grade
 *
 * @param int $studentId Student ID
 * @param int $moduleId Module ID
 * @param int $niveauId Level ID
 * @param int $filiereId Field ID
 * @param string $semestre Semester
 * @param string $session Session
 * @param float $grade Grade value
 * @return bool|array True on success, error array on failure
 */
function updateStudentGrade($studentId, $moduleId, $niveauId = null, $filiereId = null, $semestre = null, $session, $grade) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in updateStudentGrade");
        return ["error" => "Database connection error"];
    }

    try {
        // Start transaction to ensure data consistency
        mysqli_begin_transaction($conn);

        // Sanitize inputs
        $studentId = mysqli_real_escape_string($conn, $studentId);
        $moduleId = mysqli_real_escape_string($conn, $moduleId);
        $session = mysqli_real_escape_string($conn, $session);
        $grade = (float) $grade;

        // Use provided values or get them if null
        if ($niveauId) {
            $niveauId = mysqli_real_escape_string($conn, $niveauId);
        } else {
            // Get student's niveau_id
            $studentSql = "SELECT id_niveau FROM etudiant WHERE id_etudiant = '$studentId'";
            $studentResult = mysqli_query($conn, $studentSql);

            if (!$studentResult || mysqli_num_rows($studentResult) == 0) {
                $error = mysqli_error($conn);
                error_log("Error getting student info: " . $error);
                throw new Exception("Error getting student info: " . $error);
            }

            $studentData = mysqli_fetch_assoc($studentResult);
            $niveauId = $studentData['id_niveau'];
        }

        if ($filiereId) {
            $filiereId = mysqli_real_escape_string($conn, $filiereId);
        } else {
            // Get student's filiere_id
            $studentSql = "SELECT id_filiere FROM etudiant WHERE id_etudiant = '$studentId'";
            $studentResult = mysqli_query($conn, $studentSql);

            if (!$studentResult || mysqli_num_rows($studentResult) == 0) {
                $error = mysqli_error($conn);
                error_log("Error getting student info: " . $error);
                throw new Exception("Error getting student info: " . $error);
            }

            $studentData = mysqli_fetch_assoc($studentResult);
            $filiereId = $studentData['id_filiere'];
        }

        if ($semestre) {
            $semestre = mysqli_real_escape_string($conn, $semestre);
        } else {
            // Get module's semestre
            $moduleSql = "SELECT semestre FROM module WHERE id = '$moduleId'";
            $moduleResult = mysqli_query($conn, $moduleSql);

            if (!$moduleResult || mysqli_num_rows($moduleResult) == 0) {
                $error = mysqli_error($conn);
                error_log("Error getting module info: " . $error);
                throw new Exception("Error getting module info: " . $error);
            }

            $moduleData = mysqli_fetch_assoc($moduleResult);
            $semestre = $moduleData['semestre'];
        }

        // Log all parameters for debugging
        error_log("updateStudentGrade parameters: studentId=$studentId, moduleId=$moduleId, niveauId=$niveauId, filiereId=$filiereId, semestre=$semestre, session=$session, grade=$grade");

        // First, check if a record with these criteria already exists
        $checkSql = "SELECT id_note FROM note
                    WHERE id_etudiant = '$studentId'
                    AND id_module = '$moduleId'
                    AND id_niveau = '$niveauId'
                    AND id_filiere = '$filiereId'
                    AND semestre = '$semestre'
                    AND session = '$session'";

        error_log("Check SQL: $checkSql");
        $checkResult = mysqli_query($conn, $checkSql);

        if (!$checkResult) {
            $error = mysqli_error($conn);
            error_log("Error checking note existence: " . $error);
            throw new Exception("Error checking note existence: " . $error);
        }

        if (mysqli_num_rows($checkResult) > 0) {
            // Update existing note
            $row = mysqli_fetch_assoc($checkResult);
            $noteId = $row['id_note'];

            $updateSql = "UPDATE note SET valeur = '$grade', date_saisie = NOW()
                        WHERE id_note = '$noteId'";

            error_log("Update SQL: $updateSql");
            $updateResult = mysqli_query($conn, $updateSql);

            if (!$updateResult) {
                $error = mysqli_error($conn);
                error_log("Error updating grade: " . $error);
                throw new Exception("Error updating grade: " . $error);
            }

            error_log("Note updated successfully for student ID: $studentId, module ID: $moduleId");
        } else {
            // Before inserting, check if the table has AUTO_INCREMENT for id_note
            $tableInfoSql = "SHOW COLUMNS FROM note WHERE Field = 'id_note'";
            $tableInfoResult = mysqli_query($conn, $tableInfoSql);

            if (!$tableInfoResult) {
                $error = mysqli_error($conn);
                error_log("Error checking table structure: " . $error);
                throw new Exception("Error checking table structure: " . $error);
            }

            $columnInfo = mysqli_fetch_assoc($tableInfoResult);
            $hasAutoIncrement = (strpos($columnInfo['Extra'], 'auto_increment') !== false);

            error_log("Note table id_note column info: " . print_r($columnInfo, true));
            error_log("Has AUTO_INCREMENT: " . ($hasAutoIncrement ? 'Yes' : 'No'));

            if (!$hasAutoIncrement) {
                // If id_note doesn't have AUTO_INCREMENT, find the max id_note and increment it
                $maxIdSql = "SELECT MAX(id_note) as max_id FROM note";
                $maxIdResult = mysqli_query($conn, $maxIdSql);

                if (!$maxIdResult) {
                    $error = mysqli_error($conn);
                    error_log("Error getting max id_note: " . $error);
                    throw new Exception("Error getting max id_note: " . $error);
                }

                $maxIdRow = mysqli_fetch_assoc($maxIdResult);
                $nextId = ($maxIdRow['max_id'] ?? 0) + 1;

                error_log("Next id_note to use: $nextId");

                // Insert with explicit id_note
                $insertSql = "INSERT INTO note (id_note, id_etudiant, id_module, id_niveau, id_filiere, semestre, session, valeur, date_saisie)
                            VALUES ('$nextId', '$studentId', '$moduleId', '$niveauId', '$filiereId', '$semestre', '$session', '$grade', NOW())";
            } else {
                // If id_note has AUTO_INCREMENT, let MySQL handle it
                $insertSql = "INSERT INTO note (id_etudiant, id_module, id_niveau, id_filiere, semestre, session, valeur, date_saisie)
                            VALUES ('$studentId', '$moduleId', '$niveauId', '$filiereId', '$semestre', '$session', '$grade', NOW())";
            }

            error_log("Insert SQL: $insertSql");
            $insertResult = mysqli_query($conn, $insertSql);

            if (!$insertResult) {
                $error = mysqli_error($conn);
                error_log("Error inserting grade: " . $error);
                throw new Exception("Error inserting grade: " . $error);
            }

            error_log("New note inserted successfully for student ID: $studentId, module ID: $moduleId");
        }

        // Commit the transaction
        mysqli_commit($conn);
        mysqli_close($conn);
        return true;

    } catch (Exception $e) {
        // Rollback the transaction on error
        if ($conn) {
            mysqli_rollback($conn);
            mysqli_close($conn);
        }
        error_log("Exception in updateStudentGrade: " . $e->getMessage());
        return ["error" => $e->getMessage()];
    }
}

/**
 * Get top performing students by module
 *
 * @param int $moduleId Module ID
 * @param string $session Session
 * @param int $limit Number of top students to return
 * @return array Array of top students with their grades
 */
function getTopNotesByModule($moduleId, $session, $limit = 3) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getTopNotesByModule");
        return ["error" => "Database connection error"];
    }

    // Sanitize inputs
    $moduleId = mysqli_real_escape_string($conn, $moduleId);
    $session = mysqli_real_escape_string($conn, $session);
    $limit = (int) $limit;

    // Build the query
    $sql = "SELECT n.id, n.id_etudiant, n.valeur, e.nom, e.prenom, e.CNE
            FROM note n
            JOIN etudiant e ON n.id_etudiant = e.id_etudiant
            WHERE n.id_module = '$moduleId'";

    if ($session) {
        $sql .= " AND n.session = '$session'";
    }

    $sql .= " ORDER BY n.valeur DESC
              LIMIT $limit";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getTopNotesByModule: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching top notes: " . $error];
    }

    $topNotes = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $topNotes[] = $row;
    }

    mysqli_close($conn);
    return $topNotes;
}

/**
 * Delete a note
 *
 * @param int $id Note ID
 * @return bool|array True on success, error array on failure
 */
function deleteNote($id) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in deleteNote");
        return ["error" => "Database connection error"];
    }

    $id = mysqli_real_escape_string($conn, $id);

    // Delete the note
    $query = "DELETE FROM note WHERE id_note = '$id'";
    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in deleteNote: " . $error);
        mysqli_close($conn);
        return ["error" => "Error deleting note: " . $error];
    }

    mysqli_close($conn);
    return true;
}
?>