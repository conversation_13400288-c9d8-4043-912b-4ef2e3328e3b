/* Modules Page Styles */
.modules-content {
    padding: 2rem;
    width: 100%;
}

.modules-header {
    margin-bottom: 2rem;
    text-align: center;
}

.modules-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.5rem;
}

.subtitle {
    color: #666;
    font-size: 1.1rem;
}

/* Filters */
.modules-filters {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 2rem;
}

.filter-btn {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 20px;
    padding: 0.5rem 1.25rem;
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover {
    background-color: #e9ecef;
}

.filter-btn.active {
    background-color: #6c757d;
    color: white;
    border-color: #6c757d;
}

/* Module Categories */
.module-category {
    margin-bottom: 3rem;
}

.category-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #f0f0f0;
}

/* Modules Grid */
.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

/* Module Card */
.module-card {
    position: relative;
    border-radius: 16px;
    padding: 1.5rem;
    height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.module-card::before {
    content: "";
    position: absolute;
    top: -15px;
    left: -15px;
    width: 30px;
    height: 30px;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
}

.module-card::after {
    content: "";
    position: absolute;
    bottom: -15px;
    right: -15px;
    width: 30px;
    height: 30px;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
}

.module-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.module-emoji {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    text-align: center;
}

.module-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.module-actions {
    display: flex;
    justify-content: center;
}

.btn-view-module {
    background-color: rgba(255, 255, 255, 0.7);
    border: none;
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-view-module:hover {
    background-color: rgba(255, 255, 255, 0.9);
}

/* Modal Styles */
.modal-content {
    border-radius: 16px;
    border: none;
}

.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-radius: 16px 16px 0 0;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 16px 16px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .modules-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .modules-header h1 {
        font-size: 2rem;
    }

    .filter-btn {
        padding: 0.4rem 1rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .modules-grid {
        grid-template-columns: 1fr;
    }

    .modules-content {
        padding: 1rem;
    }

    .modules-filters {
        flex-direction: column;
        align-items: center;
    }

    .filter-btn {
        width: 100%;
        max-width: 200px;
    }
}