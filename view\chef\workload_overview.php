<?php
session_start();

// Check if user is logged in and is a department head
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'chef de departement') {
    header("Location: ../auth/login.php");
    exit();
}

// Get user information
$user = $_SESSION['user'];
$chefId = $user['id_enseignant'];
$departmentId = $user['id_departement'];
$fullName = $user['prenom'] . ' ' . $user['nom'];

// Get department name
require_once '../../model/departementModel.php';
$departmentInfo = getDepartementById($departmentId);
$departmentName = $departmentInfo ? $departmentInfo['nom_departement'] : 'Département';

// Page configuration
$pageTitle = "Aperçu de la Charge de Travail";
$currentPage = "workload_overview";
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">

    <style>
        .workload-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }

        .workload-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
        }

        .teacher-item {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s ease;
        }

        .teacher-item:hover {
            background-color: #f8f9fa;
        }

        .teacher-item:last-child {
            border-bottom: none;
        }

        .progress-custom {
            height: 8px;
            border-radius: 4px;
            background-color: #e9ecef;
        }

        .progress-bar-custom {
            border-radius: 4px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .year-selector {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .year-selector:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 1rem;
        }

        .no-data {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }

        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="content-wrapper">
                <div class="container-fluid p-4">
                    <!-- Page Header -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <h1 class="page-title">
                                <i class="fas fa-chart-line me-3"></i>
                                Aperçu de la Charge de Travail
                            </h1>
                            <p class="text-muted mb-0">
                                Suivi dynamique de la charge de travail des enseignants du département <strong><?php echo htmlspecialchars($departmentName); ?></strong>
                            </p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <select id="academicYearSelector" class="form-select year-selector">
                                <option value="">Chargement...</option>
                            </select>
                        </div>
                    </div>

                    <!-- Summary Statistics -->
                    <div class="row mb-4" id="summaryStats">
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-value" id="totalTeachers">-</div>
                                <div class="stat-label">Enseignants</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-value" id="totalHours">-</div>
                                <div class="stat-label">Heures Totales</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-value" id="totalAssignments">-</div>
                                <div class="stat-label">Affectations</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-value" id="averageHours">-</div>
                                <div class="stat-label">Moyenne/Enseignant</div>
                            </div>
                        </div>
                    </div>

                    <!-- Main Content -->
                    <div class="row">
                        <!-- Teachers Workload List -->
                        <div class="col-lg-8">
                            <div class="workload-card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-users me-2"></i>
                                        Charge de Travail par Enseignant
                                    </h5>
                                </div>
                                <div class="card-body p-0">
                                    <div id="teachersWorkloadList">
                                        <div class="loading-spinner">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Chargement...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Workload Distribution Chart -->
                        <div class="col-lg-4">
                            <div class="workload-card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chart-pie me-2"></i>
                                        Répartition par Type
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="workloadChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Historical Comparison -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="workload-card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        Évolution Historique de la Charge de Travail
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container" style="height: 400px;">
                                        <canvas id="historicalChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/sidebar.js"></script>

    <script>
        // Global variables
        let currentAcademicYear = '';
        let workloadChart = null;
        let historicalChart = null;
        const departmentId = <?php echo $departmentId; ?>;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadAcademicYears();

            // Academic year selector change event
            document.getElementById('academicYearSelector').addEventListener('change', function() {
                currentAcademicYear = this.value;
                if (currentAcademicYear) {
                    loadWorkloadData();
                }
            });
        });

        // Load available academic years
        async function loadAcademicYears() {
            try {
                const response = await fetch('../../controller/workloadController.php?action=getAvailableAcademicYears');
                const data = await response.json();

                if (data.success) {
                    const selector = document.getElementById('academicYearSelector');
                    selector.innerHTML = '';

                    data.data.forEach((year, index) => {
                        const option = document.createElement('option');
                        option.value = year;
                        option.textContent = year;
                        if (index === 0) {
                            option.selected = true;
                            currentAcademicYear = year;
                        }
                        selector.appendChild(option);
                    });

                    // Load data for the first year
                    if (currentAcademicYear) {
                        loadWorkloadData();
                    }
                } else {
                    console.error('Error loading academic years:', data.error);
                }
            } catch (error) {
                console.error('Error loading academic years:', error);
            }
        }

        // Load workload data for selected academic year
        async function loadWorkloadData() {
            try {
                // Load summary statistics
                await loadSummaryStats();

                // Load teachers workload
                await loadTeachersWorkload();

                // Load historical data
                await loadHistoricalData();

            } catch (error) {
                console.error('Error loading workload data:', error);
            }
        }

        // Load summary statistics
        async function loadSummaryStats() {
            try {
                const response = await fetch(`../../controller/workloadController.php?action=getDepartmentWorkloadSummary&department_id=${departmentId}&academic_year=${currentAcademicYear}`);
                const data = await response.json();

                if (data.success) {
                    const summary = data.data;
                    document.getElementById('totalTeachers').textContent = summary.total_teachers;
                    document.getElementById('totalHours').textContent = summary.total_hours + 'h';
                    document.getElementById('totalAssignments').textContent = summary.total_assignments;
                    document.getElementById('averageHours').textContent = summary.average_hours + 'h';
                } else {
                    console.error('Error loading summary stats:', data.error);
                }
            } catch (error) {
                console.error('Error loading summary stats:', error);
            }
        }

        // Load teachers workload list
        async function loadTeachersWorkload() {
            try {
                const response = await fetch(`../../controller/workloadController.php?action=getDepartmentWorkloadByYear&department_id=${departmentId}&academic_year=${currentAcademicYear}`);
                const data = await response.json();

                if (data.success) {
                    displayTeachersWorkload(data.data);
                    updateWorkloadChart(data.data);
                } else {
                    console.error('Error loading teachers workload:', data.error);
                    document.getElementById('teachersWorkloadList').innerHTML = '<div class="no-data"><i class="fas fa-exclamation-triangle fa-3x mb-3"></i><p>Erreur lors du chargement des données</p></div>';
                }
            } catch (error) {
                console.error('Error loading teachers workload:', error);
                document.getElementById('teachersWorkloadList').innerHTML = '<div class="no-data"><i class="fas fa-exclamation-triangle fa-3x mb-3"></i><p>Erreur lors du chargement des données</p></div>';
            }
        }

        // Display teachers workload list
        function displayTeachersWorkload(teachers) {
            const container = document.getElementById('teachersWorkloadList');

            if (teachers.length === 0) {
                container.innerHTML = '<div class="no-data"><i class="fas fa-users fa-3x mb-3"></i><p>Aucune donnée de charge trouvée pour cette année</p></div>';
                return;
            }

            const maxHours = Math.max(...teachers.map(t => t.total_hours));

            let html = '';
            teachers.forEach(teacher => {
                const progressPercentage = maxHours > 0 ? (teacher.total_hours / maxHours) * 100 : 0;
                const progressColor = getProgressColor(teacher.total_hours);

                html += `
                    <div class="teacher-item">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <h6 class="mb-1">
                                    <i class="fas fa-user me-2"></i>
                                    ${teacher.prenom} ${teacher.nom}
                                </h6>
                                <small class="text-muted">${teacher.role}</small>
                            </div>
                            <div class="text-end">
                                <strong class="text-primary">${teacher.total_hours}h</strong>
                                <br>
                                <small class="text-muted">${teacher.total_assignments} affectations</small>
                            </div>
                        </div>
                        <div class="progress progress-custom mb-2">
                            <div class="progress-bar progress-bar-custom bg-${progressColor}"
                                 style="width: ${progressPercentage}%"></div>
                        </div>
                        <div class="row text-center">
                            <div class="col-4">
                                <small class="text-muted">Cours: ${teacher.cours_hours}h</small>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">TD: ${teacher.td_hours}h</small>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">TP: ${teacher.tp_hours}h</small>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // Get progress bar color based on hours
        function getProgressColor(hours) {
            if (hours >= 200) return 'success';
            if (hours >= 150) return 'warning';
            if (hours >= 100) return 'info';
            return 'secondary';
        }

        // Update workload distribution chart
        function updateWorkloadChart(teachers) {
            const ctx = document.getElementById('workloadChart').getContext('2d');

            // Calculate totals by type
            const coursTotal = teachers.reduce((sum, t) => sum + parseInt(t.cours_hours), 0);
            const tdTotal = teachers.reduce((sum, t) => sum + parseInt(t.td_hours), 0);
            const tpTotal = teachers.reduce((sum, t) => sum + parseInt(t.tp_hours), 0);

            if (workloadChart) {
                workloadChart.destroy();
            }

            workloadChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Cours', 'TD', 'TP'],
                    datasets: [{
                        data: [coursTotal, tdTotal, tpTotal],
                        backgroundColor: [
                            '#667eea',
                            '#764ba2',
                            '#f093fb'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed + 'h';
                                }
                            }
                        }
                    }
                }
            });
        }

        // Load historical data for comparison
        async function loadHistoricalData() {
            try {
                const response = await fetch(`../../controller/workloadController.php?action=getAvailableAcademicYears`);
                const data = await response.json();

                if (data.success) {
                    const years = data.data.slice(0, 5); // Last 5 years
                    const historicalData = [];

                    for (const year of years) {
                        const yearResponse = await fetch(`../../controller/workloadController.php?action=getDepartmentWorkloadSummary&department_id=${departmentId}&academic_year=${year}`);
                        const yearData = await yearResponse.json();

                        if (yearData.success) {
                            historicalData.push({
                                year: year,
                                totalHours: yearData.data.total_hours,
                                totalTeachers: yearData.data.total_teachers,
                                averageHours: yearData.data.average_hours
                            });
                        }
                    }

                    updateHistoricalChart(historicalData);
                }
            } catch (error) {
                console.error('Error loading historical data:', error);
            }
        }

        // Update historical comparison chart
        function updateHistoricalChart(data) {
            const ctx = document.getElementById('historicalChart').getContext('2d');

            if (historicalChart) {
                historicalChart.destroy();
            }

            historicalChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.map(d => d.year),
                    datasets: [{
                        label: 'Heures Totales',
                        data: data.map(d => d.totalHours),
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        yAxisID: 'y'
                    }, {
                        label: 'Moyenne par Enseignant',
                        data: data.map(d => d.averageHours),
                        borderColor: '#764ba2',
                        backgroundColor: 'rgba(118, 75, 162, 0.1)',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y + 'h';
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: 'Année Académique'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Heures Totales'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Moyenne (h)'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>
