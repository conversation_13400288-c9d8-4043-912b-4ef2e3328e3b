<?php
// Inclure les constantes
require_once __DIR__ . "/../config/constants.php";
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Réinitialisation de mot de passe - ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <style>
        :root {
            --royal-blue: #1a73e8;
            --azure: #3a8ff7;
            --sky-blue: #64b5f6;
            --light-blue: #e8f0fe;
            --navy-blue: #0d47a1;
        }

        body {
            background-color: #f8f9fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }

        .reset-container {
            max-width: 450px;
            width: 100%;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: none;
        }

        .card-header {
            background-color: var(--royal-blue);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            padding: 20px;
            text-align: center;
        }

        .card-body {
            padding: 30px;
        }

        .form-control {
            border-radius: 5px;
            padding: 12px 15px;
            border: 1px solid #ced4da;
            margin-bottom: 20px;
        }

        .btn-primary {
            background-color: var(--royal-blue);
            border-color: var(--royal-blue);
            border-radius: 5px;
            padding: 12px 15px;
            font-weight: 500;
            width: 100%;
        }

        .btn-primary:hover {
            background-color: var(--navy-blue);
            border-color: var(--navy-blue);
        }

        .alert {
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .logo {
            max-width: 100px;
            margin-bottom: 15px;
        }

        .steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 33%;
        }

        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .step.active .step-number {
            background-color: var(--royal-blue);
            color: white;
        }

        .step-text {
            font-size: 12px;
            color: #6c757d;
            text-align: center;
        }

        .step.active .step-text {
            color: var(--royal-blue);
            font-weight: 500;
        }

        .step-line {
            flex-grow: 1;
            height: 2px;
            background-color: #e9ecef;
            margin: 15px 5px 0;
        }

        .back-to-login {
            text-align: center;
            margin-top: 20px;
        }

        .back-to-login a {
            color: var(--royal-blue);
            text-decoration: none;
        }

        .back-to-login a:hover {
            text-decoration: underline;
        }

        #emailSent {
            display: none;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="card">
            <div class="card-header">
                <img src="<?php echo BASE_URL; ?>/view/assets/img/logo.png" alt="ENSAH Logo" class="logo">
                <h4>Réinitialisation de mot de passe</h4>
            </div>
            <div class="card-body">
                <div class="steps">
                    <div class="step active">
                        <div class="step-number">1</div>
                        <div class="step-text">Identification</div>
                    </div>
                    <div class="step-line"></div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-text">Vérification</div>
                    </div>
                    <div class="step-line"></div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-text">Nouveau mot de passe</div>
                    </div>
                </div>

                <div id="requestForm">
                    <div class="alert alert-info" role="alert">
                        <i class="bi bi-info-circle-fill me-2"></i>
                        Entrez votre CNI (pour les administrateurs et enseignants) ou CNE (pour les étudiants) pour recevoir un code de réinitialisation par email.
                    </div>

                    <div class="alert alert-danger" id="errorAlert" style="display: none;" role="alert">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <span id="errorMessage"></span>
                    </div>

                    <form id="resetRequestForm">
                        <div class="mb-3">
                            <label for="identifier" class="form-label">CNI ou CNE</label>
                            <input type="text" class="form-control" id="identifier" name="identifier" required>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-envelope me-2"></i>
                            Envoyer le code
                        </button>
                    </form>
                </div>

                <div id="emailSent">
                    <div class="alert alert-success" role="alert">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        Un code de réinitialisation a été envoyé à l'adresse email associée à votre compte.
                    </div>

                    <div class="text-center mb-4">
                        <i class="bi bi-envelope-check" style="font-size: 3rem; color: var(--royal-blue);"></i>
                        <p class="mt-3">Nous avons envoyé un code à <strong id="maskedEmail"></strong></p>
                        <p class="text-muted">Le code est valable pendant 15 minutes</p>
                    </div>

                    <!-- Affichage du code en mode développement -->
                    <div id="debugCode" class="alert alert-warning mb-4" style="display: none;">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-bug-fill me-2"></i>
                            <strong>Mode développement</strong>
                        </div>
                        <p class="mb-0 mt-2">Code de réinitialisation: <span id="debugCodeValue" class="fw-bold"></span></p>
                    </div>

                    <a href="verify-code.php" id="continueBtn" class="btn btn-primary">
                        <i class="bi bi-arrow-right me-2"></i>
                        Continuer
                    </a>
                </div>

                <div class="back-to-login">
                    <a href="<?php echo BASE_URL; ?>/view/index.php">
                        <i class="bi bi-arrow-left me-1"></i>
                        Retour à la page de connexion
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const resetRequestForm = document.getElementById('resetRequestForm');
            const requestForm = document.getElementById('requestForm');
            const emailSent = document.getElementById('emailSent');
            const errorAlert = document.getElementById('errorAlert');
            const errorMessage = document.getElementById('errorMessage');
            const maskedEmail = document.getElementById('maskedEmail');
            const continueBtn = document.getElementById('continueBtn');

            resetRequestForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const identifier = document.getElementById('identifier').value.trim();

                if (!identifier) {
                    showError('Veuillez entrer votre CNI ou CNE');
                    return;
                }

                // Désactiver le bouton pendant la requête
                const submitBtn = resetRequestForm.querySelector('button[type="submit"]');
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Envoi en cours...';

                // Masquer les erreurs précédentes
                errorAlert.style.display = 'none';

                // Envoyer la requête
                fetch('<?php echo BASE_URL; ?>/route/reset-password-index.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'request',
                        identifier: identifier
                    })
                })
                .then(response => response.json())
                .then(data => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="bi bi-envelope me-2"></i>Envoyer le code';

                    if (data.success) {
                        // Stocker l'identifiant dans sessionStorage
                        sessionStorage.setItem('resetIdentifier', data.identifier);

                        // Afficher l'email masqué
                        maskedEmail.textContent = data.email;

                        // Afficher le message de succès
                        requestForm.style.display = 'none';
                        emailSent.style.display = 'block';

                        // Vérifier s'il y a un code de débogage
                        if (data.debug_code) {
                            const debugCode = document.getElementById('debugCode');
                            const debugCodeValue = document.getElementById('debugCodeValue');
                            debugCodeValue.textContent = data.debug_code;
                            debugCode.style.display = 'block';
                        } else {
                            // Vérifier si nous sommes en environnement de développement
                            const isLocalhost = window.location.hostname === 'localhost' ||
                                               window.location.hostname === '127.0.0.1';
                            if (isLocalhost) {
                                // Faire une requête pour récupérer le code de débogage
                                fetch('<?php echo BASE_URL; ?>/route/reset-password-index.php', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json'
                                    },
                                    body: JSON.stringify({
                                        action: 'get_debug_code'
                                    })
                                })
                                .then(response => response.json())
                                .then(debugData => {
                                    if (debugData.debug_code) {
                                        const debugCode = document.getElementById('debugCode');
                                        const debugCodeValue = document.getElementById('debugCodeValue');
                                        debugCodeValue.textContent = debugData.debug_code;
                                        debugCode.style.display = 'block';
                                    }
                                })
                                .catch(error => {
                                    console.error('Error fetching debug code:', error);
                                });
                            }
                        }

                        // Mettre à jour le lien de continuation
                        continueBtn.href = 'verify-code.php';
                    } else {
                        showError(data.error);
                    }
                })
                .catch(error => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="bi bi-envelope me-2"></i>Envoyer le code';
                    showError('Une erreur est survenue. Veuillez réessayer plus tard.');
                    console.error('Error:', error);
                });
            });

            function showError(message) {
                errorMessage.textContent = message;
                errorAlert.style.display = 'block';
            }
        });
    </script>
</body>
</html>
