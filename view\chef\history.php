<?php
/**
 * Vue pour la page d'historique du chef de département
 *
 * Cette page affiche l'historique complet des activités de gestion du département
 * à travers les années universitaires.
 */

// Inclure la vérification d'authentification pour les chefs de département
require_once '../includes/auth_check_chef.php';

// Récupérer l'ID du chef de département depuis la session
$chefId = $_SESSION['user']['teacher_id'] ?? null;

// Récupérer le nom complet du chef de département
$chefFullName = ($_SESSION['user']['prenom'] ?? '') . ' ' . ($_SESSION['user']['nom'] ?? '');
$departmentName = $_SESSION['user']['department_name'] ?? 'Non spécifié';

// Titre de la page
$pageTitle = "Historique du Département";

// Inclure le modèle des visites et enregistrer la visite
require_once '../../model/visitsModel.php';
recordVisit('chef de departement', 'history');
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/dashboard-style.css" rel="stylesheet">
    <link href="../assets/css/header-fix.css" rel="stylesheet">

    <style>
        /* Enhanced styles for chef history page with consistent design patterns */
        :root {
            --primary-color: #6c9bcf;
            --secondary-color: #f8f9fa;
            --accent-color: #a5d8ff;
            --text-color: #495057;
            --light-accent: #e9f2ff;
            --border-color: #dee2e6;
            --pastel-blue: #cfeaf4;
            --pastel-purple: #e8c4fd;
            --pastel-orange: #ffd4a3;
            --pastel-yellow: #fff2cc;
            --pastel-teal: #a8e6cf;
            --pastel-green: #c8e6c9;
            --pastel-pink: #f8bbd9;
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --hover-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        body {
            color: var(--text-color);
            background-color: #f5f7fa;
            overflow-x: hidden;
            max-width: 100%;
        }

        .year-filter-container {
            background: linear-gradient(135deg, var(--pastel-blue) 0%, var(--pastel-purple) 100%);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        #yearFilter {
            background: white;
            border: 2px solid transparent;
            border-radius: 12px;
            color: #333;
            padding: 12px 16px;
            transition: all 0.3s ease;
            font-weight: 500;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        #yearFilter:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }

        #yearFilter:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }

        .form-label {
            color: #333;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .history-stats-card {
            background: linear-gradient(135deg, #cfeaf4, #e8c4fd 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 10px 0;
            text-align: center;
            transition: transform 0.3s ease;
            border: none;
            box-shadow: var(--card-shadow);
        }

        .history-stats-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--hover-shadow);
        }

        .history-stats-card h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .history-stats-card p {
            color: #555;
            font-weight: 500;
            margin: 0;
        }

        .history-stats-card i {
            color: #667eea;
            margin-bottom: 15px;
        }

        .assignment-card {
            background: linear-gradient(135deg, var(--pastel-orange) 0%, var(--pastel-yellow) 100%);
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #ff8c42;
            transition: all 0.3s ease;
            box-shadow: var(--card-shadow);
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .assignment-card:hover {
            transform: translateX(8px);
            box-shadow: var(--hover-shadow);
        }

        .workload-item {
            background: linear-gradient(135deg, var(--pastel-teal) 0%, var(--pastel-green) 100%);
            border-radius: 12px;
            padding: 18px;
            margin: 12px 0;
            border-left: 4px solid #4caf50;
            transition: all 0.3s ease;
            box-shadow: var(--card-shadow);
        }

        .workload-item:hover {
            transform: translateX(5px);
            box-shadow: var(--hover-shadow);
        }

        .observation-item {
            background: linear-gradient(135deg, rgba(248, 187, 208, 0.3) 0%, rgba(181, 126, 220, 0.3) 100%);
            border-radius: 8px;
            padding: 10px 12px;
            margin: 6px 0;
            transition: all 0.3s ease;
            box-shadow: 0 1px 4px rgba(0,0,0,0.08);
            font-size: 0.85rem;
            border: 1px solid rgba(248, 187, 208, 0.2);
        }

        .observation-item:hover {
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.12);
            background: linear-gradient(135deg, rgb(125 122 255 / 30%) 0%, rgb(255 168 250 / 30%) 100%);
        }

        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }

        .no-data {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .no-data i {
            color: var(--pastel-purple);
        }

        .download-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .timeline-item {
            position: relative;
            padding-left: 35px;
            margin-bottom: 25px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: 12px;
            top: 8px;
            width: 12px;
            height: 12px;
            background: #667eea;
            border-radius: 50%;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }

        .timeline-item::after {
            content: '';
            position: absolute;
            left: 17px;
            top: 20px;
            width: 2px;
            height: calc(100% + 5px);
            background: linear-gradient(to bottom, #667eea, var(--pastel-blue));
        }

        .timeline-item:last-child::after {
            display: none;
        }

        /* Chart container styles */
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(0,0,0,0.05);
        }

        .chart-title {
            color: #333;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
        }

        /* Perfect alignment for side-by-side cards */
        .no-gap-row {
            margin-left: 0;
            margin-right: 0;
        }

        .no-gap-col {
            padding-left: 0;
            padding-right: 0;
        }

        .no-gap-col:first-child .perfect-align-card {
            margin-right: 10px;
        }

        .no-gap-col:last-child .perfect-align-card {
            margin-left: 10px;
        }

        .perfect-align-card {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .perfect-align-card .card-body {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .perfect-align-card .chart-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .no-gap-col:first-child .perfect-align-card {
                margin-right: 0;
                margin-bottom: 20px;
            }

            .no-gap-col:last-child .perfect-align-card {
                margin-left: 0;
            }

            .history-stats-card {
                margin: 8px 0;
                padding: 20px;
            }

            .timeline-item {
                padding-left: 25px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4">
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="page-title">
                            <i class="fas fa-history me-3"></i>
                            Historique du Département
                        </h1>
                        <p class="text-muted mb-0">
                            <strong><?php echo htmlspecialchars($chefFullName); ?></strong> -
                            Chef du Département <?php echo htmlspecialchars($departmentName); ?>
                        </p>
                    </div>
                    <div>
                        <button class="btn download-btn" id="downloadFullReport">
                            <i class="fas fa-download me-2"></i>
                            Rapport Complet
                        </button>
                    </div>
                </div>

                <!-- Academic Year Filter -->
                <div class="year-filter-container">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <label for="yearFilter" class="form-label">
                                <i class="fas fa-calendar-alt me-2"></i>
                                Année Universitaire
                            </label>
                            <select class="form-select" id="yearFilter">
                                <option value="">Chargement...</option>
                            </select>
                        </div>
                        <div class="col-md-9">
                            <div class="d-flex align-items-center justify-content-end">
                                <div class="text-end">
                                    <h6 class="mb-1 text-dark">Gestion Départementale</h6>
                                    <small class="text-muted">Suivi des activités et performances</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loading Spinner -->
                <div id="loadingSpinner" class="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>

                <!-- Main Content -->
                <div id="historyContent" style="display: none;">
                    <!-- Statistics Overview -->
                    <div class="row mb-4" id="statisticsSection">
                        <div class="col-md-3">
                            <div class="history-stats-card">
                                <i class="fas fa-users fa-2x"></i>
                                <h3 id="totalProfessors">0</h3>
                                <p>Professeurs</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="history-stats-card">
                                <i class="fas fa-book fa-2x"></i>
                                <h3 id="totalModules">0</h3>
                                <p>Modules Gérés</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="history-stats-card">
                                <i class="fas fa-tasks fa-2x"></i>
                                <h3 id="assignmentsMade">0</h3>
                                <p>Affectations</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="history-stats-card">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                                <h3 id="vacantModules">0</h3>
                                <p>Modules Vacants</p>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Section -->
                    <div class="row no-gap-row mb-4">
                        <div class="col-lg-6 no-gap-col">
                            <div class="card perfect-align-card">
                                <div class="card-body">
                                    <h5 class="card-title chart-title">
                                        <i class="fas fa-chart-pie me-2"></i>
                                        Répartition de la Charge de Travail
                                    </h5>
                                    <div class="chart-container">
                                        <canvas id="workloadChart" width="400" height="300"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 no-gap-col">
                            <div class="card perfect-align-card">
                                <div class="card-body">
                                    <h5 class="card-title chart-title">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        Statut des Modules
                                    </h5>
                                    <div class="chart-container">
                                        <canvas id="moduleStatusChart" width="400" height="300"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Sections -->
                    <div class="row">
                        <!-- Professor Assignments -->
                        <div class="col-lg-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-user-tie me-2"></i>
                                        Affectations des Professeurs
                                    </h5>
                                    <small class="text-muted">Professeurs assignés aux modules pour cette année académique</small>
                                </div>
                                <div class="card-body" style="min-height: 400px; max-height: 400px; overflow-y: auto;">
                                    <div id="assignmentsList">
                                        <div class="no-data">
                                            <i class="fas fa-clipboard-list fa-3x mb-3"></i>
                                            <p>Aucune affectation trouvée pour cette année</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Module Choice History -->
                        <div class="col-lg-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-history me-2"></i>
                                        Historique des choix de modules
                                    </h5>
                                    <small class="text-muted">Ce que chaque enseignant a choisi chaque année - Validé ou refusé par le chef</small>
                                </div>
                                <div class="card-body" style="min-height: 400px; max-height: 400px; overflow-y: auto;">
                                    <div id="moduleChoicesHistory">
                                        <div class="no-data">
                                            <i class="fas fa-clipboard-list fa-3x mb-3"></i>
                                            <p>Aucun historique de choix trouvé pour cette année</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Workload Distribution - Full Width -->
                    <div class="row">
                        <div class="col-12 mb-4">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-clock me-2"></i>
                                        Charge de Travail des Professeurs
                                    </h5>
                                </div>
                                <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                                    <div id="workloadList">
                                        <div class="no-data">
                                            <i class="fas fa-hourglass-half fa-3x mb-3"></i>
                                            <p>Aucune donnée de charge trouvée</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Module Status -->
                    <div class="row">
                        <div class="col-12 mb-4">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-list-alt me-2"></i>
                                        État des Modules du Département
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover" id="modulesTable">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>Module</th>
                                                    <th>Filière</th>
                                                    <th>Niveau</th>
                                                    <th>Volume Horaire</th>
                                                    <th>Professeur Assigné</th>
                                                    <th>Statut</th>
                                                    <th>Date d'Affectation</th>
                                                </tr>
                                            </thead>
                                            <tbody id="modulesTableBody">
                                                <tr>
                                                    <td colspan="7" class="text-center">
                                                        <i class="fas fa-spinner fa-spin me-2"></i>
                                                        Chargement des modules...
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- No Data Message -->
                <div id="noDataMessage" class="no-data" style="display: none;">
                    <i class="fas fa-database fa-4x mb-4"></i>
                    <h4>Aucune donnée disponible</h4>
                    <p>Aucune donnée historique trouvée pour l'année sélectionnée.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jsPDF for PDF generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <script>
        // Global variables
        let currentChefId = <?php echo json_encode($chefId); ?>;
        let currentAcademicYear = '';
        let historyData = {};
        let workloadChart = null;
        let moduleStatusChart = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadAcademicYears();
            setupEventListeners();
        });

        // Setup event listeners
        function setupEventListeners() {
            document.getElementById('yearFilter').addEventListener('change', function() {
                currentAcademicYear = this.value;
                if (currentAcademicYear) {
                    loadHistoryData();
                }
            });

            document.getElementById('downloadFullReport').addEventListener('click', function() {
                if (currentAcademicYear) {
                    generatePDFReport();
                } else {
                    alert('Veuillez sélectionner une année universitaire');
                }
            });
        }

        // Load academic years
        function loadAcademicYears() {
            console.log('Loading academic years for chef ID:', currentChefId);

            // Use jQuery AJAX like other pages in the application
            $.ajax({
                url: '../../route/chefHistoryRoute.php',
                method: 'GET',
                data: {
                    action: 'getAcademicYears',
                    chef_id: currentChefId
                },
                dataType: 'json',
                success: function(response) {
                    console.log('Academic years response:', response);
                    if (response.success && response.data) {
                        populateYearFilter(response.data);
                    } else {
                        console.error('Error in response:', response.error || 'Unknown error');
                        // Fallback to current year if no data
                        const currentYear = new Date().getFullYear();
                        const fallbackYears = [`${currentYear}/${currentYear + 1}`];
                        populateYearFilter(fallbackYears);
                        showError('Impossible de charger les années. Utilisation de l\'année actuelle.');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX error loading academic years:', status, error);
                    console.error('Response text:', xhr.responseText);

                    // Fallback to current year
                    const currentYear = new Date().getFullYear();
                    const fallbackYears = [`${currentYear}/${currentYear + 1}`];
                    populateYearFilter(fallbackYears);
                    showError('Erreur de connexion. Utilisation de l\'année actuelle.');
                }
            });
        }

        // Populate year filter dropdown
        function populateYearFilter(years) {
            const yearFilter = document.getElementById('yearFilter');
            yearFilter.innerHTML = '<option value="">Sélectionner une année</option>';

            years.forEach(year => {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year;
                yearFilter.appendChild(option);
            });

            // Auto-select current year if available
            if (years.length > 0) {
                yearFilter.value = years[0];
                currentAcademicYear = years[0];
                loadHistoryData();
            }
        }

        // Load complete history data
        function loadHistoryData() {
            showLoading();

            console.log('Loading history data for year:', currentAcademicYear);

            // Use jQuery AJAX like other pages in the application
            $.ajax({
                url: '../../route/chefHistoryRoute.php',
                method: 'GET',
                data: {
                    action: 'getCompleteHistory',
                    chef_id: currentChefId,
                    academic_year: currentAcademicYear
                },
                dataType: 'json',
                success: function(response) {
                    console.log('History data response:', response);
                    hideLoading();

                    if (response.success && response.data) {
                        historyData = response.data;
                        displayHistoryData();
                    } else {
                        console.error('Error in response:', response.error || 'Unknown error');
                        showError('Erreur lors du chargement des données historiques: ' + (response.error || 'Erreur inconnue'));
                        showNoData();
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX error loading history data:', status, error);
                    console.error('Response text:', xhr.responseText);
                    hideLoading();

                    showError('Erreur de connexion lors du chargement des données historiques.');
                    showNoData();
                }
            });
        }

        // Display all history data
        function displayHistoryData() {
            if (!historyData || Object.keys(historyData).length === 0) {
                showNoData();
                return;
            }

            // Update statistics
            updateStatistics();

            // Update lists
            updateAssignmentsList();
            updateModuleChoicesHistory();
            updateWorkloadList();
            updateModulesTable();

            // Update charts
            updateCharts();

            // Show content
            document.getElementById('historyContent').style.display = 'block';
            document.getElementById('noDataMessage').style.display = 'none';
        }

        // Update statistics cards
        function updateStatistics() {
            const stats = historyData.statistics || {};

            document.getElementById('totalProfessors').textContent = stats.total_professors || 0;
            document.getElementById('totalModules').textContent = stats.total_modules || 0;
            document.getElementById('assignmentsMade').textContent = stats.assignments_made || 0;
            document.getElementById('vacantModules').textContent = stats.vacant_modules || 0;
        }

        // Update assignments list
        function updateAssignmentsList() {
            const assignments = historyData.assignments || [];
            const container = document.getElementById('assignmentsList');

            if (assignments.length === 0) {
                container.innerHTML = `
                    <div class="no-data">
                        <i class="fas fa-clipboard-list fa-3x mb-3"></i>
                        <p>Aucune affectation trouvée pour cette année</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = assignments.map(assignment => `
                <div class="assignment-card">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">
                                <i class="fas fa-user me-2"></i>
                                ${assignment.prenom} ${assignment.nom}
                            </h6>
                            <p class="mb-1">
                                <i class="fas fa-book me-2"></i>
                                <strong>${assignment.nom_ue}</strong> - ${assignment.nom_module}
                            </p>
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                ${formatDate(assignment.date_affectation)}
                            </small>
                        </div>
                        <span class="badge bg-success">Assigné</span>
                    </div>
                    ${assignment.commentaire ? `<p class="mt-2 mb-0 text-muted"><em>${assignment.commentaire}</em></p>` : ''}
                </div>
            `).join('');
        }

        // Update module choices history
        function updateModuleChoicesHistory() {
            const moduleChoices = historyData.module_choices_history || [];
            const container = document.getElementById('moduleChoicesHistory');

            if (moduleChoices.length === 0) {
                container.innerHTML = `
                    <div class="no-data">
                        <i class="fas fa-clipboard-list fa-3x mb-3"></i>
                        <p>Aucun historique de choix trouvé pour cette année</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = moduleChoices.map(choice => {
                const statusClass = getModuleChoiceStatusClass(choice.statut);
                const statusIcon = getModuleChoiceStatusIcon(choice.statut);

                return `
                    <div class="observation-item">
                        <div class="d-flex justify-content-between align-items-start mb-1">
                            <strong>${choice.enseignant_prenom} ${choice.enseignant_nom}</strong>
                            <span class="badge ${statusClass}">
                                <i class="${statusIcon} me-1"></i>
                                ${getModuleChoiceStatusText(choice.statut)}
                            </span>
                        </div>
                        <p class="mb-1 small">
                            <i class="fas fa-book me-2"></i>
                            <strong>${choice.ue_type}</strong> - ${choice.module_name}
                        </p>
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            Demandé: ${formatDate(choice.date_demande)}
                        </small>
                    </div>
                `;
            }).join('');
        }

        // Update workload list
        function updateWorkloadList() {
            const workload = historyData.workload || [];
            const container = document.getElementById('workloadList');

            if (workload.length === 0) {
                container.innerHTML = `
                    <div class="no-data">
                        <i class="fas fa-hourglass-half fa-3x mb-3"></i>
                        <p>Aucune donnée de charge trouvée</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = workload.map(prof => `
                <div class="workload-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">
                                <i class="fas fa-user me-2"></i>
                                ${prof.prenom} ${prof.nom}
                            </h6>
                            <small class="text-muted">${prof.role}</small>
                        </div>
                        <div class="text-end">
                            <strong>${prof.total_hours || 0}h</strong>
                            <br>
                            <small class="text-muted">${prof.total_assignments || 0} modules</small>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            Cours: ${prof.cours_hours || 0}h |
                            TD: ${prof.td_hours || 0}h |
                            TP: ${prof.tp_hours || 0}h
                        </small>
                    </div>
                </div>
            `).join('');
        }



        // Update modules table
        function updateModulesTable() {
            const modules = historyData.modules || [];
            const tbody = document.getElementById('modulesTableBody');

            if (modules.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center">
                            <i class="fas fa-inbox fa-2x mb-2"></i>
                            <br>Aucun module trouvé pour cette année
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = modules.map(module => `
                <tr>
                    <td>
                        <strong>${module.nom_ue}</strong>
                        <br>
                        <small class="text-muted">${module.nom_module}</small>
                    </td>
                    <td>${module.nom_filiere}</td>
                    <td>${module.niveau_nom}</td>
                    <td>
                        <small>
                            C: ${module.volume_horaire_cours || 0}h<br>
                            TD: ${module.volume_horaire_td || 0}h<br>
                            TP: ${module.volume_horaire_tp || 0}h
                        </small>
                    </td>
                    <td>
                        ${module.prof_nom ? `${module.prof_prenom} ${module.prof_nom}${module.prof_role === 'vacataire' ? ' <small class="text-muted">(Vacataire)</small>' : ''}` : '-'}
                    </td>
                    <td>
                        <span class="badge ${getModuleStatusBadgeClass(module.statut)}">${module.statut}</span>
                    </td>
                    <td>
                        ${module.date_affectation ? formatDate(module.date_affectation) : '-'}
                    </td>
                </tr>
            `).join('');
        }

        // Update charts
        function updateCharts() {
            updateWorkloadChart();
            updateModuleStatusChart();
        }

        // Update workload chart
        function updateWorkloadChart() {
            const workload = historyData.workload || [];
            const ctx = document.getElementById('workloadChart').getContext('2d');

            // Destroy existing chart
            if (workloadChart) {
                workloadChart.destroy();
            }

            if (workload.length === 0) {
                ctx.font = '16px Arial';
                ctx.fillStyle = '#666';
                ctx.textAlign = 'center';
                ctx.fillText('Aucune donnée disponible', ctx.canvas.width / 2, ctx.canvas.height / 2);
                return;
            }

            const labels = workload.map(prof => `${prof.prenom} ${prof.nom}`);
            const data = workload.map(prof => prof.total_hours || 0);

            workloadChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Heures de travail',
                        data: data,
                        backgroundColor: [
                            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                            '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                        ],
                        borderColor: [
                            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                            '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed + 'h';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 10
                            }
                        },
                        x: {
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45
                            }
                        }
                    }
                }
            });
        }

        // Update module status chart
        function updateModuleStatusChart() {
            const modules = historyData.modules || [];
            const ctx = document.getElementById('moduleStatusChart').getContext('2d');

            // Destroy existing chart
            if (moduleStatusChart) {
                moduleStatusChart.destroy();
            }

            if (modules.length === 0) {
                ctx.font = '16px Arial';
                ctx.fillStyle = '#666';
                ctx.textAlign = 'center';
                ctx.fillText('Aucune donnée disponible', ctx.canvas.width / 2, ctx.canvas.height / 2);
                return;
            }

            // Count modules by status
            const statusCounts = modules.reduce((acc, module) => {
                acc[module.statut] = (acc[module.statut] || 0) + 1;
                return acc;
            }, {});

            const labels = Object.keys(statusCounts);
            const data = Object.values(statusCounts);

            moduleStatusChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: [
                            '#28a745', '#dc3545', '#ffc107', '#17a2b8', '#6c757d'
                        ],
                        borderColor: '#fff',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed + ' modules';
                                }
                            }
                        }
                    }
                }
            });
        }

        // Utility functions
        function formatDate(dateString) {
            if (!dateString) return '-';

            // Check if it's an academic year format (e.g., "2024/2025" or "2024-2025")
            if (dateString.match(/^\d{4}[\/\-]\d{4}$/)) {
                return dateString.replace('-', '/'); // Normalize to slash format
            }

            // Try to parse as a regular date
            const date = new Date(dateString);
            if (isNaN(date.getTime())) {
                return dateString; // Return original string if not a valid date
            }

            return date.toLocaleDateString('fr-FR');
        }

        function getStatusBadgeClass(status) {
            switch (status?.toLowerCase()) {
                case 'accepté':
                case 'validé':
                    return 'bg-success';
                case 'en attente':
                    return 'bg-warning';
                case 'refusé':
                    return 'bg-danger';
                default:
                    return 'bg-secondary';
            }
        }

        function getGravityBadgeClass(gravity) {
            switch (gravity?.toLowerCase()) {
                case 'faible':
                    return 'bg-info';
                case 'moyen':
                    return 'bg-warning';
                case 'élevé':
                    return 'bg-danger';
                default:
                    return 'bg-secondary';
            }
        }

        function getModuleChoiceStatusClass(status) {
            switch (status?.toLowerCase()) {
                case 'acceptee':
                case 'accepté':
                case 'accepte':
                case 'validé':
                case 'valide':
                    return 'bg-success';
                case 'en_attente':
                case 'en attente':
                case 'pending':
                    return 'bg-warning text-dark';
                case 'rejetee':
                case 'refusé':
                case 'refuse':
                case 'rejeté':
                case 'rejete':
                    return 'bg-danger';
                default:
                    return 'bg-secondary';
            }
        }

        function getModuleChoiceStatusIcon(status) {
            switch (status?.toLowerCase()) {
                case 'acceptee':
                case 'accepté':
                case 'accepte':
                case 'validé':
                case 'valide':
                    return 'fas fa-check-circle';
                case 'en_attente':
                case 'en attente':
                case 'pending':
                    return 'fas fa-clock';
                case 'rejetee':
                case 'refusé':
                case 'refuse':
                case 'rejeté':
                case 'rejete':
                    return 'fas fa-times-circle';
                default:
                    return 'fas fa-question-circle';
            }
        }

        function getModuleChoiceStatusText(status) {
            switch (status?.toLowerCase()) {
                case 'acceptee':
                    return 'Acceptée';
                case 'en_attente':
                    return 'En attente';
                case 'rejetee':
                    return 'Rejetée';
                default:
                    return status || 'Inconnu';
            }
        }

        function getModuleStatusBadgeClass(status) {
            switch (status?.toLowerCase()) {
                case 'assigné':
                    return 'bg-success';
                case 'vacant':
                    return 'bg-danger';
                case 'non assigné':
                    return 'bg-warning';
                default:
                    return 'bg-secondary';
            }
        }

        function showLoading() {
            document.getElementById('loadingSpinner').style.display = 'flex';
            document.getElementById('historyContent').style.display = 'none';
            document.getElementById('noDataMessage').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loadingSpinner').style.display = 'none';
        }

        function showNoData() {
            document.getElementById('historyContent').style.display = 'none';
            document.getElementById('noDataMessage').style.display = 'block';
            document.getElementById('loadingSpinner').style.display = 'none';
        }

        function showError(message) {
            alert('Erreur: ' + message);
        }

        // PDF Report Generation - Professional Design (Based on Teacher History)
        function generatePDFReport() {
            if (!historyData || Object.keys(historyData).length === 0) {
                alert('Aucune donnée à exporter');
                return;
            }

            // Create a new window for the PDF content
            const printWindow = window.open('', '_blank');

            // Helper function to format dates
            function formatDate(dateString) {
                if (!dateString) return 'N/A';
                const date = new Date(dateString);
                return date.toLocaleDateString('fr-FR');
            }

            // Helper function to get module choice status text
            function getModuleChoiceStatusText(status) {
                switch(status) {
                    case 'approved': return 'Approuvé';
                    case 'rejected': return 'Refusé';
                    case 'pending': return 'En attente';
                    default: return status;
                }
            }

            const htmlContent = `
                <!DOCTYPE html>
                <html lang="fr">
                <head>
                    <meta charset="UTF-8">
                    <title>Rapport de Gestion Départementale - <?php echo addslashes($chefFullName); ?> - ${currentAcademicYear}</title>
                    <style>
                        body {
                            font-family: 'Arial', 'Helvetica', sans-serif;
                            margin: 0;
                            padding: 15mm;
                            color: #2c3e50;
                            line-height: 1.4;
                            background: #fff;
                            font-size: 12px;
                        }

                        /* Professional Header */
                        .header {
                            border-bottom: 3px solid #2c3e50;
                            padding-bottom: 15px;
                            margin-bottom: 20px;
                            display: flex;
                            justify-content: space-between;
                            align-items: flex-start;
                        }
                        .header-left {
                            flex: 1;
                        }
                        .header-right {
                            text-align: right;
                            flex: 1;
                        }
                        .institution-name {
                            font-size: 16px;
                            font-weight: bold;
                            color: #2c3e50;
                            margin: 0;
                            text-transform: uppercase;
                            letter-spacing: 0.5px;
                        }
                        .institution-subtitle {
                            font-size: 11px;
                            color: #7f8c8d;
                            margin: 2px 0 0 0;
                        }
                        .report-title {
                            font-size: 18px;
                            font-weight: bold;
                            color: #2c3e50;
                            margin: 0;
                            text-transform: uppercase;
                        }
                        .report-meta {
                            font-size: 11px;
                            color: #7f8c8d;
                            margin: 5px 0 0 0;
                        }

                        /* Department Info Box */
                        .department-info {
                            background: #ecf0f1;
                            border: 1px solid #bdc3c7;
                            padding: 12px;
                            margin-bottom: 20px;
                            border-radius: 3px;
                        }
                        .department-info table {
                            width: 100%;
                            border-collapse: collapse;
                        }
                        .department-info td {
                            padding: 3px 8px;
                            font-size: 11px;
                        }
                        .department-info .label {
                            font-weight: bold;
                            color: #2c3e50;
                            width: 120px;
                        }
                        .department-info .value {
                            color: #34495e;
                        }

                        /* Professional Section Styles */
                        .section {
                            margin-bottom: 25px;
                            page-break-inside: avoid;
                        }
                        .section-title {
                            font-size: 14px;
                            font-weight: bold;
                            color: #2c3e50;
                            margin: 0 0 12px 0;
                            text-transform: uppercase;
                            letter-spacing: 0.5px;
                            border-bottom: 2px solid #34495e;
                            padding-bottom: 5px;
                        }

                        /* Professional Table Styles */
                        .data-table {
                            width: 100%;
                            border-collapse: collapse;
                            margin: 10px 0;
                            font-size: 11px;
                            border: 1px solid #bdc3c7;
                        }
                        .data-table th {
                            background: #34495e;
                            color: white;
                            padding: 8px 6px;
                            text-align: left;
                            font-weight: bold;
                            font-size: 10px;
                            text-transform: uppercase;
                            letter-spacing: 0.3px;
                        }
                        .data-table td {
                            padding: 6px;
                            border-bottom: 1px solid #ecf0f1;
                            vertical-align: top;
                        }
                        .data-table tr:nth-child(even) {
                            background: #f8f9fa;
                        }
                        .data-table tr:hover {
                            background: #e8f4f8;
                        }

                        /* Status Badges */
                        .status-badge {
                            display: inline-block;
                            padding: 2px 6px;
                            border-radius: 3px;
                            font-size: 9px;
                            font-weight: bold;
                            text-transform: uppercase;
                        }
                        .badge-assigne { background: #d5f4e6; color: #27ae60; }
                        .badge-vacant { background: #fdeaea; color: #e74c3c; }
                        .badge-non-assigne { background: #fff3cd; color: #856404; }
                        .badge-approved { background: #d5f4e6; color: #27ae60; }
                        .badge-rejected { background: #fdeaea; color: #e74c3c; }
                        .badge-pending { background: #fff3cd; color: #856404; }

                        /* Professional Footer */
                        .footer {
                            margin-top: 30px;
                            padding-top: 15px;
                            border-top: 2px solid #bdc3c7;
                            font-size: 10px;
                            color: #7f8c8d;
                            text-align: center;
                        }
                        .footer-content {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                        }
                        .footer-left, .footer-right {
                            flex: 1;
                        }
                        .footer-center {
                            flex: 2;
                            text-align: center;
                        }
                        .footer-right {
                            text-align: right;
                        }

                        /* Print Optimization */
                        @media print {
                            body {
                                padding: 10mm;
                                font-size: 11px;
                                line-height: 1.3;
                            }
                            .section {
                                page-break-inside: avoid;
                                margin-bottom: 15px;
                            }
                            .data-table {
                                font-size: 10px;
                            }
                            .data-table th {
                                padding: 5px 4px;
                            }
                            .data-table td {
                                padding: 4px;
                            }
                            .footer {
                                margin-top: 20px;
                                font-size: 9px;
                            }
                        }
                    </style>
                </head>
                <body>
                    <!-- Professional Header -->
                    <div class="header">
                        <div class="header-left">
                            <h1 class="institution-name">École Nationale des Sciences Appliquées</h1>
                            <p class="institution-subtitle">Al Hoceima - Université Mohammed Premier</p>
                        </div>
                        <div class="header-right">
                            <h2 class="report-title">Rapport de Gestion Départementale</h2>
                            <p class="report-meta">Année Universitaire ${currentAcademicYear}</p>
                            <p class="report-meta">Généré le ${new Date().toLocaleDateString('fr-FR')}</p>
                        </div>
                    </div>

                    <!-- Department Information -->
                    <div class="department-info">
                        <table>
                            <tr>
                                <td class="label">Chef de Département :</td>
                                <td class="value"><?php echo addslashes($chefFullName); ?></td>
                                <td class="label">Période :</td>
                                <td class="value">${currentAcademicYear}</td>
                            </tr>
                            <tr>
                                <td class="label">Département :</td>
                                <td class="value"><?php echo addslashes($departmentName); ?></td>
                                <td class="label">Type de rapport :</td>
                                <td class="value">Gestion départementale annuelle</td>
                            </tr>
                        </table>
                    </div>

                    <!-- Statistics Summary -->
                    <div class="section">
                        <h3 class="section-title">Résumé Statistique</h3>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Indicateur</th>
                                    <th>Valeur</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>Professeurs</strong></td>
                                    <td>${historyData.statistics?.total_professors || 0}</td>
                                    <td>Nombre total de professeurs dans le département</td>
                                </tr>
                                <tr>
                                    <td><strong>Modules Gérés</strong></td>
                                    <td>${historyData.statistics?.total_modules || 0}</td>
                                    <td>Nombre total de modules sous la responsabilité du département</td>
                                </tr>
                                <tr>
                                    <td><strong>Affectations Réalisées</strong></td>
                                    <td>${historyData.statistics?.assignments_made || 0}</td>
                                    <td>Nombre d'affectations de professeurs aux modules</td>
                                </tr>
                                <tr>
                                    <td><strong>Modules Vacants</strong></td>
                                    <td>${historyData.statistics?.vacant_modules || 0}</td>
                                    <td>Modules nécessitant une affectation d'enseignant</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    ${historyData.assignments && historyData.assignments.length > 0 ? `
                    <!-- Professor Assignments -->
                    <div class="section">
                        <h3 class="section-title">Affectations des Professeurs</h3>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Professeur</th>
                                    <th>Module</th>
                                    <th>Type UE</th>
                                    <th>Filière</th>
                                    <th>Date d'Affectation</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${historyData.assignments.slice(0, 15).map(assignment => `
                                    <tr>
                                        <td><strong>${assignment.prenom} ${assignment.nom}</strong></td>
                                        <td>${assignment.nom_module}</td>
                                        <td>${assignment.nom_ue}</td>
                                        <td>${assignment.nom_filiere || 'N/A'}</td>
                                        <td>${formatDate(assignment.date_affectation)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                    ` : ''}

                    ${historyData.workload && historyData.workload.length > 0 ? `
                    <!-- Workload Distribution -->
                    <div class="section">
                        <h3 class="section-title">Charge de Travail des Professeurs</h3>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Professeur</th>
                                    <th>Rôle</th>
                                    <th>Total Heures</th>
                                    <th>Cours</th>
                                    <th>TD</th>
                                    <th>TP</th>
                                    <th>Modules</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${historyData.workload.slice(0, 15).map(prof => `
                                    <tr>
                                        <td><strong>${prof.prenom} ${prof.nom}</strong></td>
                                        <td>${prof.role}</td>
                                        <td><strong>${prof.total_hours || 0}h</strong></td>
                                        <td>${prof.cours_hours || 0}h</td>
                                        <td>${prof.td_hours || 0}h</td>
                                        <td>${prof.tp_hours || 0}h</td>
                                        <td>${prof.total_assignments || 0}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                    ` : ''}

                    ${historyData.modules && historyData.modules.length > 0 ? `
                    <!-- Module Status Overview -->
                    <div class="section">
                        <h3 class="section-title">État des Modules du Département</h3>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Module</th>
                                    <th>Filière</th>
                                    <th>Niveau</th>
                                    <th>Professeur Assigné</th>
                                    <th>Statut</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${historyData.modules.slice(0, 20).map(module => `
                                    <tr>
                                        <td>
                                            <strong>${module.nom_ue}</strong><br>
                                            <small>${module.nom_module}</small>
                                        </td>
                                        <td>${module.nom_filiere}</td>
                                        <td>${module.niveau_nom}</td>
                                        <td>${module.prof_nom ? `${module.prof_prenom} ${module.prof_nom}${module.prof_role === 'vacataire' ? ' (Vacataire)' : ''}` : '-'}</td>
                                        <td><span class="status-badge badge-${module.statut.toLowerCase().replace(' ', '-')}">${module.statut}</span></td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                    ` : ''}

                    ${historyData.module_choices_history && historyData.module_choices_history.length > 0 ? `
                    <!-- Module Choice History -->
                    <div class="section">
                        <h3 class="section-title">Historique des Choix de Modules</h3>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Enseignant</th>
                                    <th>Module</th>
                                    <th>Type UE</th>
                                    <th>Statut</th>
                                    <th>Date de Demande</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${historyData.module_choices_history.slice(0, 12).map(choice => `
                                    <tr>
                                        <td><strong>${choice.enseignant_prenom} ${choice.enseignant_nom}</strong></td>
                                        <td>${choice.module_name}</td>
                                        <td>${choice.ue_type}</td>
                                        <td><span class="status-badge badge-${choice.statut}">${getModuleChoiceStatusText(choice.statut)}</span></td>
                                        <td>${formatDate(choice.date_demande)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                    ` : ''}

                    <!-- Professional Footer -->
                    <div class="footer">
                        <div class="footer-content">
                            <div class="footer-left">
                                <strong>ENSAH</strong><br>
                                École Nationale des Sciences Appliquées
                            </div>
                            <div class="footer-center">
                                <strong>Rapport de Gestion Départementale</strong><br>
                                <?php echo addslashes($chefFullName); ?> - ${currentAcademicYear}
                            </div>
                            <div class="footer-right">
                                Généré le ${new Date().toLocaleDateString('fr-FR')}<br>
                                Document confidentiel
                            </div>
                        </div>
                    </div>
                </body>
                </html>
            `;

            printWindow.document.write(htmlContent);
            printWindow.document.close();

            // Wait for content to load then print
            printWindow.onload = function() {
                setTimeout(() => {
                    printWindow.print();
                    printWindow.close();
                }, 500);
            };
        }
    </script>
</body>
</html>