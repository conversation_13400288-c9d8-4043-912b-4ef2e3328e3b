<?php
// Vérifier l'authentification
require_once '../includes/auth_check_etudiant.php';

// Récupérer les informations de l'étudiant depuis la session
$userName = $_SESSION['user']['username'] ?? 'Etudiant';
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$filiereName = $_SESSION['user']['filiere_name'] ?? 'Non spécifié';
$niveau = $_SESSION['user']['niveau'] ?? 'Non spécifié';

// Construire le nom complet
$fullName = $prenom . ' ' . $nom;
if (trim($fullName) === '') {
    $fullName = $userName;
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Étudiant - Tableau de <PERSON>rd</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard-style.css">
    <link rel="stylesheet" href="../assets/css/header-fix.css">
</head>
<body>
    <div class="dashboard-container">
        <?php
        include '../includes/sidebar.php';

        // Inclure le modèle des visites et enregistrer la visite du dashboard
        require_once '../../model/visitsModel.php';
        recordVisit('etudiant', 'dashboard');
        ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>
            <div class="container-fluid p-4">
                <h1 class="page-title">Tableau de Bord - Étudiant</h1>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="text-muted">Bienvenue, <strong><?php echo htmlspecialchars($fullName); ?></strong>. Vous êtes étudiant en <strong><?php echo htmlspecialchars($filiereName); ?></strong>, niveau <strong><?php echo htmlspecialchars($niveau); ?></strong>.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <div class="d-inline-block me-2">
                            <span class="badge bg-primary rounded-pill">
                                <?php echo date('d M Y'); ?>
                            </span>
                        </div>
                        <div class="d-inline-block me-2">
                            <span class="badge bg-info rounded-pill">
                                <i class="fas fa-clock me-1"></i> <span id="current-time"></span>
                            </span>
                        </div>
                        <div class="d-inline-block">
                            <span class="badge bg-secondary rounded-pill">
                                <i class="fas fa-user me-1"></i> Étudiant
                            </span>
                        </div>
                    </div>
                </div>

                <div class="row g-4">
                    <!-- Carte des Cours -->
                    <div class="col-md-6 col-lg-4 animate-fade-in delay-1">
                        <a href="courses.php" class="text-decoration-none">
                            <div class="card dashboard-card card-courses h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="card-icon icon-courses">
                                            <i class="fas fa-book"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Mes Cours</h5>
                                            <div class="small text-muted">Modules du semestre</div>
                                        </div>
                                    </div>
                                    <ul class="dashboard-list">
                                        <!-- Données statiques pour les modules de l'étudiant -->
                                        <li><span><i class="fas fa-book-open me-2 text-primary"></i>Programmation Orientée Objet</span></li>
                                        <li><span><i class="fas fa-book-open me-2 text-primary"></i>Bases de Données Avancées</span></li>
                                        <li><span><i class="fas fa-book-open me-2 text-primary"></i>Développement Web</span></li>
                                    </ul>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte des Notes -->
                    <div class="col-md-6 col-lg-4 animate-fade-in delay-2">
                        <a href="grades.php" class="text-decoration-none">
                            <div class="card dashboard-card card-grades h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="card-icon icon-grades">
                                            <i class="fas fa-graduation-cap"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Mes Notes</h5>
                                            <div class="small text-muted">Résultats académiques</div>
                                        </div>
                                    </div>
                                    <div class="text-center py-4">
                                        <p>Consultez vos notes et résultats pour tous les modules.</p>
                                        <button class="btn btn-outline-primary mt-2">Voir mes notes</button>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte des Informations Personnelles -->
                    <div class="col-md-6 col-lg-4 animate-fade-in delay-3">
                        <a href="profile.php" class="text-decoration-none">
                            <div class="card dashboard-card card-profile h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="card-icon icon-profile">
                                            <i class="fas fa-user-circle"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Profil</h5>
                                            <div class="small text-muted">Mes informations</div>
                                        </div>
                                    </div>
                                    <ul class="dashboard-list">
                                        <li>
                                            <span class="fw-bold">Filière:</span>
                                            <span><?php echo htmlspecialchars($filiereName); ?></span>
                                        </li>
                                        <li>
                                            <span class="fw-bold">Niveau:</span>
                                            <span><?php echo htmlspecialchars($niveau); ?></span>
                                        </li>
                                        <li>
                                            <span class="fw-bold">CNE:</span>
                                            <span><?php echo htmlspecialchars($userName); ?></span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Emploi du Temps -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-4">
                        <a href="schedule.php" class="text-decoration-none">
                            <div class="card dashboard-card card-schedule square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-schedule mb-3">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <h5 class="card-title">Emploi du Temps</h5>
                                    <div class="small text-muted">Mon planning</div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Absences -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-5">
                        <a href="absences.php" class="text-decoration-none">
                            <div class="card dashboard-card card-absences square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-absences mb-3">
                                        <i class="fas fa-calendar-times"></i>
                                    </div>
                                    <h5 class="card-title">Absences</h5>
                                    <div class="small text-muted">Suivi des absences</div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Documents -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-6">
                        <a href="documents.php" class="text-decoration-none">
                            <div class="card dashboard-card card-documents square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-documents mb-3">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    <h5 class="card-title">Documents</h5>
                                    <div class="small text-muted">Attestations et relevés</div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Communication -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-7">
                        <a href="messages.php" class="text-decoration-none">
                            <div class="card dashboard-card card-messages square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-messages mb-3">
                                        <i class="fas fa-comments"></i>
                                    </div>
                                    <h5 class="card-title">Messages</h5>
                                    <div class="small text-muted">Communiquer avec les enseignants</div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Dashboard Scripts -->
    <script src="../assets/js/dashboard-dynamic.js"></script>
    <script src="../assets/js/dashboard-notifications.js"></script>
    <!-- Notifications JS -->
    <script src="../assets/js/notifications.js"></script>
    <script src="../assets/js/sidebar.js"></script>
</body>
</html>
