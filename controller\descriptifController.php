<?php
require_once "../model/descriptifModel.php";
require_once "../utils/response.php";

/**
 * API endpoint to get all teaching units
 */
function getAllUniteEnseignementAPI() {
    $unites = getAllUniteEnseignement();

    if (isset($unites['error'])) {
        jsonResponse(['error' => $unites['error']], 404);
    }

    jsonResponse(['data' => $unites], 200);
}

/**
 * API endpoint to get teaching units by module ID
 *
 * @param int $moduleId Module ID
 */
function getUniteEnseignementByModuleAPI($moduleId) {
    // Validate module ID
    if (!is_numeric($moduleId)) {
        error_log("Invalid module ID in getUniteEnseignementByModuleAPI: " . $moduleId);
        jsonResponse(['error' => 'Invalid module ID'], 400);
        return;
    }

    try {
        $unites = getUniteEnseignementByModule($moduleId);

        if (isset($unites['error'])) {
            // If the error is "Module not found", return 404
            if (strpos($unites['error'], 'Module not found') !== false) {
                jsonResponse(['error' => $unites['error']], 404);
            } else {
                // For other errors, return 500
                jsonResponse(['error' => $unites['error']], 500);
            }
            return;
        }

        jsonResponse(['data' => $unites], 200);
    } catch (Exception $e) {
        error_log("Exception in getUniteEnseignementByModuleAPI: " . $e->getMessage());
        jsonResponse(['error' => 'Server error: ' . $e->getMessage()], 500);
    }
}

/**
 * API endpoint to get teaching units by filiere ID
 *
 * @param int $filiereId Filiere ID
 */
function getUniteEnseignementByFiliereAPI($filiereId) {
    $unites = getUniteEnseignementByFiliere($filiereId);

    if (isset($unites['error'])) {
        jsonResponse(['error' => $unites['error']], 404);
    }

    jsonResponse(['data' => $unites], 200);
}

/**
 * API endpoint to get a teaching unit by ID
 *
 * @param int $id Teaching unit ID
 */
function getUniteEnseignementByIdAPI($id) {
    $unite = getUniteEnseignementById($id);

    if (!$unite || isset($unite['error'])) {
        jsonResponse(['error' => 'Teaching unit not found'], 404);
    }

    jsonResponse(['data' => $unite], 200);
}

/**
 * API endpoint to create a new teaching unit
 */
function createUniteEnseignementAPI() {
    // Get JSON data from request body
    $data = json_decode(file_get_contents('php://input'), true);

    if (!$data) {
        jsonResponse(['error' => 'Invalid JSON data'], 400);
    }

    // Validate required fields
    $requiredFields = ['module_id', 'type', 'volume_horaire'];
    $missingFields = [];

    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            $missingFields[] = $field;
        }
    }

    if (!empty($missingFields)) {
        jsonResponse(['error' => 'Missing required fields: ' . implode(', ', $missingFields)], 400);
    }

    // For coordinators, validate that the module belongs to their assigned filiere
    if (isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'coordinateur') {
        // Get the module to check its filiere_id
        $module = getModuleById($data['module_id']);

        if (!$module || isset($module['error'])) {
            jsonResponse(['error' => 'Module not found'], 404);
        }

        // Check if the module belongs to the coordinator's filiere
        if (isset($_SESSION['user']['filiere_id'])) {
            if ($module['filiere_id'] != $_SESSION['user']['filiere_id']) {
                jsonResponse(['error' => 'You can only add teaching units to modules from your assigned department'], 403);
            }
        }
    }

    // Always set nb_groupes to 1 as we're removing this functionality
    $data['nb_groupes'] = 1;

    // Create the teaching unit
    $result = createUniteEnseignement($data);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 500);
    }

    jsonResponse(['success' => true, 'id' => $result['id']], 201);
}

/**
 * API endpoint to update a teaching unit
 *
 * @param int $id Teaching unit ID
 */
function updateUniteEnseignementAPI($id) {
    // Get JSON data from request body
    $data = json_decode(file_get_contents('php://input'), true);

    if (!$data) {
        jsonResponse(['error' => 'Invalid JSON data'], 400);
    }

    // Validate required fields
    $requiredFields = ['module_id', 'type', 'volume_horaire'];
    $missingFields = [];

    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            $missingFields[] = $field;
        }
    }

    if (!empty($missingFields)) {
        jsonResponse(['error' => 'Missing required fields: ' . implode(', ', $missingFields)], 400);
    }

    // For coordinators, validate that the module belongs to their assigned filiere
    if (isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'coordinateur') {
        // Get the module to check its filiere_id
        $module = getModuleById($data['module_id']);

        if (!$module || isset($module['error'])) {
            jsonResponse(['error' => 'Module not found'], 404);
        }

        // Check if the module belongs to the coordinator's filiere
        if (isset($_SESSION['user']['filiere_id'])) {
            if ($module['filiere_id'] != $_SESSION['user']['filiere_id']) {
                jsonResponse(['error' => 'You can only edit teaching units for modules from your assigned department'], 403);
            }
        }
    }

    // Always set nb_groupes to 1 as we're removing this functionality
    $data['nb_groupes'] = 1;

    // Update the teaching unit
    $result = updateUniteEnseignement($id, $data);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 500);
    }

    jsonResponse(['success' => true], 200);
}

/**
 * API endpoint to delete a teaching unit
 *
 * @param int $id Teaching unit ID
 */
function deleteUniteEnseignementAPI($id) {
    // For coordinators, validate that the teaching unit belongs to a module in their assigned filiere
    if (isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'coordinateur') {
        // First, get the teaching unit to find its module_id
        $unite = getUniteEnseignementById($id);

        if (!$unite || isset($unite['error'])) {
            jsonResponse(['error' => 'Teaching unit not found'], 404);
        }

        // Get the module to check its filiere_id
        $module = getModuleById($unite['module_id']);

        if (!$module || isset($module['error'])) {
            jsonResponse(['error' => 'Module not found'], 404);
        }

        // Check if the module belongs to the coordinator's filiere
        if (isset($_SESSION['user']['filiere_id'])) {
            if ($module['filiere_id'] != $_SESSION['user']['filiere_id']) {
                jsonResponse(['error' => 'You can only delete teaching units for modules from your assigned department'], 403);
            }
        }
    }

    $result = deleteUniteEnseignement($id);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 500);
    }

    jsonResponse(['success' => true], 200);
}

/**
 * API endpoint to get all modules
 */
function getAllModulesAPI() {
    // For coordinators, filter modules by their assigned filiere_id
    $filiereId = null;

    // Check if the user is a coordinator
    if (isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'coordinateur') {
        // Get the coordinator's filiere_id from the session
        if (isset($_SESSION['user']['filiere_id'])) {
            $filiereId = $_SESSION['user']['filiere_id'];
        }
    }

    $modules = getAllModules($filiereId);

    if (isset($modules['error'])) {
        jsonResponse(['error' => $modules['error']], 404);
    }

    jsonResponse(['data' => $modules], 200);
}

/**
 * API endpoint to get a module by ID
 *
 * @param int $id Module ID
 */
function getModuleByIdAPI($id) {
    $module = getModuleById($id);

    if (!$module || isset($module['error'])) {
        jsonResponse(['error' => 'Module not found'], 404);
    }

    jsonResponse(['data' => $module], 200);
}

/**
 * API endpoint to create a new module
 */
function createModuleAPI() {
    // Get JSON data from request body
    $data = json_decode(file_get_contents('php://input'), true);

    if (!$data) {
        jsonResponse(['error' => 'Invalid JSON data'], 400);
    }

    // Validate required fields
    $requiredFields = ['nom', 'volume_total', 'specialite_id', 'filiere_id', 'id_niveau', 'id_semestre'];
    $missingFields = [];

    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            $missingFields[] = $field;
        }
    }

    if (!empty($missingFields)) {
        jsonResponse(['error' => 'Missing required fields: ' . implode(', ', $missingFields)], 400);
    }

    // For coordinators, validate that the filiere_id matches their assigned filiere
    if (isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'coordinateur') {
        if (isset($_SESSION['user']['filiere_id']) && $data['filiere_id'] != $_SESSION['user']['filiere_id']) {
            jsonResponse(['error' => 'You can only create modules for your assigned department'], 403);
        }
    }

    // Create the module
    $result = createModule($data);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 500);
    }

    jsonResponse(['success' => true, 'id' => $result['id']], 201);
}

/**
 * API endpoint to update a module
 *
 * @param int $id Module ID
 */
function updateModuleAPI($id) {
    // Get JSON data from request body
    $data = json_decode(file_get_contents('php://input'), true);

    if (!$data) {
        jsonResponse(['error' => 'Invalid JSON data'], 400);
    }

    // Validate required fields
    $requiredFields = ['nom', 'volume_total', 'specialite_id', 'filiere_id', 'id_niveau', 'id_semestre'];
    $missingFields = [];

    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            $missingFields[] = $field;
        }
    }

    if (!empty($missingFields)) {
        jsonResponse(['error' => 'Missing required fields: ' . implode(', ', $missingFields)], 400);
    }

    // For coordinators, validate that the module belongs to their assigned filiere
    if (isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'coordinateur') {
        // First, get the current module to check its filiere_id
        $module = getModuleById($id);

        if (!$module || isset($module['error'])) {
            jsonResponse(['error' => 'Module not found'], 404);
        }

        // Check if the module belongs to the coordinator's filiere
        if (isset($_SESSION['user']['filiere_id'])) {
            if ($module['filiere_id'] != $_SESSION['user']['filiere_id']) {
                jsonResponse(['error' => 'You can only edit modules from your assigned department'], 403);
            }

            // Also ensure they're not trying to change the filiere_id
            if ($data['filiere_id'] != $_SESSION['user']['filiere_id']) {
                jsonResponse(['error' => 'You cannot change the department of a module'], 403);
            }
        }
    }

    // Update the module
    $result = updateModule($id, $data);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 500);
    }

    jsonResponse(['success' => true], 200);
}

/**
 * API endpoint to delete a module
 *
 * @param int $id Module ID
 */
function deleteModuleAPI($id) {
    // For coordinators, validate that the module belongs to their assigned filiere
    if (isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'coordinateur') {
        // First, get the current module to check its filiere_id
        $module = getModuleById($id);

        if (!$module || isset($module['error'])) {
            jsonResponse(['error' => 'Module not found'], 404);
        }

        // Check if the module belongs to the coordinator's filiere
        if (isset($_SESSION['user']['filiere_id'])) {
            if ($module['filiere_id'] != $_SESSION['user']['filiere_id']) {
                jsonResponse(['error' => 'You can only delete modules from your assigned department'], 403);
            }
        }
    }

    $result = deleteModule($id);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 500);
    }

    jsonResponse(['success' => true], 200);
}

/**
 * API endpoint to get all filieres
 */
function getAllFilieresAPI() {
    $filieres = getAllFilieres();

    if (isset($filieres['error'])) {
        jsonResponse(['error' => $filieres['error']], 404);
    }

    jsonResponse(['data' => $filieres], 200);
}

/**
 * API endpoint to get all specialties
 */
function getAllSpecialitesAPI() {
    $specialites = getAllSpecialites();

    if (isset($specialites['error'])) {
        jsonResponse(['error' => $specialites['error']], 404);
    }

    jsonResponse(['data' => $specialites], 200);
}

/**
 * API endpoint to get all semesters
 */
function getAllSemestresAPI() {
    // Check if niveau_id is provided for filtering
    $niveauId = isset($_GET['niveau_id']) ? $_GET['niveau_id'] : null;

    $semestres = getAllSemestres($niveauId);

    if (isset($semestres['error'])) {
        jsonResponse(['error' => $semestres['error']], 404);
    }

    jsonResponse(['data' => $semestres], 200);
}

/**
 * API endpoint to get all niveaux (levels)
 */
function getAllNiveauxAPI() {
    // Check if filiere_id is provided for filtering
    $filiereId = isset($_GET['filiere_id']) ? $_GET['filiere_id'] : null;

    // For coordinators, use the filiere_id from the session if not explicitly provided
    if ($filiereId === null && isset($_SESSION['user']) && $_SESSION['user']['role'] === 'coordinateur' && isset($_SESSION['user']['filiere_id'])) {
        $filiereId = $_SESSION['user']['filiere_id'];
        error_log("[DEBUG] Using coordinator's filiere_id from session in getAllNiveauxAPI: " . $filiereId);
    } else {
        error_log("[DEBUG] getAllNiveauxAPI called with filiereId: " . ($filiereId ?? 'null') .
                 ", user role: " . (isset($_SESSION['user']) ? $_SESSION['user']['role'] : 'not set') .
                 ", filiere_id in session: " . (isset($_SESSION['user']['filiere_id']) ? $_SESSION['user']['filiere_id'] : 'not set'));
    }

    // Force using filiere_id for coordinators, even if provided in GET
    if (isset($_SESSION['user']) && $_SESSION['user']['role'] === 'coordinateur' && isset($_SESSION['user']['filiere_id'])) {
        $filiereId = $_SESSION['user']['filiere_id'];
        error_log("[DEBUG] Forcing coordinator's filiere_id in getAllNiveauxAPI: " . $filiereId);
    }

    $niveaux = getAllNiveaux($filiereId);

    if (isset($niveaux['error'])) {
        jsonResponse(['error' => $niveaux['error']], 404);
    }

    jsonResponse(['data' => $niveaux], 200);
}

/**
 * API endpoint to get all teachers
 */
function getAllEnseignantsAPI() {
    $enseignants = getAllEnseignants();

    if (isset($enseignants['error'])) {
        jsonResponse(['error' => $enseignants['error']], 404);
    }

    jsonResponse(['data' => $enseignants], 200);
}

/**
 * API endpoint to import teaching units from CSV file
 */
function importUniteEnseignementFromCSVAPI() {
    // Check if file was uploaded
    if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
        jsonResponse(['error' => 'No file uploaded or upload error'], 400);
    }

    $filePath = $_FILES['csv_file']['tmp_name'];

    // Import the teaching units
    $result = importUniteEnseignementFromCSV($filePath);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 500);
    }

    jsonResponse([
        'success' => true,
        'imported' => $result['imported'],
        'errors' => $result['errors']
    ], 200);
}

/**
 * API endpoint to import modules from file (CSV or Excel)
 */
function importModulesAPI() {
    // Check if file was uploaded
    if (!isset($_FILES['modules_file']) || $_FILES['modules_file']['error'] !== UPLOAD_ERR_OK) {
        jsonResponse(['error' => 'No file uploaded or upload error'], 400);
    }

    $filePath = $_FILES['modules_file']['tmp_name'];
    $fileType = pathinfo($_FILES['modules_file']['name'], PATHINFO_EXTENSION);
    $hasHeader = isset($_POST['has_header']) && $_POST['has_header'] === 'on';

    // Check file type
    if (!in_array($fileType, ['csv', 'xlsx', 'xls'])) {
        jsonResponse(['error' => 'Invalid file type. Only CSV and Excel files are supported.'], 400);
    }

    // For coordinators, get their filiere_id from the session or POST data
    $coordinatorFiliereId = null;
    if (isset($_SESSION['user']) && $_SESSION['user']['role'] === 'coordinateur' && isset($_SESSION['user']['filiere_id'])) {
        $coordinatorFiliereId = $_SESSION['user']['filiere_id'];
        error_log("[DEBUG] Using coordinator's filiere_id from session for import: " . $coordinatorFiliereId);
    } elseif (isset($_POST['coordinator_filiere_id'])) {
        $coordinatorFiliereId = $_POST['coordinator_filiere_id'];
        error_log("[DEBUG] Using coordinator's filiere_id from POST data for import: " . $coordinatorFiliereId);
    }

    // Import the modules
    $result = importModulesFromFile($filePath, $fileType, $hasHeader, $coordinatorFiliereId);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 500);
    }

    jsonResponse([
        'success' => true,
        'count' => $result['count'],
        'errors' => $result['errors'] ?? []
    ], 200);
}

// Handle API requests
if (isset($_GET['action'])) {
    $action = $_GET['action'];

    switch ($action) {
        case 'getAllUnites':
            getAllUniteEnseignementAPI();
            break;

        case 'getUnitesByModule':
            if (!isset($_GET['module_id'])) {
                jsonResponse(['error' => 'Module ID is required'], 400);
            }
            getUniteEnseignementByModuleAPI($_GET['module_id']);
            break;

        case 'getUnitesByFiliere':
            if (!isset($_GET['filiere_id'])) {
                jsonResponse(['error' => 'Filiere ID is required'], 400);
            }
            getUniteEnseignementByFiliereAPI($_GET['filiere_id']);
            break;

        case 'getUniteById':
            if (!isset($_GET['id'])) {
                jsonResponse(['error' => 'Teaching unit ID is required'], 400);
            }
            getUniteEnseignementByIdAPI($_GET['id']);
            break;

        case 'createUnite':
            createUniteEnseignementAPI();
            break;

        case 'updateUnite':
            if (!isset($_GET['id'])) {
                jsonResponse(['error' => 'Teaching unit ID is required'], 400);
            }
            updateUniteEnseignementAPI($_GET['id']);
            break;

        case 'deleteUnite':
            if (!isset($_GET['id'])) {
                jsonResponse(['error' => 'Teaching unit ID is required'], 400);
            }
            deleteUniteEnseignementAPI($_GET['id']);
            break;

        case 'getAllModules':
            getAllModulesAPI();
            break;

        case 'getModuleById':
            if (!isset($_GET['id'])) {
                jsonResponse(['error' => 'Module ID is required'], 400);
            }
            getModuleByIdAPI($_GET['id']);
            break;

        case 'createModule':
            createModuleAPI();
            break;

        case 'updateModule':
            if (!isset($_GET['id'])) {
                jsonResponse(['error' => 'Module ID is required'], 400);
            }
            updateModuleAPI($_GET['id']);
            break;

        case 'deleteModule':
            if (!isset($_GET['id'])) {
                jsonResponse(['error' => 'Module ID is required'], 400);
            }
            deleteModuleAPI($_GET['id']);
            break;

        case 'getAllFilieres':
            getAllFilieresAPI();
            break;

        case 'getAllSpecialites':
            getAllSpecialitesAPI();
            break;

        case 'getAllSemestres':
            getAllSemestresAPI();
            break;

        case 'getAllNiveaux':
            getAllNiveauxAPI();
            break;

        case 'getAllEnseignants':
            getAllEnseignantsAPI();
            break;

        case 'importFromCSV':
            importUniteEnseignementFromCSVAPI();
            break;

        default:
            jsonResponse(['error' => 'Invalid action'], 400);
    }
}
?>