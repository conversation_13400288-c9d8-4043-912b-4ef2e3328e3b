<?php
require_once __DIR__ . "/../controller/passwordResetController.php";
require_once __DIR__ . "/../utils/response.php";

// Activer CORS pour les requêtes
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

// Gérer les requêtes OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Récupérer la méthode HTTP
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'POST':
            // Récupérer les données JSON
            $data = json_decode(file_get_contents('php://input'), true);

            if (isset($data['action'])) {
                switch ($data['action']) {
                    case 'request':
                        // Demande de réinitialisation
                        if (isset($data['identifier'])) {
                            $result = requestPasswordReset($data['identifier']);
                            jsonResponse($result);
                        } else {
                            jsonResponse(['success' => false, 'error' => 'Identifiant requis'], 400);
                        }
                        break;

                    case 'verify':
                        // Vérification du code
                        if (isset($data['identifier']) && isset($data['code'])) {
                            $result = verifyResetCodeAPI($data['identifier'], $data['code']);
                            jsonResponse($result);
                        } else {
                            jsonResponse(['success' => false, 'error' => 'Identifiant et code requis'], 400);
                        }
                        break;

                    case 'reset':
                        // Réinitialisation du mot de passe
                        if (isset($data['token']) && isset($data['newPassword']) && isset($data['confirmPassword'])) {
                            $result = resetPasswordAPI($data['token'], $data['newPassword'], $data['confirmPassword']);
                            jsonResponse($result);
                        } else {
                            jsonResponse(['success' => false, 'error' => 'Paramètres manquants'], 400);
                        }
                        break;

                    case 'get_debug_code':
                        // Récupérer le code de débogage (uniquement en environnement de développement)
                        if ($_SERVER['SERVER_NAME'] == 'localhost' || strpos($_SERVER['SERVER_NAME'], '127.0.0.1') !== false) {
                            if (session_status() === PHP_SESSION_NONE) {
                                session_start();
                            }

                            if (isset($_SESSION['debug_reset_code'])) {
                                jsonResponse(['success' => true, 'debug_code' => $_SESSION['debug_reset_code']]);
                            } else {
                                jsonResponse(['success' => false, 'error' => 'Aucun code de débogage disponible']);
                            }
                        } else {
                            jsonResponse(['success' => false, 'error' => 'Action non autorisée en production'], 403);
                        }
                        break;

                    default:
                        jsonResponse(['success' => false, 'error' => 'Action non valide'], 400);
                        break;
                }
            } else {
                jsonResponse(['success' => false, 'error' => 'Action non spécifiée'], 400);
            }
            break;

        default:
            jsonResponse(['success' => false, 'error' => 'Méthode non autorisée'], 405);
            break;
    }
} catch (Exception $e) {
    error_log('Erreur dans passwordResetRoute: ' . $e->getMessage());
    jsonResponse(['success' => false, 'error' => 'Erreur serveur'], 500);
}
?>
