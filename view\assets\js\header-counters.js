/**
 * Script pour gérer les compteurs de notifications et messages dans le header
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialiser les compteurs
    updateNotificationsCount();
    updateMessagesCount();

    // Mettre à jour les compteurs toutes les 60 secondes
    setInterval(function() {
        updateNotificationsCount();
        updateMessagesCount();
    }, 60000); // 60 secondes
});

/**
 * Mettre à jour le compteur de notifications non lues
 */
function updateNotificationsCount() {
    fetch('../../route/notificationsRoute.php?action=getUnreadCount')
        .then(response => {
            if (!response.ok) {
                throw new Error('Erreur réseau lors de la récupération du nombre de notifications');
            }
            return response.json();
        })
        .then(data => {
            const countElement = document.getElementById('notifications-count');
            if (countElement) {
                const count = data.count || 0;
                countElement.textContent = count;
                
                // Afficher ou masquer le badge en fonction du nombre
                if (count > 0) {
                    countElement.classList.remove('d-none');
                } else {
                    countElement.classList.add('d-none');
                }
            }
        })
        .catch(error => {
            console.error('Erreur lors de la récupération du nombre de notifications:', error);
        });
}

/**
 * Mettre à jour le compteur de messages non lus
 */
function updateMessagesCount() {
    fetch('../../route/messagesRoute.php?action=getUnreadCount')
        .then(response => {
            if (!response.ok) {
                throw new Error('Erreur réseau lors de la récupération du nombre de messages');
            }
            return response.json();
        })
        .then(data => {
            const countElement = document.getElementById('messages-count');
            if (countElement) {
                const count = data.count || 0;
                countElement.textContent = count;
                
                // Afficher ou masquer le badge en fonction du nombre
                if (count > 0) {
                    countElement.classList.remove('d-none');
                } else {
                    countElement.classList.add('d-none');
                }
            }
        })
        .catch(error => {
            console.error('Erreur lors de la récupération du nombre de messages:', error);
        });
}
