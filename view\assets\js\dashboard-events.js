document.addEventListener("DOMContentLoaded", function () {
    const eventsContainer = document.getElementById('recent-events-list');

    if (!eventsContainer) {
        console.error("Container d'événements non trouvé");
        return;
    }

    // Afficher un message de chargement
    eventsContainer.innerHTML = '<li class="mb-2">Chargement des événements...</li>';

    // Chemin vers la route des événements en utilisant une URL dynamique
    // Obtenir l'URL de base (jusqu'à Projet-Web)
    const baseUrl = window.location.origin + window.location.pathname.split('/view/')[0];
    const basePath = baseUrl + "/route/eventsRoute.php";
    console.log("URL de l'API:", basePath); // Pour déboguer

    // Fonction pour formater la date
    function formatDate(dateStr, timeStr) {
        const date = new Date(dateStr);
        const day = date.getDate().toString().padStart(2, '0');
        const month = date.toLocaleString('fr-FR', { month: 'short' });
        return `${day} ${month}`;
    }

    // Récupérer les événements récents
    console.log("Tentative de récupération des événements à:", basePath);
    fetch(basePath)
        .then(response => {
            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status}`);
            }
            return response.json();
        })
        .then(result => {
            if (!result.success || !result.data || result.data.length === 0) {
                eventsContainer.innerHTML = '<li>Aucun événement à venir</li>';
                return;
            }

            // Trier les événements par date
            const sortedEvents = result.data.sort((a, b) => {
                const dateA = new Date(a.event_date + ' ' + a.event_time);
                const dateB = new Date(b.event_date + ' ' + b.event_time);
                return dateA - dateB;
            });

            // Prendre les 3 prochains événements
            const recentEvents = sortedEvents.slice(0, 3);

            // Afficher les événements
            eventsContainer.innerHTML = '';
            recentEvents.forEach(event => {
                const formattedDate = formatDate(event.event_date, event.event_time);
                const li = document.createElement('li');
                li.className = 'mb-2';
                li.textContent = `${event.title} - ${formattedDate}`;
                eventsContainer.appendChild(li);
            });
        })
        .catch(error => {
            console.error('Erreur lors du chargement des événements:', error);
            console.error('URL utilisée:', basePath);
            console.error('Détails de l\'erreur:', error.message);
            eventsContainer.innerHTML = '<li>Erreur lors du chargement des événements</li>';

            // Essayons une approche alternative en cas d'échec
            try {
                // Créer un élément script pour charger les données via JSONP
                const script = document.createElement('script');
                script.src = basePath + '?callback=handleEventsData';
                document.body.appendChild(script);

                // Fonction globale pour recevoir les données
                window.handleEventsData = function(data) {
                    if (data && data.success && data.data && data.data.length > 0) {
                        displayEvents(data.data);
                    } else {
                        eventsContainer.innerHTML = '<li>Aucun événement trouvé</li>';
                    }
                };

                // Fonction pour afficher les événements
                function displayEvents(events) {
                    const sortedEvents = events.sort((a, b) => {
                        const dateA = new Date(a.event_date + ' ' + a.event_time);
                        const dateB = new Date(b.event_date + ' ' + b.event_time);
                        return dateA - dateB;
                    });

                    const recentEvents = sortedEvents.slice(0, 3);

                    eventsContainer.innerHTML = '';
                    recentEvents.forEach(event => {
                        const formattedDate = formatDate(event.event_date, event.event_time);
                        const li = document.createElement('li');
                        li.className = 'mb-2';
                        li.textContent = `${event.title} - ${formattedDate}`;
                        eventsContainer.appendChild(li);
                    });
                }
            } catch (fallbackError) {
                console.error('Erreur lors de la tentative alternative:', fallbackError);
            }
        });
});