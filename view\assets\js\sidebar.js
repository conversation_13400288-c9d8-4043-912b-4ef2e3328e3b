// sidebar.js - Enhanced Responsive Sidebar
console.log('Sidebar.js loaded successfully');

document.addEventListener('DOMContentLoaded', function() {
    console.log('Sidebar DOM content loaded');

    // Elements
    const sidebarToggle = document.getElementById('sidebarToggle');
    const mobileSidebar = document.getElementById('mobileSidebar');
    const sidebarOverlay = document.getElementById('sidebarOverlay');

    // State management
    let sidebarOpen = false;

    // Check if we're on mobile/tablet
    function isMobile() {
        return window.innerWidth <= 991.98;
    }

    // Initialize sidebar state based on screen size
    function initializeSidebar() {
        if (isMobile()) {
            // Mobile/Tablet: Hide sidebar by default
            mobileSidebar.classList.remove('show');
            sidebarOverlay.classList.remove('show');
            document.body.style.overflow = 'auto';
            sidebarOpen = false;
        } else {
            // Desktop: Show sidebar always
            mobileSidebar.classList.remove('show');
            sidebarOverlay.classList.remove('show');
            document.body.style.overflow = 'auto';
            sidebarOpen = false;
        }
    }

    // Toggle sidebar function
    function toggleSidebar() {
        if (!isMobile()) return; // Don't toggle on desktop

        sidebarOpen = !sidebarOpen;

        if (sidebarOpen) {
            // Open sidebar
            mobileSidebar.classList.add('show');
            sidebarOverlay.classList.add('show');
            document.body.style.overflow = 'hidden';
        } else {
            // Close sidebar
            mobileSidebar.classList.remove('show');
            sidebarOverlay.classList.remove('show');
            document.body.style.overflow = 'auto';
        }
    }

    // Close sidebar
    function closeSidebar() {
        if (!isMobile()) return;

        sidebarOpen = false;
        mobileSidebar.classList.remove('show');
        sidebarOverlay.classList.remove('show');
        document.body.style.overflow = 'auto';
    }

    // Event listeners
    if (sidebarToggle && mobileSidebar && sidebarOverlay) {
        sidebarToggle.addEventListener('click', toggleSidebar);
        sidebarOverlay.addEventListener('click', closeSidebar);

        // Close sidebar when clicking on a link (mobile only)
        const sidebarLinks = mobileSidebar.querySelectorAll('a[href]:not([href="#"])');
        sidebarLinks.forEach(link => {
            link.addEventListener('click', () => {
                if (isMobile()) {
                    setTimeout(closeSidebar, 150); // Small delay for better UX
                }
            });
        });

    } else {
        console.error("Un ou plusieurs éléments de la barre latérale sont manquants.");
        console.log("sidebarToggle:", sidebarToggle);
        console.log("mobileSidebar:", mobileSidebar);
        console.log("sidebarOverlay:", sidebarOverlay);
    }

    // Handle window resize
    window.addEventListener('resize', function() {
        initializeSidebar();
    });

    // Initialize on load
    initializeSidebar();

    // Initialize all accordions/collapsibles in the sidebar
    const accordionButtons = document.querySelectorAll('[data-bs-toggle="collapse"]');
    accordionButtons.forEach(button => {
      button.addEventListener('click', function() {
        const icon = this.querySelector('.submenu-icon');
        const isExpanded = this.getAttribute('aria-expanded') === 'true';

        if (isExpanded) {
          icon.style.transform = 'rotate(180deg)';
        } else {
          icon.style.transform = 'rotate(0deg)';
        }
      });
    });
  });