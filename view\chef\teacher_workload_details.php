<?php
session_start();

// Check if user is logged in and is a department head
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'chef de departement') {
    header("Location: ../auth/login.php");
    exit();
}

// Get user information
$user = $_SESSION['user'];
$chefId = $user['id_enseignant'];
$departmentId = $user['id_departement'];
$fullName = $user['prenom'] . ' ' . $user['nom'];

// Get department name
require_once '../../model/departementModel.php';
$departmentInfo = getDepartementById($departmentId);
$departmentName = $departmentInfo ? $departmentInfo['nom_departement'] : 'Département';

// Page configuration
$pageTitle = "Détails de la Charge de Travail";
$currentPage = "teacher_workload_details";
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - ENSAH</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <style>
        .teacher-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
            cursor: pointer;
        }
        
        .teacher-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
        }
        
        .teacher-card.selected {
            border: 2px solid #667eea;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.2);
        }
        
        .workload-details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .year-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 500;
            margin-bottom: 1rem;
            display: inline-block;
        }
        
        .assignment-item {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-left: 4px solid #667eea;
        }
        
        .no-data {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <?php include '../includes/sidebar.php'; ?>
        
        <div class="main-content">
            <?php include '../includes/header.php'; ?>
            
            <div class="content-wrapper">
                <div class="container-fluid p-4">
                    <!-- Page Header -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <h1 class="page-title">
                                <i class="fas fa-user-chart me-3"></i>
                                Détails de la Charge de Travail par Enseignant
                            </h1>
                            <p class="text-muted mb-0">
                                Analyse détaillée de la charge de travail des enseignants du département <strong><?php echo htmlspecialchars($departmentName); ?></strong>
                            </p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <select id="academicYearSelector" class="form-select">
                                <option value="">Chargement...</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Main Content -->
                    <div class="row">
                        <!-- Teachers List -->
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-users me-2"></i>
                                        Enseignants du Département
                                    </h5>
                                </div>
                                <div class="card-body p-0" style="max-height: 600px; overflow-y: auto;">
                                    <div id="teachersList">
                                        <div class="loading-spinner">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Chargement...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Teacher Details -->
                        <div class="col-lg-8">
                            <div id="teacherDetails">
                                <div class="no-data">
                                    <i class="fas fa-user-plus fa-3x mb-3"></i>
                                    <h5>Sélectionnez un enseignant</h5>
                                    <p>Cliquez sur un enseignant dans la liste pour voir ses détails de charge de travail</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/sidebar.js"></script>
    
    <script>
        // Global variables
        let currentAcademicYear = '';
        let selectedTeacherId = null;
        let teachersData = [];
        let workloadChart = null;
        const departmentId = <?php echo $departmentId; ?>;
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadAcademicYears();
            
            // Academic year selector change event
            document.getElementById('academicYearSelector').addEventListener('change', function() {
                currentAcademicYear = this.value;
                if (currentAcademicYear) {
                    loadTeachers();
                    selectedTeacherId = null;
                    showDefaultMessage();
                }
            });
        });
        
        // Load available academic years
        async function loadAcademicYears() {
            try {
                const response = await fetch('../../controller/workloadController.php?action=getAvailableAcademicYears');
                const data = await response.json();
                
                if (data.success) {
                    const selector = document.getElementById('academicYearSelector');
                    selector.innerHTML = '';
                    
                    data.data.forEach((year, index) => {
                        const option = document.createElement('option');
                        option.value = year;
                        option.textContent = year;
                        if (index === 0) {
                            option.selected = true;
                            currentAcademicYear = year;
                        }
                        selector.appendChild(option);
                    });
                    
                    // Load teachers for the first year
                    if (currentAcademicYear) {
                        loadTeachers();
                    }
                } else {
                    console.error('Error loading academic years:', data.error);
                }
            } catch (error) {
                console.error('Error loading academic years:', error);
            }
        }
        
        // Load teachers list
        async function loadTeachers() {
            try {
                const response = await fetch(`../../controller/workloadController.php?action=getDepartmentWorkloadByYear&department_id=${departmentId}&academic_year=${currentAcademicYear}`);
                const data = await response.json();
                
                if (data.success) {
                    teachersData = data.data;
                    displayTeachers(teachersData);
                } else {
                    console.error('Error loading teachers:', data.error);
                    document.getElementById('teachersList').innerHTML = '<div class="no-data"><i class="fas fa-exclamation-triangle fa-3x mb-3"></i><p>Erreur lors du chargement des enseignants</p></div>';
                }
            } catch (error) {
                console.error('Error loading teachers:', error);
                document.getElementById('teachersList').innerHTML = '<div class="no-data"><i class="fas fa-exclamation-triangle fa-3x mb-3"></i><p>Erreur lors du chargement des enseignants</p></div>';
            }
        }
        
        // Display teachers list
        function displayTeachers(teachers) {
            const container = document.getElementById('teachersList');
            
            if (teachers.length === 0) {
                container.innerHTML = '<div class="no-data"><i class="fas fa-users fa-3x mb-3"></i><p>Aucun enseignant trouvé</p></div>';
                return;
            }
            
            let html = '';
            teachers.forEach(teacher => {
                html += `
                    <div class="teacher-card p-3" onclick="selectTeacher(${teacher.id_enseignant})" data-teacher-id="${teacher.id_enseignant}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">
                                    <i class="fas fa-user me-2"></i>
                                    ${teacher.prenom} ${teacher.nom}
                                </h6>
                                <small class="text-muted">${teacher.role}</small>
                            </div>
                            <div class="text-end">
                                <strong class="text-primary">${teacher.total_hours}h</strong>
                                <br>
                                <small class="text-muted">${teacher.total_assignments} UE</small>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        // Select teacher and show details
        function selectTeacher(teacherId) {
            selectedTeacherId = teacherId;
            
            // Update visual selection
            document.querySelectorAll('.teacher-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-teacher-id="${teacherId}"]`).classList.add('selected');
            
            // Load teacher details
            loadTeacherDetails(teacherId);
        }
        
        // Load teacher workload details
        async function loadTeacherDetails(teacherId) {
            try {
                const response = await fetch(`../../controller/workloadController.php?action=getTeacherWorkloadHistory&teacher_id=${teacherId}`);
                const data = await response.json();
                
                if (data.success) {
                    displayTeacherDetails(teacherId, data.data);
                } else {
                    console.error('Error loading teacher details:', data.error);
                    showErrorMessage();
                }
            } catch (error) {
                console.error('Error loading teacher details:', error);
                showErrorMessage();
            }
        }
        
        // Display teacher workload details
        function displayTeacherDetails(teacherId, history) {
            const teacher = teachersData.find(t => t.id_enseignant == teacherId);
            if (!teacher) return;
            
            const container = document.getElementById('teacherDetails');
            
            let html = `
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            ${teacher.prenom} ${teacher.nom} - ${teacher.role}
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Current Year Summary -->
                        <div class="workload-details">
                            <div class="year-badge">${currentAcademicYear}</div>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-primary">${teacher.total_hours}h</h4>
                                        <small class="text-muted">Total</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-success">${teacher.cours_hours}h</h4>
                                        <small class="text-muted">Cours</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-warning">${teacher.td_hours}h</h4>
                                        <small class="text-muted">TD</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-info">${teacher.tp_hours}h</h4>
                                        <small class="text-muted">TP</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Historical Data -->
                        <h6 class="mb-3">
                            <i class="fas fa-history me-2"></i>
                            Historique de la Charge de Travail
                        </h6>
            `;
            
            if (history.length > 0) {
                history.forEach(year => {
                    html += `
                        <div class="assignment-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${year.annee_academique}</strong>
                                    <br>
                                    <small class="text-muted">${year.total_assignments} affectations</small>
                                </div>
                                <div class="text-end">
                                    <strong>${year.total_hours}h</strong>
                                    <br>
                                    <small class="text-muted">
                                        C: ${year.cours_hours}h | TD: ${year.td_hours}h | TP: ${year.tp_hours}h
                                    </small>
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                // Add chart
                html += `
                    <div class="mt-4">
                        <h6 class="mb-3">
                            <i class="fas fa-chart-line me-2"></i>
                            Évolution de la Charge de Travail
                        </h6>
                        <div class="chart-container">
                            <canvas id="teacherWorkloadChart"></canvas>
                        </div>
                    </div>
                `;
            } else {
                html += '<div class="no-data"><i class="fas fa-info-circle fa-2x mb-3"></i><p>Aucun historique disponible</p></div>';
            }
            
            html += `
                    </div>
                </div>
            `;
            
            container.innerHTML = html;
            
            // Create chart if history exists
            if (history.length > 0) {
                createTeacherChart(history);
            }
        }
        
        // Create teacher workload chart
        function createTeacherChart(history) {
            const ctx = document.getElementById('teacherWorkloadChart');
            if (!ctx) return;
            
            if (workloadChart) {
                workloadChart.destroy();
            }
            
            workloadChart = new Chart(ctx.getContext('2d'), {
                type: 'line',
                data: {
                    labels: history.map(h => h.annee_academique),
                    datasets: [{
                        label: 'Total Heures',
                        data: history.map(h => h.total_hours),
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }, {
                        label: 'Cours',
                        data: history.map(h => h.cours_hours),
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        borderWidth: 2,
                        fill: false
                    }, {
                        label: 'TD',
                        data: history.map(h => h.td_hours),
                        borderColor: '#ffc107',
                        backgroundColor: 'rgba(255, 193, 7, 0.1)',
                        borderWidth: 2,
                        fill: false
                    }, {
                        label: 'TP',
                        data: history.map(h => h.tp_hours),
                        borderColor: '#17a2b8',
                        backgroundColor: 'rgba(23, 162, 184, 0.1)',
                        borderWidth: 2,
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y + 'h';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Heures'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Année Académique'
                            }
                        }
                    }
                }
            });
        }
        
        // Show default message
        function showDefaultMessage() {
            document.getElementById('teacherDetails').innerHTML = `
                <div class="no-data">
                    <i class="fas fa-user-plus fa-3x mb-3"></i>
                    <h5>Sélectionnez un enseignant</h5>
                    <p>Cliquez sur un enseignant dans la liste pour voir ses détails de charge de travail</p>
                </div>
            `;
        }
        
        // Show error message
        function showErrorMessage() {
            document.getElementById('teacherDetails').innerHTML = `
                <div class="no-data">
                    <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                    <h5>Erreur</h5>
                    <p>Impossible de charger les détails de l'enseignant</p>
                </div>
            `;
        }
    </script>
</body>
</html>
