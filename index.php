<?php
// Inclure les constantes
require_once __DIR__ . "/config/constants.php";
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - ENSAH</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        :root {
            --royal-blue: #1a73e8;
            --azure: #3a8ff7;
            --sky-blue: #64b5f6;
            --light-blue: #e8f0fe;
            --navy-blue: #0d47a1;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: none;
        }

        .card-header {
            background-color: var(--royal-blue);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            padding: 20px;
        }

        .btn-primary {
            background-color: var(--royal-blue);
            border-color: var(--royal-blue);
        }

        .btn-primary:hover {
            background-color: var(--navy-blue);
            border-color: var(--navy-blue);
        }

        .forgot-password {
            text-align: center;
            margin-top: 20px;
        }

        .forgot-password a {
            color: var(--royal-blue);
            text-decoration: none;
        }

        .forgot-password a:hover {
            text-decoration: underline;
        }

        .logo {
            max-width: 100px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header text-center">
                        <img src="<?php echo BASE_URL; ?>/view/assets/img/logo.png" alt="ENSAH Logo" class="logo">
                        <h3>Connexion</h3>
                    </div>
                    <div class="card-body">
                        <div id="errorMessage" class="alert alert-danger d-none" role="alert"></div>
                        <form id="loginForm">
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="bi bi-person me-2"></i>CNI ou CNE
                                </label>
                                <input type="text" class="form-control" id="username" name="username" placeholder="Entrez votre identifiant" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="bi bi-lock me-2"></i>Mot de passe
                                </label>
                                <input type="password" class="form-control" id="password" name="password" placeholder="Entrez votre mot de passe" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi bi-box-arrow-in-right me-2"></i>Se connecter
                            </button>
                        </form>

                        <div class="forgot-password">
                            <a href="<?php echo BASE_URL; ?>/view/reset-password.php">
                                <i class="bi bi-key me-1"></i>Mot de passe oublié?
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorMessage = document.getElementById('errorMessage');

            fetch('controller/authController.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'login',
                    username: username,
                    password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = data.redirect;
                } else {
                    errorMessage.textContent = data.error || 'Erreur de connexion';
                    errorMessage.classList.remove('d-none');
                }
            })
            .catch(error => {
                errorMessage.textContent = 'Erreur de connexion au serveur';
                errorMessage.classList.remove('d-none');
                console.error('Error:', error);
            });
        });
    </script>
</body>
</html>