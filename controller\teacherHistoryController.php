<?php
require_once "../model/teacherHistoryModel.php";
require_once "../utils/response.php";

/**
 * API endpoint to get academic years for a teacher
 *
 * @param int $teacherId The teacher's ID
 */
function getTeacherAcademicYearsAPI($teacherId) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a teacher
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'enseignant') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    if (!$teacherId) {
        jsonResponse(['error' => 'Teacher ID is required'], 400);
        exit;
    }

    $years = getTeacherAcademicYears($teacherId);

    if (isset($years['error'])) {
        jsonResponse(['error' => $years['error']], 500);
        exit;
    }

    jsonResponse(['success' => true, 'data' => $years], 200);
}

/**
 * API endpoint to get UE assignments for a teacher in a specific year
 *
 * @param int $teacherId The teacher's ID
 * @param string $academicYear The academic year
 */
function getTeacherUEAssignmentsAPI($teacherId, $academicYear) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a teacher
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'enseignant') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    if (!$teacherId || !$academicYear) {
        jsonResponse(['error' => 'Teacher ID and academic year are required'], 400);
        exit;
    }

    $assignments = getTeacherUEAssignments($teacherId, $academicYear);

    if (isset($assignments['error'])) {
        jsonResponse(['error' => $assignments['error']], 500);
        exit;
    }

    jsonResponse(['success' => true, 'data' => $assignments], 200);
}

/**
 * API endpoint to get modules for a teacher in a specific year
 *
 * @param int $teacherId The teacher's ID
 * @param string $academicYear The academic year
 */
function getTeacherModulesAPI($teacherId, $academicYear) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a teacher
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'enseignant') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    if (!$teacherId || !$academicYear) {
        jsonResponse(['error' => 'Teacher ID and academic year are required'], 400);
        exit;
    }

    $modules = getTeacherModules($teacherId, $academicYear);

    if (isset($modules['error'])) {
        jsonResponse(['error' => $modules['error']], 500);
        exit;
    }

    jsonResponse(['success' => true, 'data' => $modules], 200);
}

/**
 * API endpoint to get annual statistics for a teacher
 *
 * @param int $teacherId The teacher's ID
 * @param string $academicYear The academic year
 */
function getTeacherAnnualStatsAPI($teacherId, $academicYear) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a teacher
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'enseignant') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    if (!$teacherId || !$academicYear) {
        jsonResponse(['error' => 'Teacher ID and academic year are required'], 400);
        exit;
    }

    $stats = getTeacherAnnualStats($teacherId, $academicYear);

    if (isset($stats['error'])) {
        jsonResponse(['error' => $stats['error']], 500);
        exit;
    }

    jsonResponse(['success' => true, 'data' => $stats], 200);
}

/**
 * API endpoint to get grade history for a teacher
 *
 * @param int $teacherId The teacher's ID
 * @param string $academicYear The academic year
 */
function getTeacherGradeHistoryAPI($teacherId, $academicYear) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a teacher
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'enseignant') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    if (!$teacherId || !$academicYear) {
        jsonResponse(['error' => 'Teacher ID and academic year are required'], 400);
        exit;
    }

    $gradeHistory = getTeacherGradeHistory($teacherId, $academicYear);

    if (isset($gradeHistory['error'])) {
        jsonResponse(['error' => $gradeHistory['error']], 500);
        exit;
    }

    jsonResponse(['success' => true, 'data' => $gradeHistory], 200);
}

/**
 * API endpoint to generate annual report for a teacher
 *
 * @param int $teacherId The teacher's ID
 * @param string $academicYear The academic year
 */
function generateAnnualReportAPI($teacherId, $academicYear) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a teacher
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'enseignant') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    if (!$teacherId || !$academicYear) {
        jsonResponse(['error' => 'Teacher ID and academic year are required'], 400);
        exit;
    }

    $report = generateAnnualReport($teacherId, $academicYear);

    if (isset($report['error'])) {
        jsonResponse(['error' => $report['error']], 500);
        exit;
    }

    jsonResponse(['success' => true, 'data' => $report], 200);
}

/**
 * API endpoint to get teacher's administrative observations
 *
 * @param int $teacherId The teacher's ID
 * @param string $academicYear The academic year
 */
function getTeacherObservationsAPI($teacherId, $academicYear) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a teacher
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'enseignant') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    if (!$teacherId || !$academicYear) {
        jsonResponse(['error' => 'Teacher ID and academic year are required'], 400);
        exit;
    }

    $observations = getTeacherObservations($teacherId, $academicYear);

    if (isset($observations['error'])) {
        jsonResponse(['error' => $observations['error']], 500);
        exit;
    }

    jsonResponse(['success' => true, 'data' => $observations], 200);
}

/**
 * API endpoint to get complete history data for a teacher
 *
 * @param int $teacherId The teacher's ID
 * @param string $academicYear The academic year
 */
function getCompleteHistoryAPI($teacherId, $academicYear) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a teacher
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'enseignant') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    if (!$teacherId || !$academicYear) {
        jsonResponse(['error' => 'Teacher ID and academic year are required'], 400);
        exit;
    }

    // Get all data for the year
    $ueAssignments = getTeacherUEAssignments($teacherId, $academicYear);
    $modules = getTeacherModules($teacherId, $academicYear);
    $stats = getTeacherAnnualStats($teacherId, $academicYear);
    $gradeHistory = getTeacherGradeHistory($teacherId, $academicYear);
    $observations = getTeacherObservations($teacherId, $academicYear);

    // Check for errors
    if (isset($ueAssignments['error']) || isset($modules['error']) ||
        isset($stats['error']) || isset($gradeHistory['error']) || isset($observations['error'])) {
        jsonResponse(['error' => 'Error fetching history data'], 500);
        exit;
    }

    $completeHistory = [
        'academic_year' => $academicYear,
        'ue_assignments' => $ueAssignments,
        'modules' => $modules,
        'statistics' => $stats,
        'grade_history' => $gradeHistory,
        'observations' => $observations
    ];

    jsonResponse(['success' => true, 'data' => $completeHistory], 200);
}

// Handle the request
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $action = $_GET['action'] ?? '';

    switch ($action) {
        case 'getAcademicYears':
            if (!isset($_GET['teacher_id'])) {
                jsonResponse(['error' => 'Teacher ID is required'], 400);
            }
            getTeacherAcademicYearsAPI($_GET['teacher_id']);
            break;

        case 'getUEAssignments':
            if (!isset($_GET['teacher_id']) || !isset($_GET['academic_year'])) {
                jsonResponse(['error' => 'Teacher ID and academic year are required'], 400);
            }
            getTeacherUEAssignmentsAPI($_GET['teacher_id'], $_GET['academic_year']);
            break;

        case 'getModules':
            if (!isset($_GET['teacher_id']) || !isset($_GET['academic_year'])) {
                jsonResponse(['error' => 'Teacher ID and academic year are required'], 400);
            }
            getTeacherModulesAPI($_GET['teacher_id'], $_GET['academic_year']);
            break;

        case 'getAnnualStats':
            if (!isset($_GET['teacher_id']) || !isset($_GET['academic_year'])) {
                jsonResponse(['error' => 'Teacher ID and academic year are required'], 400);
            }
            getTeacherAnnualStatsAPI($_GET['teacher_id'], $_GET['academic_year']);
            break;

        case 'getGradeHistory':
            if (!isset($_GET['teacher_id']) || !isset($_GET['academic_year'])) {
                jsonResponse(['error' => 'Teacher ID and academic year are required'], 400);
            }
            getTeacherGradeHistoryAPI($_GET['teacher_id'], $_GET['academic_year']);
            break;

        case 'getObservations':
            if (!isset($_GET['teacher_id']) || !isset($_GET['academic_year'])) {
                jsonResponse(['error' => 'Teacher ID and academic year are required'], 400);
            }
            getTeacherObservationsAPI($_GET['teacher_id'], $_GET['academic_year']);
            break;

        case 'generateReport':
            if (!isset($_GET['teacher_id']) || !isset($_GET['academic_year'])) {
                jsonResponse(['error' => 'Teacher ID and academic year are required'], 400);
            }
            generateAnnualReportAPI($_GET['teacher_id'], $_GET['academic_year']);
            break;

        case 'getCompleteHistory':
            if (!isset($_GET['teacher_id']) || !isset($_GET['academic_year'])) {
                jsonResponse(['error' => 'Teacher ID and academic year are required'], 400);
            }
            getCompleteHistoryAPI($_GET['teacher_id'], $_GET['academic_year']);
            break;

        default:
            jsonResponse(['error' => 'Invalid action'], 400);
            break;
    }
} else {
    jsonResponse(['error' => 'Method not allowed'], 405);
}
?>
