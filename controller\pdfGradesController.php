<?php
// Utiliser un chemin absolu pour éviter les problèmes d'inclusion
$modelPath = __DIR__ . "/../model/pdfGradesModel.php";
$utilsPath = __DIR__ . "/../utils/response.php";

// Essayer différents chemins pour le modèle
if (file_exists($modelPath)) {
    require_once $modelPath;
} else {
    // Essayer un autre chemin comme solution de secours
    $altModelPath = __DIR__ . "/../../model/pdfGradesModel.php";
    if (file_exists($altModelPath)) {
        require_once $altModelPath;
    } else {
        die("Impossible de trouver le fichier pdfGradesModel.php.");
    }
}

// Essayer différents chemins pour les utilitaires
if (file_exists($utilsPath)) {
    require_once $utilsPath;
} else {
    // Essayer un autre chemin comme solution de secours
    $altUtilsPath = __DIR__ . "/../../utils/response.php";
    if (file_exists($altUtilsPath)) {
        require_once $altUtilsPath;
    } else {
        die("Impossible de trouver le fichier response.php.");
    }
}

// Désactiver l'affichage des erreurs pour éviter de renvoyer du HTML
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Démarrer la session si elle n'est pas déjà démarrée
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Définir le type de contenu comme JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

/**
 * API pour récupérer les PDFs de notes en fonction des filtres
 *
 * @param int|null $teacherId ID de l'enseignant (optionnel)
 * @param int|null $moduleId ID du module (optionnel)
 * @param int|null $levelId ID du niveau (optionnel)
 * @param int|null $semesterId ID du semestre (optionnel)
 * @param string|null $session Session (optionnel)
 */
function getPdfGradesAPI($teacherId = null, $moduleId = null, $levelId = null, $semesterId = null, $session = null) {
    // Log des paramètres reçus
    error_log("getPdfGradesAPI - Paramètres reçus: teacherId=$teacherId, moduleId=$moduleId, levelId=$levelId, semesterId=$semesterId, session=$session");

    // Récupérer l'ID de la filière depuis la session si disponible
    $filiereId = isset($_SESSION['user']['filiere_id']) ? $_SESSION['user']['filiere_id'] : null;

    // Si l'ID de la filière n'est pas disponible, utiliser une valeur par défaut
    if (!$filiereId) {
        error_log("getPdfGradesAPI - ID de la filière non trouvé dans la session, utilisation d'une valeur par défaut");
        $filiereId = 1; // Valeur par défaut
    }

    // Log de l'ID de la filière
    error_log("getPdfGradesAPI - ID de la filière: $filiereId");

    // Log de la session complète pour débogage
    error_log("getPdfGradesAPI - Contenu de la session: " . print_r($_SESSION, true));

    // Appeler la fonction du modèle
    $pdfGrades = getPdfGrades($filiereId, $teacherId, $moduleId, $levelId, $semesterId, $session);

    // Vérifier s'il y a une erreur
    if (isset($pdfGrades['error'])) {
        error_log("getPdfGradesAPI - Erreur: " . $pdfGrades['error']);
        jsonResponse(['error' => $pdfGrades['error'], 'success' => false], 500);
        return;
    }

    // Log du nombre de PDFs trouvés
    error_log("getPdfGradesAPI - Nombre de PDFs trouvés: " . count($pdfGrades));

    // Log des données des PDFs pour débogage
    if (count($pdfGrades) > 0) {
        error_log("getPdfGradesAPI - Premier PDF trouvé: " . print_r($pdfGrades[0], true));
    }

    // Renvoyer les PDFs
    jsonResponse(['data' => $pdfGrades, 'success' => true], 200);
}

// Fonction pour vérifier et créer le dossier uploads/pdfs s'il n'existe pas
function ensureUploadDirectoryExists() {
    $uploadsDir = __DIR__ . "/../uploads";
    $pdfsDir = $uploadsDir . "/pdfs";

    // Vérifier si le dossier uploads existe, sinon le créer
    if (!file_exists($uploadsDir)) {
        error_log("Création du dossier uploads: " . $uploadsDir);
        if (!mkdir($uploadsDir, 0777, true)) {
            error_log("Échec de la création du dossier uploads: " . $uploadsDir);
            return false;
        }
    }

    // Vérifier si le dossier pdfs existe, sinon le créer
    if (!file_exists($pdfsDir)) {
        error_log("Création du dossier pdfs: " . $pdfsDir);
        if (!mkdir($pdfsDir, 0777, true)) {
            error_log("Échec de la création du dossier pdfs: " . $pdfsDir);
            return false;
        }
    }

    // Vérifier les permissions
    if (!is_writable($pdfsDir)) {
        error_log("Le dossier pdfs n'est pas accessible en écriture: " . $pdfsDir);
        chmod($pdfsDir, 0777);
    }

    return true;
}

// S'assurer que le dossier uploads/pdfs existe
ensureUploadDirectoryExists();

// Traiter les requêtes
try {
    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'GET':
            // Récupérer l'action depuis les paramètres de la requête
            $action = isset($_GET['action']) ? $_GET['action'] : '';

            switch ($action) {
                case 'getPdfGrades':
                    // Récupérer les paramètres de filtrage
                    $teacherId = isset($_GET['teacherId']) ? $_GET['teacherId'] : null;
                    $moduleId = isset($_GET['moduleId']) ? $_GET['moduleId'] : null;
                    $levelId = isset($_GET['levelId']) ? $_GET['levelId'] : null;
                    $semesterId = isset($_GET['semesterId']) ? $_GET['semesterId'] : null;
                    $session = isset($_GET['session']) ? $_GET['session'] : null;

                    // Appeler l'API
                    getPdfGradesAPI($teacherId, $moduleId, $levelId, $semesterId, $session);
                    break;

                case 'checkPdfExists':
                    // Vérifier si un fichier PDF existe
                    $filePath = isset($_GET['filePath']) ? $_GET['filePath'] : '';

                    if (empty($filePath)) {
                        jsonResponse(['error' => 'Chemin du fichier non spécifié', 'success' => false], 400);
                        break;
                    }

                    // Sécuriser le chemin du fichier pour éviter les attaques de traversée de répertoire
                    $filePath = basename($filePath);
                    $fullPath = __DIR__ . "/../uploads/pdfs/" . $filePath;

                    // Vérifier si le fichier existe
                    $exists = file_exists($fullPath);

                    // Log pour débogage
                    error_log("Vérification de l'existence du fichier: $fullPath - Résultat: " . ($exists ? "Existe" : "N'existe pas"));

                    jsonResponse(['exists' => $exists, 'success' => true], 200);
                    break;

                default:
                    jsonResponse(['error' => 'Action non reconnue', 'success' => false], 400);
                    break;
            }
            break;

        default:
            jsonResponse(['error' => 'Méthode non autorisée', 'success' => false], 405);
            break;
    }
} catch (Exception $e) {
    // Log de l'erreur
    error_log("Erreur dans pdfGradesController.php: " . $e->getMessage());

    // Renvoyer une réponse JSON avec l'erreur
    jsonResponse(['error' => 'Erreur serveur: ' . $e->getMessage(), 'success' => false], 500);
}
?>
