/* Exam Schedule Table Fixes */

/* Fix for uneven column widths */
.timetable {
    width: 100%;
    table-layout: fixed; /* This ensures all columns have equal width */
}

.timetable th,
.timetable td {
    width: calc(100% / 5); /* Divide the table width by 5 (1 for day column + 4 time slots) */
    box-sizing: border-box;
    overflow: hidden;
}

/* First column (day) can be slightly narrower */
.timetable th:first-child,
.timetable td:first-child {
    width: 120px;
}

/* Style for day slots */
.day-slot {
    font-weight: 600;
    color: #343a40;
    background: linear-gradient(to right, #f8f9fa, #e9ecef);
    border-right: 2px solid #dee2e6;
    position: relative;
}

.day-slot:after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 2px;
    background: linear-gradient(to bottom, #4361ee, #3a56d4);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.day-slot:hover:after {
    transform: scaleY(1);
}

/* Style for time headers */
.time-header {
    background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    font-weight: 600;
    color: #343a40;
    padding: 14px 14px 5px 14px;
    border-bottom: 2px solid #dee2e6;
    position: relative;
}

/* Make sure content doesn't overflow */
.class-card {
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
}

/* Ensure consistent cell heights */
.empty-cell,
.session-cell {
    height: 120px; /* Fixed height for all cells */
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .timetable th,
    .timetable td {
        padding: 8px;
    }

    .class-card {
        padding: 8px;
        min-height: 100px;
    }

    .empty-cell,
    .session-cell {
        height: 100px;
    }
}

/* Ensure day dates are displayed consistently */
.day-date {
    display: block;
    height: 20px;
    line-height: 20px;
    font-size: 0.8rem;
    font-weight: normal;
    color: #6c757d;
    margin-top: 5px;
    padding-top: 3px;
    border-top: 1px dashed #dee2e6;
}