<?php
require_once "../controller/niveauController.php";

header('Content-Type: application/json');

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        if (isset($_GET['id'])) {
            getNiveauByIdAPI($_GET['id']);
        } else if (isset($_GET['id_filiere'])) {
            getNiveauxByFiliereAPI($_GET['id_filiere']);
        } else if (isset($_GET['cycle_id'])) {
            getNiveauxByCycleAPI($_GET['cycle_id']);
        } else {
            getAllNiveauxAPI();
        }
        break;
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        break;
}