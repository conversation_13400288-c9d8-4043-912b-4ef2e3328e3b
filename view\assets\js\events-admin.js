/**
 * Script pour la gestion des événements (admin)
 */

document.addEventListener('DOMContentLoaded', function() {
    // Variables globales
    let allEvents = [];
    let currentPage = 1;
    const eventsPerPage = 10;
    let filteredEvents = [];
    let selectedImages = [];
    let currentEventId = null;

    /**
     * Fonction d'aide pour comparer les ID d'événements
     * @param {string|number} id1 - Premier ID à comparer
     * @param {string|number} id2 - Deuxième ID à comparer
     * @returns {boolean} - True si les ID sont égaux, false sinon
     */
    function compareEventIds(id1, id2) {
        // Convertir les deux ID en nombres pour la comparaison
        return parseInt(id1, 10) === parseInt(id2, 10);
    }

    // Éléments DOM
    const eventsTableBody = document.getElementById('eventsTableBody');
    const eventsPagination = document.getElementById('eventsPagination');
    const displayedCountEl = document.getElementById('displayedCount');
    const totalCountEl = document.getElementById('totalCount');
    const categoryFilterEl = document.getElementById('categoryFilter');
    const dateFilterEl = document.getElementById('dateFilter');
    const applyFiltersBtn = document.getElementById('applyFiltersBtn');
    const addEventBtn = document.getElementById('addEventBtn');
    const eventForm = document.getElementById('eventForm');
    const saveEventBtn = document.getElementById('saveEventBtn');
    const eventModal = new bootstrap.Modal(document.getElementById('eventModal'));
    const deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    const viewEventModal = new bootstrap.Modal(document.getElementById('viewEventModal'));
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    const addImageBtn = document.getElementById('addImageBtn');
    const imageFileInput = document.getElementById('imageFile');
    const imagesListContainer = document.getElementById('imagesList');
    const editEventBtn = document.getElementById('editEventBtn');

    // Initialiser Flatpickr pour les sélecteurs de date/heure
    flatpickr('#dateFilter', {
        locale: 'fr',
        dateFormat: 'Y-m-d',
        allowInput: true,
        altInput: true,
        altFormat: 'j F Y',
        placeholder: 'Sélectionner une date'
    });

    flatpickr('#eventDate', {
        locale: 'fr',
        dateFormat: 'Y-m-d',
        allowInput: true,
        altInput: true,
        altFormat: 'j F Y',
        placeholder: 'Sélectionner une date'
    });

    flatpickr('#eventTime', {
        locale: 'fr',
        enableTime: true,
        noCalendar: true,
        dateFormat: 'H:i',
        time_24hr: true,
        allowInput: true,
        placeholder: 'Sélectionner une heure'
    });

    // Charger les événements au chargement de la page
    fetchEvents();

    // Gestionnaires d'événements
    addEventBtn.addEventListener('click', openAddEventModal);
    saveEventBtn.addEventListener('click', saveEvent);
    applyFiltersBtn.addEventListener('click', applyFilters);
    confirmDeleteBtn.addEventListener('click', deleteEvent);
    addImageBtn.addEventListener('click', addImage);
    editEventBtn.addEventListener('click', function() {
        viewEventModal.hide();
        openEditEventModal(currentEventId);
    });

    // Réinitialiser les filtres
    categoryFilterEl.value = '';
    dateFilterEl.value = '';

    // Initialiser les tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    /**
     * Récupère tous les événements depuis l'API
     */
    function fetchEvents() {
        // Afficher un message de chargement
        eventsTableBody.innerHTML = '<tr><td colspan="6" class="text-center">Chargement des événements...</td></tr>';

        fetch('../../route/eventsRoute.php')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erreur lors de la récupération des événements');
                }
                return response.json();
            })
            .then(data => {
                if (data.success && Array.isArray(data.data)) {
                    allEvents = data.data;
                    filteredEvents = [...allEvents];

                    // Trier les événements par date (du plus récent au plus ancien)
                    filteredEvents.sort((a, b) => {
                        const dateA = new Date(a.event_date + ' ' + a.event_time);
                        const dateB = new Date(b.event_date + ' ' + b.event_time);
                        return dateB - dateA;
                    });

                    updateEventsTable();
                } else {
                    eventsTableBody.innerHTML = '<tr><td colspan="6" class="text-center">Aucun événement trouvé</td></tr>';
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                eventsTableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Erreur lors du chargement des événements</td></tr>';
            });
    }

    /**
     * Met à jour le tableau des événements avec les événements filtrés et paginés
     */
    function updateEventsTable() {
        // Calculer les indices de début et de fin pour la pagination
        const startIndex = (currentPage - 1) * eventsPerPage;
        const endIndex = Math.min(startIndex + eventsPerPage, filteredEvents.length);

        // Mettre à jour les compteurs
        displayedCountEl.textContent = filteredEvents.length > 0 ? `${startIndex + 1}-${endIndex}` : '0';
        totalCountEl.textContent = filteredEvents.length;

        // Vider le tableau
        eventsTableBody.innerHTML = '';

        // Si aucun événement n'est trouvé
        if (filteredEvents.length === 0) {
            eventsTableBody.innerHTML = '<tr><td colspan="6" class="text-center">Aucun événement trouvé</td></tr>';
            eventsPagination.innerHTML = '';
            return;
        }

        // Ajouter les événements paginés au tableau
        const eventsToShow = filteredEvents.slice(startIndex, endIndex);

        eventsToShow.forEach(event => {
            const row = document.createElement('tr');
            row.className = 'fade-in';

            // Formater la date et l'heure
            const date = new Date(event.event_date);
            const formattedDate = date.toLocaleDateString('fr-FR', {
                day: '2-digit',
                month: 'long',
                year: 'numeric'
            });

            row.innerHTML = `
                <td>${event.title}</td>
                <td><span class="badge bg-info">${event.category}</span></td>
                <td>${formattedDate}</td>
                <td>${event.event_time}</td>
                <td>${event.location}</td>
                <td>
                    <button type="button" class="btn action-btn view-btn" data-id="${event.id_event}" title="Voir">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button type="button" class="btn action-btn edit-btn" data-id="${event.id_event}" title="Modifier">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn action-btn delete-btn" data-id="${event.id_event}" data-title="${event.title}" title="Supprimer">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;

            eventsTableBody.appendChild(row);
        });

        // Ajouter les gestionnaires d'événements pour les boutons d'action
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const eventId = this.getAttribute('data-id');
                openViewEventModal(eventId);
            });
        });

        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const eventId = this.getAttribute('data-id');
                openEditEventModal(eventId);
            });
        });

        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const eventId = this.getAttribute('data-id');
                const eventTitle = this.getAttribute('data-title');
                openDeleteConfirmModal(eventId, eventTitle);
            });
        });

        // Mettre à jour la pagination
        updatePagination();
    }

    /**
     * Met à jour la pagination
     */
    function updatePagination() {
        const totalPages = Math.ceil(filteredEvents.length / eventsPerPage);

        // Vider la pagination
        eventsPagination.innerHTML = '';

        // Si moins de 2 pages, ne pas afficher la pagination
        if (totalPages <= 1) {
            return;
        }

        // Ajouter le bouton "Précédent"
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
        prevLi.innerHTML = `<a class="page-link" href="#" aria-label="Précédent"><span aria-hidden="true">&laquo;</span></a>`;
        prevLi.addEventListener('click', function(e) {
            e.preventDefault();
            if (currentPage > 1) {
                currentPage--;
                updateEventsTable();
            }
        });
        eventsPagination.appendChild(prevLi);

        // Déterminer les pages à afficher
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, startPage + 4);

        if (endPage - startPage < 4) {
            startPage = Math.max(1, endPage - 4);
        }

        // Ajouter les boutons de page
        for (let i = startPage; i <= endPage; i++) {
            const pageLi = document.createElement('li');
            pageLi.className = `page-item ${i === currentPage ? 'active' : ''}`;
            pageLi.innerHTML = `<a class="page-link" href="#">${i}</a>`;
            pageLi.addEventListener('click', function(e) {
                e.preventDefault();
                currentPage = i;
                updateEventsTable();
            });
            eventsPagination.appendChild(pageLi);
        }

        // Ajouter le bouton "Suivant"
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
        nextLi.innerHTML = `<a class="page-link" href="#" aria-label="Suivant"><span aria-hidden="true">&raquo;</span></a>`;
        nextLi.addEventListener('click', function(e) {
            e.preventDefault();
            if (currentPage < totalPages) {
                currentPage++;
                updateEventsTable();
            }
        });
        eventsPagination.appendChild(nextLi);
    }

    /**
     * Applique les filtres sélectionnés
     */
    function applyFilters() {
        const categoryFilter = categoryFilterEl.value;
        const dateFilter = dateFilterEl.value;

        // Réinitialiser les événements filtrés
        filteredEvents = [...allEvents];

        // Appliquer le filtre de catégorie
        if (categoryFilter) {
            filteredEvents = filteredEvents.filter(event => event.category === categoryFilter);
        }

        // Appliquer le filtre de date
        if (dateFilter) {
            filteredEvents = filteredEvents.filter(event => event.event_date === dateFilter);
        }

        // Réinitialiser la pagination
        currentPage = 1;

        // Mettre à jour le tableau
        updateEventsTable();
    }

    /**
     * Ouvre le modal pour ajouter un événement
     */
    function openAddEventModal() {
        // Réinitialiser le formulaire
        eventForm.reset();
        document.getElementById('eventId').value = '';
        document.getElementById('eventModalLabel').textContent = 'Ajouter un événement';

        // Réinitialiser les images
        selectedImages = [];
        updateImagesList();

        // Ouvrir le modal
        eventModal.show();
    }

    /**
     * Ouvre le modal pour modifier un événement
     * @param {string} eventId - ID de l'événement à modifier
     */
    function openEditEventModal(eventId) {
        // Afficher des informations de débogage
        console.log('ID de l\'événement à modifier:', eventId, 'Type:', typeof eventId);
        console.log('Tous les événements:', allEvents);

        // Trouver l'événement en utilisant la fonction d'aide
        const event = allEvents.find(e => compareEventIds(e.id_event, eventId));

        if (!event) {
            console.error('Événement non trouvé');
            alert('Événement non trouvé. ID: ' + eventId);
            return;
        }

        // Remplir le formulaire
        document.getElementById('eventId').value = event.id_event;
        document.getElementById('eventTitle').value = event.title;
        document.getElementById('eventCategory').value = event.category;
        document.getElementById('eventDate').value = event.event_date;
        document.getElementById('eventTime').value = event.event_time;
        document.getElementById('eventLocation').value = event.location;
        document.getElementById('eventDescription').value = event.description;
        document.getElementById('sendNotification').checked = event.send_notification === '1';

        // Mettre à jour le titre du modal
        document.getElementById('eventModalLabel').textContent = 'Modifier l\'événement';

        // Mettre à jour les images
        selectedImages = Array.isArray(event.images) ? [...event.images] : [];
        updateImagesList();

        // Ouvrir le modal
        eventModal.show();
    }

    /**
     * Ouvre le modal pour visualiser un événement
     * @param {string} eventId - ID de l'événement à visualiser
     */
    function openViewEventModal(eventId) {
        // Mettre à jour l'ID de l'événement courant
        currentEventId = eventId;

        // Trouver l'événement en utilisant la fonction d'aide
        const event = allEvents.find(e => compareEventIds(e.id_event, eventId));

        if (!event) {
            console.error('Événement non trouvé');
            alert('Événement non trouvé. ID: ' + eventId);
            return;
        }

        // Formater la date
        const date = new Date(event.event_date);
        const formattedDate = date.toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: 'long',
            year: 'numeric'
        });

        // Remplir le modal
        document.getElementById('viewEventTitle').textContent = event.title;
        document.getElementById('viewEventCategory').textContent = event.category;
        document.getElementById('viewEventDate').textContent = formattedDate;
        document.getElementById('viewEventTime').textContent = event.event_time;
        document.getElementById('viewEventLocation').textContent = event.location;
        document.getElementById('viewEventDescription').textContent = event.description;

        // Afficher les images
        const imagesContainer = document.querySelector('#viewEventImages .event-images');
        imagesContainer.innerHTML = '';

        if (Array.isArray(event.images) && event.images.length > 0) {
            event.images.forEach(imagePath => {
                const img = document.createElement('img');
                // Vérifier si le chemin est une URL complète ou un chemin relatif
                if (imagePath.startsWith('http')) {
                    img.src = imagePath;
                } else {
                    // Chemin relatif (fichier uploadé)
                    img.src = '../../' + imagePath;
                }
                img.alt = 'Image de l\'événement';
                img.className = 'img-fluid';
                imagesContainer.appendChild(img);
            });
            document.getElementById('viewEventImages').style.display = 'block';
        } else {
            document.getElementById('viewEventImages').style.display = 'none';
        }

        // Ouvrir le modal
        viewEventModal.show();
    }

    /**
     * Ouvre le modal de confirmation de suppression
     * @param {string} eventId - ID de l'événement à supprimer
     * @param {string} eventTitle - Titre de l'événement à supprimer
     */
    function openDeleteConfirmModal(eventId, eventTitle) {
        // Mettre à jour l'ID de l'événement courant
        currentEventId = eventId;

        // Mettre à jour le titre de l'événement
        document.getElementById('deleteEventTitle').textContent = eventTitle;

        // Ouvrir le modal
        deleteConfirmModal.show();
    }

    /**
     * Enregistre un événement (création ou modification)
     */
    function saveEvent() {
        // Récupérer les données du formulaire
        const eventId = document.getElementById('eventId').value;
        const title = document.getElementById('eventTitle').value;
        const category = document.getElementById('eventCategory').value;
        const eventDate = document.getElementById('eventDate').value;
        const eventTime = document.getElementById('eventTime').value;
        const location = document.getElementById('eventLocation').value;
        const description = document.getElementById('eventDescription').value;
        const sendNotification = document.getElementById('sendNotification').checked ? 1 : 0;

        // Valider les données
        if (!title || !category || !eventDate || !eventTime || !location || !description) {
            alert('Veuillez remplir tous les champs obligatoires');
            return;
        }

        // Préparer les données
        const eventData = {
            title,
            category,
            event_date: eventDate,
            event_time: eventTime,
            location,
            description,
            send_notification: sendNotification,
            images: selectedImages
        };

        // Déterminer si c'est une création ou une modification
        const isEdit = eventId !== '';

        // URL et méthode
        const url = isEdit ? `../../route/eventsRoute.php?id=${eventId}` : '../../route/eventsRoute.php';
        const method = isEdit ? 'PUT' : 'POST';

        // Envoyer la requête
        fetch(url, {
            method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(eventData)
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erreur lors de l\'enregistrement de l\'événement');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Fermer le modal
                    eventModal.hide();

                    // Rafraîchir les événements
                    fetchEvents();

                    // Afficher un message de succès
                    const successMessage = isEdit ? 'Événement modifié avec succès' : 'Événement créé avec succès';

                    // Ajouter un message concernant la notification si elle est activée
                    if (sendNotification) {
                        alert(successMessage + '\nUne notification a été envoyée pour annoncer cet événement.');
                    } else {
                        alert(successMessage);
                    }
                } else {
                    alert(data.message || 'Une erreur est survenue');
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                alert('Une erreur est survenue lors de l\'enregistrement de l\'événement');
            });
    }

    /**
     * Supprime un événement
     */
    function deleteEvent() {
        // Vérifier si un événement est sélectionné
        if (!currentEventId) {
            console.error('Aucun événement sélectionné');
            return;
        }

        // Envoyer la requête
        fetch(`../../route/eventsRoute.php?id=${currentEventId}`, {
            method: 'DELETE'
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erreur lors de la suppression de l\'événement');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Fermer le modal
                    deleteConfirmModal.hide();

                    // Rafraîchir les événements
                    fetchEvents();

                    // Afficher un message de succès
                    alert('Événement supprimé avec succès');
                } else {
                    alert(data.message || 'Une erreur est survenue');
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                alert('Une erreur est survenue lors de la suppression de l\'événement');
            });
    }

    /**
     * Ajoute une image à la liste des images sélectionnées
     */
    function addImage() {
        const imageFileInput = document.getElementById('imageFile');

        if (!imageFileInput.files || !imageFileInput.files[0]) {
            alert('Veuillez sélectionner une image');
            return;
        }

        // Créer un objet FormData pour l'upload
        const formData = new FormData();
        formData.append('image', imageFileInput.files[0]);

        // Afficher un indicateur de chargement
        const loadingItem = document.createElement('div');
        loadingItem.className = 'image-preview loading';
        loadingItem.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Chargement...</span></div>';
        imagesListContainer.appendChild(loadingItem);

        // Envoyer la requête d'upload
        fetch('../../route/uploadImageRoute.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Erreur lors de l\'upload de l\'image');
            }
            return response.json();
        })
        .then(data => {
            // Supprimer l'indicateur de chargement
            imagesListContainer.removeChild(loadingItem);

            if (data.success) {
                // Ajouter l'image à la liste
                selectedImages.push(data.filePath);

                // Mettre à jour la liste des images
                updateImagesList();

                // Réinitialiser le champ
                imageFileInput.value = '';
            } else {
                alert(data.message || 'Erreur lors de l\'upload de l\'image');
            }
        })
        .catch(error => {
            // Supprimer l'indicateur de chargement
            if (loadingItem.parentNode) {
                imagesListContainer.removeChild(loadingItem);
            }

            console.error('Erreur:', error);
            alert('Erreur lors de l\'upload de l\'image');

            // Réinitialiser le champ
            imageFileInput.value = '';
        });
    }

    /**
     * Met à jour la liste des images sélectionnées
     */
    function updateImagesList() {
        // Vider la liste
        imagesListContainer.innerHTML = '';

        // Ajouter les images
        selectedImages.forEach((imagePath, index) => {
            const imagePreview = document.createElement('div');
            imagePreview.className = 'image-preview';

            const img = document.createElement('img');
            // Vérifier si le chemin est une URL complète ou un chemin relatif
            if (imagePath.startsWith('http')) {
                img.src = imagePath;
            } else {
                // Chemin relatif (fichier uploadé)
                img.src = '../../' + imagePath;
            }
            img.alt = 'Image de l\'événement';

            const removeBtn = document.createElement('div');
            removeBtn.className = 'remove-image';
            removeBtn.innerHTML = '<i class="fas fa-times"></i>';
            removeBtn.addEventListener('click', function() {
                selectedImages.splice(index, 1);
                updateImagesList();
            });

            imagePreview.appendChild(img);
            imagePreview.appendChild(removeBtn);
            imagesListContainer.appendChild(imagePreview);
        });
    }
});
