<?php
require_once __DIR__ . '/../utils/response.php';

// Vérifier si la requête est de type POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => 'Méthode non autorisée'], 405);
    exit;
}

// Vérifier si un fichier a été uploadé
if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
    $error = isset($_FILES['image']) ? $_FILES['image']['error'] : 'Aucun fichier uploadé';
    jsonResponse(['success' => false, 'message' => 'Erreur lors de l\'upload du fichier', 'error' => $error], 400);
    exit;
}

// Vérifier le type de fichier
$allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
$fileType = $_FILES['image']['type'];

if (!in_array($fileType, $allowedTypes)) {
    jsonResponse(['success' => false, 'message' => 'Type de fichier non autorisé. Seuls les formats JPG, PNG et GIF sont acceptés.'], 400);
    exit;
}

// Vérifier la taille du fichier (5 MB max)
$maxFileSize = 5 * 1024 * 1024; // 5 MB en octets
if ($_FILES['image']['size'] > $maxFileSize) {
    jsonResponse(['success' => false, 'message' => 'Le fichier est trop volumineux. Taille maximale: 5 MB.'], 400);
    exit;
}

// Créer le dossier de destination s'il n'existe pas
$uploadDir = __DIR__ . '/../uploads/events/';
if (!is_dir($uploadDir)) {
    mkdir($uploadDir, 0777, true);
}

// Générer un nom de fichier unique
$fileName = uniqid() . '_' . basename($_FILES['image']['name']);
$filePath = $uploadDir . $fileName;

// Déplacer le fichier uploadé vers le dossier de destination
if (move_uploaded_file($_FILES['image']['tmp_name'], $filePath)) {
    // Retourner le chemin relatif du fichier
    $relativePath = 'uploads/events/' . $fileName;
    jsonResponse(['success' => true, 'message' => 'Image uploadée avec succès', 'filePath' => $relativePath]);
} else {
    jsonResponse(['success' => false, 'message' => 'Erreur lors de l\'enregistrement du fichier'], 500);
}
?>
