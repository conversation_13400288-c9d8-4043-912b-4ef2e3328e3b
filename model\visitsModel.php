<?php
require_once __DIR__ . '/../config/db.php';

/**
 * Enregistre une visite pour la date actuelle
 *
 * @param string $userType Type d'utilisateur (admin, student, professor, anonymous)
 * @param string $pageVisited Page visitée
 * @return bool Succès de l'opération
 */
function recordVisit($userType = 'anonymous', $pageVisited = 'dashboard') {
    try {
        $conn = getConnection();
        $today = date('Y-m-d');

        // Vérifier si une entrée existe déjà pour aujourd'hui
        $stmt = $conn->prepare("SELECT id, visit_count FROM visits WHERE visit_date = ? AND user_type = ? AND page_visited = ?");
        $stmt->bind_param("sss", $today, $userType, $pageVisited);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            // Mettre à jour le compteur existant
            $row = $result->fetch_assoc();
            $newCount = $row['visit_count'] + 1;
            $id = $row['id'];

            $updateStmt = $conn->prepare("UPDATE visits SET visit_count = ? WHERE id = ?");
            $updateStmt->bind_param("ii", $newCount, $id);
            return $updateStmt->execute();
        } else {
            // Créer une nouvelle entrée
            $insertStmt = $conn->prepare("INSERT INTO visits (visit_date, visit_count, user_type, page_visited) VALUES (?, 1, ?, ?)");
            $insertStmt->bind_param("sss", $today, $userType, $pageVisited);
            return $insertStmt->execute();
        }
    } catch (Exception $e) {
        error_log("Exception dans recordVisit: " . $e->getMessage());
        return false;
    }
}

/**
 * Récupère les statistiques de visite pour les derniers jours
 *
 * @param int $days Nombre de jours à récupérer
 * @param string $userType Type d'utilisateur (all pour tous)
 * @param string $pageVisited Page visitée (all pour toutes)
 * @return array Données de visite
 */
function getVisitStats($days = 30, $userType = 'all', $pageVisited = 'all') {
    try {
        // Ajouter des logs pour le débogage
        error_log("getVisitStats - Paramètres: days=$days, userType=$userType, pageVisited=$pageVisited");

        $conn = getConnection();
        $query = "SELECT visit_date, SUM(visit_count) as total_visits FROM visits WHERE 1=1";
        $params = [];
        $types = "";

        // Ajouter la condition de date
        $startDate = date('Y-m-d', strtotime("-$days days"));
        $query .= " AND visit_date >= ?";
        $params[] = $startDate;
        $types .= "s";

        // Ajouter la condition de type d'utilisateur si nécessaire
        if ($userType !== 'all') {
            $query .= " AND user_type = ?";
            $params[] = $userType;
            $types .= "s";
        }

        // Ajouter la condition de page visitée si nécessaire
        if ($pageVisited !== 'all') {
            $query .= " AND page_visited = ?";
            $params[] = $pageVisited;
            $types .= "s";
        }

        // Grouper par date et trier
        $query .= " GROUP BY visit_date ORDER BY visit_date ASC";

        // Ajouter des logs pour le débogage
        error_log("getVisitStats - Requête SQL: $query");
        error_log("getVisitStats - Paramètres: " . json_encode($params));

        $stmt = $conn->prepare($query);

        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }

        $stmt->execute();
        $result = $stmt->get_result();

        $visitData = [];
        while ($row = $result->fetch_assoc()) {
            $visitData[] = $row;
        }

        // Ajouter des logs pour le débogage
        error_log("getVisitStats - Nombre de résultats: " . count($visitData));

        return $visitData;
    } catch (Exception $e) {
        error_log("Exception dans getVisitStats: " . $e->getMessage());
        return [];
    }
}

/**
 * Récupère les statistiques globales (total des visites, moyenne par jour, etc.)
 *
 * @return array Statistiques globales
 */
function getGlobalStats() {
    try {
        $conn = getConnection();

        // Total des visites
        $totalQuery = "SELECT SUM(visit_count) as total FROM visits";
        $totalResult = $conn->query($totalQuery);
        $totalRow = $totalResult->fetch_assoc();
        $total = $totalRow['total'] ?? 0;

        // Nombre d'étudiants
        $studentsQuery = "SELECT COUNT(*) as count FROM student";
        $studentsResult = $conn->query($studentsQuery);
        $studentsRow = $studentsResult->fetch_assoc();
        $students = $studentsRow['count'] ?? 0;

        // Nombre de professeurs
        $professorsQuery = "SELECT COUNT(*) as count FROM professor";
        $professorsResult = $conn->query($professorsQuery);
        $professorsRow = $professorsResult->fetch_assoc();
        $professors = $professorsRow['count'] ?? 0;

        // Nombre de filières
        $departmentsQuery = "SELECT COUNT(*) as count FROM filiere";
        $departmentsResult = $conn->query($departmentsQuery);
        $departmentsRow = $departmentsResult->fetch_assoc();
        $departments = $departmentsRow['count'] ?? 0;

        return [
            'total_visits' => $total,
            'students' => $students,
            'professors' => $professors,
            'departments' => $departments
        ];
    } catch (Exception $e) {
        error_log("Exception dans getGlobalStats: " . $e->getMessage());
        return [
            'total_visits' => 0,
            'students' => 0,
            'professors' => 0,
            'departments' => 0
        ];
    }
}
?>
