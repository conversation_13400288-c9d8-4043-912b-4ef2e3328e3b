<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UniAdmin - Tableau de Bord</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard-style.css">
    <link rel="stylesheet" href="../assets/css/header-fix.css">
</head>
<body>
    <div class="dashboard-container">
        <?php
        include '../includes/sidebar.php';

        // Inclure le modèle des visites et enregistrer la visite du dashboard
        require_once '../../model/visitsModel.php';
        recordVisit('admin', 'dashboard');
        ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>
            <div class="container-fluid p-4">
                <h1 class="page-title">Tableau de Bord</h1>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="text-muted">Bienvenue dans votre espace d'administration. Voici un aperçu des activités récentes.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <div class="d-inline-block me-2">
                            <span class="badge bg-primary rounded-pill">
                                <?php echo date('d M Y'); ?>
                            </span>
                        </div>
                        <div class="d-inline-block me-2">
                            <span class="badge bg-info rounded-pill">
                                <i class="fas fa-clock me-1"></i> <span id="current-time"></span>
                            </span>
                        </div>
                        <div class="d-inline-block">
                            <span class="badge bg-secondary rounded-pill">
                                <i class="fas fa-user me-1"></i> Admin
                            </span>
                        </div>
                    </div>
                </div>

                <div class="row g-4">
                    <!-- Carte des Événements Récents -->
                    <div class="col-md-6 col-lg-4 animate-fade-in delay-1">
                        <a href="events.php" class="text-decoration-none">
                            <div class="card dashboard-card card-events h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="card-icon icon-events">
                                            <i class="fas fa-calendar-alt"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Événements Récents</h5>
                                            <div class="small text-muted">3 derniers événements</div>
                                        </div>
                                    </div>
                                    <ul class="dashboard-list" id="recent-events-list">
                                        <?php
                                        // Inclure la configuration de la base de données
                                        require_once '../../config/db.php';

                                        try {
                                            // Connexion à la base de données
                                            $conn = getConnection();

                                            // Vérifier si la connexion est établie
                                            if ($conn->connect_error) {
                                                echo '<li><span>Erreur de connexion à la base de données</span><span class="date-badge">Erreur</span></li>';
                                                echo '<li><span>Conférence sur l\'IA</span><span class="date-badge">15 déc</span></li>';
                                                echo '<li><span>Journée Portes Ouvertes</span><span class="date-badge">20 déc</span></li>';
                                                echo '<li><span>Remise des Diplômes</span><span class="date-badge">25 déc</span></li>';
                                                return;
                                            }

                                            // Vérifier si la table events existe
                                            $checkTable = $conn->query("SHOW TABLES LIKE 'events'");
                                            if ($checkTable->num_rows == 0) {
                                                // La table n'existe pas, afficher un message et les données statiques
                                                echo '<li><span>La table events n\'existe pas</span><span class="date-badge">Info</span></li>';
                                                echo '<li><span>Conférence sur l\'IA</span><span class="date-badge">15 déc</span></li>';
                                                echo '<li><span>Journée Portes Ouvertes</span><span class="date-badge">20 déc</span></li>';
                                                echo '<li><span>Remise des Diplômes</span><span class="date-badge">25 déc</span></li>';
                                                $conn->close();
                                                return;
                                            }

                                            // Requête pour récupérer les événements
                                            $query = "SELECT * FROM events ORDER BY event_date, event_time LIMIT 3";
                                            $result = $conn->query($query);

                                            if ($result && $result->num_rows > 0) {
                                                // Afficher les événements
                                                while ($row = $result->fetch_assoc()) {
                                                    $date = new DateTime($row['event_date']);
                                                    $formattedDate = $date->format('d M');
                                                    $category = !empty($row['category']) ? $row['category'] : 'Événement';
                                                    echo '<li>';
                                                    echo '<span><i class="fas fa-dot-circle me-2 text-primary"></i>' . htmlspecialchars($row['title']) . '</span>';
                                                    echo '<span class="date-badge">' . $formattedDate . '</span>';
                                                    echo '</li>';
                                                }
                                            } else {
                                                // Si aucun événement n'est trouvé, afficher un message
                                                echo '<li><span>Aucun événement à venir</span><span class="date-badge">Info</span></li>';
                                            }

                                            // Fermer la connexion
                                            $conn->close();
                                        } catch (Exception $e) {
                                            // En cas d'erreur, afficher un message d'erreur
                                            echo '<li><span>Erreur lors de la récupération des événements</span><span class="date-badge">Erreur</span></li>';
                                            // Afficher les événements statiques comme solution de secours
                                            echo '<li><span>Conférence sur l\'IA</span><span class="date-badge">15 déc</span></li>';
                                            echo '<li><span>Journée Portes Ouvertes</span><span class="date-badge">20 déc</span></li>';
                                            echo '<li><span>Remise des Diplômes</span><span class="date-badge">25 déc</span></li>';
                                        }
                                        ?>
                                    </ul>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte des Notifications -->
                    <div class="col-md-6 col-lg-4 animate-fade-in delay-2">
                        <a href="notifications.php" class="text-decoration-none">
                            <div class="card dashboard-card card-notifications h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="card-icon icon-notifications">
                                            <i class="fas fa-bell"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Notifications</h5>
                                            <div class="small text-muted">3 dernières notifications</div>
                                        </div>
                                    </div>
                                    <ul class="dashboard-list">
                                        <li><span><i class="fas fa-info-circle me-2 text-warning"></i>Mise à jour du système</span><span class="notification-badge">Nouveau</span></li>
                                        <li><span><i class="fas fa-info-circle me-2 text-warning"></i>Maintenance prévue</span><span class="notification-badge">Info</span></li>
                                        <li><span><i class="fas fa-info-circle me-2 text-warning"></i>Nouvelle fonctionnalité</span><span class="notification-badge">Nouveau</span></li>
                                    </ul>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte des Statistiques -->
                    <div class="col-md-6 col-lg-4 animate-fade-in delay-3">
                        <a href="statistics.php" class="text-decoration-none">
                            <div class="card dashboard-card card-stats h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="card-icon icon-stats">
                                            <i class="fas fa-chart-bar"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Statistiques</h5>
                                            <div class="small text-muted">Aperçu des données</div>
                                        </div>
                                    </div>
                                    <div class="row g-3 mt-2">
                                        <?php
                                        // Inclure le modèle des visites
                                        require_once '../../model/visitsModel.php';

                                        // Récupérer les statistiques globales
                                        $stats = getGlobalStats();
                                        ?>
                                        <div class="col-4">
                                            <div class="stat-card stat-students">
                                                <p class="stat-value"><?php echo $stats['students']; ?></p>
                                                <p class="stat-label">Étudiants</p>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-card stat-professors">
                                                <p class="stat-value"><?php echo $stats['professors']; ?></p>
                                                <p class="stat-label">Professeurs</p>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-card stat-departments">
                                                <p class="stat-value"><?php echo $stats['departments']; ?></p>
                                                <p class="stat-label">Filières</p>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Emploi du Temps -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-4">
                        <a href="schedule.php" class="text-decoration-none">
                            <div class="card dashboard-card card-schedule square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-schedule mb-3">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <h5 class="card-title">Emploi du Temps</h5>
                                    <div class="small text-muted">Gestion des horaires</div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Notes -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-5">
                        <a href="grades.php" class="text-decoration-none">
                            <div class="card dashboard-card card-grades square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-grades mb-3">
                                        <i class="fas fa-graduation-cap"></i>
                                    </div>
                                    <h5 class="card-title">Notes</h5>
                                    <div class="small text-muted">Gestion des résultats</div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Staff -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-6">
                        <a href="staff.php" class="text-decoration-none">
                            <div class="card dashboard-card card-staff square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-staff mb-3">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <h5 class="card-title">Personnel</h5>
                                    <div class="small text-muted">Gestion des employés</div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Demande -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-7">
                        <a href="request.php" class="text-decoration-none">
                            <div class="card dashboard-card card-request square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-request mb-3">
                                        <i class="fas fa-clipboard-list"></i>
                                    </div>
                                    <h5 class="card-title">Demande</h5>
                                    <div class="small text-muted">Formulaire de demande</div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Graphique des Visites -->
                    <div class="col-md-12 animate-fade-in delay-8">
                        <div class="card dashboard-card card-visits h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="card-icon icon-visits">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div>
                                        <h5 class="card-title mb-0">Statistiques de Visites</h5>
                                        <div class="small text-muted">Évolution des visites</div>
                                    </div>
                                    <div class="ms-auto">
                                        <select id="chart-type-selector" class="form-select form-select-sm">
                                            <option value="line">Ligne</option>
                                            <option value="bar">Barres</option>
                                            <option value="area">Aire</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="chart-container" style="position: relative; height: 300px;">
                                    <canvas id="visitsChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Dashboard Scripts -->
    <script src="../assets/js/dashboard-dynamic.js"></script>
    <script src="../assets/js/dashboard-chart.js"></script>
    <script src="../assets/js/dashboard-notifications.js"></script>
    <!-- Notifications JS -->
    <script src="../assets/js/notifications.js"></script>
    <script src="../assets/js/sidebar.js"></script>

    <!-- Styles déplacés vers dashboard-style.css -->
</body>
</html>