<?php
require_once __DIR__ . "/../config/db.php";

/**
 * Vérifie si la table password_reset_codes existe et la crée si nécessaire
 */
function ensurePasswordResetTable() {
    try {
        $conn = getConnection();

        if (!$conn) {
            error_log("Erreur de connexion à la base de données dans ensurePasswordResetTable");
            return false;
        }

        // Vérifier si la table existe
        $tableExists = $conn->query("SHOW TABLES LIKE 'password_reset_codes'");

        if (!$tableExists) {
            error_log("Erreur lors de la vérification de l'existence de la table: " . $conn->error);
            $conn->close();
            return false;
        }

        if ($tableExists->num_rows == 0) {
            error_log("La table password_reset_codes n'existe pas, tentative de création...");

            // Créer la table si elle n'existe pas
            $sql = "CREATE TABLE password_reset_codes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                identifier VARCHAR(50) NOT NULL,
                code VARCHAR(10) NOT NULL,
                email VARCHAR(100) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NULL,
                used TINYINT(1) DEFAULT 0
            )";

            if (!$conn->query($sql)) {
                error_log("Erreur lors de la création de la table password_reset_codes: " . $conn->error);
                $conn->close();
                return false;
            }

            error_log("Table password_reset_codes créée avec succès");
        } else {
            error_log("La table password_reset_codes existe déjà");
        }

        $conn->close();
        return true;
    } catch (Exception $e) {
        error_log("Exception dans ensurePasswordResetTable: " . $e->getMessage());
        return false;
    }
}

/**
 * Trouve un utilisateur par CNI ou CNE et retourne son email
 *
 * @param string $identifier CNI ou CNE de l'utilisateur
 * @return array Résultat de la recherche avec email ou erreur
 */
function findUserByIdentifier($identifier) {
    try {
        $conn = getConnection();

        if (!$conn) {
            error_log("Erreur de connexion à la base de données dans findUserByIdentifier");
            return ['success' => false, 'error' => 'Erreur de connexion à la base de données'];
        }

        // Nettoyer l'identifiant
        $identifier = mysqli_real_escape_string($conn, $identifier);

        error_log("Recherche de l'utilisateur avec l'identifiant: " . $identifier);

        // Chercher d'abord dans la table admin (CNI)
        $sql = "SELECT email FROM admin WHERE CNI = '$identifier'";
        $result = mysqli_query($conn, $sql);

        if (!$result) {
            error_log("Erreur lors de la recherche dans la table admin: " . mysqli_error($conn));
        } else if (mysqli_num_rows($result) > 0) {
            $user = mysqli_fetch_assoc($result);
            error_log("Utilisateur trouvé dans la table admin: " . $user['email']);
            mysqli_close($conn);
            return ['success' => true, 'email' => $user['email'], 'type' => 'admin'];
        } else {
            error_log("Aucun utilisateur trouvé dans la table admin avec cet identifiant");
        }

        // Chercher ensuite dans la table etudiant (CNE)
        $sql = "SELECT email FROM etudiant WHERE CNE = '$identifier'";
        $result = mysqli_query($conn, $sql);

        if (!$result) {
            error_log("Erreur lors de la recherche dans la table etudiant: " . mysqli_error($conn));
        } else if (mysqli_num_rows($result) > 0) {
            $user = mysqli_fetch_assoc($result);
            error_log("Utilisateur trouvé dans la table etudiant: " . $user['email']);
            mysqli_close($conn);
            return ['success' => true, 'email' => $user['email'], 'type' => 'etudiant'];
        } else {
            error_log("Aucun utilisateur trouvé dans la table etudiant avec cet identifiant");
        }

        // Chercher dans la table enseignant (CNI)
        $sql = "SELECT email FROM enseignant WHERE CNI = '$identifier'";
        $result = mysqli_query($conn, $sql);

        if (!$result) {
            error_log("Erreur lors de la recherche dans la table enseignant: " . mysqli_error($conn));
        } else if (mysqli_num_rows($result) > 0) {
            $user = mysqli_fetch_assoc($result);
            error_log("Utilisateur trouvé dans la table enseignant: " . $user['email']);
            mysqli_close($conn);
            return ['success' => true, 'email' => $user['email'], 'type' => 'enseignant'];
        } else {
            error_log("Aucun utilisateur trouvé dans la table enseignant avec cet identifiant");
        }

        // Pour le débogage en environnement de développement, créer un utilisateur fictif
        if ($_SERVER['SERVER_NAME'] == 'localhost' || strpos($_SERVER['SERVER_NAME'], '127.0.0.1') !== false) {
            error_log("Mode développement: création d'un utilisateur fictif pour le test");
            mysqli_close($conn);
            return ['success' => true, 'email' => '<EMAIL>', 'type' => 'etudiant'];
        }

        error_log("Aucun utilisateur trouvé avec l'identifiant: " . $identifier);
        mysqli_close($conn);
        return ['success' => false, 'error' => 'Aucun utilisateur trouvé avec cet identifiant'];
    } catch (Exception $e) {
        error_log("Exception dans findUserByIdentifier: " . $e->getMessage());
        return ['success' => false, 'error' => 'Erreur lors de la recherche de l\'utilisateur'];
    }
}

/**
 * Génère un code de réinitialisation aléatoire
 *
 * @param int $length Longueur du code
 * @return string Code généré
 */
function generateResetCode($length = 6) {
    $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $code = '';

    for ($i = 0; $i < $length; $i++) {
        $code .= $characters[rand(0, strlen($characters) - 1)];
    }

    return $code;
}

/**
 * Enregistre un code de réinitialisation dans la base de données
 *
 * @param string $identifier CNI ou CNE de l'utilisateur
 * @param string $email Email de l'utilisateur
 * @param string $code Code de réinitialisation
 * @return bool Succès de l'opération
 */
function saveResetCode($identifier, $email, $code) {
    try {
        // S'assurer que la table existe
        if (!ensurePasswordResetTable()) {
            error_log("Échec de la vérification/création de la table password_reset_codes");
            return false;
        }

        $conn = getConnection();

        if (!$conn) {
            error_log("Erreur de connexion à la base de données dans saveResetCode");
            return false;
        }

        // Nettoyer les entrées
        $identifier = mysqli_real_escape_string($conn, $identifier);
        $email = mysqli_real_escape_string($conn, $email);
        $code = mysqli_real_escape_string($conn, $code);

        error_log("Enregistrement du code pour l'identifiant: " . $identifier . ", email: " . $email);

        // Définir la date d'expiration (15 minutes)
        $expiresAt = date('Y-m-d H:i:s', strtotime('+15 minutes'));

        // Désactiver les anciens codes pour cet identifiant
        $sql = "UPDATE password_reset_codes SET used = 1 WHERE identifier = '$identifier'";
        if (!mysqli_query($conn, $sql)) {
            error_log("Erreur lors de la désactivation des anciens codes: " . mysqli_error($conn));
            // Continuer malgré l'erreur, car c'est peut-être la première fois que l'utilisateur demande un code
        }

        // Insérer le nouveau code
        $sql = "INSERT INTO password_reset_codes (identifier, code, email, expires_at)
                VALUES ('$identifier', '$code', '$email', '$expiresAt')";

        $result = mysqli_query($conn, $sql);

        if (!$result) {
            error_log("Erreur lors de l'insertion du code: " . mysqli_error($conn));
        } else {
            error_log("Code enregistré avec succès pour: " . $identifier);

            // Pour le débogage, stocker le code dans la session
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }
            $_SESSION['debug_reset_code'] = $code;
            $_SESSION['debug_reset_identifier'] = $identifier;
        }

        mysqli_close($conn);
        return $result;
    } catch (Exception $e) {
        error_log("Exception dans saveResetCode: " . $e->getMessage());
        return false;
    }
}

/**
 * Vérifie si un code de réinitialisation est valide
 *
 * @param string $identifier CNI ou CNE de l'utilisateur
 * @param string $code Code de réinitialisation
 * @return array Résultat de la vérification
 */
function verifyResetCode($identifier, $code) {
    $conn = getConnection();

    // Nettoyer les entrées
    $identifier = mysqli_real_escape_string($conn, $identifier);
    $code = mysqli_real_escape_string($conn, $code);

    // Vérifier le code
    $sql = "SELECT * FROM password_reset_codes
            WHERE identifier = '$identifier'
            AND code = '$code'
            AND used = 0
            AND expires_at > NOW()
            ORDER BY created_at DESC
            LIMIT 1";

    $result = mysqli_query($conn, $sql);

    if ($result && mysqli_num_rows($result) > 0) {
        $resetCode = mysqli_fetch_assoc($result);
        mysqli_close($conn);
        return ['success' => true, 'resetCode' => $resetCode];
    }

    mysqli_close($conn);
    return ['success' => false, 'error' => 'Code invalide ou expiré'];
}

/**
 * Marque un code de réinitialisation comme utilisé
 *
 * @param int $codeId ID du code de réinitialisation
 * @return bool Succès de l'opération
 */
function markResetCodeAsUsed($codeId) {
    $conn = getConnection();

    // Nettoyer l'entrée
    $codeId = (int)$codeId;

    // Marquer le code comme utilisé
    $sql = "UPDATE password_reset_codes SET used = 1 WHERE id = $codeId";

    $result = mysqli_query($conn, $sql);

    mysqli_close($conn);
    return $result;
}

/**
 * Réinitialise le mot de passe d'un utilisateur
 *
 * @param string $identifier CNI ou CNE de l'utilisateur
 * @param string $newPassword Nouveau mot de passe
 * @return array Résultat de la réinitialisation
 */
function resetUserPassword($identifier, $newPassword) {
    $conn = getConnection();

    // Nettoyer les entrées
    $identifier = mysqli_real_escape_string($conn, $identifier);

    // Hacher le nouveau mot de passe
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

    // Trouver l'utilisateur dans la table users
    $sql = "SELECT * FROM users WHERE username = '$identifier'";
    $result = mysqli_query($conn, $sql);

    if ($result && mysqli_num_rows($result) > 0) {
        // Mettre à jour le mot de passe
        $updateSql = "UPDATE users SET password = '$hashedPassword' WHERE username = '$identifier'";

        if (mysqli_query($conn, $updateSql)) {
            mysqli_close($conn);
            return ['success' => true, 'message' => 'Mot de passe mis à jour avec succès'];
        } else {
            mysqli_close($conn);
            return ['success' => false, 'error' => 'Erreur lors de la mise à jour du mot de passe'];
        }
    }

    // Si l'utilisateur n'est pas trouvé directement, chercher dans les autres tables
    $userInfo = findUserByIdentifier($identifier);

    if (!$userInfo['success']) {
        mysqli_close($conn);
        return ['success' => false, 'error' => 'Utilisateur non trouvé'];
    }

    // Vérifier si un utilisateur existe déjà dans la table users
    $sql = "SELECT * FROM users WHERE username = '$identifier'";
    $result = mysqli_query($conn, $sql);

    if ($result && mysqli_num_rows($result) > 0) {
        // Mettre à jour le mot de passe
        $updateSql = "UPDATE users SET password = '$hashedPassword' WHERE username = '$identifier'";

        if (mysqli_query($conn, $updateSql)) {
            mysqli_close($conn);
            return ['success' => true, 'message' => 'Mot de passe mis à jour avec succès'];
        } else {
            mysqli_close($conn);
            return ['success' => false, 'error' => 'Erreur lors de la mise à jour du mot de passe'];
        }
    } else {
        // Créer un nouvel utilisateur
        $role = $userInfo['type'];
        $insertSql = "INSERT INTO users (username, password, role) VALUES ('$identifier', '$hashedPassword', '$role')";

        if (mysqli_query($conn, $insertSql)) {
            mysqli_close($conn);
            return ['success' => true, 'message' => 'Compte créé avec succès'];
        } else {
            mysqli_close($conn);
            return ['success' => false, 'error' => 'Erreur lors de la création du compte'];
        }
    }
}
?>
