<?php
require_once __DIR__ . '/../config/db.php';

/**
 * Vérifie si la table notifications existe et la crée si nécessaire
 *
 * @return bool True si la table existe ou a été créée avec succès, False sinon
 */
function ensureNotificationsTableExists() {
    $conn = getConnection();
    if (!$conn) {
        error_log("Database connection error in ensureNotificationsTableExists");
        return false;
    }

    // Vérifier si la table existe
    $checkTableQuery = "SHOW TABLES LIKE 'notifications'";
    $tableExists = mysqli_query($conn, $checkTableQuery);

    if (mysqli_num_rows($tableExists) == 0) {
        // La table n'existe pas, la créer
        $createTableQuery = "CREATE TABLE notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            media_url VARCHAR(255) NULL,
            file_path VARCHAR(255) NULL,
            type VARCHAR(50) DEFAULT 'message',
            is_read TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX (type),
            INDEX (is_read)
        )";

        if (!mysqli_query($conn, $createTableQuery)) {
            $error = mysqli_error($conn);
            error_log("Error creating notifications table: " . $error);
            mysqli_close($conn);
            return false;
        }

        error_log("Table notifications created successfully");
    }

    mysqli_close($conn);
    return true;
}

function getNotifications() {
    try {
        $conn = getConnection();
        $stmt = $conn->prepare("
            SELECT id, title, message, media_url, file_path, type, is_read, created_at
            FROM notifications
            ORDER BY created_at DESC
        ");

        if (!$stmt) {
            error_log("Erreur de préparation de la requête: " . $conn->error);
            return [];
        }

        $result = $stmt->execute();

        if (!$result) {
            error_log("Erreur d'exécution de la requête: " . $stmt->error);
            return [];
        }

        $result = $stmt->get_result();
        $notifications = [];

        while ($row = $result->fetch_assoc()) {
            $notifications[] = $row;
        }

        return $notifications;
    } catch (Exception $e) {
        error_log("Exception dans getNotifications: " . $e->getMessage());
        return [];
    }
}

function markNotificationAsRead($id) {
    $conn = getConnection();
    $stmt = $conn->prepare("
        UPDATE notifications
        SET is_read = 1
        WHERE id = ?
    ");
    $stmt->bind_param("i", $id);
    return $stmt->execute();
}

function deleteNotification($id) {
    $conn = getConnection();
    $stmt = $conn->prepare("
        DELETE FROM notifications
        WHERE id = ?
    ");
    $stmt->bind_param("i", $id);
    return $stmt->execute();
}

function markAllNotificationsAsRead() {
    $conn = getConnection();
    $stmt = $conn->prepare("
        UPDATE notifications
        SET is_read = 1
    ");
    return $stmt->execute();
}

function createNotification($title, $message, $media_url = null, $file_path = null, $type = 'message') {
    // S'assurer que la table existe
    if (!ensureNotificationsTableExists()) {
        error_log("Failed to ensure notifications table exists");
        return false;
    }

    try {
        $conn = getConnection();
        if (!$conn) {
            error_log("Database connection error in createNotification");
            return false;
        }

        // Utiliser mysqli_prepare au lieu de $conn->prepare pour plus de compatibilité
        $stmt = mysqli_prepare($conn, "
            INSERT INTO notifications (title, message, media_url, file_path, type)
            VALUES (?, ?, ?, ?, ?)
        ");

        if (!$stmt) {
            error_log("Error preparing statement: " . mysqli_error($conn));
            mysqli_close($conn);
            return false;
        }

        mysqli_stmt_bind_param($stmt, "sssss", $title, $message, $media_url, $file_path, $type);
        $result = mysqli_stmt_execute($stmt);

        if (!$result) {
            error_log("Error executing statement: " . mysqli_stmt_error($stmt));
        }

        mysqli_stmt_close($stmt);
        mysqli_close($conn);

        return $result;
    } catch (Exception $e) {
        error_log("Exception in createNotification: " . $e->getMessage());
        return false;
    }
}

function getUnreadNotificationsCount() {
    $conn = getConnection();
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count
        FROM notifications
        WHERE is_read = 0
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    return $row['count'];
}
?>