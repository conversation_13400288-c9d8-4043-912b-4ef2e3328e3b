// DOM Elements
const staffTable = document.getElementById('staffTable');
const addStaffForm = document.getElementById('addStaffForm');
const roleFilter = document.getElementById('roleFilter');
const searchInput = document.getElementById('searchInput');
const addStaffButton = document.querySelector('[data-bs-target="#addStaffModal"]');

// Reset form when Add Staff button is clicked
addStaffButton.addEventListener('click', () => {
    addStaffForm.reset();
    document.querySelector('#addStaffModal .modal-title').textContent = 'Add Staff Member';
    // Clear the hidden id field
    document.querySelector('input[name="id_staff"]').value = '';
});

// Load staff data when the page loads
document.addEventListener('DOMContentLoaded', () => {
    loadStaff();
});

// Pagination variables
let currentPage = 1;
let staffPerPage = 20;
let staffMembers = [];

// Load staff data
async function loadStaff() {
    try {
        const response = await fetch('../../route/staffRoute.php');
        if (!response.ok) throw new Error('Failed to fetch staff data');

        const data = await response.json();
        if (data.error) throw new Error(data.error);

        // Update role filter options
        updateRoleFilter(data.data);
        displayStaff(data.data);
    } catch (error) {
        console.error('Error loading staff:', error);
        showAlert('Error loading staff data', 'danger');
    }
}
 
// Update role filter and form options
function updateRoleFilter(staffData) {
    const roleSelect = document.getElementById('roleFilter');
    const roleFormSelect = document.getElementById('role');
    roleSelect.innerHTML = '<option value="">All Roles</option>';
    roleFormSelect.innerHTML = '';
    
    // Get unique roles
    const uniqueRoles = [...new Set(staffData.map(staff => staff.role).filter(role => role))];
    
    // Add role options to both selects
    uniqueRoles.forEach(role => {
        // Filter select
        const filterOption = document.createElement('option');
        filterOption.value = role;
        filterOption.textContent = role;
        roleSelect.appendChild(filterOption);

        // Form select
        const formOption = document.createElement('option');
        formOption.value = role;
        formOption.textContent = role;
        roleFormSelect.appendChild(formOption);
    });
}

// Display staff in the table with pagination
function displayStaff(staff) {
    const tbody = staffTable.querySelector('tbody');
    tbody.innerHTML = '';

    // Calculate pagination
    const totalPages = Math.max(1, Math.ceil(staff.length / staffPerPage));
    if (currentPage > totalPages) {
        currentPage = totalPages;
    }
    const start = (currentPage - 1) * staffPerPage;
    const end = start + staffPerPage;
    const paginatedStaff = staff.slice(start, end);

    // Display paginated staff
    paginatedStaff.forEach(member => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${member.CNI || ''}</td>
            <td>${member.nom || ''}</td>
            <td>${member.prenom || ''}</td>
            <td>${member.phone || ''}</td>
            <td>${member.sexe || ''}</td>
            <td>${member.role || ''}</td>
            <td class="student-actions">
                <button class="btn btn-sm btn-primary" onclick="editStaff(${member.id_staff})">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteStaff(${member.id_staff})">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });

    // Update pagination controls
    updatePaginationControls(currentPage, totalPages, staff.length);
}

// Update pagination controls
function updatePaginationControls(current, total, totalStaff) {
    if (total === 0) return;

    const paginationContainer = document.querySelector('.card.student-table .card-body');
    const paginationDiv = document.createElement('div');
    paginationDiv.className = 'pagination-controls d-flex flex-wrap justify-content-between align-items-center mt-3 gap-3';
    paginationDiv.style.borderTop = '1px solid #dee2e6';
    paginationDiv.style.paddingTop = '1rem';
    
    const existingPagination = paginationContainer.querySelector('.pagination-controls');
    if (existingPagination) {
        existingPagination.remove();
    }

    let pageButtons = '';
    const maxVisiblePages = 5;
    const halfVisible = Math.floor(maxVisiblePages / 2);
    let startPage = Math.max(1, current - halfVisible);
    let endPage = Math.min(total, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    if (startPage > 1) {
        pageButtons += `
            <button class="btn btn-sm btn-outline-primary mx-1" onclick="changePage(1)">1</button>
            ${startPage > 2 ? '<span class="mx-2">...</span>' : ''}`;
    }

    for (let i = startPage; i <= endPage; i++) {
        pageButtons += `
            <button class="btn btn-sm ${i === current ? 'btn-primary' : 'btn-outline-primary'} mx-1" 
                    onclick="changePage(${i})">
                ${i}
            </button>`;
    }

    if (endPage < total) {
        pageButtons += `
            ${endPage < total - 1 ? '<span class="mx-2">...</span>' : ''}
            <button class="btn btn-sm btn-outline-primary mx-1" onclick="changePage(${total})">${total}</button>`;
    }

    paginationDiv.innerHTML = `
        <div class="d-flex align-items-center">
            <span>Showing ${Math.min((current - 1) * staffPerPage + 1, totalStaff)} - ${Math.min(current * staffPerPage, totalStaff)} of ${totalStaff} staff members</span>
        </div>
        <div class="pagination-buttons">
            <button class="btn btn-sm btn-outline-primary me-2" ${current === 1 ? 'disabled' : ''} onclick="changePage(${current - 1})">
                <i class="bi bi-chevron-left"></i> Previous
            </button>
            ${pageButtons}
            <button class="btn btn-sm btn-outline-primary ms-2" ${current === total ? 'disabled' : ''} onclick="changePage(${current + 1})">
                Next <i class="bi bi-chevron-right"></i>
            </button>
        </div>
    `;

    paginationContainer.appendChild(paginationDiv);
}

// Change page function
function changePage(newPage) {
    if (!Array.isArray(staffMembers) || staffMembers.length === 0) {
        filterStaff();
        return;
    }
    const totalPages = Math.max(1, Math.ceil(staffMembers.length / staffPerPage));
    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        displayStaff(staffMembers);
    }
}

// Handle form submission
addStaffForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    const formData = new FormData(addStaffForm);
    const staffData = Object.fromEntries(formData.entries());
    
    try {
        // Validate required fields
        const requiredFields = ['CNI', 'nom', 'prenom', 'phone', 'sexe', 'role'];
        const missingFields = requiredFields.filter(field => !staffData[field]);
        if (missingFields.length > 0) {
            throw new Error(`Champs requis manquants: ${missingFields.join(', ')}`);
        }

        const cni = staffData.CNI;
        const method = document.querySelector('#addStaffModal .modal-title').textContent === 'Edit Staff Member' ? 'PUT' : 'POST';
        const url = '../../route/staffRoute.php' + (method === 'PUT' ? `?cni=${cni}` : '');

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(staffData)
        });

        const result = await response.json();
        if (!response.ok || result.error) {
            throw new Error(result.error || 'Failed to save staff member');
        }

        // Close modal and reload data
        bootstrap.Modal.getInstance(document.getElementById('addStaffModal')).hide();
        addStaffForm.reset();
        loadStaff();
        showAlert('Staff member saved successfully', 'success');
    } catch (error) {
        console.error('Error saving staff member:', error);
        showAlert(`Error saving staff member: ${error.message}`, 'danger');
    }
});

// Filter staff
roleFilter.addEventListener('change', filterStaff);
searchInput.addEventListener('input', filterStaff);

async function filterStaff() {
    const roleValue = roleFilter.value;
    const searchValue = searchInput.value.toLowerCase();

    try {
        const response = await fetch('../../route/staffRoute.php');
        if (!response.ok) throw new Error('Failed to fetch staff');

        const data = await response.json();
        if (data.error) throw new Error(data.error);
        if (!Array.isArray(data.data)) throw new Error('Invalid data format');

        staffMembers = data.data.filter(member => {
            if (!member) return false;

            const matchesRole = !roleValue || member.role === roleValue;

            const searchableFields = ['CNI', 'nom', 'prenom', 'phone'];
            const matchesSearch = !searchValue || 
                searchableFields.some(field => 
                    member[field] && member[field].toString().toLowerCase().includes(searchValue)
                );

            return matchesRole && matchesSearch;
        });

        if (currentPage > Math.ceil(staffMembers.length / staffPerPage)) {
            currentPage = 1;
        }

        displayStaff(staffMembers);
    } catch (error) {
        console.error('Error filtering staff:', error);
        showAlert('Error filtering staff members', 'danger');
    }
}

// Show alert message
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.student-management-container');
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout(() => alertDiv.remove(), 5000);
}

// Edit staff function
async function editStaff(staffId) {
    try {
        const row = document.querySelector(`tr button[onclick="editStaff(${staffId})"]`).closest('tr');
        const cni = row.cells[0].textContent;
        
        if (!cni) throw new Error('Could not find staff CNI');

        const response = await fetch(`../../route/staffRoute.php?cni=${cni}`);
        if (!response.ok) throw new Error('Failed to fetch staff data');

        const data = await response.json();
        if (data.error) throw new Error(data.error);

        const staff = data.data;
        if (!staff) throw new Error('No staff data received');

        const form = document.getElementById('addStaffForm');
        form.reset();

        // Mapping des champs du formulaire
        const fieldMapping = {
            'CNI': 'CNI',
            'nom': 'nom',
            'prenom': 'prenom',
            'phone': 'phone',
            'sexe': 'sexe',
            'role': 'role'
        };

        // Remplir le formulaire en utilisant le mapping
        Object.entries(fieldMapping).forEach(([formField, apiField]) => {
            const element = form.elements[formField];
            if (element && staff[apiField] !== undefined && staff[apiField] !== null) {
                element.value = staff[apiField];
            }
        });

        const modalTitle = document.querySelector('#addStaffModal .modal-title');
        modalTitle.textContent = 'Edit Staff Member';

        const modal = new bootstrap.Modal(document.getElementById('addStaffModal'));
        modal.show();
    } catch (error) {
        console.error('Error fetching staff:', error);
        showAlert('Error loading staff data', 'danger');
    }
}

// Delete staff function
async function deleteStaff(staffId) {
    if (!confirm('Are you sure you want to delete this staff member?')) {
        return;
    }

    try {
        const row = document.querySelector(`tr button[onclick="deleteStaff(${staffId})"]`).closest('tr');
        const cni = row.cells[0].textContent;
        
        if (!cni) throw new Error('Could not find staff CNI');

        const deleteResponse = await fetch(`../../route/staffRoute.php?cni=${cni}`, {
            method: 'DELETE'
        });

        if (!deleteResponse.ok) throw new Error('Failed to delete staff member');

        const result = await deleteResponse.json();
        if (result.error) throw new Error(result.error);

        loadStaff();
        showAlert('Staff member deleted successfully', 'success');
    } catch (error) {
        console.error('Error deleting staff:', error);
        showAlert('Error deleting staff member', 'danger');
    }
}