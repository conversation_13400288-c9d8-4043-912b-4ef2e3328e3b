<?php
require_once __DIR__ . '/../model/etudiantModel.php';
require_once __DIR__ . '/../model/enseignantModel.php';
require_once __DIR__ . '/../config/db.php';

function handleProfileRequest($action, $data, $queryParams) {
    header('Content-Type: application/json');

    // Debug log
    error_log("Profile request: Action=$action, Params=" . json_encode($queryParams));

    try {
        switch ($action) {
            case 'getById':
                if (!isset($queryParams['id']) || !isset($queryParams['type'])) {
                    http_response_code(400);
                    echo json_encode(['error' => 'ID and type parameters are required']);
                    return;
                }

                $id = $queryParams['id'];
                $type = strtolower($queryParams['type']);

                if ($type === 'student') {
                    // Get student by ID
                    $profile = getEtudiantById($id);
                } else {
                    // Get teacher by ID
                    $profile = getEnseignantById($id);
                }

                if ($profile) {
                    echo json_encode(['success' => true, 'profile' => $profile]);
                } else {
                    echo json_encode(['success' => false, 'error' => 'Profile not found']);
                }
                break;

            case 'getByName':
                if (!isset($queryParams['name']) || !isset($queryParams['type'])) {
                    http_response_code(400);
                    echo json_encode(['error' => 'Name and type parameters are required']);
                    return;
                }

                $name = $queryParams['name'];
                $type = strtolower($queryParams['type']);

                if ($type === 'student') {
                    // Get student by name
                    $profile = getEtudiantByName($name);
                } else {
                    // Get teacher by name
                    $profile = getEnseignantByName($name);
                }

                if ($profile) {
                    echo json_encode(['success' => true, 'profile' => $profile]);
                } else {
                    echo json_encode(['success' => false, 'error' => 'Profile not found']);
                }
                break;

            default:
                http_response_code(400);
                echo json_encode(['error' => 'Invalid action']);
                break;
        }
    } catch (Exception $e) {
        // Log the error for debugging
        error_log("Profile API Error: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());

        http_response_code(500);
        echo json_encode([
            'error' => 'Server error: ' . $e->getMessage(),
            'details' => 'Check server logs for more information'
        ]);
    }
}

// Helper function to get student by ID
function getEtudiantById($id) {
    $conn = getConnection();

    // Log the request for debugging
    error_log("Fetching student with ID: $id");

    // Check if the etudiant table exists
    $tableCheck = mysqli_query($conn, "SHOW TABLES LIKE 'etudiant'");
    if (mysqli_num_rows($tableCheck) == 0) {
        error_log("Table 'etudiant' does not exist");
        return null;
    }

    // Check for id_etudiant column first (based on database structure)
    $checkIdColumn = mysqli_query($conn, "SHOW COLUMNS FROM etudiant LIKE 'id_etudiant'");

    if (mysqli_num_rows($checkIdColumn) > 0) {
        error_log("Using 'id_etudiant' column to find student");
        // Check if profile_picture column exists
        $checkPictureColumn = mysqli_query($conn, "SHOW COLUMNS FROM etudiant LIKE 'profile_picture'");
        $selectColumns = "*";

        // Log whether profile_picture column exists
        if (mysqli_num_rows($checkPictureColumn) > 0) {
            error_log("Profile picture column exists in etudiant table");
        } else {
            error_log("Profile picture column does not exist in etudiant table");
        }

        $query = "SELECT $selectColumns FROM etudiant WHERE id_etudiant = ?";
        error_log("SQL Query: $query with ID=$id");
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, "i", $id);
    } else {
        // Try regular id column
        $checkIdColumn = mysqli_query($conn, "SHOW COLUMNS FROM etudiant LIKE 'id'");

        if (mysqli_num_rows($checkIdColumn) > 0) {
            error_log("Using 'id' column to find student");
            $query = "SELECT * FROM etudiant WHERE id = ?";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, "i", $id);
        } else {
            // Fallback to CNE if no id columns exist
            error_log("Using 'CNE' column to find student");
            $query = "SELECT * FROM etudiant WHERE CNE = ?";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, "s", $id);
        }
    }

    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $student = mysqli_fetch_assoc($result);

    if ($student) {
        error_log("Found student: " . json_encode($student));
    } else {
        error_log("No student found with ID: $id");
    }

    return $student;
}

// Helper function to get student by name
function getEtudiantByName($name) {
    $conn = getConnection();

    // Split the name into first and last name
    $nameParts = explode(' ', $name);
    $firstName = $nameParts[0] ?? '';
    $lastName = $nameParts[1] ?? '';

    // Try to find by first name or last name
    $query = "SELECT * FROM etudiant WHERE prenom LIKE ? OR nom LIKE ?";
    $firstName = "%$firstName%";
    $lastName = "%$lastName%";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "ss", $firstName, $lastName);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    return mysqli_fetch_assoc($result);
}

// Helper function to get teacher by ID
function getEnseignantById($id) {
    $conn = getConnection();

    // Log the request for debugging
    error_log("Fetching teacher with ID: $id");

    // Check if the enseignant table exists
    $tableCheck = mysqli_query($conn, "SHOW TABLES LIKE 'enseignant'");
    if (mysqli_num_rows($tableCheck) == 0) {
        error_log("Table 'enseignant' does not exist");
        return null;
    }

    // Check for id_enseignant column first (based on your database screenshot)
    $checkIdColumn = mysqli_query($conn, "SHOW COLUMNS FROM enseignant LIKE 'id_enseignant'");

    if (mysqli_num_rows($checkIdColumn) > 0) {
        error_log("Using 'id_enseignant' column to find teacher");
        // Check if profile_picture column exists
        $checkPictureColumn = mysqli_query($conn, "SHOW COLUMNS FROM enseignant LIKE 'profile_picture'");
        $selectColumns = "*";

        // Log whether profile_picture column exists
        if (mysqli_num_rows($checkPictureColumn) > 0) {
            error_log("Profile picture column exists in enseignant table");
        } else {
            error_log("Profile picture column does not exist in enseignant table");
        }

        $query = "SELECT $selectColumns FROM enseignant WHERE id_enseignant = ?";
        error_log("SQL Query: $query with ID=$id");
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, "i", $id);
    } else {
        // Try regular id column
        $checkIdColumn = mysqli_query($conn, "SHOW COLUMNS FROM enseignant LIKE 'id'");

        if (mysqli_num_rows($checkIdColumn) > 0) {
            error_log("Using 'id' column to find teacher");
            $query = "SELECT * FROM enseignant WHERE id = ?";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, "i", $id);
        } else {
            // Fallback to CNI if no id columns exist
            error_log("Using 'CNI' column to find teacher");
            $query = "SELECT * FROM enseignant WHERE CNI = ?";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, "s", $id);
        }
    }

    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $teacher = mysqli_fetch_assoc($result);

    if ($teacher) {
        error_log("Found teacher: " . json_encode($teacher));
    } else {
        error_log("No teacher found with ID: $id");
    }

    return $teacher;
}

// Helper function to get teacher by name
function getEnseignantByName($name) {
    $conn = getConnection();

    // Split the name into first and last name
    $nameParts = explode(' ', $name);
    $firstName = $nameParts[0] ?? '';
    $lastName = $nameParts[1] ?? '';

    // Try to find by first name or last name
    $query = "SELECT * FROM enseignant WHERE prenom LIKE ? OR nom LIKE ?";
    $firstName = "%$firstName%";
    $lastName = "%$lastName%";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "ss", $firstName, $lastName);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    return mysqli_fetch_assoc($result);
}
?>
