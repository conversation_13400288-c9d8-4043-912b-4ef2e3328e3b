<?php
require_once __DIR__ . '/../model/notificationsModel.php';
require_once __DIR__ . '/../utils/response.php';

function handleNotification($action, $data) {
    header('Content-Type: application/json');

    try {
        switch ($action) {
            case 'getAll':
                $notifications = getNotifications();
                echo json_encode($notifications);
                break;

            case 'markAsRead':
                if (!isset($data['id'])) {
                    http_response_code(400);
                    echo json_encode(['error' => 'Notification ID is required']);
                    return;
                }
                $success = markNotificationAsRead($data['id']);
                echo json_encode(['success' => $success]);
                break;

            case 'deleteNotification':
                if (!isset($data['id'])) {
                    http_response_code(400);
                    echo json_encode(['error' => 'Notification ID is required']);
                    return;
                }
                $success = deleteNotification($data['id']);
                echo json_encode(['success' => $success]);
                break;

            case 'markAllAsRead':
                $success = markAllNotificationsAsRead();
                echo json_encode(['success' => $success]);
                break;

            case 'getUnreadCount':
                $count = getUnreadNotificationsCount();
                echo json_encode(['count' => $count]);
                break;

            case 'create':
                if (!isset($data['title']) || !isset($data['message'])) {
                    http_response_code(400);
                    echo json_encode(['error' => 'Title and message are required']);
                    return;
                }

                $title = $data['title'];
                $message = $data['message'];
                $media_url = $data['media_url'] ?? null;
                $file_path = $data['file_path'] ?? null;
                $type = $data['type'] ?? 'message';

                // Vérifier que le type est valide
                $valid_types = ['assignment', 'message', 'meeting', 'system', 'event'];
                if (!in_array($type, $valid_types)) {
                    $type = 'message'; // Type par défaut si invalide
                }

                // Traitement du fichier uploadé si présent
                if (isset($_FILES['file']) && $_FILES['file']['error'] === UPLOAD_ERR_OK) {
                    $upload_dir = '../uploads/notifications/';

                    // Créer le répertoire s'il n'existe pas
                    if (!file_exists($upload_dir)) {
                        mkdir($upload_dir, 0777, true);
                    }

                    $file_name = time() . '_' . basename($_FILES['file']['name']);
                    $file_path = $upload_dir . $file_name;

                    if (move_uploaded_file($_FILES['file']['tmp_name'], $file_path)) {
                        $file_path = 'uploads/notifications/' . $file_name;
                    } else {
                        $file_path = null;
                    }
                }

                $success = createNotification($title, $message, $media_url, $file_path, $type);
                echo json_encode(['success' => $success]);
                break;

            default:
                http_response_code(400);
                echo json_encode(['error' => 'Invalid action']);
                break;
        }
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
    }
}
?>