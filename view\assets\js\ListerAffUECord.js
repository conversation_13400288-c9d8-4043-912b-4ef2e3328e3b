/**
 * JavaScript for the ListerAffUECord page
 *
 * This script handles loading and displaying teaching unit affectations for the coordinator's filiere,
 * as well as filtering functionality.
 */

// Global variables
let allAffectations = [];
let filteredAffectations = [];
let semesters = new Set();
let modules = new Set();

// DOM elements
const affectationsContainer = document.getElementById('affectationsContainer');
const moduleFilter = document.getElementById('moduleFilter');
const ueTypeFilter = document.getElementById('ueTypeFilter');
const semestreFilter = document.getElementById('semestreFilter');
const filterSelects = document.querySelectorAll('.filter-select');
const affectationCount = document.getElementById('affectationCount');
const filterStatus = document.getElementById('filterStatus');
const alertContainer = document.getElementById('alertContainer');

// Load affectations when the page loads
document.addEventListener('DOMContentLoaded', () => {
    loadListerAffUECord();

    // Add change event listeners to all filter selects
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            // Add visual feedback for active filters
            if (this.value) {
                this.classList.add('filter-active');
            } else {
                this.classList.remove('filter-active');
            }

            // Apply filters immediately
            applyFilters();
        });
    });
});

// Load assignments from the server
async function loadListerAffUECord() {
    if (!filiereId) {
        showAlert('Program ID not available. Please log in again.', 'danger');
        return;
    }

    console.log('Loading affectations for filiere ID:', filiereId);

    try {
        const url = `../../route/ListerAffUECordRoute.php?action=getListerAffUECordByFiliere&filiere_id=${filiereId}`;
        console.log('Fetching URL:', url);

        const response = await fetch(url);
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        // Get response text first to debug
        const responseText = await response.text();
        console.log('Raw response:', responseText);

        // Check if response is OK
        if (!response.ok) {
            console.error('Server error:', responseText);
            showAlert(`Server error: ${response.status} ${response.statusText}`, 'danger');
            return;
        }

        // Try to parse as JSON
        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error('JSON parse error:', parseError);
            console.error('Response text:', responseText);
            showAlert('Error: Server response is not valid JSON format', 'danger');
            return;
        }

        console.log('Parsed data:', data);

        if (data.error) {
            console.error('API error:', data.error);
            showAlert(`Error: ${data.error}`, 'danger');
            return;
        }

        if (!data.data) {
            console.warn('No data property in response');
            data.data = [];
        }

        if (!Array.isArray(data.data)) {
            console.error('Invalid data format - not an array:', data.data);
            showAlert('Error: Invalid data format', 'danger');
            return;
        }

        console.log('Found assignments:', data.data.length);

        // Store the assignments
        allAffectations = data.data;
        filteredAffectations = [...allAffectations];

        // Update the assignment count
        affectationCount.textContent = allAffectations.length;

        // Extract unique semesters and modules for filters
        populateFilters();

        // Render the assignments
        renderAffectations();

        if (allAffectations.length === 0) {
            showAlert('No assignments found for this program. Assignments will appear here after validation by the department head.', 'info');
        }
    } catch (error) {
        console.error('Error loading assignments:', error);
        showAlert(`Error loading assignments: ${error.message}`, 'danger');
    }
}

// Populate filter dropdowns
function populateFilters() {
    // Clear existing options (except the first one)
    semestreFilter.innerHTML = '<option value="">All semesters</option>';
    moduleFilter.innerHTML = '<option value="">All modules</option>';

    // Extract unique semesters and modules
    const semestersMap = new Map();
    const modulesMap = new Map();

    allAffectations.forEach(affectation => {
        if (affectation.semestre && affectation.semestre_id) {
            semestersMap.set(affectation.semestre_id, affectation.semestre);
        }
        if (affectation.module_id && affectation.module_name) {
            modulesMap.set(affectation.module_id, affectation.module_name);
        }
    });

    // Sort semesters by ID
    const sortedSemesters = Array.from(semestersMap.entries())
        .sort((a, b) => a[0] - b[0]);

    // Add semester options
    sortedSemesters.forEach(([id, name]) => {
        const option = document.createElement('option');
        option.value = id;
        option.textContent = name;
        semestreFilter.appendChild(option);
    });

    // Sort modules alphabetically
    const sortedModules = Array.from(modulesMap.entries())
        .sort((a, b) => a[1].localeCompare(b[1]));

    // Add module options
    sortedModules.forEach(([id, name]) => {
        const option = document.createElement('option');
        option.value = id;
        option.textContent = name;
        moduleFilter.appendChild(option);
    });
}

// Apply filters to the assignments
function applyFilters() {
    const selectedSemestre = semestreFilter.value;
    const selectedModule = moduleFilter.value;
    const selectedUeType = ueTypeFilter.value;

    // Apply filters
    filteredAffectations = allAffectations.filter(affectation => {
        return (!selectedSemestre || affectation.semestre_id == selectedSemestre) &&
               (!selectedModule || affectation.module_id == selectedModule) &&
               (!selectedUeType || affectation.ue_type === selectedUeType);
    });

    // Update the assignment count
    affectationCount.textContent = filteredAffectations.length;

    // Update filter status text
    updateFilterStatus();

    // Render the filtered assignments
    renderAffectations();
}

// Update the filter status text
function updateFilterStatus() {
    const activeFilters = [];

    if (semestreFilter.value) {
        activeFilters.push(`Semester: ${semestreFilter.options[semestreFilter.selectedIndex].text}`);
    }
    if (moduleFilter.value) {
        activeFilters.push(`Module: ${moduleFilter.options[moduleFilter.selectedIndex].text}`);
    }
    if (ueTypeFilter.value) {
        activeFilters.push(`Type: ${ueTypeFilter.options[ueTypeFilter.selectedIndex].text}`);
    }

    if (activeFilters.length > 0) {
        filterStatus.textContent = `Active filters: ${activeFilters.join(', ')}`;
    } else {
        filterStatus.textContent = '';
    }
}

// Render assignments grouped by semester
function renderAffectations() {
    if (filteredAffectations.length === 0) {
        affectationsContainer.innerHTML = `
        <div class="empty-state">
            <i class="bi bi-clipboard-x"></i>
            <h5>No assignments found</h5>
            <p>There are no assignments matching your search criteria.</p>
        </div>`;
        return;
    }

    // Group assignments by semester
    const semesterGroups = {};
    filteredAffectations.forEach(affectation => {
        const semesterId = affectation.semestre_id || 'unknown';
        if (!semesterGroups[semesterId]) {
            semesterGroups[semesterId] = {
                id: semesterId,
                name: affectation.semestre || 'Semester not specified',
                modules: {}
            };
        }

        // Group by module within each semester
        const moduleId = affectation.module_id;
        if (!semesterGroups[semesterId].modules[moduleId]) {
            semesterGroups[semesterId].modules[moduleId] = {
                id: moduleId,
                name: affectation.module_name,
                filiere: affectation.nom_filiere,
                niveau: affectation.niveau,
                affectations: []
            };
        }

        semesterGroups[semesterId].modules[moduleId].affectations.push(affectation);
    });

    // Sort semesters by ID
    const sortedSemesters = Object.values(semesterGroups).sort((a, b) => {
        if (a.id === 'unknown') return 1;
        if (b.id === 'unknown') return -1;
        return a.id - b.id;
    });

    let html = '';

    // Render each semester
    sortedSemesters.forEach(semester => {
        html += `
        <div class="semester-section mb-4">
            <h3 class="semester-title">${semester.name}</h3>
            <div class="row">`;

        // Sort modules alphabetically
        const sortedModules = Object.values(semester.modules).sort((a, b) =>
            a.name.localeCompare(b.name)
        );

        sortedModules.forEach((module) => {
            html += `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card module-card">
                    <div class="module-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>${module.name}</div>
                        </div>
                        <div class="text-muted small">${module.filiere} - ${module.niveau || 'N/A'}</div>
                    </div>
                    <div class="module-body">
                        <ul class="ue-list">`;

            // Sort assignments by UE type (Cours, TD, TP)
            const sortedAffectations = module.affectations.sort((a, b) => {
                const typeOrder = { 'Cours': 1, 'TD': 2, 'TP': 3 };
                return typeOrder[a.ue_type] - typeOrder[b.ue_type];
            });

            sortedAffectations.forEach(affectation => {
                // Translate UE type to English
                let ueTypeDisplay = affectation.ue_type;
                if (affectation.ue_type === 'Cours') ueTypeDisplay = 'Lecture';
                else if (affectation.ue_type === 'TD') ueTypeDisplay = 'Tutorial';
                else if (affectation.ue_type === 'TP') ueTypeDisplay = 'Practical';

                html += `
                        <li class="ue-item">
                            <div class="ue-type">${ueTypeDisplay}</div>
                            <div class="ue-details">
                                <div class="ue-teacher">
                                    <i class="bi bi-person-badge"></i>
                                    <span>${affectation.enseignant_nom} ${affectation.enseignant_prenom}</span>
                                </div>
                                <div class="ue-hours">${affectation.volume_horaire}h</div>
                            </div>
                        </li>`;
            });

            html += `
                        </ul>
                    </div>
                </div>
            </div>`;
        });

        html += `
            </div>
        </div>`;
    });

    affectationsContainer.innerHTML = html;
}

// Show an alert message
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    alertContainer.appendChild(alertDiv);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const bsAlert = new bootstrap.Alert(alertDiv);
        bsAlert.close();
    }, 5000);
}