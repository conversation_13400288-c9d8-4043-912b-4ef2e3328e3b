<?php
// Vérifier l'authentification
require_once '../includes/auth_check_chef.php';

// Inclure le contrôleur
require_once __DIR__ . '/../../controller/vacantUEController.php';

// Récupérer l'ID du département du chef connecté
$departementId = null;

// Vérifier si le department_id est déjà dans la session
if (isset($_SESSION['user']['department_id'])) {
    $departementId = $_SESSION['user']['department_id'];
}

// Get department head's information
$chefId = $_SESSION['user']['teacher_id'] ?? null;
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$chefName = trim($prenom . ' ' . $nom);
if (empty($chefName)) {
    $chefName = $_SESSION['user']['username'] ?? 'Chef de département';
}

// Get current academic year
$currentAcademicYear = getCurrentYear();

// Page title
$pageTitle = "View and Validate Vacant Teaching Units";
$currentPage = "vacant_teaching_units.php";
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unités d'Enseignement Vacantes - Chef de Département</title>
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🎓</text></svg>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard-style.css">

    <style>
        /* Dashboard title styles matching listerUEdepart.css */
        .dashboard-title {
            color: #343a40;
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            position: relative;
            display: inline-block;
            letter-spacing: -0.5px;
        }

        .dashboard-title:after {
            content: '';
            position: absolute;
            left: 0;
            bottom: -8px;
            width: 50px;
            height: 4px;
            background-color: #4e73df;
        }

        /* Department card matching the established design */
        .department-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            margin-bottom: 25px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            position: relative;
        }

        .department-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, #4e73df, #36b9cc);
        }

        .department-header {
            padding: 20px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .department-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            box-shadow: 0 4px 8px rgba(78, 115, 223, 0.25);
            flex-shrink: 0;
        }

        .department-icon i {
            font-size: 20px;
            color: white;
        }

        /* Stats cards with pastel colors */
        .stats-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            border: none;
            overflow: hidden;
            position: relative;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08);
        }

        /* Vacant unit cards with consistent styling */
        .vacant-unit-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            border: none;
            overflow: hidden;
            margin-bottom: 20px;
            position: relative;
        }

        .vacant-unit-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08);
        }

        .vacant-unit-card.is-vacant::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: #dc3545;
        }

        .vacant-unit-card.not-vacant::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: #28a745;
        }

        .vacant-unit-card.unmarked::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: #ffc107;
        }

        /* Pastel button styles */
        .btn-vacant {
            background-color: #ffe8eb;
            color: #e64980;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            transition: all 0.2s ease;
            font-size: 0.85rem;
        }

        .btn-vacant:hover {
            background-color: #ffd0d9;
            color: #d6336c;
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
        }

        .btn-not-vacant {
            background-color: #ebfbee;
            color: #40c057;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            transition: all 0.2s ease;
            font-size: 0.85rem;
        }

        .btn-not-vacant:hover {
            background-color: #d3f9d8;
            color: #37b24d;
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
        }

        /* Status badges */
        .status-badge {
            font-size: 0.75rem;
            padding: 5px 12px;
            border-radius: 20px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .status-vacant {
            background-color: #ffe8eb;
            color: #e64980;
            border: 1px solid rgba(230, 73, 128, 0.1);
        }

        .status-not-vacant {
            background-color: #ebfbee;
            color: #40c057;
            border: 1px solid rgba(64, 192, 87, 0.1);
        }

        .status-unmarked {
            background-color: #fff3bf;
            color: #e67700;
            border: 1px solid rgba(230, 119, 0, 0.1);
        }

        /* Filter buttons */
        .filter-btn-group .btn {
            background-color: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
            transition: all 0.2s ease;
        }

        .filter-btn-group .btn.active,
        .filter-btn-group .btn:hover {
            background-color: #4e73df;
            color: white;
            border-color: #4e73df;
        }

        /* Loading spinner */
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 3rem;
        }

        .spinner-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 3rem;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>
            <div class="container-fluid p-4">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="dashboard-title">Unités d'Enseignement Vacantes</h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Tableau de bord</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Unités d'enseignement vacantes</li>
                            </ol>
                        </nav>
                    </div>
                </div>

                <?php if (!$departementId): ?>
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Vous n'êtes pas associé à un département en tant que chef. Veuillez contacter l'administrateur.
                </div>
                <?php else: ?>

                <!-- Department Information -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="department-card">
                            <div class="department-header">
                                <div class="department-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="department-title">
                                    <h5>
                                        Département: <span class="department-name" id="departmentName">Chargement...</span>
                                    </h5>
                                    <p class="department-description">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Gérez et validez les unités d'enseignement vacantes pour l'année académique <?php echo $currentAcademicYear; ?>
                                    </p>
                                    <p class="department-description mb-0">
                                        <i class="fas fa-user-tie me-2"></i>
                                        Chef de département: <?php echo htmlspecialchars($chefName); ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4" id="statsContainer">
                    <div class="col-md-3">
                        <div class="stats-card p-3">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <div class="icon-container" style="background-color: rgba(78, 115, 223, 0.1);">
                                        <i class="fas fa-list-ul" style="color: #4e73df;"></i>
                                    </div>
                                </div>
                                <div>
                                    <h3 class="mb-0" id="totalUnits">0</h3>
                                    <p class="mb-0 text-muted">Total Non Assignées</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card p-3">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <div class="icon-container" style="background-color: rgba(230, 73, 128, 0.1);">
                                        <i class="fas fa-exclamation-triangle" style="color: #e64980;"></i>
                                    </div>
                                </div>
                                <div>
                                    <h3 class="mb-0" id="vacantUnits">0</h3>
                                    <p class="mb-0 text-muted">Marquées Vacantes</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card p-3">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <div class="icon-container" style="background-color: rgba(64, 192, 87, 0.1);">
                                        <i class="fas fa-check-circle" style="color: #40c057;"></i>
                                    </div>
                                </div>
                                <div>
                                    <h3 class="mb-0" id="notVacantUnits">0</h3>
                                    <p class="mb-0 text-muted">Non Vacantes</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card p-3">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <div class="icon-container" style="background-color: rgba(230, 119, 0, 0.1);">
                                        <i class="fas fa-question-circle" style="color: #e67700;"></i>
                                    </div>
                                </div>
                                <div>
                                    <h3 class="mb-0" id="unmarkedUnits">0</h3>
                                    <p class="mb-0 text-muted">Non Marquées</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Controls -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <button class="btn" style="background-color: #e7f5ff; color: #1c7ed6; border: none; padding: 8px 16px; border-radius: 6px;" onclick="refreshData()">
                            <i class="fas fa-sync-alt me-2"></i>Actualiser les données
                        </button>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="btn-group filter-btn-group" role="group">
                            <button type="button" class="btn active" onclick="filterUnits('all', this)">Toutes</button>
                            <button type="button" class="btn" onclick="filterUnits('unmarked', this)">Non marquées</button>
                            <button type="button" class="btn" onclick="filterUnits('vacant', this)">Vacantes</button>
                            <button type="button" class="btn" onclick="filterUnits('not-vacant', this)">Non vacantes</button>
                        </div>
                    </div>
                </div>

                <!-- Loading Spinner -->
                <div class="loading-spinner" id="loadingSpinner">
                    <div class="spinner-container">
                        <div class="spinner-border" style="color: #4e73df;" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                        <p class="mt-3">Chargement des unités d'enseignement non assignées...</p>
                    </div>
                </div>

                <!-- Teaching Units Container -->
                <div id="unitsContainer">
                    <!-- Units will be loaded here via JavaScript -->
                    <div class="col-12">
                        <div class="spinner-container">
                            <div class="spinner-border" style="color: #4e73df;" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                            <p class="mt-3">Chargement des unités d'enseignement...</p>
                        </div>
                    </div>
                </div>

                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/vacant_teaching_units.js"></script>

    <script>
        // Add a console log to verify the script is loaded
        console.log('vacant_teaching_units.js script loaded');

        // Add a fallback in case the script fails to load
        window.addEventListener('error', function(e) {
            if (e.target.tagName === 'SCRIPT' && e.target.src.includes('vacant_teaching_units.js')) {
                console.error('Failed to load vacant_teaching_units.js, trying alternative path');
                var script = document.createElement('script');
                script.src = '../../view/assets/js/vacant_teaching_units.js';
                document.body.appendChild(script);
            }
        }, true);

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            const departmentId = <?php echo json_encode($departementId); ?>;
            const chefId = <?php echo json_encode($chefId); ?>;

            if (departmentId) {
                initializeVacantUnitsPage(departmentId, chefId);
            }
        });
    </script>
</body>
</html>
