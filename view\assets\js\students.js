// DOM Elements
const studentTable = document.getElementById('studentTable');
const addStudentForm = document.getElementById('addStudentForm');
const fieldFilter = document.getElementById('fieldFilter');
const levelFilter = document.getElementById('levelFilter');
const searchInput = document.getElementById('searchInput');
const addStudentButton = document.querySelector('[data-bs-target="#addStudentModal"]');

// Function to toggle between table and cards view based on screen size
function toggleTableCardsView() {
    const studentCardsContainer = document.getElementById('studentCards');
    const tableContainer = document.querySelector('.table-responsive');

    if (window.innerWidth < 768) {
        // Mobile view - show cards, hide table
        studentCardsContainer.classList.remove('d-none');
        tableContainer.classList.add('d-none');
    } else {
        // Desktop view - show table, hide cards
        studentCardsContainer.classList.add('d-none');
        tableContainer.classList.remove('d-none');
    }
}

// Reset form when Add Student button is clicked
addStudentButton.addEventListener('click', () => {
    addStudentForm.reset();
    document.querySelector('#addStudentModal .modal-title').textContent = 'Add Student';
    // Reset filiere dropdown to empty state
    document.getElementById('filiere').value = '';
    // Clear the hidden id field
    document.querySelector('input[name="id_etudiant"]').value = '';
});

// Load filieres (fields of study) when the page loads
document.addEventListener('DOMContentLoaded', () => {
    loadFilieres();
    loadStudents();
    toggleTableCardsView(); // Initial toggle based on screen size

    // Listen for window resize to toggle view
    window.addEventListener('resize', toggleTableCardsView);
});

// Load niveaux when filiere is selected in filter
fieldFilter.addEventListener('change', async (e) => {
    const filiereId = e.target.value;
    const niveauSelect = document.getElementById('levelFilter');
    niveauSelect.innerHTML = '<option value="">All Levels</option>';

    if (!filiereId) return;

    try {
        const response = await fetch(`../../route/niveauRoute.php?id_filiere=${filiereId}`);
        if (!response.ok) throw new Error('Failed to fetch levels');

        const data = await response.json();
        if (data.error) throw new Error(data.error);

        const niveaux = data.data;
        if (Array.isArray(niveaux)) {
            niveaux.forEach(niveau => {
                const niveauText = niveau.niveau || niveau.id_niveau;
                niveauSelect.appendChild(createOption(niveau.id_niveau, niveauText));
            });
        }
    } catch (error) {
        console.error('Error loading levels:', error);
        showAlert('Error loading levels', 'danger');
    }
});

// Fetch filieres from API and populate dropdowns
async function loadFilieres() {
    try {
        const response = await fetch('../../route/filiereRoute.php');
        if (!response.ok) throw new Error('Failed to fetch filieres');

        const data = await response.json();
        if (data.error) throw new Error(data.error);

        const filieres = data.data;
        updateFiliereDropdowns(filieres);
    } catch (error) {
        console.error('Error loading filieres:', error);
        showAlert('Error loading fields of study', 'danger');
    }
}

// Update filiere dropdowns with fetched data
function updateFiliereDropdowns(filieres) {
    const filterSelect = document.getElementById('fieldFilter');
    const modalSelect = document.getElementById('filiere');

    // Clear existing options except the first one for filter
    filterSelect.innerHTML = '<option value="">All Fields of Study</option>';
    modalSelect.innerHTML = '';

    filieres.forEach(filiere => {
        // Add option to filter dropdown
        filterSelect.appendChild(createOption(filiere.id_filiere, filiere.nom_filiere));

        // Add option to modal dropdown
        modalSelect.appendChild(createOption(filiere.id_filiere, filiere.nom_filiere));
    });
}

// Helper function to create option elements
function createOption(value, text) {
    const option = document.createElement('option');
    option.value = value;
    option.textContent = text;
    return option;
}

// Load students data
async function loadStudents() {
    try {
        const response = await fetch('../../route/etudiantRoute.php');
        if (!response.ok) throw new Error('Failed to fetch students');

        const data = await response.json();
        if (data.error) throw new Error(data.error);

        // Mettre à jour la variable globale students
        students = data.data;

        // Réinitialiser la page courante
        currentPage = 1;

        // Afficher les étudiants
        displayStudents(students);
    } catch (error) {
        console.error('Error loading students:', error);
        showAlert('Error loading students data', 'danger');
    }
}

// Pagination variables
let currentPage = 1;
let studentsPerPage = 20; // Fixed number of students per page
let students = []; // Variable globale pour stocker les étudiants

// Display students in the table with pagination
function displayStudents(students) {
    const tbody = studentTable.querySelector('tbody');
    const studentCardsContainer = document.getElementById('studentCards');

    // Clear both containers
    tbody.innerHTML = '';
    studentCardsContainer.innerHTML = '';

    // Calculate pagination
    const totalPages = Math.max(1, Math.ceil(students.length / studentsPerPage));
    // Ensure currentPage doesn't exceed totalPages
    if (currentPage > totalPages) {
        currentPage = totalPages;
    }
    const start = (currentPage - 1) * studentsPerPage;
    const end = start + studentsPerPage;
    const paginatedStudents = students.slice(start, end);

    // Display paginated students in table (desktop view)
    paginatedStudents.forEach(student => {
        // Create table row for desktop
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${student.CNE}</td>
            <td>${student.nom}</td>
            <td>${student.prenom}</td>
            <td>${student.email}</td>
            <td>${student.sexe}</td>
            <td>${student.nom_filiere || student.filiere || ''}</td>
            <td>${student.niveau || ''}</td>
            <td class="student-actions">
                <div class="d-flex justify-content-center gap-1">
                    <button class="btn btn-info" onclick="editStudent(${student.id_etudiant})">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-danger" onclick="deleteStudent(${student.id_etudiant})">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);

        // Create card for mobile view
        const card = document.createElement('div');
        card.className = 'student-card';
        card.innerHTML = `
            <div class="student-card-header">
                <h5>${student.nom} ${student.prenom}</h5>
                <span class="badge">${student.niveau || ''}</span>
            </div>
            <div class="student-card-body">
                <ul class="student-card-info">
                    <li>
                        <span class="student-card-label">CNE</span>
                        <span class="student-card-value">${student.CNE}</span>
                    </li>
                    <li>
                        <span class="student-card-label">Email</span>
                        <span class="student-card-value">${student.email}</span>
                    </li>
                    <li>
                        <span class="student-card-label">Gender</span>
                        <span class="student-card-value">${student.sexe}</span>
                    </li>
                    <li>
                        <span class="student-card-label">Field</span>
                        <span class="student-card-value">${student.nom_filiere || student.filiere || ''}</span>
                    </li>
                </ul>
                <div class="student-card-actions">
                    <button class="btn btn-info" onclick="editStudent(${student.id_etudiant})">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-danger" onclick="deleteStudent(${student.id_etudiant})">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `;
        studentCardsContainer.appendChild(card);
    });

    // Update pagination controls
    updatePaginationControls(currentPage, totalPages, students.length);
}

// Update pagination controls
function updatePaginationControls(current, total, totalStudents) {
    if (total === 0) return; // Ne pas afficher la pagination s'il n'y a pas de pages

    const paginationContainer = document.querySelector('.card.student-table .card-body');
    const paginationDiv = document.createElement('div');
    paginationDiv.className = 'pagination-controls d-flex flex-column align-items-center mt-4';
    paginationDiv.style.borderTop = '1px solid #dee2e6';
    paginationDiv.style.paddingTop = '1.5rem';

    // Remove existing pagination controls if any
    const existingPagination = paginationContainer.querySelector('.pagination-controls');
    if (existingPagination) {
        existingPagination.remove();
    }

    // Create page number buttons with improved visibility
    let pageButtons = '';
    const maxVisiblePages = 5;
    const halfVisible = Math.floor(maxVisiblePages / 2);
    let startPage = Math.max(1, current - halfVisible);
    let endPage = Math.min(total, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // Add first page button if not visible
    if (startPage > 1) {
        pageButtons += `
            <button class="btn btn-sm btn-outline-primary mx-1" onclick="changePage(1)">1</button>
            ${startPage > 2 ? '<span class="mx-2">...</span>' : ''}`;
    }

    // Add visible page buttons
    for (let i = startPage; i <= endPage; i++) {
        pageButtons += `
            <button class="btn btn-sm ${i === current ? 'btn-primary' : 'btn-outline-primary'} mx-1"
                    onclick="changePage(${i})">
                ${i}
            </button>`;
    }

    // Add last page button if not visible
    if (endPage < total) {
        pageButtons += `
            ${endPage < total - 1 ? '<span class="mx-2">...</span>' : ''}
            <button class="btn btn-sm btn-outline-primary mx-1" onclick="changePage(${total})">${total}</button>`;
    }

    paginationDiv.innerHTML = `
        <div class="text-center mb-3">
            <span>Affichage ${Math.min((current - 1) * studentsPerPage + 1, totalStudents)} - ${Math.min(current * studentsPerPage, totalStudents)} sur ${totalStudents} étudiants</span>
        </div>
        <div class="pagination-buttons d-flex justify-content-center flex-wrap gap-2">
            <button class="btn btn-sm btn-outline-primary me-2" ${current === 1 ? 'disabled' : ''} onclick="changePage(${current - 1})">
                <i class="bi bi-chevron-left"></i> Previous
            </button>
            ${pageButtons}
            <button class="btn btn-sm btn-outline-primary ms-2" ${current === total ? 'disabled' : ''} onclick="changePage(${current + 1})">
                Next <i class="bi bi-chevron-right"></i>
            </button>
        </div>
    `;

    paginationContainer.appendChild(paginationDiv);
    }



// Change page function
function changePage(newPage) {
    if (!Array.isArray(students) || students.length === 0) {
        filterStudents();
        return;
    }
    const totalPages = Math.max(1, Math.ceil(students.length / studentsPerPage));
    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        displayStudents(students); // Use current filtered students
    }
}

// Handle form submission
addStudentForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    const formData = new FormData(addStudentForm);
    const studentData = Object.fromEntries(formData.entries());

    try {
        const cne = studentData.CNE;
        const method = document.querySelector('#addStudentModal .modal-title').textContent === 'Edit Student' ? 'PUT' : 'POST';
        const url = '../../route/etudiantRoute.php' + (method === 'PUT' ? `?cne=${cne}` : '');

        // Convert filiere and niveau to integers
        if (studentData.filiere) {
            studentData.id_filiere = parseInt(studentData.filiere);
            delete studentData.filiere;
        }
        if (studentData.niveau) {
            studentData.id_niveau = parseInt(studentData.niveau);
            delete studentData.niveau;
        }

        // Handle createAccount checkbox
        if (studentData.createAccount === 'on') {
            studentData.createAccount = true;
        } else {
            studentData.createAccount = false;
        }

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(studentData)
        });

        const result = await response.json();
        if (!response.ok || result.error) {
            throw new Error(result.error || 'Failed to save student');
        }

        // Close modal and reload data
        bootstrap.Modal.getInstance(document.getElementById('addStudentModal')).hide();
        addStudentForm.reset();
        loadStudents();

        // Show success message with account creation info if applicable
        if (method === 'POST' && studentData.createAccount) {
            showSuccessModal('Student saved successfully. A user account has been created and an initialization email has been sent.');
        } else {
            showAlert('Student saved successfully', 'success');
        }
    } catch (error) {
        console.error('Error saving student:', error);
        showAlert(`Error saving student: ${error.message}`, 'danger');
    }
});

// Filter students
fieldFilter.addEventListener('change', filterStudents);
levelFilter.addEventListener('change', filterStudents);
searchInput.addEventListener('input', filterStudents);

async function filterStudents() {
    const fieldValue = fieldFilter.value;
    const levelValue = levelFilter.value;
    const searchValue = searchInput.value.toLowerCase();

    try {
        const response = await fetch('../../route/etudiantRoute.php');
        if (!response.ok) throw new Error('Failed to fetch students');

        const data = await response.json();
        if (data.error) throw new Error(data.error);
        if (!Array.isArray(data.data)) throw new Error('Invalid data format');

        students = data.data.filter(student => {
            if (!student) return false;

            const matchesField = !fieldValue ||
                (student.id_filiere && student.id_filiere.toString() === fieldValue);

            const matchesLevel = !levelValue ||
                (student.id_niveau && student.id_niveau.toString() === levelValue);

            const searchableFields = ['CNE', 'nom', 'prenom', 'email'];
            const matchesSearch = !searchValue ||
                searchableFields.some(field =>
                    student[field] && student[field].toString().toLowerCase().includes(searchValue)
                );

            return matchesField && matchesLevel && matchesSearch;
        });

        // Reset to first page when filters change
        if (currentPage > Math.ceil(students.length / studentsPerPage)) {
            currentPage = 1;
        }

        displayStudents(students);
    } catch (error) {
        console.error('Error filtering students:', error);
        showAlert('Erreur lors du filtrage des étudiants', 'danger');
    }
}

// Show alert message
function showAlert(message, type) {
    // Supprimer les alertes existantes du même type pour éviter la duplication
    const existingAlerts = document.querySelectorAll(`.alert.alert-${type}`);
    existingAlerts.forEach(alert => alert.remove());

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.student-management-container');
    container.insertBefore(alertDiv, container.firstChild);

    // Faire défiler vers le haut pour que l'alerte soit visible
    window.scrollTo({ top: 0, behavior: 'smooth' });

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.classList.remove('show');
            setTimeout(() => alertDiv.remove(), 300);
        }
    }, 5000);
}

// Show success modal with custom message
function showSuccessModal(message) {
    const successModal = document.getElementById('successModal');
    const successMessage = document.getElementById('successMessage');

    // Set the message
    successMessage.textContent = message;

    // Show the modal
    const modal = new bootstrap.Modal(successModal);
    modal.show();
}

// Edit student function
async function editStudent(studentId) {
    try {
        // Get CNE from the table row or card
        let cne = '';

        // Try to find the button in the table first
        const tableRow = document.querySelector(`tr button[onclick="editStudent(${studentId})"]`);
        if (tableRow) {
            const parentRow = tableRow.closest('tr');
            cne = parentRow.cells[0].textContent;
        } else {
            // If not found in table, try to find in cards
            const card = document.querySelector(`.student-card button[onclick="editStudent(${studentId})"]`);
            if (card) {
                const cneElement = card.closest('.student-card').querySelector('.student-card-info li:first-child .student-card-value');
                if (cneElement) {
                    cne = cneElement.textContent;
                }
            }
        }

        // If CNE is still not found, try to find the student in the global students array
        if (!cne && Array.isArray(students)) {
            const student = students.find(s => s.id_etudiant == studentId);
            if (student) {
                cne = student.CNE;
            }
        }

        if (!cne) throw new Error('Could not find student CNE');

        // Fetch student data using CNE
        const response = await fetch(`../../route/etudiantRoute.php?cne=${cne}`);
        if (!response.ok) throw new Error('Failed to fetch student data');

        const data = await response.json();
        if (data.error) throw new Error(data.error);

        const student = data.data;
        if (!student) throw new Error('No student data received');

        // Get form elements
        const form = document.getElementById('addStudentForm');
        const fields = ['id_etudiant', 'CNE', 'nom', 'prenom', 'email', 'tele', 'sexe', 'pays', 'ville',
                       'date_naissance', 'lieu_naissance', 'coordonne_parental', 'date_inscription', 'id_niveau'];

        // Reset form before populating
        form.reset();

        // Populate form fields safely
        fields.forEach(field => {
            const element = form.elements[field];
            if (element && student[field] !== undefined) {
                element.value = student[field];
            }
        });

        // Set filiere field separately since it needs id_filiere
        const filiereElement = form.elements['filiere'];
        if (filiereElement && student.id_filiere !== undefined) {
            filiereElement.value = student.id_filiere;
            // Trigger change event to load niveaux
            const event = new Event('change');
            filiereElement.dispatchEvent(event);

            // Set niveau after a short delay to ensure niveaux are loaded
            setTimeout(() => {
                const niveauElement = form.elements['niveau'];
                if (niveauElement && student.id_niveau !== undefined) {
                    niveauElement.value = student.id_niveau;
                }
            }, 500);
        }

        // Update modal title to indicate editing
        const modalTitle = document.querySelector('#addStudentModal .modal-title');
        modalTitle.textContent = 'Edit Student';

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('addStudentModal'));
        modal.show();
    } catch (error) {
        console.error('Error fetching student:', error);
        showAlert('Error loading student data', 'danger');
    }
}

// Delete student function
async function deleteStudent(studentId) {
    // Show confirmation dialog
    if (!confirm('Are you sure you want to delete this student?')) {
        return;
    }

    try {
        // Get CNE from the table row or card
        let cne = '';

        // Try to find the button in the table first
        const tableRow = document.querySelector(`tr button[onclick="deleteStudent(${studentId})"]`);
        if (tableRow) {
            const parentRow = tableRow.closest('tr');
            cne = parentRow.cells[0].textContent;
        } else {
            // If not found in table, try to find in cards
            const card = document.querySelector(`.student-card button[onclick="deleteStudent(${studentId})"]`);
            if (card) {
                const cneElement = card.closest('.student-card').querySelector('.student-card-info li:first-child .student-card-value');
                if (cneElement) {
                    cne = cneElement.textContent;
                }
            }
        }

        // If CNE is still not found, try to find the student in the global students array
        if (!cne && Array.isArray(students)) {
            const student = students.find(s => s.id_etudiant == studentId);
            if (student) {
                cne = student.CNE;
            }
        }

        if (!cne) throw new Error('Could not find student CNE');

        // Send delete request
        const deleteResponse = await fetch(`../../route/etudiantRoute.php?cne=${cne}`, {
            method: 'DELETE'
        });

        if (!deleteResponse.ok) throw new Error('Failed to delete student');

        const result = await deleteResponse.json();
        if (result.error) throw new Error(result.error);

        // Reload students and show success message
        loadStudents();
        showAlert('Student deleted successfully', 'success');
    } catch (error) {
        console.error('Error deleting student:', error);
        showAlert('Error deleting student', 'danger');
    }
}

// Load niveaux when filiere is selected
document.getElementById('filiere').addEventListener('change', async (e) => {
    const filiereId = e.target.value;
    const niveauSelect = document.getElementById('niveau');
    niveauSelect.innerHTML = '';

    if (!filiereId) {
        niveauSelect.innerHTML = '<option value="">Sélectionnez un niveau</option>';
        return;
    }

    try {
        const response = await fetch(`../../route/niveauRoute.php?id_filiere=${filiereId}`);
        if (!response.ok) throw new Error('Échec de la récupération des niveaux');

        const data = await response.json();
        if (data.error) throw new Error(data.error);

        const niveaux = data.data;
        niveauSelect.innerHTML = '<option value="">Sélectionnez un niveau</option>';
        if (Array.isArray(niveaux)) {
            niveaux.forEach(niveau => {
                const niveauText = niveau.niveau || niveau.id_niveau;
                niveauSelect.appendChild(createOption(niveau.id_niveau, niveauText));
            });
        };
    } catch (error) {
        console.error('Erreur lors du chargement des niveaux:', error);
        showAlert('Erreur lors du chargement des niveaux', 'danger');
    }
});
