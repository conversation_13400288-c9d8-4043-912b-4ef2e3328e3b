<?php
// Ensure no output before headers
ob_start();

require_once "../controller/salleController.php";

// Set proper content type and CORS headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        if (isset($_GET['id'])) {
            getSalleByIdAPI($_GET['id']);
        } else {
            getAllSallesAPI();
        }
        break;

    case 'POST':
        addSalleAPI();
        break;

    case 'PUT':
        updateSalleAPI();
        break;

    case 'DELETE':
        if (isset($_GET['id'])) {
            deleteSalleAPI($_GET['id']);
        } else {
            jsonResponse(['error' => 'Room ID is required'], 400);
        }
        break;

    default:
        jsonResponse(['error' => 'Method not allowed'], 405);
        break;
}
?>