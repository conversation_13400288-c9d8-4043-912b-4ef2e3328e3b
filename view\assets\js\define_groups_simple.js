// Document ready
$(document).ready(function() {
    // Get filiere ID from the data attribute
    const filiereId = $('#cycles-container').data('filiere-id');

    // Load cycles for the filiere
    loadCycles(filiereId);

    // Apply page title animation only on initial page load
    const pageTitle = $('.page-title');
    const pageSubtitle = $('.page-title').next('p');

    // Check if this is the first load in this session
    if (!sessionStorage.getItem('pageTitleAnimationShown')) {
        // Apply the animation classes
        pageTitle.addClass('page-title-animation');
        pageSubtitle.addClass('page-subtitle-animation');

        // Mark that we've shown the animation in this session
        sessionStorage.setItem('pageTitleAnimationShown', 'true');
    }
});

// Load cycles for the filiere
function loadCycles(filiereId) {
    fetch(`../../route/defineGrpRoute.php?cycles_by_filiere=${filiereId}`)
        .then(response => response.json())
        .then(data => {
            if (data.data) {
                displayCycles(data.data, filiereId);
            } else {
                showError("Erreur lors du chargement des cycles");
            }
        })
        .catch(error => {
            console.error('Error loading cycles:', error);
            showError("Erreur lors du chargement des cycles");
        });
}

// Display cycles
function displayCycles(cycles, filiereId) {
    const container = $('#cycles-container');
    container.empty();

    if (cycles.length === 0) {
        container.html('<div class="alert alert-info">Aucun cycle trouvé pour cette filière.</div>');
        return;
    }

    // Create a card for each cycle
    cycles.forEach(cycle => {
        const cycleCard = createCycleCard(cycle, filiereId);
        container.append(cycleCard);
    });
}

// Create a cycle card
function createCycleCard(cycle, filiereId) {
    // Get cycle class based on cycle ID
    const cycleClass = getCycleClass(cycle.id);

    // Get sample modules for this cycle to display in the card
    let sampleModules = [];
    fetch(`../../route/defineGrpRoute.php?filiere_id=${filiereId}&cycle_id=${cycle.id}`)
        .then(response => response.json())
        .then(data => {
            if (data.data && data.data.length > 0) {
                sampleModules = data.data.slice(0, 3); // Get up to 3 sample modules

                // Update the modules list in the card
                const modulesList = $(`#sample-modules-${cycle.id}`);
                if (sampleModules.length > 0) {
                    let moduleHtml = '<div class="sample-modules mt-2">';
                    sampleModules.forEach(module => {
                        moduleHtml += `<span class="badge bg-light text-dark me-1 mb-1">${module.nom}</span>`;
                    });

                    if (data.data.length > 3) {
                        moduleHtml += `<span class="badge bg-secondary">+${data.data.length - 3} autres</span>`;
                    }

                    moduleHtml += '</div>';
                    modulesList.html(moduleHtml);
                }
            }
        })
        .catch(error => {
            console.error('Error loading sample modules:', error);
        });

    // Create the card
    const card = $(`
        <div class="cycle-card ${cycleClass} mb-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="mb-0">${cycle.nom}</h3>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <p class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Définissez le nombre de groupes TD et TP pour tous les modules du ${cycle.nom}.
                            </p>
                            <div id="sample-modules-${cycle.id}">
                                <div class="spinner-border spinner-border-sm text-secondary" role="status">
                                    <span class="visually-hidden">Chargement...</span>
                                </div>
                                <span class="text-muted ms-2">Chargement des modules...</span>
                            </div>
                        </div>
                    </div>
                    <form class="cycle-form" data-cycle-id="${cycle.id}" data-filiere-id="${filiereId}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="nb_groupe_td_${cycle.id}" class="form-label">Nombre de groupes TD:</label>
                                    <input type="number" class="form-control" id="nb_groupe_td_${cycle.id}"
                                           name="nb_groupe_td" value="0" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="nb_groupe_tp_${cycle.id}" class="form-label">Nombre de groupes TP:</label>
                                    <input type="number" class="form-control" id="nb_groupe_tp_${cycle.id}"
                                           name="nb_groupe_tp" value="0" min="0" required>
                                </div>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <button type="button" class="btn btn-primary save-cycle" data-cycle-id="${cycle.id}">
                                <i class="fas fa-save me-2"></i>Appliquer à tous les modules
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `);

    // Add event listener for the save button
    card.find('.save-cycle').on('click', function() {
        const cycleId = $(this).data('cycle-id');
        const form = $(this).closest('form');

        // Validate form
        if (!form[0].checkValidity()) {
            form.addClass('was-validated');
            return;
        }

        const nbGroupeTd = form.find('input[name="nb_groupe_td"]').val();
        const nbGroupeTp = form.find('input[name="nb_groupe_tp"]').val();
        const filiereId = form.data('filiere-id');

        updateCycleGroups(filiereId, cycleId, nbGroupeTd, nbGroupeTp);
    });

    return card;
}

// Update groups for all modules in a cycle
function updateCycleGroups(filiereId, cycleId, nbGroupeTd, nbGroupeTp) {
    const data = {
        nb_groupe_td: parseInt(nbGroupeTd),
        nb_groupe_tp: parseInt(nbGroupeTp)
    };

    // Show loading state
    const saveButton = $(`.save-cycle[data-cycle-id="${cycleId}"]`);
    const originalText = saveButton.html();
    saveButton.html('<i class="fas fa-spinner fa-spin me-2"></i>Mise à jour en cours...');
    saveButton.prop('disabled', true);

    fetch(`../../route/defineGrpRoute.php?filiere_id=${filiereId}&cycle_id=${cycleId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the sample modules display to show the new group numbers
            fetch(`../../route/defineGrpRoute.php?filiere_id=${filiereId}&cycle_id=${cycleId}`)
                .then(response => response.json())
                .then(moduleData => {
                    if (moduleData.data && moduleData.data.length > 0) {
                        const sampleModules = moduleData.data.slice(0, 3);
                        const modulesList = $(`#sample-modules-${cycleId}`);

                        if (sampleModules.length > 0) {
                            let moduleHtml = '<div class="sample-modules mt-2">';
                            sampleModules.forEach(module => {
                                moduleHtml += `<span class="badge bg-light text-dark me-1 mb-1">${module.nom}</span>`;
                            });

                            if (moduleData.data.length > 3) {
                                moduleHtml += `<span class="badge bg-secondary">+${moduleData.data.length - 3} autres</span>`;
                            }

                            moduleHtml += '</div>';
                            modulesList.html(moduleHtml);
                        }
                    }
                });

            // Show success message with more details
            let message = `<strong>Mise à jour réussie!</strong><br>`;
            message += `${data.affected_modules} modules concernés<br><br>`;
            message += `Unités d'enseignement mises à jour:<br>`;
            message += `- ${data.td_units_updated} unité${data.td_units_updated > 1 ? 's' : ''} TD avec ${nbGroupeTd} groupe${nbGroupeTd > 1 ? 's' : ''}<br>`;
            message += `- ${data.tp_units_updated} unité${data.tp_units_updated > 1 ? 's' : ''} TP avec ${nbGroupeTp} groupe${nbGroupeTp > 1 ? 's' : ''}`;

            showSuccess(message);
        } else {
            showError(data.error || "Erreur lors de la mise à jour des groupes");
        }

        // Restore button state
        saveButton.html(originalText);
        saveButton.prop('disabled', false);
    })
    .catch(error => {
        console.error('Error updating cycle groups:', error);
        showError("Erreur lors de la mise à jour des groupes");

        // Restore button state
        saveButton.html(originalText);
        saveButton.prop('disabled', false);
    });
}

// Helper function to get cycle class based on cycle ID
function getCycleClass(cycleId) {
    switch (parseInt(cycleId)) {
        case 1: return 'cycle1';
        case 2: return 'cycle2';
        case 3: return 'cycle3';
        default: return '';
    }
}

// Show success message
function showSuccess(message) {
    // Remove any existing alerts
    $('.alert-success').remove();

    const alert = $(`
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <div class="alert-message">${message}</div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `);

    $('.container-fluid').prepend(alert);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        alert.alert('close');
    }, 5000);
}

// Show error message
function showError(message) {
    // Remove any existing alerts
    $('.alert-danger').remove();

    const alert = $(`
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <div class="alert-message">${message}</div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `);

    $('.container-fluid').prepend(alert);

    // Auto-dismiss after 7 seconds
    setTimeout(() => {
        alert.alert('close');
    }, 7000);
}
