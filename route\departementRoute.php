<?php
require_once '../model/departementModel.php';
require_once '../utils/response.php';

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        try {
            $departments = getAllDepartements();
            jsonResponse($departments);
        } catch (Exception $e) {
            jsonResponse(['error' => $e->getMessage()], 500);
        }
        break;
    
    default:
        jsonResponse(['error' => 'Method not allowed'], 405);
        break;
}
?>