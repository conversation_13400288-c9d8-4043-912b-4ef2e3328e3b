/* Upload Grades Page Styles */

/* Module Header Styles */
.module-header {
    max-height: 250px;
    background-color: #f8f9fa;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin-bottom: 20px;
}

.university-section {
    background: linear-gradient(to right, #f8f9fa, #e9ecef);
    padding: 10px 15px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
}

.university-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
    margin-right: 15px;
}

.university-info {
    flex: 1;
}

.university-name {
    color: #3f51b5;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0;
    line-height: 1.2;
}

.university-school {
    color: #666;
    font-size: 0.8rem;
    margin-bottom: 0;
    line-height: 1.2;
}

.academic-year {
    background-color: #e8eaf6;
    color: #3f51b5;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-left: 15px;
}

.module-info-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1px;
    background-color: #e9ecef;
}

.grid-item {
    padding: 10px 15px;
    background-color: #fff;
}

.grid-item.header {
    background-color: #f1f8e9;
    font-weight: 500;
    color: #558b2f;
    font-size: 0.85rem;
}

.grid-item.value {
    font-weight: 400;
    color: #333;
    font-size: 0.9rem;
}

.grid-item.highlight {
    background-color: #e3f2fd;
}

.grid-item.span-2 {
    grid-column: span 2;
}

.grid-item.span-3 {
    grid-column: span 3;
}

.grid-item.span-4 {
    grid-column: span 4;
}

.module-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #3f51b5;
    padding: 10px 15px;
    background-color: #e8eaf6;
    border-bottom: 1px solid #c5cae9;
}

.personnel-info {
    display: flex;
    padding: 10px 15px;
    background-color: #f5f5f5;
    border-top: 1px solid #e0e0e0;
}

.personnel-item {
    display: flex;
    align-items: center;
    margin-right: 30px;
}

.personnel-item i {
    color: #7986cb;
    margin-right: 8px;
    font-size: 1rem;
}

.personnel-label {
    color: #666;
    font-size: 0.85rem;
    margin-right: 5px;
}

.personnel-value {
    color: #333;
    font-weight: 500;
    font-size: 0.9rem;
}

/* Main container styles */
.container-fluid {
    padding-top: 20px;
}

/* Card styles */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 25px;
    overflow: hidden;
}

.card-header {
    background: linear-gradient(to right, #f8f9fa, #e9ecef);
    border-bottom: none;
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
}

/* Filter card styles */
.filter-card {
    background: linear-gradient(to right bottom, #ffffff, #f8f9fa);
}

.filter-icon i {
    font-size: 1.2rem;
    color: #6c63ff;
}

.filter-label {
    font-weight: 500;
    margin-bottom: 8px;
    color: #495057;
}

.filter-select {
    border-radius: 10px;
    border: 1px solid #ced4da;
    padding: 10px 15px;
    width: 100%;
    transition: all 0.3s ease;
}

.filter-select:focus {
    border-color: #6c63ff;
    box-shadow: 0 0 0 0.2rem rgba(108, 99, 255, 0.25);
}

.filter-group {
    margin-bottom: 15px;
}

/* Button styles */
.button-container {
    margin-top: 15px;
}

.btn-primary {
    background: linear-gradient(to right, #6c63ff, #4e42f5);
    border: none;
    border-radius: 10px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(to right, #5a52d5, #3f35c8);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 99, 255, 0.3);
}

.btn-success {
    background: linear-gradient(to right, #28a745, #20c997);
    border: none;
    border-radius: 10px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-success:hover {
    background: linear-gradient(to right, #218838, #1aa179);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

/* Applied filters styles */
.applied-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.filter-badge {
    background: linear-gradient(to right, #e9ecef, #dee2e6);
    border-radius: 20px;
    padding: 8px 15px;
    font-size: 0.9rem;
    color: #495057;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.filter-badge-label {
    font-weight: 600;
}

/* Table styles */
.table {
    width: 100%;
    margin-bottom: 0;
}

.table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    border-top: none;
    padding: 15px;
}

.table td {
    padding: 15px;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(108, 99, 255, 0.05);
}

.table-hover tbody tr:hover {
    background-color: rgba(108, 99, 255, 0.1);
}

/* Grade input styles */
.grade-input {
    width: 80px;
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 8px 12px;
    text-align: center;
    transition: all 0.3s ease;
}

.grade-input:focus {
    border-color: #6c63ff;
    box-shadow: 0 0 0 0.2rem rgba(108, 99, 255, 0.25);
    outline: none;
}

/* Animation styles */
.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Alert styles */
.alert {
    border-radius: 10px;
    padding: 15px 20px;
}

.alert-info {
    background-color: #e1f5fe;
    border-color: #b3e5fc;
    color: #0288d1;
}

.alert-success {
    background-color: #e8f5e9;
    border-color: #c8e6c9;
    color: #2e7d32;
}

.alert-warning {
    background-color: #fff8e1;
    border-color: #ffecb3;
    color: #ff8f00;
}

.alert-danger {
    background-color: #ffebee;
    border-color: #ffcdd2;
    color: #c62828;
}

/* Toast notification styles */
.toast {
    max-width: 350px;
    font-size: 0.875rem;
    background-color: rgba(255, 255, 255, 0.95);
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 0.5rem;
    border-left: 4px solid #6c63ff;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.toast.show {
    opacity: 1;
}

.toast.success {
    border-left-color: #28a745;
}

.toast.error {
    border-left-color: #dc3545;
}

.toast.warning {
    border-left-color: #ffc107;
}

.toast.info {
    border-left-color: #17a2b8;
}

.toast-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 0.75rem 1rem;
}

.toast-body {
    padding: 0.75rem 1rem;
    color: #495057;
}

.toast-icon {
    margin-right: 0.5rem;
    font-size: 1.25rem;
}

.toast-icon.success {
    color: #28a745;
}

.toast-icon.error {
    color: #dc3545;
}

.toast-icon.warning {
    color: #ffc107;
}

.toast-icon.info {
    color: #17a2b8;
}

/* Status message styles */
#status-message-container {
    max-width: 80%;
    z-index: 1050;
}

#status-message {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    margin-bottom: 0;
    animation: fadeInDown 0.5s ease-in-out;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translate(-50%, -20px);
    }
    to {
        opacity: 1;
        transform: translate(-50%, 0);
    }
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .module-info-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .grid-item.span-2,
    .grid-item.span-3,
    .grid-item.span-4 {
        grid-column: span 2;
    }

    .personnel-info {
        flex-wrap: wrap;
    }

    .personnel-item {
        margin-bottom: 5px;
        width: 100%;
    }
}

@media (max-width: 768px) {
    .university-section {
        flex-direction: column;
        text-align: center;
        padding: 10px;
    }

    .university-logo {
        margin-right: 0;
        margin-bottom: 8px;
    }

    .academic-year {
        margin-left: 0;
        margin-top: 8px;
    }

    .module-title {
        text-align: center;
    }

    .filter-group {
        margin-bottom: 15px;
    }

    .card-header {
        padding: 12px 15px;
    }

    .card-body {
        padding: 15px;
    }

    .table th, .table td {
        padding: 10px;
    }
}

@media (max-width: 576px) {
    .module-info-grid {
        grid-template-columns: 1fr;
    }

    .grid-item.span-2,
    .grid-item.span-3,
    .grid-item.span-4 {
        grid-column: span 1;
    }
}