        <?php
        // Récupérer les informations de l'utilisateur connecté
        // Les compteurs de notifications et messages seront chargés dynamiquement via JavaScript
        $userName = isset($_SESSION['user']['username']) ? $_SESSION['user']['username'] : 'Admin';
        $userRole = isset($_SESSION['user']['role']) ? $_SESSION['user']['role'] : 'admin';
        ?>

        <!-- Header -->
        <header class="header bg-white border-bottom border-gray">
          <div class="d-flex justify-content-between align-items-center px-3 py-3">
            <div class="d-flex d-lg-none">
              <button class="btn btn-link text-decoration-none" id="sidebarToggle">
                <i class="bi bi-list fs-4"></i>
              </button>
            </div>

            <div class="search-bar d-none d-md-block">
              <form class="position-relative">
                <i class="bi bi-search position-absolute search-icon"></i>
                <input type="search" class="form-control search-input" placeholder="Search...">
              </form>
            </div>

            <div class="header-icons d-flex align-items-center">
              <div class="position-relative mx-2">
                  <?php
                  // Déterminer le chemin correct en fonction du rôle
                  $notificationsPath = '';
                  switch ($userRole) {
                      case 'admin':
                          $notificationsPath = '../admin/notifications.php';
                          break;
                      case 'chef de departement':
                          $notificationsPath = '../chef/notifications.php';
                          break;
                      case 'coordinateur':
                          $notificationsPath = '../coordinator/notifications.php';
                          break;
                      case 'enseignant':
                          $notificationsPath = '../enseignant/notifications.php';
                          break;
                      case 'vacataire':
                          $notificationsPath = '../vacataire/notifications.php';
                          break;
                      default:
                          $notificationsPath = '../admin/notifications.php';
                  }
                  ?>
                  <a href="<?php echo $notificationsPath; ?>" class="btn btn-link text-decoration-none position-relative">
                      <i class="bi bi-bell fs-5"></i>
                      <span class="notification-badge d-none" id="notifications-count">0</span>
                  </a>
              </div>

              <div class="position-relative mx-2">
                  <?php
                  // Déterminer le chemin correct en fonction du rôle
                  $messagesPath = '';
                  switch ($userRole) {
                      case 'admin':
                          $messagesPath = '../admin/messages.php';
                          break;
                      case 'chef de departement':
                          $messagesPath = '../chef/messages.php';
                          break;
                      case 'coordinateur':
                          $messagesPath = '../coordinator/messages.php';
                          break;
                      case 'enseignant':
                          $messagesPath = '../enseignant/messages.php';
                          break;
                      case 'vacataire':
                          $messagesPath = '../vacataire/messages.php';
                          break;
                      default:
                          $messagesPath = '../admin/messages.php';
                  }
                  ?>
                  <a href="<?php echo $messagesPath; ?>" class="btn btn-link text-decoration-none position-relative">
                      <i class="bi bi-envelope fs-5"></i>
                      <span class="notification-badge d-none" id="messages-count">0</span>
                  </a>
              </div>

              <div class="dropdown ms-2">
                <button class="btn btn-link dropdown-toggle text-decoration-none d-flex align-items-center" type="button" data-bs-toggle="dropdown">
                  <img src="../assets/img/default-profile.svg" alt="User" class="rounded-circle border" width="32" height="32">
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                  <li><span class="dropdown-item-text"><?php echo $userName; ?></span></li>
                  <li><span class="dropdown-item-text text-muted small"><?php echo ucfirst($userRole); ?></span></li>
                  <li><hr class="dropdown-divider"></li>
                  <?php
                  // Déterminer le chemin correct en fonction du rôle
                  $profilePath = '';
                  $settingsPath = '';
                  switch ($userRole) {
                      case 'admin':
                          $profilePath = '../admin/profile.php';
                          $settingsPath = '../admin/settings.php';
                          break;
                      case 'chef de departement':
                          $profilePath = '../chef/profile.php';
                          $settingsPath = '../chef/settings.php';
                          break;
                      case 'coordinateur':
                          $profilePath = '../coordinator/profile.php';
                          $settingsPath = '../coordinator/settings.php';
                          break;
                      case 'enseignant':
                          $profilePath = '../enseignant/profile.php';
                          $settingsPath = '../enseignant/settings.php';
                          break;
                      case 'vacataire':
                          $profilePath = '../vacataire/profile.php';
                          $settingsPath = '../vacataire/settings.php';
                          break;
                      default:
                          $profilePath = '../admin/profile.php';
                          $settingsPath = '../admin/settings.php';
                  }
                  ?>
                  <li><a class="dropdown-item" href="<?php echo $profilePath; ?>">Profile</a></li>
                  <li><a class="dropdown-item" href="<?php echo $settingsPath; ?>">Settings</a></li>
                  <li><hr class="dropdown-divider"></li>
                  <li><a class="dropdown-item" href="javascript:void(0);" onclick="logout()">Log out</a></li>

                  <script>
                  function logout() {
                      // Envoyer une requête de déconnexion
                      fetch('../../route/authRoute.php', {
                          method: 'POST',
                          headers: {
                              'Content-Type': 'application/json'
                          },
                          body: JSON.stringify({
                              action: 'logout'
                          })
                      })
                      .then(response => response.json())
                      .then(data => {
                          if (data.success) {
                              // Rediriger vers la page de connexion
                              window.location.href = data.redirect || '../../index.php';
                          } else {
                              // En cas d'erreur, rediriger quand même vers la page de connexion
                              window.location.href = '../../index.php';
                          }
                      })
                      .catch(error => {
                          console.error('Erreur lors de la déconnexion:', error);
                          // En cas d'erreur, rediriger quand même vers la page de connexion
                          window.location.href = '../../index.php';
                      });
                  }
                  </script>
                </ul>
              </div>
            </div>
          </div>
        </header>

        <!-- Script pour les compteurs de notifications et messages -->
        <script src="../assets/js/header-counters.js"></script>