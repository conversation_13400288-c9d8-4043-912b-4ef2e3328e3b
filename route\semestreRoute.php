<?php
require_once "../controller/semestreController.php";

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        // Check which action is requested
        $action = isset($_GET['action']) ? $_GET['action'] : '';

        switch ($action) {
            case 'getSemestresByFiliereAndLevel':
                getSemestresByFiliereAndLevelAPI();
                break;
            default:
                // Default action is to get all semestres (with optional niveau_id filter)
                getAllSemestresAPI();
                break;
        }
        break;
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        break;
}
?>