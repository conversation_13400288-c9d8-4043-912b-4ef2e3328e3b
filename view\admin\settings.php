<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';

require_once "../../config/constants.php";

$pageTitle = "Paramètres";
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - UniAdmin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .settings-card {
            transition: transform 0.3s, box-shadow 0.3s;
            margin-bottom: 20px;
            border-radius: 15px;
            border: none;
        }
        .settings-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .settings-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }
        .card-body {
            text-align: center;
            padding: 30px 20px;
            border-radius: 15px;
        }
        /* Couleurs pastel pour les cartes */
        .card-users {
            background-color: #E0F7FA; /* Bleu pastel */
        }
        .card-users .settings-icon {
            color: #00ACC1;
        }
        .card-security {
            background-color: #F1F8E9; /* Vert pastel */
        }
        .card-security .settings-icon {
            color: #7CB342;
        }
        .card-email {
            background-color: #FFF3E0; /* Orange pastel */
        }
        .card-email .settings-icon {
            color: #FF9800;
        }
        .card-backup {
            background-color: #E8EAF6; /* Indigo pastel */
        }
        .card-backup .settings-icon {
            color: #3F51B5;
        }
        .card-general {
            background-color: #FCE4EC; /* Rose pastel */
        }
        .card-general .settings-icon {
            color: #EC407A;
        }
        .card-logs {
            background-color: #F3E5F5; /* Violet pastel */
        }
        .card-logs .settings-icon {
            color: #9C27B0;
        }
        /* Style pour les en-têtes de carte */
        .card-header {
            border-radius: 15px 15px 0 0 !important;
            padding: 15px 20px;
        }
        .card-header.bg-pastel-blue {
            background-color: #81D4FA !important;
            color: #0D47A1;
        }
        .card-header.bg-pastel-green {
            background-color: #A5D6A7 !important;
            color: #1B5E20;
        }
        /* Style pour les badges */
        .badge.bg-pastel {
            background-color: #B3E5FC !important;
            color: #01579B;
        }
        /* Style pour les boutons */
        .btn-pastel-blue {
            background-color: #4FC3F7;
            border-color: #4FC3F7;
            color: white;
        }
        .btn-pastel-blue:hover {
            background-color: #29B6F6;
            border-color: #29B6F6;
            color: white;
        }
        .btn-pastel-green {
            background-color: #81C784;
            border-color: #81C784;
            color: white;
        }
        .btn-pastel-green:hover {
            background-color: #66BB6A;
            border-color: #66BB6A;
            color: white;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
    <?php include "../includes/sidebar.php"; ?>

    <div class="main-content">
        <?php include "../includes/header.php"; ?>

        <div class="container-fluid mt-4">
            <div class="row">
                <div class="col-12">
                    <div class="card mb-4">
                        <div class="card-header bg-pastel-blue">
                            <h5 class="mb-0">Paramètres du système</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card settings-card">
                                        <div class="card-body card-users">
                                            <i class="bi bi-people-fill settings-icon"></i>
                                            <h5 class="card-title">Gestion des comptes utilisateurs</h5>
                                            <p class="card-text">Gérer les comptes utilisateurs, activer/désactiver des comptes, et plus encore.</p>
                                            <a href="<?php echo BASE_URL; ?>/view/admin/user-accounts.php" class="btn btn-pastel-blue">
                                                <i class="bi bi-arrow-right"></i> Accéder
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="card settings-card">
                                        <div class="card-body card-security">
                                            <i class="bi bi-shield-lock-fill settings-icon"></i>
                                            <h5 class="card-title">Sécurité</h5>
                                            <p class="card-text">Configurer les paramètres de sécurité, les politiques de mot de passe, etc.</p>
                                            <button class="btn btn-secondary" disabled>
                                                <i class="bi bi-clock"></i> Bientôt disponible
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="card settings-card">
                                        <div class="card-body card-email">
                                            <i class="bi bi-envelope-fill settings-icon"></i>
                                            <h5 class="card-title">Configuration des emails</h5>
                                            <p class="card-text">Configurer les paramètres d'envoi d'emails et les modèles.</p>
                                            <button class="btn btn-secondary" disabled>
                                                <i class="bi bi-clock"></i> Bientôt disponible
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card settings-card">
                                        <div class="card-body card-backup">
                                            <i class="bi bi-database-fill settings-icon"></i>
                                            <h5 class="card-title">Sauvegarde et restauration</h5>
                                            <p class="card-text">Gérer les sauvegardes de la base de données et les restaurations.</p>
                                            <button class="btn btn-secondary" disabled>
                                                <i class="bi bi-clock"></i> Bientôt disponible
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="card settings-card">
                                        <div class="card-body card-general">
                                            <i class="bi bi-gear-fill settings-icon"></i>
                                            <h5 class="card-title">Paramètres généraux</h5>
                                            <p class="card-text">Configurer les paramètres généraux du système.</p>
                                            <button class="btn btn-secondary" disabled>
                                                <i class="bi bi-clock"></i> Bientôt disponible
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="card settings-card">
                                        <div class="card-body card-services">
                                            <i class="bi bi-toggles2 settings-icon"></i>
                                            <h5 class="card-title">Gestion des Services</h5>
                                            <p class="card-text">Contrôler la disponibilité des services système avec activation temporisée.</p>
                                            <a href="<?php echo BASE_URL; ?>/view/admin/service-management.php" class="btn btn-pastel-blue">
                                                <i class="bi bi-arrow-right"></i> Accéder
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="card settings-card">
                                        <div class="card-body card-logs">
                                            <i class="bi bi-file-earmark-text-fill settings-icon"></i>
                                            <h5 class="card-title">Journaux système</h5>
                                            <p class="card-text">Consulter les journaux système et les activités des utilisateurs.</p>
                                            <button class="btn btn-secondary" disabled>
                                                <i class="bi bi-clock"></i> Bientôt disponible
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header bg-pastel-green">
                            <h5 class="mb-0">Informations système</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Informations du serveur</h6>
                                    <ul class="list-group mb-4">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Version PHP
                                            <span class="badge bg-pastel"><?php echo phpversion(); ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Serveur Web
                                            <span class="badge bg-pastel"><?php echo $_SERVER['SERVER_SOFTWARE']; ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Base de données
                                            <span class="badge bg-pastel">MySQL</span>
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>Informations de l'application</h6>
                                    <ul class="list-group">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Version
                                            <span class="badge bg-pastel">1.0.0</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Date d'installation
                                            <span class="badge bg-pastel"><?php echo date('Y-m-d'); ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Environnement
                                            <span class="badge bg-pastel">Production</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/dashboard-dynamic.js"></script>
    <script src="../assets/js/dashboard-chart.js"></script>
    <script src="../assets/js/dashboard-notifications.js"></script>
    <!-- Notifications JS -->
    <script src="../assets/js/notifications.js"></script>
    <script src="../assets/js/sidebar.js"></script>
</body>
</html>
