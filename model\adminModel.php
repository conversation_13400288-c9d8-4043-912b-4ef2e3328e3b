<?php
require_once __DIR__ . "/../config/db.php";

function getAllAdmins() {
    $conn = getConnection();
    $query = "SELECT * FROM admin";
    $result = mysqli_query($conn, $query);
    $admins = mysqli_fetch_all($result, MYSQLI_ASSOC);
    mysqli_close($conn);
    return $admins;
}

function getAdminByCNI($cni) {
    $conn = getConnection();
    $cni = mysqli_real_escape_string($conn, $cni);
    $query = "SELECT * FROM admin WHERE CNI = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "s", $cni);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $admin = mysqli_fetch_assoc($result);
    mysqli_close($conn);
    return $admin;
}

function createAdmin($data) {
    $conn = getConnection();

    // Validate required fields
    $required_fields = ['CNI', 'nom', 'prenom', 'email', 'tele', 'date_naissance',
                       'lieu_naissance', 'sexe', 'ville', 'pays', 'date_debut_travail'];
    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            return false;
        }
    }

    // Validate sexe value
    if (!in_array($data['sexe'], ['masculin', 'féminin'])) {
        return false;
    }

    // Sanitize inputs
    foreach ($data as $key => $value) {
        $data[$key] = mysqli_real_escape_string($conn, $value);
    }

    // Handle photo_url (optional)
    $photo_url = isset($data['photo_url']) ? $data['photo_url'] : null;

    $query = "INSERT INTO admin (CNI, nom, prenom, email, tele, date_naissance,
              lieu_naissance, sexe, ville, pays, date_debut_travail, photo_url)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "ssssssssssss",
        $data['CNI'], $data['nom'], $data['prenom'], $data['email'], $data['tele'],
        $data['date_naissance'], $data['lieu_naissance'], $data['sexe'], $data['ville'],
        $data['pays'], $data['date_debut_travail'], $photo_url
    );
    $result = mysqli_stmt_execute($stmt);
    mysqli_close($conn);
    return $result;
}

function updateAdmin($cni, $data) {
    $conn = getConnection();

    // Validate CNI
    if (empty($cni)) {
        return false;
    }

    // Validate sexe if provided
    if (isset($data['sexe'])) {
        // Journaliser la valeur reçue pour le sexe
        error_log("Model - Valeur reçue pour le sexe: " . $data['sexe']);

        if (!in_array($data['sexe'], ['masculin', 'féminin'])) {
            error_log("Model - Valeur invalide pour le sexe: " . $data['sexe']);
            return false;
        }
    }

    // Sanitize inputs
    $cni = mysqli_real_escape_string($conn, $cni);
    foreach ($data as $key => $value) {
        $data[$key] = mysqli_real_escape_string($conn, $value);
    }

    // Si nous mettons à jour uniquement la photo de profil
    if (count($data) === 1 && isset($data['photo_url'])) {
        $query = "UPDATE admin SET photo_url=? WHERE CNI=?";
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, "ss", $data['photo_url'], $cni);
    }
    // Si nous mettons à jour tous les champs
    else if (isset($data['nom']) && isset($data['prenom']) && isset($data['email']) &&
             isset($data['tele']) && isset($data['date_naissance']) && isset($data['lieu_naissance']) &&
             isset($data['sexe']) && isset($data['ville']) && isset($data['pays']) &&
             isset($data['date_debut_travail'])) {

        // Valeur par défaut pour photo_url si non fournie
        if (!isset($data['photo_url'])) {
            $data['photo_url'] = '';
        }

        $query = "UPDATE admin SET nom=?, prenom=?, email=?, tele=?, date_naissance=?,
                  lieu_naissance=?, sexe=?, ville=?, pays=?, date_debut_travail=?, photo_url=?
                  WHERE CNI=?";
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, "ssssssssssss",
            $data['nom'], $data['prenom'], $data['email'], $data['tele'], $data['date_naissance'],
            $data['lieu_naissance'], $data['sexe'], $data['ville'], $data['pays'],
            $data['date_debut_travail'], $data['photo_url'], $cni
        );
    }
    // Sinon, nous construisons une requête dynamique pour les champs fournis
    else {
        $setClause = [];
        $types = "";
        $values = [];

        // Construire la clause SET et les paramètres
        foreach ($data as $key => $value) {
            $setClause[] = "{$key}=?";
            $types .= "s"; // Tous les champs sont des chaînes
            $values[] = $value;
        }

        // Ajouter le CNI pour la clause WHERE
        $types .= "s";
        $values[] = $cni;

        $query = "UPDATE admin SET " . implode(", ", $setClause) . " WHERE CNI=?";
        $stmt = mysqli_prepare($conn, $query);

        // Créer un tableau de références pour bind_param
        $bindParams = array($stmt, $types);
        foreach ($values as $key => $value) {
            $bindParams[] = &$values[$key];
        }

        call_user_func_array('mysqli_stmt_bind_param', $bindParams);
    }

    $result = mysqli_stmt_execute($stmt);

    if (!$result) {
        error_log("Erreur SQL: " . mysqli_error($conn));
    }

    mysqli_close($conn);
    return $result;
}

function deleteAdmin($cni) {
    $conn = getConnection();

    // Validate CNI
    if (empty($cni)) {
        return false;
    }

    $cni = mysqli_real_escape_string($conn, $cni);
    $query = "DELETE FROM admin WHERE CNI=?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "s", $cni);
    $result = mysqli_stmt_execute($stmt);
    mysqli_close($conn);
    return $result;
}
?>
