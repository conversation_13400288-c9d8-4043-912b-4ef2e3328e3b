<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Debug - Affecter UE Vacataire</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .endpoint { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .loading { background-color: #fff3cd; border-color: #ffeaa7; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>API Debug - Affecter UE Vacataire</h1>
    
    <div id="session-info">
        <h2>Session Information</h2>
        <div id="session-data"></div>
    </div>

    <div class="endpoint" id="statistics">
        <h3>Statistics API</h3>
        <button onclick="testEndpoint('getStatistics', 'statistics')">Test Statistics</button>
        <div class="result" id="statistics-result"></div>
    </div>

    <div class="endpoint" id="vacantues">
        <h3>Vacant UEs API</h3>
        <button onclick="testEndpoint('getVacantUEs', 'vacantues')">Test Vacant UEs</button>
        <div class="result" id="vacantues-result"></div>
    </div>

    <div class="endpoint" id="vacataires">
        <h3>Vacataires API</h3>
        <button onclick="testEndpoint('getVacataires', 'vacataires')">Test Vacataires</button>
        <div class="result" id="vacataires-result"></div>
    </div>

    <div class="endpoint" id="assignment">
        <h3>Assignment API</h3>
        <button onclick="testAssignment()">Test Assignment</button>
        <div class="result" id="assignment-result"></div>
    </div>

    <script>
        // Initialize session
        fetch('route/affecterUEVacataireRoute.php?action=getStatistics')
            .then(response => response.text())
            .then(data => {
                document.getElementById('session-data').innerHTML = 
                    '<p>Session initialized. Check browser console for details.</p>';
                console.log('Session initialization response:', data);
            })
            .catch(error => {
                document.getElementById('session-data').innerHTML = 
                    '<p style="color: red;">Error initializing session: ' + error.message + '</p>';
            });

        async function testEndpoint(action, containerId) {
            const container = document.getElementById(containerId);
            const resultDiv = document.getElementById(containerId + '-result');
            
            container.className = 'endpoint loading';
            resultDiv.innerHTML = '<p>Loading...</p>';

            try {
                const response = await fetch(`route/affecterUEVacataireRoute.php?action=${action}`);
                const responseText = await response.text();
                
                console.log(`${action} response:`, responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    throw new Error('Invalid JSON response: ' + responseText.substring(0, 200));
                }

                if (data.error) {
                    container.className = 'endpoint error';
                    resultDiv.innerHTML = `
                        <p><strong>Error:</strong> ${data.error}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    container.className = 'endpoint success';
                    const dataCount = Array.isArray(data.data) ? data.data.length : 'N/A';
                    resultDiv.innerHTML = `
                        <p><strong>Success!</strong> Data count: ${dataCount}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                container.className = 'endpoint error';
                resultDiv.innerHTML = `
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
                console.error(`Error testing ${action}:`, error);
            }
        }

        async function testAssignment() {
            const container = document.getElementById('assignment');
            const resultDiv = document.getElementById('assignment-result');
            
            container.className = 'endpoint loading';
            resultDiv.innerHTML = '<p>Testing assignment...</p>';

            try {
                // First get vacataires and vacant UEs
                const vacatairesResponse = await fetch('route/affecterUEVacataireRoute.php?action=getVacataires');
                const vacatairesData = await vacatairesResponse.json();
                
                const uesResponse = await fetch('route/affecterUEVacataireRoute.php?action=getVacantUEs');
                const uesData = await uesResponse.json();

                if (vacatairesData.error || uesData.error) {
                    throw new Error('Cannot test assignment: ' + (vacatairesData.error || uesData.error));
                }

                if (!vacatairesData.data || vacatairesData.data.length === 0) {
                    throw new Error('No vacataires available for testing');
                }

                if (!uesData.data || uesData.data.length === 0) {
                    throw new Error('No vacant UEs available for testing');
                }

                // Test assignment with first vacataire and first UE
                const testData = {
                    vacataire_id: vacatairesData.data[0].id_enseignant,
                    ue_ids: [uesData.data[0].ue_id],
                    comments: 'Test assignment from debug page'
                };

                const assignResponse = await fetch('route/affecterUEVacataireRoute.php?action=assignUEs', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                const assignData = await assignResponse.json();

                if (assignData.error) {
                    container.className = 'endpoint error';
                    resultDiv.innerHTML = `
                        <p><strong>Assignment Error:</strong> ${assignData.error}</p>
                        <pre>${JSON.stringify(assignData, null, 2)}</pre>
                    `;
                } else {
                    container.className = 'endpoint success';
                    resultDiv.innerHTML = `
                        <p><strong>Assignment Success!</strong> ${assignData.message}</p>
                        <pre>${JSON.stringify(assignData, null, 2)}</pre>
                    `;
                }

            } catch (error) {
                container.className = 'endpoint error';
                resultDiv.innerHTML = `
                    <p><strong>Assignment Error:</strong> ${error.message}</p>
                `;
                console.error('Error testing assignment:', error);
            }
        }
    </script>
</body>
</html>
