<?php
require_once "../controller/moduleController.php";
require_once "../utils/response.php";

// Désactiver l'affichage des erreurs pour éviter de renvoyer du HTML
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Définir le type de contenu comme JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

// Capturer toutes les erreurs
try {
    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'GET':
            // Get action from query parameters
            $action = isset($_GET['action']) ? $_GET['action'] : '';
            
            switch ($action) {
                case 'getTeacherModules':
                    // Get modules for a specific teacher
                    $teacherId = isset($_GET['enseignant']) ? $_GET['enseignant'] : null;
                    $filiereId = isset($_GET['filiere']) ? $_GET['filiere'] : null;
                    $niveauId = isset($_GET['niveau']) ? $_GET['niveau'] : null;
                    
                    if (!$teacherId) {
                        jsonResponse(['error' => 'Teacher ID is required', 'success' => false], 400);
                        exit;
                    }
                    
                    getTeacherModulesAPI($teacherId, $filiereId, $niveauId);
                    break;
                
                case 'getModulesByDepartment':
                    if (!isset($_GET['department_id'])) {
                        jsonResponse(['error' => 'Department ID is required'], 400);
                    }
                    getModulesByDepartmentAPI($_GET['department_id']);
                    break;

                    
                default:
                    // Default to getting all modules
                    getAllModulesAPI();
                    break;
            }
            break;

        case 'POST':
            // Create a new module
            $data = json_decode(file_get_contents('php://input'), true);
            createModuleAPI($data);
            break;

        case 'PUT':
            // Update a module
            if (isset($_GET['id'])) {
                $data = json_decode(file_get_contents('php://input'), true);
                updateModuleAPI($_GET['id'], $data);
            } else {
                jsonResponse(['error' => 'Module ID is required for update'], 400);
            }
            break;

        case 'DELETE':
            // Delete a module
            if (isset($_GET['id'])) {
                deleteModuleAPI($_GET['id']);
            } else {
                jsonResponse(['error' => 'Module ID is required for deletion'], 400);
            }
            break;

        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
            break;
    }
} catch (Exception $e) {
    // Log the error
    error_log("Error in moduleRoute.php: " . $e->getMessage());

    // Return a JSON response with the error
    jsonResponse(['error' => 'Server error: ' . $e->getMessage()], 500);
}


