<?php
require_once __DIR__ . '/../config/db.php';

function getAllDemands($page = 1, $perPage = 8) {
    try {
        $conn = getConnection();
        if (!$conn) {
            throw new Exception("Database connection failed");
        }

        // Check if the demands table exists
        $tableCheckResult = $conn->query("SHOW TABLES LIKE 'demands'");
        if ($tableCheckResult->num_rows === 0) {
            throw new Exception("The demands table does not exist");
        }

        // Check the structure of the demands table
        $columnsResult = $conn->query("SHOW COLUMNS FROM demands");
        $columns = [];
        while ($column = $columnsResult->fetch_assoc()) {
            $columns[] = $column['Field'];
        }

        // Build the query based on available columns
        $selectColumns = ['id', 'title', 'description', 'type', 'created_at', 'status', 'message'];

        // Add optional columns if they exist
        if (in_array('author_name', $columns)) {
            $selectColumns[] = 'author_name';
        }

        if (in_array('author_avatar', $columns)) {
            $selectColumns[] = 'author_avatar';
        }

        if (in_array('author_id', $columns)) {
            $selectColumns[] = 'author_id';
        }

        $selectClause = implode(', ', $selectColumns);

        // Get total count for pagination
        $countQuery = "SELECT COUNT(*) as total FROM demands";
        $countResult = $conn->query($countQuery);
        $totalDemands = $countResult->fetch_assoc()['total'];
        $totalPages = ceil($totalDemands / $perPage);

        // Calculate offset for pagination
        $offset = ($page - 1) * $perPage;

        // Add pagination to the query
        $query = "SELECT $selectClause FROM demands ORDER BY created_at DESC LIMIT ?, ?";

        $stmt = $conn->prepare($query);
        $stmt->bind_param("ii", $offset, $perPage);

        if (!$stmt) {
            throw new Exception("Prepare failed: " . $conn->error);
        }

        if (!$stmt->execute()) {
            throw new Exception("Execute failed: " . $stmt->error);
        }

        $result = $stmt->get_result();
        $demands = [];

        while ($row = $result->fetch_assoc()) {
            $demands[] = $row;
        }

        return [
            'demands' => $demands,
            'pagination' => [
                'total' => $totalDemands,
                'perPage' => $perPage,
                'currentPage' => $page,
                'totalPages' => $totalPages
            ]
        ];
    } catch (Exception $e) {
        // Log the error for debugging
        error_log("Error in getAllDemands: " . $e->getMessage());
        throw $e; // Re-throw to be caught by the controller
    }
}


function updateDemandStatus($id, $status) {
    $conn = getConnection();
    $stmt = $conn->prepare("
        UPDATE demands
        SET status = ?
        WHERE id = ?
    ");
    $stmt->bind_param("si", $status, $id);
    return $stmt->execute();
}

function deleteDemand($id) {
    $conn = getConnection();
    $stmt = $conn->prepare("
        DELETE FROM demands
        WHERE id = ?
    ");
    $stmt->bind_param("i", $id);
    return $stmt->execute();
}

function createDemand($title, $description, $type, $author_name = null, $author_avatar = null, $author_id = null) {
    $conn = getConnection();

    // Check which columns exist in the table
    $columnsResult = $conn->query("SHOW COLUMNS FROM demands");
    $columns = [];
    while ($column = $columnsResult->fetch_assoc()) {
        $columns[] = $column['Field'];
    }

    // Build the query dynamically based on available columns
    $insertColumns = ['title', 'description', 'type'];
    $placeholders = ['?', '?', '?'];
    $bindTypes = 'sss';
    $bindParams = [$title, $description, $type];

    if (in_array('author_name', $columns) && $author_name) {
        $insertColumns[] = 'author_name';
        $placeholders[] = '?';
        $bindTypes .= 's';
        $bindParams[] = $author_name;
    }

    if (in_array('author_avatar', $columns) && $author_avatar) {
        $insertColumns[] = 'author_avatar';
        $placeholders[] = '?';
        $bindTypes .= 's';
        $bindParams[] = $author_avatar;
    }

    if (in_array('author_id', $columns) && $author_id) {
        $insertColumns[] = 'author_id';
        $placeholders[] = '?';
        $bindTypes .= 'i';
        $bindParams[] = $author_id;
    }

    $columnsStr = implode(', ', $insertColumns);
    $placeholdersStr = implode(', ', $placeholders);

    $query = "INSERT INTO demands ($columnsStr) VALUES ($placeholdersStr)";

    $stmt = $conn->prepare($query);

    if (!$stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }

    // Dynamically bind parameters
    $bindParamsArray = array_merge([$bindTypes], $bindParams);
    $bindParamsRefs = [];
    foreach ($bindParamsArray as $key => $value) {
        $bindParamsRefs[$key] = &$bindParamsArray[$key];
    }

    call_user_func_array([$stmt, 'bind_param'], $bindParamsRefs);

    return $stmt->execute();
}

function updateDemandMessage($id, $message) {
    $conn = getConnection();
    $stmt = $conn->prepare("
        UPDATE demands
        SET message = ?
        WHERE id = ?
    ");
    $stmt->bind_param("si", $message, $id);
    return $stmt->execute();
}
?>