<?php
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/notificationsModel.php';
require_once __DIR__ . '/configurationChargeModel.php';

/**
 * Create workload notification history table if it doesn't exist
 */
function ensureWorkloadNotificationHistoryTable() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in ensureWorkloadNotificationHistoryTable");
        return false;
    }

    $createTableSQL = "CREATE TABLE IF NOT EXISTS `workload_notification_history` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `teacher_id` int(11) NOT NULL,
        `academic_year` varchar(9) NOT NULL,
        `notification_type` enum('deficit','reminder','warning') DEFAULT 'deficit',
        `current_hours` int(11) NOT NULL,
        `required_hours` int(11) NOT NULL,
        `deficit_hours` int(11) NOT NULL,
        `notification_sent_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `sent_by` int(11) DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `idx_teacher_year` (`teacher_id`, `academic_year`),
        KEY `idx_sent_at` (`notification_sent_at`),
        CONSTRAINT `fk_workload_notif_teacher` FOREIGN KEY (`teacher_id`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE CASCADE,
        CONSTRAINT `fk_workload_notif_sender` FOREIGN KEY (`sent_by`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    $result = mysqli_query($conn, $createTableSQL);

    if (!$result) {
        error_log("Error creating workload_notification_history table: " . mysqli_error($conn));
        mysqli_close($conn);
        return false;
    }

    mysqli_close($conn);
    return true;
}

/**
 * Check if a teacher can receive a workload notification (rate limiting)
 * @param int $teacherId Teacher ID
 * @param string $academicYear Academic year
 * @param int $limitDays Number of days to wait between notifications (default: 7)
 * @return bool True if notification can be sent
 */
function canSendWorkloadNotification($teacherId, $academicYear, $limitDays = 7) {
    if (!ensureWorkloadNotificationHistoryTable()) {
        return false;
    }

    $conn = getConnection();
    if (!$conn) {
        return false;
    }

    $teacherId = mysqli_real_escape_string($conn, $teacherId);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);
    $limitDays = intval($limitDays);

    $query = "SELECT id FROM workload_notification_history 
              WHERE teacher_id = '$teacherId' 
              AND academic_year = '$academicYear' 
              AND notification_sent_at > DATE_SUB(NOW(), INTERVAL $limitDays DAY)
              ORDER BY notification_sent_at DESC 
              LIMIT 1";

    $result = mysqli_query($conn, $query);
    $canSend = mysqli_num_rows($result) == 0;

    mysqli_close($conn);
    return $canSend;
}

/**
 * Send workload deficit notification to a teacher
 * @param int $teacherId Teacher ID
 * @param string $academicYear Academic year
 * @param int $currentHours Current workload hours
 * @param int $requiredHours Required minimum hours
 * @param int $sentBy ID of the person sending the notification
 * @return array Result with success/error message
 */
function sendWorkloadDeficitNotification($teacherId, $academicYear, $currentHours, $requiredHours, $sentBy = null) {
    // Check rate limiting
    if (!canSendWorkloadNotification($teacherId, $academicYear)) {
        return ["error" => "Une notification a déjà été envoyée récemment à cet enseignant"];
    }

    $conn = getConnection();
    if (!$conn) {
        return ["error" => "Erreur de connexion à la base de données"];
    }

    try {
        // Get teacher information
        $teacherId = mysqli_real_escape_string($conn, $teacherId);
        $teacherQuery = "SELECT nom, prenom, email FROM enseignant WHERE id_enseignant = '$teacherId'";
        $teacherResult = mysqli_query($conn, $teacherQuery);

        if (!$teacherResult || mysqli_num_rows($teacherResult) == 0) {
            mysqli_close($conn);
            return ["error" => "Enseignant non trouvé"];
        }

        $teacher = mysqli_fetch_assoc($teacherResult);
        $teacherName = $teacher['prenom'] . ' ' . $teacher['nom'];
        $deficitHours = $requiredHours - $currentHours;

        // Create notification title and message
        $title = "Alerte: Charge de travail insuffisante - $academicYear";
        $message = "Cher(e) $teacherName,\n\n";
        $message .= "Nous vous informons que votre charge de travail actuelle pour l'année académique $academicYear est inférieure au minimum requis.\n\n";
        $message .= "Détails de votre charge de travail :\n";
        $message .= "• Heures actuelles : $currentHours h\n";
        $message .= "• Heures requises : $requiredHours h\n";
        $message .= "• Déficit : $deficitHours h\n\n";
        $message .= "Nous vous encourageons à prendre contact avec votre chef de département pour discuter des possibilités d'augmenter votre charge de travail.\n\n";
        $message .= "Cordialement,\n";
        $message .= "L'administration académique";

        // Create the notification
        $notificationResult = createNotification($title, $message, null, null, 'system');

        if (!$notificationResult) {
            mysqli_close($conn);
            return ["error" => "Erreur lors de la création de la notification"];
        }

        // Record in notification history
        $academicYear = mysqli_real_escape_string($conn, $academicYear);
        $currentHours = intval($currentHours);
        $requiredHours = intval($requiredHours);
        $deficitHours = intval($deficitHours);
        $sentBy = $sentBy ? intval($sentBy) : 'NULL';

        $historyQuery = "INSERT INTO workload_notification_history 
                        (teacher_id, academic_year, notification_type, current_hours, required_hours, deficit_hours, sent_by)
                        VALUES ('$teacherId', '$academicYear', 'deficit', '$currentHours', '$requiredHours', '$deficitHours', $sentBy)";

        $historyResult = mysqli_query($conn, $historyQuery);

        if (!$historyResult) {
            error_log("Error recording notification history: " . mysqli_error($conn));
        }

        mysqli_close($conn);
        return ["success" => "Notification envoyée avec succès à $teacherName"];

    } catch (Exception $e) {
        mysqli_close($conn);
        error_log("Error in sendWorkloadDeficitNotification: " . $e->getMessage());
        return ["error" => "Erreur lors de l'envoi de la notification: " . $e->getMessage()];
    }
}

/**
 * Send workload deficit notifications to multiple teachers
 * @param array $teachers Array of teacher data with workload information
 * @param string $academicYear Academic year
 * @param int $sentBy ID of the person sending the notifications
 * @return array Result with success/error counts
 */
function sendBulkWorkloadDeficitNotifications($teachers, $academicYear, $sentBy = null) {
    $successCount = 0;
    $errorCount = 0;
    $errors = [];

    foreach ($teachers as $teacher) {
        if (!isset($teacher['id_enseignant'], $teacher['current_hours'], $teacher['required_hours'])) {
            $errorCount++;
            $errors[] = "Données manquantes pour un enseignant";
            continue;
        }

        $result = sendWorkloadDeficitNotification(
            $teacher['id_enseignant'],
            $academicYear,
            $teacher['current_hours'],
            $teacher['required_hours'],
            $sentBy
        );

        if (isset($result['success'])) {
            $successCount++;
        } else {
            $errorCount++;
            $teacherName = ($teacher['prenom'] ?? '') . ' ' . ($teacher['nom'] ?? '');
            $errors[] = "Erreur pour $teacherName: " . ($result['error'] ?? 'Erreur inconnue');
        }
    }

    return [
        "success_count" => $successCount,
        "error_count" => $errorCount,
        "errors" => $errors,
        "message" => "$successCount notifications envoyées avec succès, $errorCount erreurs"
    ];
}

/**
 * Get workload notification history for a teacher
 * @param int $teacherId Teacher ID
 * @param string $academicYear Academic year (optional)
 * @return array Notification history
 */
function getWorkloadNotificationHistory($teacherId, $academicYear = null) {
    if (!ensureWorkloadNotificationHistoryTable()) {
        return ["error" => "Erreur d'initialisation de la table"];
    }

    $conn = getConnection();
    if (!$conn) {
        return ["error" => "Erreur de connexion à la base de données"];
    }

    $teacherId = mysqli_real_escape_string($conn, $teacherId);
    $whereClause = "WHERE teacher_id = '$teacherId'";

    if ($academicYear) {
        $academicYear = mysqli_real_escape_string($conn, $academicYear);
        $whereClause .= " AND academic_year = '$academicYear'";
    }

    $query = "SELECT wnh.*, e.nom as sender_nom, e.prenom as sender_prenom
              FROM workload_notification_history wnh
              LEFT JOIN enseignant e ON wnh.sent_by = e.id_enseignant
              $whereClause
              ORDER BY notification_sent_at DESC";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        mysqli_close($conn);
        return ["error" => "Erreur lors de la récupération de l'historique"];
    }

    $history = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $history[] = $row;
    }

    mysqli_close($conn);
    return $history;
}

/**
 * Get teachers below workload threshold for a department and academic year
 * @param int $departmentId Department ID
 * @param string $academicYear Academic year
 * @return array Teachers below threshold with their deficit information
 */
function getTeachersBelowWorkloadThreshold($departmentId, $academicYear) {
    require_once __DIR__ . '/affectationModel.php';

    // Get department workload data
    $workloadData = getDepartmentWorkloadByYear($departmentId, $academicYear);

    if (isset($workloadData['error'])) {
        return $workloadData;
    }

    $teachersBelowThreshold = [];

    foreach ($workloadData as $teacher) {
        // Get minimum workload requirement for this teacher's role
        $minWorkloadConfig = getMinimumWorkloadByRoleAndYear($teacher['role'], $academicYear);

        if (!isset($minWorkloadConfig['error'])) {
            $requiredHours = intval($minWorkloadConfig['charge_minimale']);
            $currentHours = intval($teacher['total_hours']);

            if ($currentHours < $requiredHours) {
                $teacher['required_hours'] = $requiredHours;
                $teacher['current_hours'] = $currentHours;
                $teacher['deficit_hours'] = $requiredHours - $currentHours;
                $teacher['deficit_percentage'] = round(($teacher['deficit_hours'] / $requiredHours) * 100, 1);
                $teachersBelowThreshold[] = $teacher;
            }
        }
    }

    // Sort by deficit (highest deficit first)
    usort($teachersBelowThreshold, function($a, $b) {
        return $b['deficit_hours'] - $a['deficit_hours'];
    });

    return $teachersBelowThreshold;
}

?>
