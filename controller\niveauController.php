<?php
require_once "../model/niveauModel.php";
require_once "../utils/response.php";

/**
 * Get all niveaux API endpoint
 */
function getAllNiveauxAPI() {
    $niveaux = getAllNiveaux();

    if (isset($niveaux['error'])) {
        jsonResponse(['error' => $niveaux['error']], 404);
    }

    jsonResponse(['data' => $niveaux], 200);
}

/**
 * Get niveaux by filiere ID API endpoint
 *
 * @param int $id_filiere Filiere ID
 */
function getNiveauxByFiliereAPI($id_filiere) {
    $niveaux = getNiveauxByFiliere($id_filiere);

    if (isset($niveaux['error'])) {
        jsonResponse(['error' => $niveaux['error']], 404);
    }

    jsonResponse(['data' => $niveaux], 200);
}

/**
 * Get niveau by ID API endpoint
 *
 * @param int $id Niveau ID
 */
function getNiveauByIdAPI($id) {
    $niveau = getNiveauById($id);

    if (isset($niveau['error'])) {
        jsonResponse(['error' => $niveau['error']], 404);
    }

    jsonResponse(['data' => $niveau], 200);
}

/**
 * Get niveaux by cycle ID API endpoint
 *
 * @param int $cycle_id Cycle ID
 */
function getNiveauxByCycleAPI($cycle_id) {
    $niveaux = getNiveauxByCycle($cycle_id);

    if (isset($niveaux['error'])) {
        jsonResponse(['error' => $niveaux['error']], 404);
    }

    jsonResponse(['data' => $niveaux], 200);
}