/**
 * Liste UE Departement JavaScript
 *
 * This file handles the dynamic functionality of the department modules page.
 */

$(document).ready(function() {
    // Get the department ID from the page
    const departmentId = getDepartmentId();

    if (departmentId) {
        // Load modules for this department
        loadDepartmentModules(departmentId);
    }

    // Initialize the units modal
    const unitsModal = new bootstrap.Modal(document.getElementById('unitsModal'));

    // Handle click on view units button
    $(document).on('click', '.btn-view-units', function() {
        const moduleId = $(this).data('module-id');
        const moduleName = $(this).data('module-name');

        // Update modal title
        $('#unitsModalLabel').html(`<i class="fas fa-book me-2"></i>Unités d'Enseignement: ${moduleName}`);

        // Show the modal
        unitsModal.show();

        // Load units for this module
        loadModuleUnits(moduleId);
    });
});

/**
 * Get the department ID from the page
 *
 * @returns {string|null} The department ID or null if not found
 */
function getDepartmentId() {
    // Try to extract from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('department_id')) {
        return urlParams.get('department_id');
    }

    // Try to find in the page content
    const departmentElement = document.querySelector('.card-title');
    if (departmentElement) {
        const departmentText = departmentElement.textContent;
        const match = departmentText.match(/Département:\s+(.+)/);
        if (match && match[1] !== 'Non spécifié') {
            // This is just the name, not the ID, but we'll use it for debugging
            console.log('Found department name:', match[1]);
        }
    }

    // If we can't find it in the URL or page content, check if there's a data attribute
    const departmentContainer = document.getElementById('filieres-container');
    if (departmentContainer && departmentContainer.dataset.departmentId) {
        return departmentContainer.dataset.departmentId;
    }

    // As a last resort, try to get it from the session via an AJAX call
    let sessionDepartmentId = null;
    $.ajax({
        url: '../../route/listerUEdepartRoute.php?action=get_department_id',
        method: 'GET',
        async: false,
        success: function(response) {
            if (response && response.department_id) {
                sessionDepartmentId = response.department_id;
            }
        },
        error: function(_, __, errorMsg) {
            console.error('Error getting department ID:', errorMsg);
            // Try with an alternative path
            $.ajax({
                url: '../route/listerUEdepartRoute.php?action=get_department_id',
                method: 'GET',
                async: false,
                success: function(response) {
                    if (response && response.department_id) {
                        sessionDepartmentId = response.department_id;
                    }
                },
                error: function(_, __, errorMsg2) {
                    console.error('Error getting department ID with alternative path:', errorMsg2);
                }
            });
        }
    });

    return sessionDepartmentId;
}

/**
 * Load modules for a department
 *
 * @param {string} departmentId The department ID
 */
function loadDepartmentModules(departmentId) {
    console.log('Loading modules for department ID:', departmentId);

    // Try the controller path first
    tryLoadModules(departmentId, '../../controller/listerUEdepartController.php?action=getModules', function(error) {
        // If controller path fails, try the route path
        console.error('Error with controller path:', error);
        tryLoadModules(departmentId, '../../route/listerUEdepartRoute.php?action=get_modules', function(error2) {
            // If route path fails, try alternative controller path
            console.error('Error with route path:', error2);
            tryLoadModules(departmentId, '../controller/listerUEdepartController.php?action=getModules', function(error3) {
                // If alternative controller path fails, try alternative route path
                console.error('Error with alternative controller path:', error3);
                tryLoadModules(departmentId, '../route/listerUEdepartRoute.php?action=get_modules', function(error4) {
                    // If all paths fail, display error message to user
                    console.error('Error with alternative route path:', error4);
                    $('#filieres-container').html(`
                        <div class="col-12">
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                Impossible de charger les modules. Veuillez rafraîchir la page ou contacter l'administrateur.
                            </div>
                        </div>
                    `);
                });
            });
        });
    });
}

// Store all filières data globally for filtering
let allFilieresData = [];

// Add event listener for filière filter
$(document).ready(function() {
    // Handle filière filter change
    $('#filiere-filter').on('change', function() {
        const selectedFiliereId = $(this).val();
        console.log('Filter changed to filière ID:', selectedFiliereId);

        if (allFilieresData.length > 0) {
            filterAndDisplayFilieres(selectedFiliereId);
        }
    });
});

/**
 * Populate the filière filter dropdown
 *
 * @param {Array} filieresData Array of filière data objects
 */
function populateFiliereFilter(filieresData) {
    const filterSelect = $('#filiere-filter');
    filterSelect.find('option:not(:first)').remove(); // Remove all options except the first one

    filieresData.forEach(function(filiereData) {
        const filiere = filiereData.filiere;
        filterSelect.append(`<option value="${filiere.id_filiere}">${filiere.nom_filiere}</option>`);
    });
}

/**
 * Filter and display filières based on selection
 *
 * @param {string} filiereId The filière ID to filter by, or 'all' for all filières
 */
function filterAndDisplayFilieres(filiereId) {
    $('#filieres-container').empty();

    if (filiereId === 'all') {
        // Show all filières
        allFilieresData.forEach(function(filiereData, index) {
            displayFiliereCard(filiereData, index);
        });
    } else {
        // Show only the selected filière
        const selectedFiliereData = allFilieresData.find(function(filiereData) {
            return filiereData.filiere.id_filiere == filiereId;
        });

        if (selectedFiliereData) {
            displayFiliereCard(selectedFiliereData, 0);
        } else {
            $('#filieres-container').html(`
                <div class="col-12">
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Aucune filière trouvée avec cet ID.
                    </div>
                </div>
            `);
        }
    }
}

/**
 * Display a filière card with pagination
 *
 * @param {Object} filiereData The filière data object
 * @param {number} index The index for styling purposes
 */
function displayFiliereCard(filiereData, index) {
    const filiere = filiereData.filiere;
    const modules = filiereData.modules;
    const modulesPerPage = 3; // Show 3 modules per page
    const totalPages = Math.ceil(modules.length / modulesPerPage);

    console.log(`Displaying filière ${filiere.nom_filiere} with ${modules.length} modules`);

    // Get filière icon
    const filiereIcon = getFilièreIcon(filiere.nom_filiere);

    const filiereCard = $(`
        <div class="col-12 col-lg-6 mb-4">
            <div class="filiere-card" data-filiere-id="${filiere.id_filiere}">
                <div class="filiere-header">
                    <div class="d-flex align-items-center">
                        <div class="filiere-icon me-3">
                            <i class="${filiereIcon}"></i>
                        </div>
                        <h5 class="mb-0">
                            ${filiere.nom_filiere}
                            <span class="filiere-modules-count">${modules.length} module${modules.length !== 1 ? 's' : ''}</span>
                        </h5>
                    </div>
                </div>

                <!-- Search section -->
                <div class="search-container p-3 border-bottom">
                    <div class="input-group">
                        <span class="input-group-text bg-light border-end-0">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" class="form-control border-start-0 module-search"
                            placeholder="Rechercher un module..."
                            data-filiere-id="${filiere.id_filiere}">
                    </div>
                </div>

                <ul class="list-group list-group-flush module-list" id="module-list-${filiere.id_filiere}">
                    ${modules.length > 0 ?
                        renderModulesPage(modules, 1, modulesPerPage) :
                        '<li class="list-group-item text-center text-muted py-4"><i class="fas fa-info-circle me-2"></i>Aucun module trouvé pour cette filière</li>'}
                </ul>
                ${modules.length > modulesPerPage ?
                    `<div class="pagination-container p-3 d-flex justify-content-center align-items-center">
                        <nav aria-label="Navigation des pages">
                            <ul class="pagination pagination-sm mb-0">
                                <li class="page-item disabled" id="prev-page-${filiere.id_filiere}">
                                    <a class="page-link" href="#" aria-label="Précédent">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item active" id="page-1-${filiere.id_filiere}">
                                    <a class="page-link" href="#">1</a>
                                </li>
                                ${Array.from({ length: totalPages - 1 }, (_, i) =>
                                    `<li class="page-item" id="page-${i + 2}-${filiere.id_filiere}">
                                        <a class="page-link" href="#">${i + 2}</a>
                                    </li>`
                                ).join('')}
                                <li class="page-item ${totalPages <= 1 ? 'disabled' : ''}" id="next-page-${filiere.id_filiere}">
                                    <a class="page-link" href="#" aria-label="Suivant">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>` :
                    ''}
            </div>
        </div>
    `);

    $('#filieres-container').append(filiereCard);

    // Store the original modules data for this filière
    const filiereElement = $(`[data-filiere-id="${filiere.id_filiere}"]`);
    filiereElement.data('original-modules', modules);

    // Add event listener for search
    const searchInput = filiereElement.find('.module-search');

    // Search functionality
    searchInput.on('input', function() {
        filterModules(filiere.id_filiere);
    });

    // Add event listeners for pagination buttons
    if (modules.length > modulesPerPage) {
        // Next page button
        filiereElement.find(`#next-page-${filiere.id_filiere} a`).on('click', function(e) {
            e.preventDefault();
            const currentPage = filiereElement.find('.page-item.active a').text();
            const newPage = parseInt(currentPage) + 1;
            const totalPages = Math.ceil(modules.length / modulesPerPage);

            if (newPage <= totalPages) {
                const filteredModules = filiereElement.data('filtered-modules') || modules;

                // Update module list
                filiereElement.find(`#module-list-${filiere.id_filiere}`).html(
                    renderModulesPage(filteredModules, newPage, modulesPerPage)
                );

                // Update active page
                filiereElement.find('.page-item.active').removeClass('active');
                filiereElement.find(`#page-${newPage}-${filiere.id_filiere}`).addClass('active');

                // Update button states
                filiereElement.find(`#prev-page-${filiere.id_filiere}`).removeClass('disabled');
                if (newPage === totalPages) {
                    filiereElement.find(`#next-page-${filiere.id_filiere}`).addClass('disabled');
                }

                // Reinitialize event handlers for the new page
                reinitializeEventHandlers();
            }
        });

        // Previous page button
        filiereElement.find(`#prev-page-${filiere.id_filiere} a`).on('click', function(e) {
            e.preventDefault();
            const currentPage = filiereElement.find('.page-item.active a').text();
            const newPage = parseInt(currentPage) - 1;

            if (newPage >= 1) {
                const filteredModules = filiereElement.data('filtered-modules') || modules;

                // Update module list
                filiereElement.find(`#module-list-${filiere.id_filiere}`).html(
                    renderModulesPage(filteredModules, newPage, modulesPerPage)
                );

                // Update active page
                filiereElement.find('.page-item.active').removeClass('active');
                filiereElement.find(`#page-${newPage}-${filiere.id_filiere}`).addClass('active');

                // Update button states
                filiereElement.find(`#next-page-${filiere.id_filiere}`).removeClass('disabled');
                if (newPage === 1) {
                    filiereElement.find(`#prev-page-${filiere.id_filiere}`).addClass('disabled');
                }

                // Reinitialize event handlers for the new page
                reinitializeEventHandlers();
            }
        });

        // Page number buttons
        for (let i = 1; i <= Math.ceil(modules.length / modulesPerPage); i++) {
            filiereElement.find(`#page-${i}-${filiere.id_filiere} a`).on('click', function(e) {
                e.preventDefault();
                const newPage = i;
                const filteredModules = filiereElement.data('filtered-modules') || modules;
                const totalPages = Math.ceil(filteredModules.length / modulesPerPage);

                // Update module list
                filiereElement.find(`#module-list-${filiere.id_filiere}`).html(
                    renderModulesPage(filteredModules, newPage, modulesPerPage)
                );

                // Update active page
                filiereElement.find('.page-item.active').removeClass('active');
                filiereElement.find(`#page-${newPage}-${filiere.id_filiere}`).addClass('active');

                // Update button states
                filiereElement.find(`#prev-page-${filiere.id_filiere}`).toggleClass('disabled', newPage === 1);
                filiereElement.find(`#next-page-${filiere.id_filiere}`).toggleClass('disabled', newPage === totalPages);

                // Reinitialize event handlers for the new page
                reinitializeEventHandlers();
            });
        }
    }
}

/**
 * Get the appropriate icon for a filière based on its name
 *
 * @param {string} filiereName The name of the filière
 * @returns {string} The CSS class for the icon
 */
function getFilièreIcon(filiereName) {
    let icon = 'fas fa-graduation-cap';

    if (filiereName.toLowerCase().includes('informatique') || filiereName.toLowerCase().includes('info')) {
        icon = 'fas fa-laptop-code';
    } else if (filiereName.toLowerCase().includes('civil') || filiereName.toLowerCase().includes('génie civil')) {
        icon = 'fas fa-hard-hat';
    } else if (filiereName.toLowerCase().includes('mécanique') || filiereName.toLowerCase().includes('mecanique')) {
        icon = 'fas fa-cogs';
    } else if (filiereName.toLowerCase().includes('électrique') || filiereName.toLowerCase().includes('electrique')) {
        icon = 'fas fa-bolt';
    } else if (filiereName.toLowerCase().includes('chimie')) {
        icon = 'fas fa-flask';
    } else if (filiereName.toLowerCase().includes('ai') || filiereName.toLowerCase().includes('intelligence')) {
        icon = 'fas fa-brain';
    } else if (filiereName.toLowerCase().includes('data')) {
        icon = 'fas fa-database';
    } else if (filiereName.toLowerCase().includes('cp') || filiereName.toLowerCase().includes('préparatoire')) {
        icon = 'fas fa-book';
    }

    return icon;
}

/**
 * Render a specific page of modules
 *
 * @param {Array} modules Array of module objects
 * @param {number} page The page number to render
 * @param {number} modulesPerPage Number of modules per page
 * @returns {string} HTML string of module items
 */
function renderModulesPage(modules, page, modulesPerPage) {
    const startIndex = (page - 1) * modulesPerPage;
    const endIndex = Math.min(startIndex + modulesPerPage, modules.length);
    const pageModules = modules.slice(startIndex, endIndex);

    return pageModules.map(module => createModuleItem(module)).join('');
}

function tryLoadModules(departmentId, url, errorCallback) {
    console.log('Trying to load modules from:', url, 'with department ID:', departmentId);

    $.ajax({
        url: url,
        method: 'GET',
        data: { department_id: departmentId },
        dataType: 'json',
        success: function(response) {
            console.log('Received response:', response);

            // Clear the loading indicator
            $('#filieres-container').empty();

            if (response.error) {
                console.error('Error in response:', response.error);
                // Display error message
                $('#filieres-container').html(`
                    <div class="col-12">
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            ${response.error}
                        </div>
                    </div>
                `);
                return;
            }

            if (!response.success) {
                console.error('Response indicates failure:', response);
                // Display error message
                $('#filieres-container').html(`
                    <div class="col-12">
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            ${response.message || 'Une erreur est survenue lors du chargement des modules.'}
                        </div>
                    </div>
                `);
                return;
            }

            if (!response.data || response.data.length === 0) {
                console.warn('No modules found in response:', response);
                // No modules found
                $('#filieres-container').html(`
                    <div class="col-12">
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            Aucun module trouvé pour les filières de ce département.
                        </div>
                    </div>
                `);
                return;
            }

            console.log('Processing modules for', response.data.length, 'filières');

            // Store the filières data globally for filtering
            allFilieresData = response.data;

            // Populate the filière filter dropdown
            populateFiliereFilter(response.data);

            // Display all filières initially
            response.data.forEach(function(filiereData, index) {
                displayFiliereCard(filiereData, index);
            });
        },
        error: function(xhr, status, errorMsg) {
            console.error('AJAX error:', status, errorMsg);
            console.error('Response text:', xhr.responseText);

            if (typeof errorCallback === 'function') {
                errorCallback(errorMsg);
            } else {
                // Display error message
                $('#filieres-container').html(`
                    <div class="col-12">
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            Erreur lors du chargement des modules: ${errorMsg}
                        </div>
                    </div>
                `);
                console.error('Error loading modules:', errorMsg);
            }
        }
    });
}

/**
 * Create a module item HTML
 *
 * @param {Object} module The module data
 * @returns {string} The HTML for the module item
 */
function createModuleItem(module) {
    // Determine which icon to use based on module name
    let moduleIcon = 'fas fa-book';
    const moduleName = module.nom || module.nom_module;
    const moduleId = module.id || module.id_module;

    if (moduleName.toLowerCase().includes('language') || moduleName.toLowerCase().includes('langue')) {
        moduleIcon = 'fas fa-language';
    } else if (moduleName.toLowerCase().includes('math')) {
        moduleIcon = 'fas fa-square-root-alt';
    } else if (moduleName.toLowerCase().includes('data') || moduleName.toLowerCase().includes('donnée')) {
        moduleIcon = 'fas fa-database';
    } else if (moduleName.toLowerCase().includes('program') || moduleName.toLowerCase().includes('code')) {
        moduleIcon = 'fas fa-code';
    } else if (moduleName.toLowerCase().includes('web')) {
        moduleIcon = 'fas fa-globe';
    } else if (moduleName.toLowerCase().includes('network') || moduleName.toLowerCase().includes('réseau')) {
        moduleIcon = 'fas fa-network-wired';
    } else if (moduleName.toLowerCase().includes('security') || moduleName.toLowerCase().includes('sécurité')) {
        moduleIcon = 'fas fa-shield-alt';
    } else if (moduleName.toLowerCase().includes('project') || moduleName.toLowerCase().includes('projet')) {
        moduleIcon = 'fas fa-tasks';
    } else if (moduleName.toLowerCase().includes('design')) {
        moduleIcon = 'fas fa-pencil-ruler';
    } else if (moduleName.toLowerCase().includes('ai') || moduleName.toLowerCase().includes('intelligence')) {
        moduleIcon = 'fas fa-robot';
    }

    // Get the semester and level values, handling different property names
    const semestre = module.semestre || '';
    const niveau = module.niveau || '';
    const specialiteId = module.specialite_id || '';
    const specialiteName = module.nom_specialite || 'Spécialité';

    return `
        <li class="list-group-item module-item">
            <div class="d-flex justify-content-between align-items-center">
                <div class="module-info">
                    <div class="fw-bold module-title">
                        <i class="${moduleIcon} me-2"></i>${moduleName}
                    </div>
                    <div class="mt-2 module-badges">
                        ${niveau ? `<span class="badge module-badge badge-niveau"><i class="fas fa-layer-group me-1"></i>${niveau}</span>` : ''}
                        ${semestre ? `<span class="badge module-badge badge-semestre"><i class="fas fa-calendar-alt me-1"></i>${semestre}</span>` : ''}
                        ${specialiteId ? `<span class="badge module-badge badge-specialite"><i class="fas fa-tag me-1"></i>${specialiteName}</span>` : ''}
                    </div>
                </div>
                <div class="module-actions">
                    <button class="btn btn-sm btn-view btn-view-units"
                            data-module-id="${moduleId}"
                            data-module-name="${moduleName}"
                            onclick="viewModuleUnits(${moduleId})">
                        <i class="fas fa-eye me-1"></i> Voir UEs
                    </button>
                </div>
            </div>
        </li>
    `;
}

/**
 * Reinitialize event handlers after pagination
 * This ensures that the view buttons work after changing pages
 */
function reinitializeEventHandlers() {
    // Reinitialize module view buttons
    $('.btn-view-units').off('click').on('click', function() {
        const moduleId = $(this).data('module-id');
        const moduleName = $(this).data('module-name');

        if (moduleId) {
            viewModuleUnits(moduleId, moduleName);
        }
    });
}

/**
 * Filter modules based on search term
 *
 * @param {string} filiereId The filière ID
 */
function filterModules(filiereId) {
    const filiereElement = $(`[data-filiere-id="${filiereId}"]`);
    const originalModules = filiereElement.data('original-modules') || [];

    // Get search term
    const searchTerm = filiereElement.find('.module-search').val().toLowerCase();

    // Filter modules
    const filteredModules = originalModules.filter(module => {
        const moduleName = (module.nom || module.nom_module || '').toLowerCase();
        const moduleCode = (module.code_module || '').toLowerCase();
        const moduleNiveau = (module.niveau || '').toLowerCase();
        const moduleSemestre = (module.semestre || '').toLowerCase();

        // Check if module matches search term
        return searchTerm === '' ||
            moduleName.includes(searchTerm) ||
            moduleCode.includes(searchTerm) ||
            moduleNiveau.includes(searchTerm) ||
            moduleSemestre.includes(searchTerm);
    });

    // Store filtered modules
    filiereElement.data('filtered-modules', filteredModules);

    // Update module count
    filiereElement.find('.filiere-modules-count').text(
        `${filteredModules.length} module${filteredModules.length !== 1 ? 's' : ''}`
    );

    // Calculate new total pages
    const modulesPerPage = 3;
    const totalPages = Math.ceil(filteredModules.length / modulesPerPage);

    // Update pagination
    if (filteredModules.length > modulesPerPage) {

        // Show pagination if needed
        if (filiereElement.find('.pagination-container').length === 0) {
            filiereElement.append(`
                <div class="pagination-container p-3 d-flex justify-content-center align-items-center">
                    <nav aria-label="Navigation des pages">
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item disabled" id="prev-page-${filiereId}">
                                <a class="page-link" href="#" aria-label="Précédent">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item active" id="page-1-${filiereId}">
                                <a class="page-link" href="#">1</a>
                            </li>
                            ${Array.from({ length: totalPages - 1 }, (_, i) =>
                                `<li class="page-item" id="page-${i + 2}-${filiereId}">
                                    <a class="page-link" href="#">${i + 2}</a>
                                </li>`
                            ).join('')}
                            <li class="page-item ${totalPages <= 1 ? 'disabled' : ''}" id="next-page-${filiereId}">
                                <a class="page-link" href="#" aria-label="Suivant">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            `);

            // Add event listeners for new pagination buttons
            // Next page button
            filiereElement.find(`#next-page-${filiereId} a`).on('click', function(e) {
                e.preventDefault();
                const currentPage = filiereElement.find('.page-item.active a').text();
                const newPage = parseInt(currentPage) + 1;

                if (newPage <= totalPages) {
                    // Update module list
                    filiereElement.find(`#module-list-${filiereId}`).html(
                        renderModulesPage(filteredModules, newPage, modulesPerPage)
                    );

                    // Update active page
                    filiereElement.find('.page-item.active').removeClass('active');
                    filiereElement.find(`#page-${newPage}-${filiereId}`).addClass('active');

                    // Update button states
                    filiereElement.find(`#prev-page-${filiereId}`).removeClass('disabled');
                    if (newPage === totalPages) {
                        filiereElement.find(`#next-page-${filiereId}`).addClass('disabled');
                    }

                    // Reinitialize event handlers for the new page
                    reinitializeEventHandlers();
                }
            });

            // Previous page button
            filiereElement.find(`#prev-page-${filiereId} a`).on('click', function(e) {
                e.preventDefault();
                const currentPage = filiereElement.find('.page-item.active a').text();
                const newPage = parseInt(currentPage) - 1;

                if (newPage >= 1) {
                    // Update module list
                    filiereElement.find(`#module-list-${filiereId}`).html(
                        renderModulesPage(filteredModules, newPage, modulesPerPage)
                    );

                    // Update active page
                    filiereElement.find('.page-item.active').removeClass('active');
                    filiereElement.find(`#page-${newPage}-${filiereId}`).addClass('active');

                    // Update button states
                    filiereElement.find(`#next-page-${filiereId}`).removeClass('disabled');
                    if (newPage === 1) {
                        filiereElement.find(`#prev-page-${filiereId}`).addClass('disabled');
                    }

                    // Reinitialize event handlers for the new page
                    reinitializeEventHandlers();
                }
            });

            // Page number buttons
            for (let i = 1; i <= totalPages; i++) {
                filiereElement.find(`#page-${i}-${filiereId} a`).on('click', function(e) {
                    e.preventDefault();
                    const newPage = i;

                    // Update module list
                    filiereElement.find(`#module-list-${filiereId}`).html(
                        renderModulesPage(filteredModules, newPage, modulesPerPage)
                    );

                    // Update active page
                    filiereElement.find('.page-item.active').removeClass('active');
                    filiereElement.find(`#page-${newPage}-${filiereId}`).addClass('active');

                    // Update button states
                    filiereElement.find(`#prev-page-${filiereId}`).toggleClass('disabled', newPage === 1);
                    filiereElement.find(`#next-page-${filiereId}`).toggleClass('disabled', newPage === totalPages);

                    // Reinitialize event handlers for the new page
                    reinitializeEventHandlers();
                });
            }
        } else {
            // Update existing pagination
            const paginationUl = filiereElement.find('.pagination');

            // Remove all page number items except first and last (prev/next buttons)
            paginationUl.find('li.page-item:not(:first-child):not(:last-child)').remove();

            // Add new page number items
            for (let i = totalPages; i >= 1; i--) {
                const isActive = i === 1;
                const pageItem = $(`<li class="page-item ${isActive ? 'active' : ''}" id="page-${i}-${filiereId}">
                    <a class="page-link" href="#">${i}</a>
                </li>`);

                // Add click event
                pageItem.find('a').on('click', function(e) {
                    e.preventDefault();
                    const newPage = i;

                    // Update module list
                    filiereElement.find(`#module-list-${filiereId}`).html(
                        renderModulesPage(filteredModules, newPage, modulesPerPage)
                    );

                    // Update active page
                    filiereElement.find('.page-item.active').removeClass('active');
                    filiereElement.find(`#page-${newPage}-${filiereId}`).addClass('active');

                    // Update button states
                    filiereElement.find(`#prev-page-${filiereId}`).toggleClass('disabled', newPage === 1);
                    filiereElement.find(`#next-page-${filiereId}`).toggleClass('disabled', newPage === totalPages);

                    // Reinitialize event handlers for the new page
                    reinitializeEventHandlers();
                });

                // Insert before next button
                pageItem.insertAfter(paginationUl.find('li:first-child'));
            }

            // Reset to page 1
            filiereElement.find(`#prev-page-${filiereId}`).addClass('disabled');
            filiereElement.find(`#next-page-${filiereId}`).toggleClass('disabled', totalPages <= 1);
        }
    } else {
        // Remove pagination if not needed
        filiereElement.find('.pagination-container').remove();
    }

    // Render first page of filtered modules
    filiereElement.find(`#module-list-${filiereId}`).html(
        filteredModules.length > 0
            ? renderModulesPage(filteredModules, 1, modulesPerPage)
            : '<li class="list-group-item text-center text-muted py-4"><i class="fas fa-info-circle me-2"></i>Aucun module trouvé avec ces critères</li>'
    );

    // Reinitialize event handlers
    reinitializeEventHandlers();
}



/**
 * View teaching units for a module
 *
 * @param {string} moduleId The module ID
 * @param {string} moduleName Optional module name for the modal title
 */
function viewModuleUnits(moduleId, moduleName) {
    // Initialize the units modal if not already initialized
    const unitsModalElement = document.getElementById('unitsModal');
    if (unitsModalElement) {
        const unitsModal = new bootstrap.Modal(unitsModalElement);

        // Update modal title if module name is provided
        if (moduleName) {
            $('#unitsModalLabel').html(`<i class="fas fa-book me-2"></i>Unités d'Enseignement: ${moduleName}`);
        } else {
            $('#unitsModalLabel').html(`<i class="fas fa-book me-2"></i>Unités d'Enseignement du Module`);
        }

        // Show the modal
        unitsModal.show();

        // Load units for this module
        loadModuleUnits(moduleId);
    } else {
        console.error('Units modal element not found');
        // Just load the units without showing a modal
        loadModuleUnits(moduleId);
    }
}

/**
 * Load teaching units for a module
 *
 * @param {string} moduleId The module ID
 */
function loadModuleUnits(moduleId) {
    // Show loading indicator in the modal
    $('#units-container').html(`
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
            <p class="mt-2">Chargement des unités d'enseignement...</p>
        </div>
    `);

    // Try the controller path first
    tryLoadUnits(moduleId, '../../controller/listerUEdepartController.php?action=getUnits', function(error) {
        // If controller path fails, try the route path
        console.error('Error with controller path for units:', error);
        tryLoadUnits(moduleId, '../../route/listerUEdepartRoute.php?action=get_units', function(error2) {
            // If route path fails, try alternative controller path
            console.error('Error with route path for units:', error2);
            tryLoadUnits(moduleId, '../controller/listerUEdepartController.php?action=getUnits', function(error3) {
                // If alternative controller path fails, try alternative route path
                console.error('Error with alternative controller path for units:', error3);
                tryLoadUnits(moduleId, '../route/listerUEdepartRoute.php?action=get_units');
            });
        });
    });
}

/**
 * Try to load teaching units from a specific URL
 *
 * @param {string} moduleId The module ID
 * @param {string} url The URL to try
 * @param {function} errorCallback Optional callback for error handling
 */
function tryLoadUnits(moduleId, url, errorCallback) {
    console.log('Trying to load units from:', url, 'with module ID:', moduleId);

    $.ajax({
        url: url,
        method: 'GET',
        data: { module_id: moduleId },
        dataType: 'json',
        success: function(response) {
            console.log('Received units response:', response);

            // Clear the loading indicator
            $('#units-container').empty();

            if (response.error) {
                console.error('Error in units response:', response.error);
                // Display error message
                $('#units-container').html(`
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        ${response.error}
                    </div>
                `);
                return;
            }

            if (!response.success) {
                console.error('Units response indicates failure:', response);
                // Display error message
                $('#units-container').html(`
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        ${response.message || 'Une erreur est survenue lors du chargement des unités.'}
                    </div>
                `);
                return;
            }

            if (!response.data || response.data.length === 0) {
                console.warn('No units found in response:', response);
                // No units found
                $('#units-container').html(`
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        Aucune unité d'enseignement trouvée pour ce module.
                    </div>
                `);
                return;
            }

            // Display units
            const unitsContainer = $('<div class="units-list"></div>');

            response.data.forEach(function(unit) {
                // Determine icon based on unit type
                let unitIcon = 'fas fa-book-open';

                if (unit.type.toLowerCase().includes('cours')) {
                    unitIcon = 'fas fa-chalkboard-teacher';
                } else if (unit.type.toLowerCase().includes('td')) {
                    unitIcon = 'fas fa-pencil-alt';
                } else if (unit.type.toLowerCase().includes('tp')) {
                    unitIcon = 'fas fa-laptop-code';
                } else if (unit.type.toLowerCase().includes('projet')) {
                    unitIcon = 'fas fa-project-diagram';
                } else if (unit.type.toLowerCase().includes('stage')) {
                    unitIcon = 'fas fa-briefcase';
                }

                const unitItem = $(`
                    <div class="unit-item">
                        <div class="unit-type">
                            <i class="${unitIcon} me-2"></i>
                            ${unit.type}
                        </div>
                        <div class="unit-details mt-3">
                            <div class="unit-detail">
                                <i class="fas fa-clock me-2"></i>
                                <strong>Volume horaire:</strong> ${unit.volume_horaire} heures
                            </div>
                            ${unit.nb_groupes ? `
                            <div class="unit-detail mt-2">
                                <i class="fas fa-users me-2"></i>
                                <strong>Nombre de groupes:</strong> ${unit.nb_groupes}
                            </div>` : ''}
                        </div>
                    </div>
                `);

                unitsContainer.append(unitItem);
            });

            $('#units-container').append(unitsContainer);
        },
        error: function(xhr, status, errorMsg) {
            console.error('AJAX error for units:', status, errorMsg);
            console.error('Response text for units:', xhr.responseText);

            if (typeof errorCallback === 'function') {
                errorCallback(errorMsg);
            } else {
                // Display error message
                $('#units-container').html(`
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Erreur lors du chargement des unités d'enseignement: ${errorMsg}
                    </div>
                `);
                console.error('Error loading units:', errorMsg);
            }
        }
    });
}
