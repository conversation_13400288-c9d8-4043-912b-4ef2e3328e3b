// Descriptif page JavaScript (updated for new database structure)

// Function to get base path for API requests
function getBasePath(routeFile = 'descriptifRoute.php') {
    // Get the base path from the current URL
    const currentPath = window.location.pathname;
    // Find the index of the "view" or "coordinator" folder
    const viewIndex = currentPath.indexOf('/view/');
    const coordIndex = currentPath.indexOf('/coordinator/');

    // Determine the base path based on the URL structure
    let basePath = '';
    if (viewIndex !== -1) {
        // If we're in a subfolder of "view"
        basePath = currentPath.substring(0, viewIndex);
    } else if (coordIndex !== -1) {
        // If we're in a subfolder of "coordinator"
        basePath = currentPath.substring(0, coordIndex);
    }

    return `${basePath}/route/${routeFile}`;
}

// Global variables
let currentModuleId = null;
let currentUnitId = null;
let modules = [];
let filieres = [];
let specialites = [];
let niveaux = [];

// Debug function to check API response
function debugApiResponse(endpoint) {
    console.log(`Debugging API endpoint: ${endpoint}`);
    fetch(`${getBasePath()}?action=${endpoint}`)
        .then(response => {
            console.log(`Response status: ${response.status}`);
            console.log(`Response headers:`, response.headers);
            return response.text();
        })
        .then(text => {
            console.log(`Response text:`, text);
            try {
                const json = JSON.parse(text);
                console.log(`Parsed JSON:`, json);

                // Check for specific issues with specialites
                if (endpoint === 'getAllSpecialites') {
                    if (json.data && Array.isArray(json.data)) {
                        console.log(`Found ${json.data.length} specialites`);
                        json.data.forEach((item, index) => {
                            console.log(`Specialite ${index}:`, item);
                        });
                    } else {
                        console.error('No specialites data found or data is not an array');
                    }
                }
            } catch (e) {
                console.error(`Error parsing JSON:`, e);
            }
        })
        .catch(error => {
            console.error(`Fetch error:`, error);
        });
}

// Function to load semestres
function loadSemestres(niveauId = null) {
    // Initialize semestres array if it doesn't exist
    if (!window.semestres) {
        window.semestres = [];
    }

    // Build the endpoint URL
    const endpoint = niveauId ?
        `${getBasePath()}?action=getAllSemestres&niveau_id=${niveauId}` :
        `${getBasePath()}?action=getAllSemestres`;

    fetch(endpoint)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            try {
                // Try to parse as JSON
                const data = JSON.parse(text);
                if (data.data) {
                    window.semestres = data.data;
                    console.log('Loaded semestres:', window.semestres);
                    populateSemestreDropdowns(window.semestres);
                } else {
                    showAlert('Error loading semestres: No data returned', 'danger');
                }
            } catch (e) {
                console.error('Error parsing JSON:', e);
                console.error('Response text:', text);
                showAlert('Error parsing semestres data', 'danger');
            }
        })
        .catch(error => {
            console.error('Error loading semestres:', error);
            showAlert('Error loading semestres: ' + error.message, 'danger');

            // Only use hardcoded values as a last resort
            const semestresHardcoded = [
                { id: 1, nom: 'S1', niveau_id: 1 },
                { id: 2, nom: 'S2', niveau_id: 1 },
                { id: 3, nom: 'S3', niveau_id: 2 },
                { id: 4, nom: 'S4', niveau_id: 2 },
                { id: 5, nom: 'S5', niveau_id: 3 },
                { id: 6, nom: 'S6', niveau_id: 3 },
                { id: 7, nom: 'S7', niveau_id: 4 },
                { id: 8, nom: 'S8', niveau_id: 4 },
                { id: 9, nom: 'S9', niveau_id: 5 },
                { id: 10, nom: 'S10', niveau_id: 5 }
            ];

            // Filter by niveau if provided
            if (niveauId) {
                window.semestres = semestresHardcoded.filter(s => s.niveau_id == niveauId);
            } else {
                window.semestres = semestresHardcoded;
            }

            populateSemestreDropdowns(window.semestres);
        });
}

// Function to populate semestre dropdowns
function populateSemestreDropdowns(semestres) {
    const moduleSemestre = document.getElementById('module-semestre');
    const editModuleSemestre = document.getElementById('edit-module-semestre');

    if (moduleSemestre) {
        moduleSemestre.innerHTML = '<option value="">Select Semestre</option>';

        // Add semestre options
        semestres.forEach(semestre => {
            const option = document.createElement('option');
            option.value = semestre.id;
            option.textContent = semestre.nom;
            // Store niveau_id as a data attribute for filtering
            if (semestre.niveau_id) {
                option.dataset.niveauId = semestre.niveau_id;
            }
            moduleSemestre.appendChild(option);
        });
    }

    if (editModuleSemestre) {
        editModuleSemestre.innerHTML = '<option value="">Select Semestre</option>';

        // Add semestre options
        semestres.forEach(semestre => {
            const option = document.createElement('option');
            option.value = semestre.id;
            option.textContent = semestre.nom;
            // Store niveau_id as a data attribute for filtering
            if (semestre.niveau_id) {
                option.dataset.niveauId = semestre.niveau_id;
            }
            editModuleSemestre.appendChild(option);
        });
    }
}

// Function to filter semestres by niveau
function filterSemestresByNiveau(niveauId, isEdit = false) {
    if (!niveauId) return;

    console.log(`Filtering semestres by niveau_id: ${niveauId}, isEdit: ${isEdit}`);

    // Get the semestre select element
    const semestreSelect = isEdit ?
        document.getElementById('edit-module-semestre') :
        document.getElementById('module-semestre');

    if (!semestreSelect) {
        console.error('Semestre select element not found');
        return;
    }

    // Clear the semestre dropdown
    semestreSelect.innerHTML = '<option value="">Select Semestre</option>';

    // Load semestres for the selected niveau
    fetch(`${getBasePath()}?action=getAllSemestres&niveau_id=${niveauId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            try {
                // Try to parse as JSON
                const data = JSON.parse(text);
                console.log('Received semestres data:', data);

                if (data.data && Array.isArray(data.data)) {
                    // Add filtered semestre options
                    data.data.forEach(semestre => {
                        const option = document.createElement('option');
                        option.value = semestre.id;
                        option.textContent = semestre.nom;
                        // Store niveau_id as a data attribute for further filtering
                        if (semestre.niveau_id) {
                            option.dataset.niveauId = semestre.niveau_id;
                        }
                        semestreSelect.appendChild(option);
                    });

                    console.log(`Added ${data.data.length} semestres options for niveau_id: ${niveauId}`);
                } else {
                    console.warn('No semestres data returned for niveau_id:', niveauId);
                }
            } catch (e) {
                console.error('Error parsing JSON:', e);
                console.error('Response text:', text);
                showAlert('Error parsing semestres data', 'danger');
            }
        })
        .catch(error => {
            console.error('Error filtering semestres:', error);
            showAlert('Error loading semestres: ' + error.message, 'danger');

            // Only use hardcoded values as a last resort
            const semestresHardcoded = [
                { id: 1, nom: 'S1', niveau_id: 1 },
                { id: 2, nom: 'S2', niveau_id: 1 },
                { id: 3, nom: 'S3', niveau_id: 2 },
                { id: 4, nom: 'S4', niveau_id: 2 },
                { id: 5, nom: 'S5', niveau_id: 3 },
                { id: 6, nom: 'S6', niveau_id: 3 },
                { id: 7, nom: 'S7', niveau_id: 4 },
                { id: 8, nom: 'S8', niveau_id: 4 },
                { id: 9, nom: 'S9', niveau_id: 5 },
                { id: 10, nom: 'S10', niveau_id: 5 }
            ];

            // Filter semestres by niveau
            const filteredSemestres = semestresHardcoded.filter(s => s.niveau_id == niveauId);

            // Add filtered semestre options
            filteredSemestres.forEach(semestre => {
                const option = document.createElement('option');
                option.value = semestre.id;
                option.textContent = semestre.nom;
                option.dataset.niveauId = semestre.niveau_id;
                semestreSelect.appendChild(option);
            });

            console.log(`Added ${filteredSemestres.length} hardcoded semestres options for niveau_id: ${niveauId}`);
        });
}

// Document ready function
document.addEventListener('DOMContentLoaded', function() {
    // Debug API endpoints
    debugApiResponse('getAllSpecialites');

    // Load initial data - removed loadAllModules() since we don't need to display modules
    loadSpecialites();
    loadNiveaux();
    loadSemestres();

    // Set up event listeners
    setupEventListeners();

    // Initialize direct form dropdowns
    initializeDirectFormDropdowns();

    // Initialize direct unit forms based on any pre-checked checkboxes
    toggleDirectUnitTypeForms();
});

// Function to initialize direct form dropdowns
function initializeDirectFormDropdowns() {
    // Check if we're on the page with the direct form
    if (document.getElementById('direct-module-form')) {
        // Populate specialite dropdown for direct form
        populateDirectSpecialiteDropdown();

        // Populate niveau dropdown for direct form
        populateDirectNiveauDropdown();

        // Add event listener to niveau dropdown to filter semestres
        const directNiveauSelect = document.getElementById('direct-module-niveau');
        if (directNiveauSelect) {
            directNiveauSelect.addEventListener('change', function() {
                filterDirectSemestresByNiveau(this.value);
            });
        }
    }
}

// Function to populate specialite dropdown for direct form
function populateDirectSpecialiteDropdown() {
    const directSpecialiteSelect = document.getElementById('direct-module-specialite');
    if (directSpecialiteSelect && specialites.length > 0) {
        directSpecialiteSelect.innerHTML = '<option value="">Select Specialite</option>';

        specialites.forEach(specialite => {
            const specialiteId = specialite.id_specialite || specialite.id;
            const specialiteName = specialite.nom_specialite || specialite.nom;

            const option = document.createElement('option');
            option.value = specialiteId;
            option.textContent = specialiteName;
            directSpecialiteSelect.appendChild(option);
        });
    } else if (directSpecialiteSelect) {
        // If specialites haven't been loaded yet, try again after a short delay
        setTimeout(populateDirectSpecialiteDropdown, 500);
    }
}

// Function to populate niveau dropdown for direct form
function populateDirectNiveauDropdown() {
    const directNiveauSelect = document.getElementById('direct-module-niveau');
    if (directNiveauSelect && niveaux.length > 0) {
        directNiveauSelect.innerHTML = '<option value="">Select Niveau</option>';

        niveaux.forEach(niveau => {
            const option = document.createElement('option');
            option.value = niveau.id_niveau || niveau.id;
            option.textContent = niveau.niveau || niveau.nom;
            if (niveau.cycle_id) {
                option.dataset.cycleId = niveau.cycle_id;
            }
            directNiveauSelect.appendChild(option);
        });
    } else if (directNiveauSelect) {
        // If niveaux haven't been loaded yet, try again after a short delay
        setTimeout(populateDirectNiveauDropdown, 500);
    }
}

// Function to filter semestres by niveau for direct form
function filterDirectSemestresByNiveau(niveauId) {
    if (!niveauId) return;

    const directSemestreSelect = document.getElementById('direct-module-semestre');
    if (!directSemestreSelect) return;

    // Clear the semestre dropdown
    directSemestreSelect.innerHTML = '<option value="">Select Semestre</option>';

    // Load semestres for the selected niveau
    fetch(`${getBasePath()}?action=getAllSemestres&niveau_id=${niveauId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.data && Array.isArray(data.data)) {
                // Add filtered semestre options
                data.data.forEach(semestre => {
                    const option = document.createElement('option');
                    option.value = semestre.id;
                    option.textContent = semestre.nom;
                    if (semestre.niveau_id) {
                        option.dataset.niveauId = semestre.niveau_id;
                    }
                    directSemestreSelect.appendChild(option);
                });
            } else {
                console.warn('No semestres data returned for niveau_id:', niveauId);
                // Use hardcoded values as fallback
                const semestresHardcoded = [
                    { id: 1, nom: 'S1', niveau_id: 1 },
                    { id: 2, nom: 'S2', niveau_id: 1 },
                    { id: 3, nom: 'S3', niveau_id: 2 },
                    { id: 4, nom: 'S4', niveau_id: 2 },
                    { id: 5, nom: 'S5', niveau_id: 3 },
                    { id: 6, nom: 'S6', niveau_id: 3 }
                ];

                // Filter and add semestre options
                const filteredSemestres = semestresHardcoded.filter(s => s.niveau_id == niveauId);
                filteredSemestres.forEach(semestre => {
                    const option = document.createElement('option');
                    option.value = semestre.id;
                    option.textContent = semestre.nom;
                    option.dataset.niveauId = semestre.niveau_id;
                    directSemestreSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error filtering semestres:', error);
        });
}

// Function to validate unit type selection
function validateUnitTypeSelection(formPrefix = '') {
    const isCours = document.getElementById(`${formPrefix}-module-is-cours`).checked;
    const isTD = document.getElementById(`${formPrefix}-module-is-td`).checked;
    const isTP = document.getElementById(`${formPrefix}-module-is-tp`).checked;

    // Ensure at least one type is selected
    if (!isCours && !isTD && !isTP) {
        showAlert('Please select at least one teaching unit type', 'warning');
        return false;
    }

    return true;
}

// Function to load all modules
function loadAllModules() {
    // Check if we're in the coordinator interface
    const isCoordinatorInterface = document.querySelector('input[type="hidden"][id="module-filiere"]') !== null;

    // For coordinators, we need to get their assigned filiere_id from the hidden input
    let endpoint = `${getBasePath()}?action=getAllModules`;

    if (isCoordinatorInterface) {
        const coordinatorFiliereId = document.querySelector('input[type="hidden"][id="module-filiere"]').value;
        if (coordinatorFiliereId) {
            endpoint = `${getBasePath()}?action=getAllModules&filiere_id=${coordinatorFiliereId}`;
            console.log(`Loading modules for coordinator's filiere_id: ${coordinatorFiliereId}`);
        }
    }

    fetch(endpoint)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            try {
                // Try to parse as JSON
                const data = JSON.parse(text);
                if (data.data) {
                    modules = data.data;
                    console.log('Loaded modules:', modules);
                    displayModules(modules);
                } else {
                    showAlert('Error loading modules: No data returned', 'danger');
                }
            } catch (e) {
                console.error('Error parsing JSON:', e);
                console.error('Response text:', text);
                showAlert('Error parsing modules data', 'danger');
            }
        })
        .catch(error => {
            console.error('Error loading modules:', error);
            showAlert('Error loading modules: ' + error.message, 'danger');
        });
}

// Function to load filieres
function loadFilieres() {
    fetch(`${getBasePath()}?action=getAllFilieres`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            try {
                // Try to parse as JSON
                const data = JSON.parse(text);
                if (data.data) {
                    filieres = data.data;
                    populateFiliereDropdowns(filieres);
                } else {
                    showAlert('Error loading filieres: No data returned', 'danger');
                }
            } catch (e) {
                console.error('Error parsing JSON:', e);
                console.error('Response text:', text);
                showAlert('Error parsing filieres data', 'danger');
            }
        })
        .catch(error => {
            console.error('Error loading filieres:', error);
            showAlert('Error loading filieres: ' + error.message, 'danger');
        });
}

// Function to load specialites
function loadSpecialites() {
    fetch(`${getBasePath()}?action=getAllSpecialites`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            try {
                // Try to parse as JSON
                const data = JSON.parse(text);
                if (data.data) {
                    specialites = data.data;
                    console.log('Loaded specialites:', specialites);
                    populateSpecialiteDropdowns(specialites);
                } else {
                    showAlert('Error loading specialites: No data returned', 'danger');
                }
            } catch (e) {
                console.error('Error parsing JSON:', e);
                console.error('Response text:', text);
                showAlert('Error parsing specialites data', 'danger');
            }
        })
        .catch(error => {
            console.error('Error loading specialites:', error);
            showAlert('Error loading specialites: ' + error.message, 'danger');

            // Fallback to hardcoded values if API fails
            const specialitesHardcoded = [
                { id: 1, nom: 'Informatique' },
                { id: 2, nom: 'Mathématiques' }
            ];

            specialites = specialitesHardcoded;
            populateSpecialiteDropdowns(specialites);
        });
}

// Function to load niveaux
function loadNiveaux() {
    // Check if the filiere filter exists (it won't for coordinators)
    const filiereFilter = document.getElementById('filiere-filter');
    const filiereId = filiereFilter ? filiereFilter.value : null;

    // Check if we're in the coordinator interface
    const isCoordinatorInterface = document.querySelector('input[type="hidden"][id="module-filiere"]') !== null;

    // For coordinators, we need to get their assigned filiere_id from the hidden input
    let coordinatorFiliereId = null;
    if (isCoordinatorInterface) {
        coordinatorFiliereId = document.querySelector('input[type="hidden"][id="module-filiere"]').value;
    }

    console.log(`Loading niveaux with filiere_id: ${filiereId || 'none'}, isCoordinatorInterface: ${isCoordinatorInterface}, coordinatorFiliereId: ${coordinatorFiliereId || 'none'}`);

    // For coordinators, always pass their filiere_id
    // For other users, if a filiere is selected, load only niveaux for that filiere
    let endpoint;
    if (isCoordinatorInterface && coordinatorFiliereId) {
        endpoint = `${getBasePath()}?action=getAllNiveaux&filiere_id=${coordinatorFiliereId}`;
        console.log(`Using coordinator's filiere_id for niveaux: ${coordinatorFiliereId}`);
    } else if (filiereId) {
        endpoint = `${getBasePath()}?action=getAllNiveaux&filiere_id=${filiereId}`;
    } else {
        endpoint = `${getBasePath()}?action=getAllNiveaux`;
    }

    fetch(endpoint)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            try {
                // Try to parse as JSON
                const data = JSON.parse(text);
                if (data.data) {
                    niveaux = data.data;
                    console.log('Loaded niveaux:', niveaux);
                    populateNiveauDropdowns(niveaux);

                    // After loading niveaux, load semestres for the first niveau
                    if (niveaux.length > 0) {
                        const firstNiveauId = niveaux[0].id_niveau || niveaux[0].id;
                        loadSemestres(firstNiveauId);
                    }
                } else {
                    console.warn('No niveaux data returned');
                    showAlert('Error loading niveaux: No data returned', 'danger');
                }
            } catch (e) {
                console.error('Error parsing JSON:', e);
                console.error('Response text:', text);
                showAlert('Error parsing niveaux data', 'danger');
            }
        })
        .catch(error => {
            console.error('Error loading niveaux:', error);
            showAlert('Error loading niveaux: ' + error.message, 'danger');

            // Only use hardcoded values as a last resort
            const niveauxHardcoded = [
                { id: 1, nom: '1', cycle_id: 1 },
                { id: 2, nom: '2', cycle_id: 1 },
                { id: 3, nom: '3', cycle_id: 1 },
                { id: 4, nom: '1', cycle_id: 2 },
                { id: 5, nom: '2', cycle_id: 2 }
            ];

            console.log('Using hardcoded niveaux values');
            niveaux = niveauxHardcoded;
            populateNiveauDropdowns(niveaux);
        });
}

// Function to show import modules modal
function showImportModulesModal() {
    // Reset form
    document.getElementById('import-modules-form').reset();

    // For coordinators, add a note about department restrictions
    const filiereIdField = document.getElementById('module-filiere');
    if (filiereIdField) {
        const coordinatorFiliereId = filiereIdField.value;
        if (coordinatorFiliereId) {
            const noteElement = document.getElementById('import-modules-note');
            if (noteElement) {
                noteElement.innerHTML = `<strong>Note:</strong> As a coordinator, you can only import modules for your assigned department (ID: ${coordinatorFiliereId}). Any modules for other departments will be skipped.`;
                noteElement.style.display = 'block';
            }
            console.log(`Showing import modal for coordinator with filiere_id: ${coordinatorFiliereId}`);
        }
    }

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('import-modules-modal'));
    modal.show();
}

// Function to import modules from file
function importModules() {
    const form = document.getElementById('import-modules-form');

    // Basic validation
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // Get form data
    const formData = new FormData(form);

    // For coordinators, add their filiere_id to the form data
    const filiereIdField = document.getElementById('module-filiere');
    if (filiereIdField) {
        const coordinatorFiliereId = filiereIdField.value;
        if (coordinatorFiliereId) {
            formData.append('coordinator_filiere_id', coordinatorFiliereId);
            console.log(`Adding coordinator's filiere_id to import form: ${coordinatorFiliereId}`);
        }
    }

    // Show loading indicator
    showAlert('Importing modules, please wait...', 'info');

    // Send the file to the server
    fetch(`${getBasePath()}?action=importModules`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Close modal
            bootstrap.Modal.getInstance(document.getElementById('import-modules-modal')).hide();

            // Show success message
            showAlert(`Successfully imported ${data.count} modules`, 'success');

            // Redirect to the module listing page after a short delay
            setTimeout(() => {
                window.location.href = 'listerUEfiliere.php';
            }, 1500);
        } else {
            showAlert('Error importing modules: ' + (data.error || 'Unknown error'), 'danger');
        }
    })
    .catch(error => {
        console.error('Error importing modules:', error);
        showAlert('Error importing modules: ' + error.message, 'danger');
    });
}

// Function to set up event listeners
function setupEventListeners() {
    // Add module button
    document.getElementById('add-module-btn').addEventListener('click', showAddModuleModal);

    // Import modules button
    document.getElementById('import-modules-btn').addEventListener('click', showImportModulesModal);
    document.getElementById('import-modules-submit-btn').addEventListener('click', importModules);

    // Save module button
    document.getElementById('save-module-btn').addEventListener('click', saveModule);

    // Direct save module button
    document.getElementById('direct-save-module-btn')?.addEventListener('click', saveDirectModule);

    // Save unit button
    document.getElementById('save-unit-btn').addEventListener('click', saveUnit);

    // Update module button
    document.getElementById('update-module-btn')?.addEventListener('click', updateModule);

    // Update unit button
    document.getElementById('update-unit-btn')?.addEventListener('click', updateUnit);

    // Checkbox events for unit types in modal
    document.getElementById('module-is-cours').addEventListener('change', toggleUnitTypeForms);
    document.getElementById('module-is-td').addEventListener('change', toggleUnitTypeForms);
    document.getElementById('module-is-tp').addEventListener('change', toggleUnitTypeForms);

    // Checkbox events for direct form unit types
    document.getElementById('direct-module-is-cours')?.addEventListener('change', function() {
        toggleDirectUnitTypeForms();
        validateUnitTypeSelection('direct');
    });
    document.getElementById('direct-module-is-td')?.addEventListener('change', function() {
        toggleDirectUnitTypeForms();
        validateUnitTypeSelection('direct');
    });
    document.getElementById('direct-module-is-tp')?.addEventListener('change', function() {
        toggleDirectUnitTypeForms();
        validateUnitTypeSelection('direct');
    });

    // Checkbox events for edit form unit types
    document.getElementById('edit-module-is-cours')?.addEventListener('change', function() {
        toggleEditUnitTypeForms();
        validateUnitTypeSelection('edit');
    });
    document.getElementById('edit-module-is-td')?.addEventListener('change', function() {
        toggleEditUnitTypeForms();
        validateUnitTypeSelection('edit');
    });
    document.getElementById('edit-module-is-tp')?.addEventListener('change', function() {
        toggleEditUnitTypeForms();
        validateUnitTypeSelection('edit');
    });
}

// Function to populate filiere dropdowns
function populateFiliereDropdowns(filieres) {
    const filiereFilter = document.getElementById('filiere-filter');
    const moduleFiliere = document.getElementById('module-filiere');
    const editModuleFiliere = document.getElementById('edit-module-filiere');

    // Clear existing options except the first one
    if (filiereFilter) {
        filiereFilter.innerHTML = '<option value="">All Filieres</option>';
    }

    if (moduleFiliere) {
        moduleFiliere.innerHTML = '<option value="">Select Filiere</option>';
        // Add event listener to filter niveaux when filiere changes
        moduleFiliere.addEventListener('change', function() {
            filterNiveauxByFiliere(this.value);
        });
    }

    if (editModuleFiliere) {
        editModuleFiliere.innerHTML = '<option value="">Select Filiere</option>';
        // Add event listener to filter niveaux when filiere changes
        editModuleFiliere.addEventListener('change', function() {
            filterNiveauxByFiliere(this.value, true);
        });
    }

    // Add filiere options
    filieres.forEach(filiere => {
        // Create option for filter dropdown (if it exists)
        if (filiereFilter) {
            const filterOption = document.createElement('option');
            filterOption.value = filiere.id_filiere;
            filterOption.textContent = filiere.nom_filiere;
            filiereFilter.appendChild(filterOption);
        }

        // Create option for module form dropdown (if it exists)
        if (moduleFiliere) {
            const moduleOption = document.createElement('option');
            moduleOption.value = filiere.id_filiere;
            moduleOption.textContent = filiere.nom_filiere;
            moduleFiliere.appendChild(moduleOption);
        }

        // Create option for edit module form dropdown (if it exists)
        if (editModuleFiliere) {
            const editOption = document.createElement('option');
            editOption.value = filiere.id_filiere;
            editOption.textContent = filiere.nom_filiere;
            editModuleFiliere.appendChild(editOption);
        }
    });
}

// Function to populate specialite dropdowns
function populateSpecialiteDropdowns(specialites) {
    const moduleSpecialite = document.getElementById('module-specialite');
    const editModuleSpecialite = document.getElementById('edit-module-specialite');

    if (moduleSpecialite) {
        moduleSpecialite.innerHTML = '<option value="">Select Specialite</option>';
    }

    if (editModuleSpecialite) {
        editModuleSpecialite.innerHTML = '<option value="">Select Specialite</option>';
    }

    // Add specialite options
    specialites.forEach(specialite => {
        // Handle different field name formats (id or id_specialite)
        const specialiteId = specialite.id_specialite || specialite.id;
        // Handle different field name formats (nom, nom_specialite)
        const specialiteName = specialite.nom_specialite || specialite.nom;

        if (moduleSpecialite) {
            const moduleOption = document.createElement('option');
            moduleOption.value = specialiteId;
            moduleOption.textContent = specialiteName;
            moduleSpecialite.appendChild(moduleOption);
        }

        if (editModuleSpecialite) {
            const editOption = document.createElement('option');
            editOption.value = specialiteId;
            editOption.textContent = specialiteName;
            editModuleSpecialite.appendChild(editOption);
        }
    });
}

// Function to populate niveau dropdowns
function populateNiveauDropdowns(niveaux) {
    const moduleNiveau = document.getElementById('module-niveau');
    const editModuleNiveau = document.getElementById('edit-module-niveau');

    if (moduleNiveau) {
        moduleNiveau.innerHTML = '<option value="">Select Niveau</option>';

        // Remove existing event listeners to prevent duplicates
        const newModuleNiveau = moduleNiveau.cloneNode(true);
        moduleNiveau.parentNode.replaceChild(newModuleNiveau, moduleNiveau);

        // Add event listener to filter semestres when niveau changes
        newModuleNiveau.addEventListener('change', function() {
            filterSemestresByNiveau(this.value);
        });
    }

    if (editModuleNiveau) {
        editModuleNiveau.innerHTML = '<option value="">Select Niveau</option>';

        // Remove existing event listeners to prevent duplicates
        const newEditModuleNiveau = editModuleNiveau.cloneNode(true);
        editModuleNiveau.parentNode.replaceChild(newEditModuleNiveau, editModuleNiveau);

        // Add event listener to filter semestres when niveau changes
        newEditModuleNiveau.addEventListener('change', function() {
            filterSemestresByNiveau(this.value, true);
        });
    }

    console.log('Populating niveau dropdowns with:', niveaux);

    // Add niveau options
    niveaux.forEach(niveau => {
        // Create option for module form dropdown
        if (moduleNiveau) {
            const moduleOption = document.createElement('option');
            moduleOption.value = niveau.id_niveau || niveau.id;
            moduleOption.textContent = niveau.niveau || niveau.nom;
            if (niveau.cycle_id) {
                moduleOption.dataset.cycleId = niveau.cycle_id;
            }
            moduleNiveau.appendChild(moduleOption);
        }

        // Create option for edit module form dropdown
        if (editModuleNiveau) {
            const editOption = document.createElement('option');
            editOption.value = niveau.id_niveau || niveau.id;
            editOption.textContent = niveau.niveau || niveau.nom;
            if (niveau.cycle_id) {
                editOption.dataset.cycleId = niveau.cycle_id;
            }
            editModuleNiveau.appendChild(editOption);
        }
    });
}

// Function to filter niveaux by filiere
function filterNiveauxByFiliere(filiereId, isEdit = false) {
    const niveauSelect = isEdit ? document.getElementById('edit-module-niveau') : document.getElementById('module-niveau');

    // If no filiere is selected, load all niveaux
    if (!filiereId) {
        loadNiveaux();
        return;
    }

    // Check if we're in the coordinator interface
    const isCoordinatorInterface = document.querySelector('input[type="hidden"][id="module-filiere"]') !== null;

    // For coordinators, we need to get their assigned filiere_id from the hidden input
    let coordinatorFiliereId = null;
    if (isCoordinatorInterface) {
        coordinatorFiliereId = document.querySelector('input[type="hidden"][id="module-filiere"]').value;
        // For coordinators, always use their assigned filiere_id
        if (coordinatorFiliereId) {
            filiereId = coordinatorFiliereId;
        }
    }

    console.log(`Filtering niveaux by filiere_id: ${filiereId}, isCoordinatorInterface: ${isCoordinatorInterface}, coordinatorFiliereId: ${coordinatorFiliereId || 'none'}`);

    // Get all niveaux for this filiere from the database
    // This will filter niveaux by the cycle_id of the selected filiere
    fetch(`${getBasePath()}?action=getAllNiveaux&filiere_id=${filiereId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            try {
                // Try to parse as JSON
                const data = JSON.parse(text);
                console.log('Received niveaux data:', data);

                if (data.data && Array.isArray(data.data)) {
                    // Clear existing options except the first one
                    niveauSelect.innerHTML = '<option value="">Select Niveau</option>';

                    // Add filtered niveau options
                    data.data.forEach(niveau => {
                        const option = document.createElement('option');
                        option.value = niveau.id_niveau || niveau.id;
                        option.textContent = niveau.niveau || niveau.nom;
                        // Store cycle_id as a data attribute for further filtering
                        if (niveau.cycle_id) {
                            option.dataset.cycleId = niveau.cycle_id;
                        }
                        niveauSelect.appendChild(option);
                    });

                    // Reset semestre dropdown since niveau has changed
                    const semestreSelect = isEdit ?
                        document.getElementById('edit-module-semestre') :
                        document.getElementById('module-semestre');

                    if (semestreSelect) {
                        semestreSelect.innerHTML = '<option value="">Select Semestre</option>';
                    }

                    console.log(`Added ${data.data.length} niveaux options for filiere_id: ${filiereId}`);
                } else {
                    console.warn('No niveaux data returned for filiere_id:', filiereId);
                }
            } catch (e) {
                console.error('Error parsing JSON:', e);
                console.error('Response text:', text);
                showAlert('Error parsing niveaux data', 'danger');
            }
        })
        .catch(error => {
            console.error('Error filtering niveaux:', error);
            showAlert('Error loading niveaux: ' + error.message, 'danger');

            // Only use hardcoded values as a last resort
            const niveauxHardcoded = [
                { id: 1, nom: '1', cycle_id: 1 },
                { id: 2, nom: '2', cycle_id: 1 },
                { id: 3, nom: '3', cycle_id: 1 },
                { id: 4, nom: '1', cycle_id: 2 },
                { id: 5, nom: '2', cycle_id: 2 }
            ];

            // Clear existing options except the first one
            niveauSelect.innerHTML = '<option value="">Select Niveau</option>';

            // Get the cycle_id for the filiere
            // In a real implementation, we would fetch this from the database
            // For now, we'll use a simplified mapping
            let cycleId;
            if (filiereId <= 2) {
                cycleId = 1; // First cycle for filieres 1 and 2
            } else if (filiereId <= 4) {
                cycleId = 2; // Second cycle for filieres 3 and 4
            } else {
                cycleId = 3; // Third cycle for other filieres
            }

            console.log(`Using hardcoded values with cycle_id: ${cycleId} for filiere_id: ${filiereId}`);

            // Filter and add niveau options
            const filteredNiveaux = niveauxHardcoded.filter(niveau => niveau.cycle_id == cycleId);
            filteredNiveaux.forEach(niveau => {
                const option = document.createElement('option');
                option.value = niveau.id;
                option.textContent = niveau.nom;
                option.dataset.cycleId = niveau.cycle_id;
                niveauSelect.appendChild(option);
            });

            console.log(`Added ${filteredNiveaux.length} hardcoded niveaux options for filiere_id: ${filiereId}`);
        });
}

// Function to filter modules
function filterModules() {
    const specialiteId = document.getElementById('specialite-filter').value;
    const niveauId = document.getElementById('niveau-filter').value;
    const semestreId = document.getElementById('semestre-filter')?.value;

    let filteredModules = modules;

    if (specialiteId) {
        filteredModules = filteredModules.filter(module => module.specialite_id == specialiteId);
    }

    if (niveauId) {
        filteredModules = filteredModules.filter(module => module.id_niveau == niveauId);
    }

    if (semestreId) {
        filteredModules = filteredModules.filter(module => module.id_semestre == semestreId);
    }

    displayModules(filteredModules);
}

// Function to display modules
function displayModules(modulesToDisplay) {
    const modulesContainer = document.getElementById('modules-container');
    const emptyState = document.getElementById('empty-state');

    // Clear the container
    modulesContainer.innerHTML = '';

    if (modulesToDisplay.length === 0) {
        // Show empty state
        modulesContainer.style.display = 'none';
        emptyState.style.display = 'block';
        return;
    }

    // Hide empty state and show modules
    modulesContainer.style.display = 'block';
    emptyState.style.display = 'none';

    // Create module cards
    modulesToDisplay.forEach(module => {
        const moduleCard = createModuleCard(module);
        modulesContainer.appendChild(moduleCard);
    });
}

// Function to create a module card
function createModuleCard(module) {
    const card = document.createElement('div');
    card.className = 'module-card';
    card.dataset.moduleId = module.id;

    // Create module header
    const header = document.createElement('div');
    header.className = 'module-header';

    const headerRow = document.createElement('div');
    headerRow.className = 'row align-items-center';

    const titleCol = document.createElement('div');
    titleCol.className = 'col-md-8';

    const title = document.createElement('h4');
    title.textContent = module.nom;

    const details = document.createElement('p');
    details.className = 'mb-0 text-muted';
    // Handle case where nom_specialite might be undefined
    const specialite = module.nom_specialite || 'N/A';
    details.textContent = `${module.nom_filiere} | ${specialite} | ${module.niveau} | ${module.semestre || 'Semestre ' + module.id_semestre} | Total: ${module.volume_total}h`;

    titleCol.appendChild(title);
    titleCol.appendChild(details);

    const actionsCol = document.createElement('div');
    actionsCol.className = 'col-md-4 text-end';

    const addUnitBtn = document.createElement('button');
    addUnitBtn.className = 'btn action-btn btn-add-unit';
    addUnitBtn.innerHTML = '<i class="bi bi-plus-circle"></i> Add Unit';
    addUnitBtn.addEventListener('click', () => showAddUnitModal(module.id));

    const editBtn = document.createElement('button');
    editBtn.className = 'btn action-btn btn-edit';
    editBtn.innerHTML = '<i class="bi bi-pencil"></i> Edit';
    editBtn.addEventListener('click', () => editModule(module.id));

    const deleteBtn = document.createElement('button');
    deleteBtn.className = 'btn action-btn btn-delete';
    deleteBtn.innerHTML = '<i class="bi bi-trash"></i> Delete';
    deleteBtn.addEventListener('click', () => deleteModule(module.id));

    actionsCol.appendChild(addUnitBtn);
    actionsCol.appendChild(editBtn);
    actionsCol.appendChild(deleteBtn);

    headerRow.appendChild(titleCol);
    headerRow.appendChild(actionsCol);
    header.appendChild(headerRow);

    // Create module body
    const body = document.createElement('div');
    body.className = 'module-body';

    // Load and display units for this module
    loadUnitsForModule(module.id, body);

    card.appendChild(header);
    card.appendChild(body);

    return card;
}

// Function to load units for a module
function loadUnitsForModule(moduleId, containerElement) {
    fetch(`${getBasePath()}?action=getUnitesByModule&module_id=${moduleId}`)
        .then(response => {
            // Check if response is OK
            if (!response.ok) {
                return response.text().then(text => {
                    console.error(`Server error (${response.status}):`, text);
                    throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                });
            }

            // Try to parse as JSON
            return response.text().then(text => {
                try {
                    return JSON.parse(text);
                } catch (e) {
                    console.error('Invalid JSON response:', text);
                    throw new Error('Invalid JSON response from server');
                }
            });
        })
        .then(data => {
            if (data.data && data.data.length > 0) {
                displayUnits(data.data, containerElement);
            } else {
                containerElement.innerHTML = '<p class="text-muted">No teaching units defined for this module.</p>';
            }
        })
        .catch(error => {
            console.error(`Error loading units for module ${moduleId}:`, error);
            containerElement.innerHTML = `<p class="text-danger">Error loading teaching units: ${error.message}</p>`;
        });
}

// Function to display units
function displayUnits(units, containerElement) {
    if (units.length === 0) {
        containerElement.innerHTML = '<p class="text-muted">No teaching units defined for this module.</p>';
        return;
    }

    const unitsList = document.createElement('div');
    unitsList.className = 'units-list';

    units.forEach(unit => {
        const unitItem = document.createElement('div');
        unitItem.className = 'unit-item d-flex justify-content-between align-items-center mb-2 p-2 border-bottom';
        unitItem.dataset.unitId = unit.id;

        const unitInfo = document.createElement('div');
        unitInfo.className = 'd-flex align-items-center';

        const unitBadge = document.createElement('span');
        unitBadge.className = `unit-badge ${unit.type.toLowerCase()}`;
        unitBadge.textContent = unit.type;

        const unitDetails = document.createElement('span');
        unitDetails.textContent = `${unit.volume_horaire}h`;

        unitInfo.appendChild(unitBadge);
        unitInfo.appendChild(unitDetails);

        const unitActions = document.createElement('div');

        const editUnitBtn = document.createElement('button');
        editUnitBtn.className = 'btn btn-sm btn-edit me-2';
        editUnitBtn.innerHTML = '<i class="bi bi-pencil"></i>';
        editUnitBtn.addEventListener('click', () => editUnit(unit.id));

        const deleteUnitBtn = document.createElement('button');
        deleteUnitBtn.className = 'btn btn-sm btn-delete';
        deleteUnitBtn.innerHTML = '<i class="bi bi-trash"></i>';
        deleteUnitBtn.addEventListener('click', () => deleteUnit(unit.id));

        unitActions.appendChild(editUnitBtn);
        unitActions.appendChild(deleteUnitBtn);

        unitItem.appendChild(unitInfo);
        unitItem.appendChild(unitActions);

        unitsList.appendChild(unitItem);
    });

    containerElement.innerHTML = '';
    containerElement.appendChild(unitsList);
}

// Function to show add module modal
function showAddModuleModal() {
    // Reset form
    document.getElementById('add-module-form').reset();
    document.getElementById('unit-forms-container').innerHTML = '';

    // For coordinators, ensure the hidden filiere_id field is set correctly
    const filiereIdField = document.getElementById('module-filiere');
    if (filiereIdField) {
        // Get the coordinator's filiere_id from the hidden input
        const coordinatorFiliereId = filiereIdField.value;
        console.log(`Coordinator's filiere_id: ${coordinatorFiliereId}`);

        // Pre-load niveaux for the coordinator's filiere
        if (coordinatorFiliereId) {
            filterNiveauxByFiliere(coordinatorFiliereId);
        }
    }

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('add-module-modal'));
    modal.show();
}

// Function to toggle unit type forms
function toggleUnitTypeForms() {
    const isCours = document.getElementById('module-is-cours').checked;
    const isTD = document.getElementById('module-is-td').checked;
    const isTP = document.getElementById('module-is-tp').checked;

    const container = document.getElementById('unit-forms-container');
    container.innerHTML = '';

    if (isCours) {
        container.appendChild(createUnitTypeForm('Cours'));
    }

    if (isTD) {
        container.appendChild(createUnitTypeForm('TD'));
    }

    if (isTP) {
        container.appendChild(createUnitTypeForm('TP'));
    }
}

// Function to toggle direct form unit type forms
function toggleDirectUnitTypeForms() {
    const isCours = document.getElementById('direct-module-is-cours').checked;
    const isTD = document.getElementById('direct-module-is-td').checked;
    const isTP = document.getElementById('direct-module-is-tp').checked;

    // Get or create container for direct unit forms
    let container = document.getElementById('direct-unit-forms-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'direct-unit-forms-container';
        document.getElementById('direct-module-form').appendChild(container);
    }

    // Clear existing forms
    container.innerHTML = '';

    // Create forms for selected unit types
    if (isCours) {
        container.appendChild(createDirectUnitTypeForm('Cours'));
    }

    if (isTD) {
        container.appendChild(createDirectUnitTypeForm('TD'));
    }

    if (isTP) {
        container.appendChild(createDirectUnitTypeForm('TP'));
    }
}

// Function to toggle edit form unit type forms
function toggleEditUnitTypeForms() {
    const isCours = document.getElementById('edit-module-is-cours').checked;
    const isTD = document.getElementById('edit-module-is-td').checked;
    const isTP = document.getElementById('edit-module-is-tp').checked;

    // Get container for edit unit forms
    const container = document.getElementById('edit-unit-forms-container');
    if (!container) return;

    // Clear existing forms
    container.innerHTML = '';

    // Create forms for selected unit types
    if (isCours) {
        container.appendChild(createEditUnitTypeForm('Cours'));
    }

    if (isTD) {
        container.appendChild(createEditUnitTypeForm('TD'));
    }

    if (isTP) {
        container.appendChild(createEditUnitTypeForm('TP'));
    }
}

// Function to create a unit type form
function createUnitTypeForm(type) {
    const formDiv = document.createElement('div');
    formDiv.className = 'unit-type-form';
    formDiv.id = `${type.toLowerCase()}-form`;

    const title = document.createElement('h5');
    title.textContent = type === 'TD' ? 'TD (Travaux Dirigés)' :
                        type === 'TP' ? 'TP (Travaux Pratiques)' : type;

    const row = document.createElement('div');
    row.className = 'row mb-3';

    const volumeCol = document.createElement('div');
    volumeCol.className = 'col-md-12'; // Changed to full width since we're removing the groups column

    const volumeLabel = document.createElement('label');
    volumeLabel.className = 'form-label';
    volumeLabel.setAttribute('for', `${type.toLowerCase()}-volume`);
    volumeLabel.textContent = 'Volume Horaire (hours)';

    const volumeInput = document.createElement('input');
    volumeInput.type = 'number';
    volumeInput.className = 'form-control';
    volumeInput.id = `${type.toLowerCase()}-volume`;
    volumeInput.name = `${type.toLowerCase()}_volume`;
    volumeInput.min = '1';
    volumeInput.required = true;
    volumeInput.value = '1'; // Set default value

    // Add validation event listener - only validate on blur (when focus leaves the field)
    volumeInput.addEventListener('blur', function() {
        // Only validate when the field loses focus
        // This allows users to type values freely
        if (this.value === '' || isNaN(this.value) || parseFloat(this.value) <= 0) {
            this.value = 1;
        }
    });

    volumeCol.appendChild(volumeLabel);
    volumeCol.appendChild(volumeInput);
    row.appendChild(volumeCol);

    formDiv.appendChild(title);
    formDiv.appendChild(row);

    return formDiv;
}

// Function to create a direct unit type form
function createDirectUnitTypeForm(type) {
    const formDiv = document.createElement('div');
    formDiv.className = 'unit-type-form';
    formDiv.id = `direct-${type.toLowerCase()}-form`;

    const title = document.createElement('h5');
    title.textContent = type === 'TD' ? 'TD (Travaux Dirigés)' :
                        type === 'TP' ? 'TP (Travaux Pratiques)' : type;

    const row = document.createElement('div');
    row.className = 'row mb-3';

    const volumeCol = document.createElement('div');
    volumeCol.className = 'col-md-12';

    const volumeLabel = document.createElement('label');
    volumeLabel.className = 'form-label';
    volumeLabel.setAttribute('for', `direct-${type.toLowerCase()}-volume`);
    volumeLabel.textContent = 'Volume Horaire (hours)';

    const volumeInput = document.createElement('input');
    volumeInput.type = 'number';
    volumeInput.className = 'form-control';
    volumeInput.id = `direct-${type.toLowerCase()}-volume`;
    volumeInput.name = `direct_${type.toLowerCase()}_volume`;
    volumeInput.min = '1';
    volumeInput.required = true;
    volumeInput.value = '1'; // Set default value

    // Add validation event listener - only validate on blur (when focus leaves the field)
    volumeInput.addEventListener('blur', function() {
        // Only validate when the field loses focus
        // This allows users to type values freely
        if (this.value === '' || isNaN(this.value) || parseFloat(this.value) <= 0) {
            this.value = 1;
        }
    });

    volumeCol.appendChild(volumeLabel);
    volumeCol.appendChild(volumeInput);
    row.appendChild(volumeCol);

    formDiv.appendChild(title);
    formDiv.appendChild(row);

    return formDiv;
}

// Function to create an edit unit type form
function createEditUnitTypeForm(type) {
    const formDiv = document.createElement('div');
    formDiv.className = 'unit-type-form';
    formDiv.id = `edit-${type.toLowerCase()}-form`;

    const title = document.createElement('h5');
    title.textContent = type === 'TD' ? 'TD (Travaux Dirigés)' :
                        type === 'TP' ? 'TP (Travaux Pratiques)' : type;

    const row = document.createElement('div');
    row.className = 'row mb-3';

    const volumeCol = document.createElement('div');
    volumeCol.className = 'col-md-12';

    const volumeLabel = document.createElement('label');
    volumeLabel.className = 'form-label';
    volumeLabel.setAttribute('for', `edit-${type.toLowerCase()}-volume`);
    volumeLabel.textContent = 'Volume Horaire (hours)';

    const volumeInput = document.createElement('input');
    volumeInput.type = 'number';
    volumeInput.className = 'form-control';
    volumeInput.id = `edit-${type.toLowerCase()}-volume`;
    volumeInput.name = `edit_${type.toLowerCase()}_volume`;
    volumeInput.min = '1';
    volumeInput.required = true;
    volumeInput.value = '1'; // Set default value

    // Add validation event listener - only validate on blur (when focus leaves the field)
    volumeInput.addEventListener('blur', function() {
        // Only validate when the field loses focus
        // This allows users to type values freely
        if (this.value === '' || isNaN(this.value) || parseFloat(this.value) <= 0) {
            this.value = 1;
        }
    });

    volumeCol.appendChild(volumeLabel);
    volumeCol.appendChild(volumeInput);
    row.appendChild(volumeCol);

    formDiv.appendChild(title);
    formDiv.appendChild(row);

    return formDiv;
}

// Function to save a module from the modal
function saveModule() {
    const form = document.getElementById('add-module-form');

    // Basic validation
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // Get form data
    const formData = new FormData(form);
    const moduleData = {
        nom: formData.get('nom'),
        volume_total: formData.get('volume_total'),
        specialite_id: formData.get('specialite_id'),
        filiere_id: formData.get('filiere_id'),
        id_niveau: formData.get('id_niveau'),
        id_semestre: formData.get('id_semestre'), // Changed from semestre to id_semestre
        is_cours: formData.has('is_cours') ? 1 : 0,
        is_td: formData.has('is_td') ? 1 : 0,
        is_tp: formData.has('is_tp') ? 1 : 0
    };

    // For coordinators, ensure the filiere_id matches their assigned department
    const filiereIdField = document.getElementById('module-filiere');
    if (filiereIdField) {
        const coordinatorFiliereId = filiereIdField.value;

        // If the filiere_id doesn't match the coordinator's department, show an error and return
        if (moduleData.filiere_id != coordinatorFiliereId) {
            showAlert('You can only create modules for your assigned department', 'danger');
            return;
        }

        console.log(`Creating module with filiere_id ${moduleData.filiere_id} for coordinator with filiere_id ${coordinatorFiliereId}`);
    }

    console.log('Module data to be saved:', moduleData);

    // Save module
    fetch(`${getBasePath()}?action=createModule`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(moduleData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Module created successfully
            const moduleId = data.id;

            // Create teaching units if any type is selected
            const promises = [];

            if (moduleData.is_cours) {
                // Get the volume from the input field if it exists, otherwise use a default value
                const coursVolume = document.getElementById('cours-volume')?.value || Math.floor(moduleData.volume_total / 3) || 1;
                promises.push(createUnit(moduleId, 'Cours', coursVolume, 1));
            }

            if (moduleData.is_td) {
                // Get the volume from the input field if it exists, otherwise use a default value
                const tdVolume = document.getElementById('td-volume')?.value || Math.floor(moduleData.volume_total / 3) || 1;
                promises.push(createUnit(moduleId, 'TD', tdVolume, 1));
            }

            if (moduleData.is_tp) {
                // Get the volume from the input field if it exists, otherwise use a default value
                const tpVolume = document.getElementById('tp-volume')?.value || Math.floor(moduleData.volume_total / 3) || 1;
                promises.push(createUnit(moduleId, 'TP', tpVolume, 1));
            }

            // Wait for all units to be created
            Promise.all(promises)
                .then(() => {
                    // Close modal
                    bootstrap.Modal.getInstance(document.getElementById('add-module-modal')).hide();

                    // Show success message
                    showAlert('Module and teaching units created successfully', 'success');

                    // Redirect to the module listing page after a short delay
                    setTimeout(() => {
                        window.location.href = 'listerUEfiliere.php';
                    }, 1500);
                })
                .catch(error => {
                    console.error('Error creating teaching units:', error);
                    showAlert('Module created but there was an error creating teaching units', 'warning');

                    // Redirect to the module listing page after a short delay
                    setTimeout(() => {
                        window.location.href = 'listerUEfiliere.php';
                    }, 1500);
                });
        } else {
            showAlert('Error creating module: ' + (data.error || 'Unknown error'), 'danger');
        }
    })
    .catch(error => {
        console.error('Error creating module:', error);
        showAlert('Error creating module: ' + error.message, 'danger');
    });
}

// Function to save a module from the direct form
function saveDirectModule() {
    const form = document.getElementById('direct-module-form');

    // Basic validation
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // Validate that at least one unit type is selected
    if (!validateUnitTypeSelection('direct')) {
        return;
    }

    // Get form data
    const formData = new FormData(form);
    const moduleData = {
        nom: formData.get('nom'),
        volume_total: formData.get('volume_total'),
        specialite_id: formData.get('specialite_id'),
        filiere_id: formData.get('filiere_id'),
        id_niveau: formData.get('id_niveau'),
        id_semestre: formData.get('id_semestre'),
        is_cours: formData.has('is_cours') ? 1 : 0,
        is_td: formData.has('is_td') ? 1 : 0,
        is_tp: formData.has('is_tp') ? 1 : 0
    };

    // For coordinators, ensure the filiere_id matches their assigned department
    const filiereIdField = document.getElementById('direct-module-filiere');
    if (filiereIdField) {
        const coordinatorFiliereId = filiereIdField.value;

        // If the filiere_id doesn't match the coordinator's department, show an error and return
        if (moduleData.filiere_id != coordinatorFiliereId) {
            showAlert('You can only create modules for your assigned department', 'danger');
            return;
        }

        console.log(`Creating module with filiere_id ${moduleData.filiere_id} for coordinator with filiere_id ${coordinatorFiliereId}`);
    }

    // Show loading indicator
    showAlert('Creating module, please wait...', 'info');

    // Save module
    fetch(`${getBasePath()}?action=createModule`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(moduleData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Module created successfully
            const moduleId = data.id;

            // Create teaching units with values from the form
            const promises = [];

            if (moduleData.is_cours) {
                // Get the volume from the input field if it exists, otherwise use a default value
                const coursVolume = document.getElementById('direct-cours-volume')?.value || Math.floor(moduleData.volume_total / 3) || 1;
                promises.push(createUnit(moduleId, 'Cours', coursVolume, 1));
            }

            if (moduleData.is_td) {
                // Get the volume from the input field if it exists, otherwise use a default value
                const tdVolume = document.getElementById('direct-td-volume')?.value || Math.floor(moduleData.volume_total / 3) || 1;
                promises.push(createUnit(moduleId, 'TD', tdVolume, 1));
            }

            if (moduleData.is_tp) {
                // Get the volume from the input field if it exists, otherwise use a default value
                const tpVolume = document.getElementById('direct-tp-volume')?.value || Math.floor(moduleData.volume_total / 3) || 1;
                promises.push(createUnit(moduleId, 'TP', tpVolume, 1));
            }

            // Wait for all units to be created
            Promise.all(promises)
                .then(() => {
                    // Show success message
                    showAlert('Module and teaching units created successfully', 'success');

                    // Reset the form
                    form.reset();

                    // Reload the dropdowns
                    initializeDirectFormDropdowns();

                    // Offer to view the module or create another
                    const viewModuleBtn = document.createElement('button');
                    viewModuleBtn.className = 'btn btn-primary mt-2 me-2';
                    viewModuleBtn.textContent = 'View Module';
                    viewModuleBtn.addEventListener('click', () => {
                        window.location.href = 'listerUEfiliere.php';
                    });

                    const alertContainer = document.getElementById('alert-container');
                    if (alertContainer) {
                        alertContainer.appendChild(viewModuleBtn);
                    }
                })
                .catch(error => {
                    console.error('Error creating teaching units:', error);
                    showAlert('Module created but there was an error creating teaching units', 'warning');
                });
        } else {
            showAlert('Error creating module: ' + (data.error || 'Unknown error'), 'danger');
        }
    })
    .catch(error => {
        console.error('Error creating module:', error);
        showAlert('Error creating module: ' + error.message, 'danger');
    });
}

// Function to create a teaching unit
function createUnit(moduleId, type, volumeHoraire, nbGroupes) {
    const unitData = {
        module_id: moduleId,
        type: type,
        volume_horaire: volumeHoraire,
        nb_groupes: nbGroupes
    };

    return fetch(`${getBasePath()}?action=createUnite`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(unitData)
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            throw new Error(data.error || 'Unknown error');
        }
        return data;
    });
}

// Function to show add unit modal
function showAddUnitModal(moduleId) {
    // Reset form
    const form = document.getElementById('add-unit-form');
    form.reset();

    // Set module ID
    document.getElementById('unit-module-id').value = moduleId;

    // Get module data
    const module = modules.find(m => m.id == moduleId);
    const typeSelect = document.getElementById('unit-type');
    typeSelect.innerHTML = '<option value="">Select Type</option>';

    // Always show all possible unit types regardless of current module flags
    // This allows adding any type of unit even if it wasn't initially selected
    const option1 = document.createElement('option');
    option1.value = 'Cours';
    option1.textContent = 'Cours';
    typeSelect.appendChild(option1);

    const option2 = document.createElement('option');
    option2.value = 'TD';
    option2.textContent = 'TD (Travaux Dirigés)';
    typeSelect.appendChild(option2);

    const option3 = document.createElement('option');
    option3.value = 'TP';
    option3.textContent = 'TP (Travaux Pratiques)';
    typeSelect.appendChild(option3);

    console.log('Showing add unit modal for module:', module);

    // Set up validation for volume horaire and number of groups
    const volumeHoraireInput = document.getElementById('unit-volume-horaire');
    const nbGroupesInput = document.getElementById('unit-nb-groupes');

    // Add validation event listeners - only validate on blur (when focus leaves the field)
    volumeHoraireInput.addEventListener('blur', function() {
        if (this.value === '' || isNaN(this.value) || parseInt(this.value) <= 0) {
            this.value = 1;
        }
        this.setCustomValidity('');
    });

    nbGroupesInput.addEventListener('blur', function() {
        if (this.value === '' || isNaN(this.value) || parseInt(this.value) <= 0) {
            this.value = 1;
        }
        this.setCustomValidity('');
    });

    // Set default values
    volumeHoraireInput.value = '1';
    nbGroupesInput.value = '1';

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('add-unit-modal'));
    modal.show();
}

// Function to save a unit
function saveUnit() {
    const form = document.getElementById('add-unit-form');

    // Enhanced validation
    const volumeHoraireInput = document.getElementById('unit-volume-horaire');
    const nbGroupesInput = document.getElementById('unit-nb-groupes');
    const typeSelect = document.getElementById('unit-type');

    // Validate volume horaire
    if (!volumeHoraireInput.value || isNaN(volumeHoraireInput.value) || parseInt(volumeHoraireInput.value) <= 0) {
        showAlert('Please enter a valid volume horaire (positive number)', 'danger');
        volumeHoraireInput.focus();
        return;
    }

    // Validate number of groups
    if (!nbGroupesInput.value || isNaN(nbGroupesInput.value) || parseInt(nbGroupesInput.value) <= 0) {
        showAlert('Please enter a valid number of groups (positive number)', 'danger');
        nbGroupesInput.focus();
        return;
    }

    // Validate type
    if (!typeSelect.value) {
        showAlert('Please select a teaching unit type', 'danger');
        typeSelect.focus();
        return;
    }

    // Basic form validation
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // Get form data
    const formData = new FormData(form);
    const unitData = {
        module_id: formData.get('module_id'),
        type: formData.get('type'),
        volume_horaire: parseInt(formData.get('volume_horaire')),
        nb_groupes: parseInt(formData.get('nb_groupes'))
    };

    console.log('Creating new teaching unit:', unitData);

    // Show loading indicator
    showAlert('Creating teaching unit...', 'info');

    // Save unit
    fetch(`${getBasePath()}?action=createUnite`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(unitData)
    })
    .then(response => {
        console.log('Create unit response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Create unit response data:', data);

        if (data.success) {
            // Close modal
            bootstrap.Modal.getInstance(document.getElementById('add-unit-modal')).hide();

            // Show success message
            showAlert('Teaching unit created successfully', 'success');

            // Redirect to the module listing page after a short delay
            setTimeout(() => {
                window.location.href = 'listerUEfiliere.php';
            }, 1500);
        } else {
            showAlert('Error creating teaching unit: ' + (data.error || 'Unknown error'), 'danger');
        }
    })
    .catch(error => {
        console.error('Error creating teaching unit:', error);
        showAlert('Error creating teaching unit: ' + error.message, 'danger');
    });
}

// Function to edit a module
function editModule(moduleId) {
    // Find the module
    const module = modules.find(m => m.id == moduleId);

    if (!module) {
        console.error('Module not found:', moduleId);
        return;
    }

    // For coordinators, check if the module belongs to their assigned department
    const filiereIdField = document.getElementById('edit-module-filiere');
    if (filiereIdField) {
        const coordinatorFiliereId = filiereIdField.value;

        // If the module doesn't belong to the coordinator's department, show an error and return
        if (module.filiere_id != coordinatorFiliereId) {
            showAlert('You can only edit modules from your assigned department', 'danger');
            return;
        }

        console.log(`Editing module ${moduleId} with filiere_id ${module.filiere_id} for coordinator with filiere_id ${coordinatorFiliereId}`);
    }

    // Populate the edit form
    const form = document.getElementById('edit-module-form');
    form.querySelector('#edit-module-id').value = module.id;
    form.querySelector('#edit-module-nom').value = module.nom;
    form.querySelector('#edit-module-volume-total').value = module.volume_total;
    form.querySelector('#edit-module-specialite').value = module.specialite_id;

    // For coordinators, always use their assigned department
    if (filiereIdField) {
        form.querySelector('#edit-module-filiere').value = filiereIdField.value;
    } else {
        form.querySelector('#edit-module-filiere').value = module.filiere_id;
    }

    form.querySelector('#edit-module-niveau').value = module.id_niveau;
    form.querySelector('#edit-module-semestre').value = module.id_semestre;
    form.querySelector('#edit-module-is-cours').checked = module.is_cours == 1;
    form.querySelector('#edit-module-is-td').checked = module.is_td == 1;
    form.querySelector('#edit-module-is-tp').checked = module.is_tp == 1;

    // Pre-load niveaux for the module's filiere
    filterNiveauxByFiliere(module.filiere_id, true);

    // Pre-load semestres for the module's niveau
    filterSemestresByNiveau(module.id_niveau, true);

    // Initialize unit type forms based on the module's flags
    toggleEditUnitTypeForms();

    // Fetch existing units to populate volume fields
    fetch(`${getBasePath()}?action=getUnitesByModule&module_id=${moduleId}`)
        .then(response => response.json())
        .then(data => {
            if (data.data && Array.isArray(data.data)) {
                const units = data.data;

                // Set volume values for existing unit types
                units.forEach(unit => {
                    if (unit.type === 'Cours') {
                        const volumeInput = document.getElementById('edit-cours-volume');
                        if (volumeInput) volumeInput.value = unit.volume_horaire;
                    } else if (unit.type === 'TD') {
                        const volumeInput = document.getElementById('edit-td-volume');
                        if (volumeInput) volumeInput.value = unit.volume_horaire;
                    } else if (unit.type === 'TP') {
                        const volumeInput = document.getElementById('edit-tp-volume');
                        if (volumeInput) volumeInput.value = unit.volume_horaire;
                    }
                });
            }
        })
        .catch(error => {
            console.error('Error fetching units for module:', error);
        });

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('edit-module-modal'));
    modal.show();
}

// Function to update a module
function updateModule() {
    const form = document.getElementById('edit-module-form');

    // Basic validation
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // Get form data
    const formData = new FormData(form);
    const moduleId = formData.get('id');
    const moduleData = {
        nom: formData.get('nom'),
        volume_total: formData.get('volume_total'),
        specialite_id: formData.get('specialite_id'),
        filiere_id: formData.get('filiere_id'),
        id_niveau: formData.get('id_niveau'),
        id_semestre: formData.get('id_semestre'), // Changed from semestre to id_semestre
        is_cours: formData.has('is_cours') ? 1 : 0,
        is_td: formData.has('is_td') ? 1 : 0,
        is_tp: formData.has('is_tp') ? 1 : 0
    };

    // For coordinators, ensure the filiere_id matches their assigned department
    const filiereIdField = document.getElementById('edit-module-filiere');
    if (filiereIdField) {
        const coordinatorFiliereId = filiereIdField.value;

        // If the filiere_id doesn't match the coordinator's department, show an error and return
        if (moduleData.filiere_id != coordinatorFiliereId) {
            showAlert('You can only update modules for your assigned department', 'danger');
            return;
        }

        // Find the original module to ensure they're not changing the department
        const originalModule = modules.find(m => m.id == moduleId);
        if (originalModule && originalModule.filiere_id != moduleData.filiere_id) {
            showAlert('You cannot change the department of a module', 'danger');
            return;
        }

        console.log(`Updating module ${moduleId} with filiere_id ${moduleData.filiere_id} for coordinator with filiere_id ${coordinatorFiliereId}`);
    }

    console.log('Module data to be updated:', moduleData);

    // Update module
    fetch(`${getBasePath()}?action=updateModule&id=${moduleId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(moduleData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Module updated successfully

            // Now update or create teaching units with values from the form
            const promises = [];

            // Get existing units for this module
            fetch(`${getBasePath()}?action=getUnitesByModule&module_id=${moduleId}`)
                .then(response => response.json())
                .then(unitsData => {
                    const existingUnits = unitsData.data || [];

                    // Handle Cours unit
                    if (moduleData.is_cours) {
                        // Get the volume from the input field if it exists
                        const coursVolume = document.getElementById('edit-cours-volume')?.value || Math.floor(moduleData.volume_total / 3) || 1;

                        // Check if a Cours unit already exists
                        const existingCours = existingUnits.find(unit => unit.type === 'Cours');
                        if (existingCours) {
                            // Update existing unit
                            promises.push(
                                fetch(`${getBasePath()}?action=updateUnite&id=${existingCours.id}`, {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({
                                        module_id: moduleId,
                                        type: 'Cours',
                                        volume_horaire: coursVolume,
                                        nb_groupes: 1
                                    })
                                })
                            );
                        } else {
                            // Create new unit
                            promises.push(createUnit(moduleId, 'Cours', coursVolume, 1));
                        }
                    }

                    // Handle TD unit
                    if (moduleData.is_td) {
                        // Get the volume from the input field if it exists
                        const tdVolume = document.getElementById('edit-td-volume')?.value || Math.floor(moduleData.volume_total / 3) || 1;

                        // Check if a TD unit already exists
                        const existingTD = existingUnits.find(unit => unit.type === 'TD');
                        if (existingTD) {
                            // Update existing unit
                            promises.push(
                                fetch(`${getBasePath()}?action=updateUnite&id=${existingTD.id}`, {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({
                                        module_id: moduleId,
                                        type: 'TD',
                                        volume_horaire: tdVolume,
                                        nb_groupes: 1
                                    })
                                })
                            );
                        } else {
                            // Create new unit
                            promises.push(createUnit(moduleId, 'TD', tdVolume, 1));
                        }
                    }

                    // Handle TP unit
                    if (moduleData.is_tp) {
                        // Get the volume from the input field if it exists
                        const tpVolume = document.getElementById('edit-tp-volume')?.value || Math.floor(moduleData.volume_total / 3) || 1;

                        // Check if a TP unit already exists
                        const existingTP = existingUnits.find(unit => unit.type === 'TP');
                        if (existingTP) {
                            // Update existing unit
                            promises.push(
                                fetch(`${getBasePath()}?action=updateUnite&id=${existingTP.id}`, {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({
                                        module_id: moduleId,
                                        type: 'TP',
                                        volume_horaire: tpVolume,
                                        nb_groupes: 1
                                    })
                                })
                            );
                        } else {
                            // Create new unit
                            promises.push(createUnit(moduleId, 'TP', tpVolume, 1));
                        }
                    }

                    // Wait for all unit operations to complete
                    Promise.all(promises)
                        .then(() => {
                            // Close modal
                            bootstrap.Modal.getInstance(document.getElementById('edit-module-modal')).hide();

                            // Show success message
                            showAlert('Module and teaching units updated successfully', 'success');

                            // Reload modules
                            loadAllModules();
                        })
                        .catch(error => {
                            console.error('Error updating teaching units:', error);
                            showAlert('Module updated but there was an error updating teaching units', 'warning');
                            loadAllModules();
                        });
                })
                .catch(error => {
                    console.error('Error fetching existing units:', error);
                    showAlert('Module updated but there was an error updating teaching units', 'warning');
                    loadAllModules();
                });
        } else {
            showAlert('Error updating module: ' + (data.error || 'Unknown error'), 'danger');
        }
    })
    .catch(error => {
        console.error('Error updating module:', error);
        showAlert('Error updating module: ' + error.message, 'danger');
    });
}

// Function to delete a module
function deleteModule(moduleId) {
    // Find the module
    const module = modules.find(m => m.id == moduleId);

    if (!module) {
        console.error('Module not found:', moduleId);
        showAlert('Module not found', 'danger');
        return;
    }

    // For coordinators, check if the module belongs to their assigned department
    const filiereIdField = document.getElementById('module-filiere');
    if (filiereIdField) {
        const coordinatorFiliereId = filiereIdField.value;

        // If the module doesn't belong to the coordinator's department, show an error and return
        if (module.filiere_id != coordinatorFiliereId) {
            showAlert('You can only delete modules from your assigned department', 'danger');
            return;
        }

        console.log(`Deleting module ${moduleId} with filiere_id ${module.filiere_id} for coordinator with filiere_id ${coordinatorFiliereId}`);
    }

    if (confirm('Are you sure you want to delete this module? This will also delete all associated teaching units.')) {
        fetch(`${getBasePath()}?action=deleteModule&id=${moduleId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Module deleted successfully', 'success');
                loadAllModules();
            } else {
                showAlert('Error deleting module: ' + (data.error || 'Unknown error'), 'danger');
            }
        })
        .catch(error => {
            console.error('Error deleting module:', error);
            showAlert('Error deleting module: ' + error.message, 'danger');
        });
    }
}

// Function to edit a unit
function editUnit(unitId) {
    // Fetch the unit data
    fetch(`${getBasePath()}?action=getUniteById&id=${unitId}`)
        .then(response => response.json())
        .then(data => {
            if (data.data) {
                const unit = data.data;

                // Populate the edit form
                const form = document.getElementById('edit-unit-form');
                form.querySelector('#edit-unit-id').value = unit.id;
                form.querySelector('#edit-unit-module-id').value = unit.module_id;
                form.querySelector('#edit-unit-type').value = unit.type;
                form.querySelector('#edit-unit-volume-horaire').value = unit.volume_horaire;
                form.querySelector('#edit-unit-nb-groupes').value = unit.nb_groupes;

                // Set up validation for volume horaire and number of groups
                const volumeHoraireInput = document.getElementById('edit-unit-volume-horaire');
                const nbGroupesInput = document.getElementById('edit-unit-nb-groupes');

                // Add validation event listeners - only validate on blur (when focus leaves the field)
                volumeHoraireInput.addEventListener('blur', function() {
                    if (this.value === '' || isNaN(this.value) || parseInt(this.value) <= 0) {
                        this.value = 1;
                    }
                    this.setCustomValidity('');
                });

                nbGroupesInput.addEventListener('blur', function() {
                    if (this.value === '' || isNaN(this.value) || parseInt(this.value) <= 0) {
                        this.value = 1;
                    }
                    this.setCustomValidity('');
                });

                // Ensure values are valid
                if (!volumeHoraireInput.value || parseInt(volumeHoraireInput.value) <= 0) {
                    volumeHoraireInput.value = '1';
                }

                if (!nbGroupesInput.value || parseInt(nbGroupesInput.value) <= 0) {
                    nbGroupesInput.value = '1';
                }

                // Show the modal
                const modal = new bootstrap.Modal(document.getElementById('edit-unit-modal'));
                modal.show();
            } else {
                showAlert('Error loading unit data: ' + (data.error || 'Unknown error'), 'danger');
            }
        })
        .catch(error => {
            console.error('Error loading unit data:', error);
            showAlert('Error loading unit data: ' + error.message, 'danger');
        });
}

// Function to update a unit
function updateUnit() {
    const form = document.getElementById('edit-unit-form');

    // Enhanced validation
    const volumeHoraireInput = document.getElementById('edit-unit-volume-horaire');
    const nbGroupesInput = document.getElementById('edit-unit-nb-groupes');
    const typeSelect = document.getElementById('edit-unit-type');

    // Validate volume horaire
    if (!volumeHoraireInput.value || isNaN(volumeHoraireInput.value) || parseInt(volumeHoraireInput.value) <= 0) {
        showAlert('Please enter a valid volume horaire (positive number)', 'danger');
        volumeHoraireInput.focus();
        return;
    }

    // Validate number of groups
    if (!nbGroupesInput.value || isNaN(nbGroupesInput.value) || parseInt(nbGroupesInput.value) <= 0) {
        showAlert('Please enter a valid number of groups (positive number)', 'danger');
        nbGroupesInput.focus();
        return;
    }

    // Validate type
    if (!typeSelect.value) {
        showAlert('Please select a teaching unit type', 'danger');
        typeSelect.focus();
        return;
    }

    // Basic form validation
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // Get form data
    const formData = new FormData(form);
    const unitId = formData.get('id');
    const unitData = {
        module_id: formData.get('module_id'),
        type: formData.get('type'),
        volume_horaire: parseInt(formData.get('volume_horaire')),
        nb_groupes: parseInt(formData.get('nb_groupes'))
    };

    console.log('Updating teaching unit:', unitData);

    // Show loading indicator
    showAlert('Updating teaching unit...', 'info');

    // Update unit
    fetch(`${getBasePath()}?action=updateUnite&id=${unitId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(unitData)
    })
    .then(response => {
        console.log('Update unit response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Update unit response data:', data);

        if (data.success) {
            // Close modal
            bootstrap.Modal.getInstance(document.getElementById('edit-unit-modal')).hide();

            // Show success message
            showAlert('Teaching unit updated successfully', 'success');

            // Reload modules
            loadAllModules();
        } else {
            showAlert('Error updating teaching unit: ' + (data.error || 'Unknown error'), 'danger');
        }
    })
    .catch(error => {
        console.error('Error updating teaching unit:', error);
        showAlert('Error updating teaching unit: ' + error.message, 'danger');
    });
}

// Function to delete a unit
function deleteUnit(unitId) {
    if (confirm('Are you sure you want to delete this teaching unit?')) {
        console.log('Deleting teaching unit with ID:', unitId);

        fetch(`${getBasePath()}?action=deleteUnite&id=${unitId}`, {
            method: 'DELETE'
        })
        .then(response => {
            console.log('Delete response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Delete response data:', data);

            if (data.success) {
                showAlert('Teaching unit deleted successfully', 'success');
                loadAllModules();
            } else {
                showAlert('Error deleting teaching unit: ' + (data.error || 'Unknown error'), 'danger');
            }
        })
        .catch(error => {
            console.error('Error deleting teaching unit:', error);
            showAlert('Error deleting teaching unit: ' + error.message, 'danger');
        });
    }
}

// Function to show alert
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alert-container');
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.role = 'alert';

    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    alertContainer.appendChild(alertDiv);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = bootstrap.Alert.getOrCreateInstance(alertDiv);
        alert.close();
    }, 5000);
}