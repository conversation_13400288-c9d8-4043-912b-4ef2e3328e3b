<?php
/**
 * Configuration Charge Route
 *
 * This file handles API requests for workload configuration functionality.
 */

// Include required files
require_once __DIR__ . '/../controller/configurationChargeController.php';
require_once __DIR__ . '/../utils/response.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if the user is authenticated and is a department head
function isDepartmentHead() {
    return isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'chef de departement';
}

// Check if the user is an admin
function isAdmin() {
    return isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'admin';
}

// Verify authentication and authorization
if (!isDepartmentHead() && !isAdmin()) {
    jsonResponse(['error' => 'Unauthorized access'], 403);
    exit;
}

// Get the department ID from session for department heads
$sessionDepartmentId = null;
if (isDepartmentHead()) {
    $sessionDepartmentId = $_SESSION['user']['department_id'] ?? null;
    
    if (!$sessionDepartmentId) {
        jsonResponse(['error' => 'Department ID not found in session'], 400);
        exit;
    }
}

// Handle different HTTP methods
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        handleGetRequest();
        break;
    
    case 'POST':
        handlePostRequest();
        break;
    
    case 'PUT':
        handlePutRequest();
        break;
    
    case 'DELETE':
        handleDeleteRequest();
        break;
    
    default:
        jsonResponse(['error' => 'Method not allowed'], 405);
        break;
}

/**
 * Handle GET requests
 */
function handleGetRequest() {
    global $sessionDepartmentId;
    
    if (!isset($_GET['action'])) {
        jsonResponse(['error' => 'Action parameter is required'], 400);
        return;
    }
    
    $action = $_GET['action'];
    
    switch ($action) {
        case 'getWorkloadConfigurationsByDepartment':
            $departmentId = $_GET['department_id'] ?? null;
            
            // Verify department access for department heads
            if (isDepartmentHead() && $departmentId != $sessionDepartmentId) {
                jsonResponse(['error' => 'Access denied to this department'], 403);
                return;
            }
            
            if (!$departmentId) {
                jsonResponse(['error' => 'Department ID is required'], 400);
                return;
            }
            
            getWorkloadConfigurationsByDepartmentAPI($departmentId);
            break;
        
        case 'getWorkloadConfigurationById':
            $configId = $_GET['config_id'] ?? null;
            
            if (!$configId) {
                jsonResponse(['error' => 'Configuration ID is required'], 400);
                return;
            }
            
            // Additional security check: verify the configuration belongs to the department head's department
            if (isDepartmentHead()) {
                $config = getWorkloadConfigurationById($configId);
                if (isset($config['error'])) {
                    jsonResponse(['error' => $config['error']], 404);
                    return;
                }
                
                if ($config['departement_id'] != $sessionDepartmentId) {
                    jsonResponse(['error' => 'Access denied to this configuration'], 403);
                    return;
                }
            }
            
            getWorkloadConfigurationByIdAPI($configId);
            break;
        
        case 'getCurrentAcademicYear':
            getCurrentAcademicYearAPI();
            break;
        
        default:
            jsonResponse(['error' => 'Invalid action'], 400);
            break;
    }
}

/**
 * Handle POST requests (Create)
 */
function handlePostRequest() {
    global $sessionDepartmentId;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
        return;
    }
    
    // For department heads, ensure they can only create configurations for their department
    if (isDepartmentHead()) {
        $input['departement_id'] = $sessionDepartmentId;
    }
    
    // Validate required fields
    if (empty($input['annee_universitaire']) || empty($input['charge_minimale']) || empty($input['departement_id'])) {
        jsonResponse(['error' => 'Missing required fields: annee_universitaire, charge_minimale, departement_id'], 400);
        return;
    }
    
    // Additional validation
    if (!is_numeric($input['charge_minimale']) || intval($input['charge_minimale']) <= 0) {
        jsonResponse(['error' => 'charge_minimale must be a positive integer'], 400);
        return;
    }
    
    if (!preg_match('/^\d{4}-\d{4}$/', $input['annee_universitaire'])) {
        jsonResponse(['error' => 'Invalid academic year format. Use YYYY-YYYY'], 400);
        return;
    }
    
    if (isset($input['type_enseignant']) && !in_array($input['type_enseignant'], ['enseignant', 'vacataire'])) {
        jsonResponse(['error' => 'Invalid type_enseignant. Must be enseignant or vacataire'], 400);
        return;
    }
    
    $result = createWorkloadConfiguration($input);
    
    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 400);
        return;
    }
    
    jsonResponse(['message' => $result['success'], 'id' => $result['id']], 201);
}

/**
 * Handle PUT requests (Update)
 */
function handlePutRequest() {
    global $sessionDepartmentId;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
        return;
    }
    
    if (empty($input['id_config'])) {
        jsonResponse(['error' => 'Configuration ID is required'], 400);
        return;
    }
    
    $configId = intval($input['id_config']);
    
    // Security check: verify the configuration belongs to the department head's department
    if (isDepartmentHead()) {
        $config = getWorkloadConfigurationById($configId);
        if (isset($config['error'])) {
            jsonResponse(['error' => $config['error']], 404);
            return;
        }
        
        if ($config['departement_id'] != $sessionDepartmentId) {
            jsonResponse(['error' => 'Access denied to this configuration'], 403);
            return;
        }
    }
    
    // Validate required fields
    if (empty($input['annee_universitaire']) || empty($input['charge_minimale'])) {
        jsonResponse(['error' => 'Missing required fields: annee_universitaire, charge_minimale'], 400);
        return;
    }
    
    // Additional validation
    if (!is_numeric($input['charge_minimale']) || intval($input['charge_minimale']) <= 0) {
        jsonResponse(['error' => 'charge_minimale must be a positive integer'], 400);
        return;
    }
    
    if (!preg_match('/^\d{4}-\d{4}$/', $input['annee_universitaire'])) {
        jsonResponse(['error' => 'Invalid academic year format. Use YYYY-YYYY'], 400);
        return;
    }
    
    if (isset($input['type_enseignant']) && !in_array($input['type_enseignant'], ['enseignant', 'vacataire'])) {
        jsonResponse(['error' => 'Invalid type_enseignant. Must be enseignant or vacataire'], 400);
        return;
    }
    
    $result = updateWorkloadConfiguration($configId, $input);
    
    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 400);
        return;
    }
    
    jsonResponse(['message' => $result['success']], 200);
}

/**
 * Handle DELETE requests
 */
function handleDeleteRequest() {
    global $sessionDepartmentId;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || empty($input['id_config'])) {
        jsonResponse(['error' => 'Configuration ID is required'], 400);
        return;
    }
    
    $configId = intval($input['id_config']);
    
    // Security check: verify the configuration belongs to the department head's department
    if (isDepartmentHead()) {
        $config = getWorkloadConfigurationById($configId);
        if (isset($config['error'])) {
            jsonResponse(['error' => $config['error']], 404);
            return;
        }
        
        if ($config['departement_id'] != $sessionDepartmentId) {
            jsonResponse(['error' => 'Access denied to this configuration'], 403);
            return;
        }
    }
    
    $result = deleteWorkloadConfiguration($configId);
    
    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 400);
        return;
    }
    
    jsonResponse(['message' => $result['success']], 200);
}
?>
