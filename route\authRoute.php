<?php
require_once "../utils/response.php";
require_once "../controller/authController.php";

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            if (isset($_GET['action']) && $_GET['action'] === 'profile' && isset($_GET['username'])) {
                $result = getUserProfile($_GET['username']);
                jsonResponse($result);
            } else {
                jsonResponse(['error' => 'Paramètres invalides'], 400);
            }
            break;

        case 'POST':
            // Get JSON data
            $data = json_decode(file_get_contents('php://input'), true);
            
            if (isset($data['action'])) {
                switch ($data['action']) {
                    case 'login':
                        if (isset($data['username']) && isset($data['password'])) {
                            $result = handleLogin($data['username'], $data['password']);
                            jsonResponse($result);
                        } else {
                            jsonResponse(['error' => 'Username et mot de passe requis'], 400);
                        }
                        break;
                        
                    case 'logout':
                        $result = logout();
                        jsonResponse($result);
                        break;
                        
                    default:
                        jsonResponse(['error' => 'Action non valide'], 400);
                        break;
                }
            } else {
                jsonResponse(['error' => 'Action non spécifiée'], 400);
            }
            break;

        default:
            jsonResponse(['error' => 'Méthode non autorisée'], 405);
            break;
    }
} catch (Exception $e) {
    jsonResponse(['error' => 'Erreur interne du serveur'], 500);
}
?>