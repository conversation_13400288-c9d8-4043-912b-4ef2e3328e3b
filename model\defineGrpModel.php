<?php
require_once "../config/db.php";

/**
 * Get all modules for a specific filiere
 *
 * @param int $filiereId Filiere ID
 * @return array Modules data
 */
function getModulesByFiliere($filiereId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getModulesByFiliere");
        return ["error" => "Database connection error"];
    }

    $filiereId = mysqli_real_escape_string($conn, $filiereId);

    // Get all modules for the filiere
    $sql = "SELECT m.*, f.nom_filiere, n.nom as niveau_nom, s.nom as semestre_nom, n.cycle_id
            FROM module m
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveaux n ON m.id_niveau = n.id
            LEFT JOIN semestre s ON m.id_semestre = s.id
            WHERE m.filiere_id = '$filiereId'
            ORDER BY n.nom, s.nom, m.nom";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getModulesByFiliere: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching modules: " . $error];
    }

    $modules = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $modules[] = $row;
    }

    mysqli_close($conn);
    return $modules;
}

/**
 * Get all modules for a specific filiere and cycle
 *
 * @param int $filiereId Filiere ID
 * @param int $cycleId Cycle ID
 * @return array Modules data
 */
function getModulesByFiliereAndCycle($filiereId, $cycleId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getModulesByFiliereAndCycle");
        return ["error" => "Database connection error"];
    }

    $filiereId = mysqli_real_escape_string($conn, $filiereId);
    $cycleId = mysqli_real_escape_string($conn, $cycleId);

    // Get all modules for the filiere and cycle
    $sql = "SELECT m.*, f.nom_filiere, n.nom as niveau_nom, s.nom as semestre_nom, n.cycle_id
            FROM module m
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveaux n ON m.id_niveau = n.id
            LEFT JOIN semestre s ON m.id_semestre = s.id
            WHERE m.filiere_id = '$filiereId' AND n.cycle_id = '$cycleId'
            ORDER BY n.nom, s.nom, m.nom";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getModulesByFiliereAndCycle: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching modules: " . $error];
    }

    $modules = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $modules[] = $row;
    }

    mysqli_close($conn);
    return $modules;
}

/**
 * Get all modules for a specific filiere and niveau
 *
 * @param int $filiereId Filiere ID
 * @param int $niveauId Niveau ID
 * @return array Modules data
 */
function getModulesByFiliereAndNiveau($filiereId, $niveauId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getModulesByFiliereAndNiveau");
        return ["error" => "Database connection error"];
    }

    $filiereId = mysqli_real_escape_string($conn, $filiereId);
    $niveauId = mysqli_real_escape_string($conn, $niveauId);

    // Get all modules for the filiere and niveau
    $sql = "SELECT m.*, f.nom_filiere, n.nom as niveau_nom, s.nom as semestre_nom, n.cycle_id
            FROM module m
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveaux n ON m.id_niveau = n.id
            LEFT JOIN semestre s ON m.id_semestre = s.id
            WHERE m.filiere_id = '$filiereId' AND m.id_niveau = '$niveauId'
            ORDER BY s.nom, m.nom";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getModulesByFiliereAndNiveau: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching modules: " . $error];
    }

    $modules = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $modules[] = $row;
    }

    mysqli_close($conn);
    return $modules;
}

/**
 * Update the number of TD and TP groups for a module
 *
 * @param int $moduleId Module ID
 * @param int $nbGroupeTd Number of TD groups
 * @param int $nbGroupeTp Number of TP groups
 * @return bool|array True on success, error array on failure
 */
function updateModuleGroups($moduleId, $nbGroupeTd, $nbGroupeTp) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in updateModuleGroups");
        return ["error" => "Database connection error"];
    }

    $moduleId = mysqli_real_escape_string($conn, $moduleId);
    $nbGroupeTd = mysqli_real_escape_string($conn, $nbGroupeTd);
    $nbGroupeTp = mysqli_real_escape_string($conn, $nbGroupeTp);

    // Start a transaction to ensure all updates are atomic
    mysqli_begin_transaction($conn);

    try {
        // We'll only update the uniteenseignement table, not the module table

        // Update TD units in uniteenseignement table
        $updateTdSql = "UPDATE uniteenseignement
                       SET nb_groupes = '$nbGroupeTd'
                       WHERE module_id = '$moduleId' AND type = 'TD'";

        $updateTdResult = mysqli_query($conn, $updateTdSql);

        if (!$updateTdResult) {
            throw new Exception("Error updating TD units: " . mysqli_error($conn));
        }

        $tdAffectedRows = mysqli_affected_rows($conn);
        error_log("Updated $tdAffectedRows TD units for module $moduleId");

        // Update TP units in uniteenseignement table
        $updateTpSql = "UPDATE uniteenseignement
                       SET nb_groupes = '$nbGroupeTp'
                       WHERE module_id = '$moduleId' AND type = 'TP'";

        $updateTpResult = mysqli_query($conn, $updateTpSql);

        if (!$updateTpResult) {
            throw new Exception("Error updating TP units: " . mysqli_error($conn));
        }

        $tpAffectedRows = mysqli_affected_rows($conn);
        error_log("Updated $tpAffectedRows TP units for module $moduleId");

        // Commit the transaction
        mysqli_commit($conn);

        mysqli_close($conn);
        return [
            "success" => true,
            "module_id" => $moduleId,
            "td_units_updated" => $tdAffectedRows,
            "tp_units_updated" => $tpAffectedRows
        ];
    } catch (Exception $e) {
        // Rollback the transaction in case of error
        mysqli_rollback($conn);
        error_log("Error in updateModuleGroups: " . $e->getMessage());
        mysqli_close($conn);
        return ["error" => $e->getMessage()];
    }
}

/**
 * Update the number of TD and TP groups for all modules in a cycle
 *
 * @param int $filiereId Filiere ID
 * @param int $cycleId Cycle ID
 * @param int $nbGroupeTd Number of TD groups
 * @param int $nbGroupeTp Number of TP groups
 * @return bool|array True on success, error array on failure
 */
function updateModuleGroupsByCycle($filiereId, $cycleId, $nbGroupeTd, $nbGroupeTp) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in updateModuleGroupsByCycle");
        return ["error" => "Database connection error"];
    }

    $filiereId = mysqli_real_escape_string($conn, $filiereId);
    $cycleId = mysqli_real_escape_string($conn, $cycleId);
    $nbGroupeTd = mysqli_real_escape_string($conn, $nbGroupeTd);
    $nbGroupeTp = mysqli_real_escape_string($conn, $nbGroupeTp);

    // Start a transaction to ensure all updates are atomic
    mysqli_begin_transaction($conn);

    try {
        // Get all module IDs for the filiere and cycle
        $moduleIdsSql = "SELECT m.id
                        FROM module m
                        JOIN niveaux n ON m.id_niveau = n.id
                        WHERE m.filiere_id = '$filiereId' AND n.cycle_id = '$cycleId'";

        $moduleIdsResult = mysqli_query($conn, $moduleIdsSql);

        if (!$moduleIdsResult) {
            throw new Exception("Error getting module IDs: " . mysqli_error($conn));
        }

        $moduleIds = [];
        while ($row = mysqli_fetch_assoc($moduleIdsResult)) {
            $moduleIds[] = $row['id'];
        }

        $totalModules = count($moduleIds);
        error_log("Found $totalModules modules for filiere $filiereId and cycle $cycleId");

        // Update uniteenseignement table for TD units
        if (!empty($moduleIds)) {
            $moduleIdsStr = implode(',', $moduleIds);

            // Update TD units
            $updateTdSql = "UPDATE uniteenseignement
                           SET nb_groupes = '$nbGroupeTd'
                           WHERE module_id IN ($moduleIdsStr) AND type = 'TD'";

            $updateTdResult = mysqli_query($conn, $updateTdSql);

            if (!$updateTdResult) {
                throw new Exception("Error updating TD units: " . mysqli_error($conn));
            }

            $tdAffectedRows = mysqli_affected_rows($conn);
            error_log("Updated $tdAffectedRows TD units for modules in cycle $cycleId");

            // Update TP units
            $updateTpSql = "UPDATE uniteenseignement
                           SET nb_groupes = '$nbGroupeTp'
                           WHERE module_id IN ($moduleIdsStr) AND type = 'TP'";

            $updateTpResult = mysqli_query($conn, $updateTpSql);

            if (!$updateTpResult) {
                throw new Exception("Error updating TP units: " . mysqli_error($conn));
            }

            $tpAffectedRows = mysqli_affected_rows($conn);
            error_log("Updated $tpAffectedRows TP units for modules in cycle $cycleId");
        }

        // Commit the transaction
        mysqli_commit($conn);

        mysqli_close($conn);
        return [
            "success" => true,
            "affected_modules" => $totalModules,
            "td_units_updated" => $tdAffectedRows ?? 0,
            "tp_units_updated" => $tpAffectedRows ?? 0
        ];
    } catch (Exception $e) {
        // Rollback the transaction in case of error
        mysqli_rollback($conn);
        error_log("Error in updateModuleGroupsByCycle: " . $e->getMessage());
        mysqli_close($conn);
        return ["error" => $e->getMessage()];
    }
}

/**
 * Get all niveaux for a specific filiere
 *
 * @param int $filiereId Filiere ID
 * @return array Niveaux data
 */
function getNiveauxByFiliere($filiereId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getNiveauxByFiliere");
        return ["error" => "Database connection error"];
    }

    $filiereId = mysqli_real_escape_string($conn, $filiereId);

    // Get all niveaux for the filiere
    $sql = "SELECT n.*
            FROM niveaux n
            JOIN filiere f ON n.cycle_id = f.id_cycle
            WHERE f.id_filiere = '$filiereId'
            ORDER BY n.nom";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getNiveauxByFiliere: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching niveaux: " . $error];
    }

    $niveaux = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $niveaux[] = $row;
    }

    mysqli_close($conn);
    return $niveaux;
}

/**
 * Get all cycles for a specific filiere
 *
 * @param int $filiereId Filiere ID
 * @return array Cycles data
 */
function getCyclesByFiliere($filiereId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getCyclesByFiliere");
        return ["error" => "Database connection error"];
    }

    $filiereId = mysqli_real_escape_string($conn, $filiereId);

    // Get the cycle ID for the filiere
    $sql = "SELECT c.*, f.nom_filiere
            FROM cycle c
            JOIN filiere f ON c.id = f.id_cycle
            WHERE f.id_filiere = '$filiereId'";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getCyclesByFiliere: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching cycles: " . $error];
    }

    $cycles = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $cycles[] = $row;
    }

    mysqli_close($conn);
    return $cycles;
}

/**
 * Get all niveaux grouped by cycle for a specific filiere
 *
 * @param int $filiereId Filiere ID
 * @return array Niveaux grouped by cycle
 */
function getNiveauxGroupedByCycle($filiereId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getNiveauxGroupedByCycle");
        return ["error" => "Database connection error"];
    }

    $filiereId = mysqli_real_escape_string($conn, $filiereId);

    // Get all niveaux for the filiere, grouped by cycle
    $sql = "SELECT n.*, c.nom as cycle_nom
            FROM niveaux n
            JOIN cycle c ON n.cycle_id = c.id
            JOIN filiere f ON c.id = f.id_cycle
            WHERE f.id_filiere = '$filiereId'
            ORDER BY c.id, n.nom";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getNiveauxGroupedByCycle: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching niveaux: " . $error];
    }

    $niveauxByCycle = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $cycleId = $row['cycle_id'];
        if (!isset($niveauxByCycle[$cycleId])) {
            $niveauxByCycle[$cycleId] = [
                'cycle_id' => $cycleId,
                'cycle_nom' => $row['cycle_nom'],
                'niveaux' => []
            ];
        }
        $niveauxByCycle[$cycleId]['niveaux'][] = $row;
    }

    mysqli_close($conn);
    return $niveauxByCycle;
}