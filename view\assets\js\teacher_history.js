/**
 * Teacher History JavaScript
 *
 * This file handles all frontend functionality for the teacher history page,
 * including loading academic years, displaying statistics, modules, and grade history.
 */

let currentSelectedYear = null;

// Initialize the page when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Teacher History page loaded');
    console.log('Teacher ID:', teacherId);

    if (!teacherId) {
        showError('Teacher ID not found. Please refresh the page.');
        return;
    }

    loadAcademicYears();
});

/**
 * Load academic years for the teacher
 */
async function loadAcademicYears() {
    try {
        const response = await fetch(`../../route/teacherHistoryRoute.php?action=getAcademicYears&teacher_id=${teacherId}`);
        const data = await response.json();

        if (data.success && data.data) {
            displayAcademicYears(data.data);
        } else {
            showError(data.error || 'Failed to load academic years');
        }
    } catch (error) {
        console.error('Error loading academic years:', error);
        showError('Failed to load academic years. Please try again.');
    }
}

/**
 * Display academic years in dropdown filter
 */
function displayAcademicYears(years) {
    const yearFilter = document.getElementById('yearFilter');

    if (years.length === 0) {
        yearFilter.innerHTML = `
            <option value="">Aucune année universitaire trouvée</option>
        `;
        return;
    }

    // Clear the dropdown and add default option
    yearFilter.innerHTML = '<option value="">Sélectionner une année...</option>';

    // Add years to dropdown
    years.forEach(year => {
        const option = document.createElement('option');
        option.value = year;
        option.textContent = year;
        yearFilter.appendChild(option);
    });

    // Auto-select the most recent year
    if (years.length > 0) {
        yearFilter.value = years[0];
        selectYear(years[0]);
    }
}

/**
 * Handle year change from dropdown
 */
function handleYearChange() {
    const yearFilter = document.getElementById('yearFilter');
    const selectedYear = yearFilter.value;

    if (selectedYear) {
        selectYear(selectedYear);
    } else {
        // Hide all sections if no year is selected
        hideAllSections();
        currentSelectedYear = null;
        document.getElementById('selectedYearDisplay').textContent = '';
    }
}

/**
 * Hide all data sections
 */
function hideAllSections() {
    document.getElementById('statsSection').style.display = 'none';
    document.getElementById('chartsSection').style.display = 'none';
    document.getElementById('mainContent').style.display = 'none';
    document.getElementById('gradeHistoryAndObservationsSection').style.display = 'none';
}

/**
 * Select an academic year and load its data
 */
async function selectYear(year) {
    // Update the dropdown selection if not already selected
    const yearFilter = document.getElementById('yearFilter');
    if (yearFilter.value !== year) {
        yearFilter.value = year;
    }

    currentSelectedYear = year;
    document.getElementById('selectedYearDisplay').textContent = year;

    // Show loading state
    showLoadingState();

    // Load data for the selected year
    await loadYearData(year);
}

/**
 * Show loading state for all sections
 */
function showLoadingState() {
    const loadingSpinner = `
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
        </div>
    `;

    // Update all containers
    document.getElementById('statsContainer').innerHTML = loadingSpinner;

    // Update UE Assignments containers
    const ueAssignments = document.getElementById('ueAssignments');
    const ueAssignments2 = document.getElementById('ueAssignments2');
    if (ueAssignments) ueAssignments.innerHTML = loadingSpinner;
    if (ueAssignments2) ueAssignments2.innerHTML = loadingSpinner;

    // Update Module Details containers
    const moduleDetails = document.getElementById('moduleDetails');
    const moduleDetails2 = document.getElementById('moduleDetails2');
    if (moduleDetails) moduleDetails.innerHTML = loadingSpinner;
    if (moduleDetails2) moduleDetails2.innerHTML = loadingSpinner;

    // Update Grade History
    document.getElementById('gradeHistory').innerHTML = loadingSpinner;

    // Update Administrative Notes
    document.getElementById('adminNotes').innerHTML = loadingSpinner;

    // Show sections
    document.getElementById('statsSection').style.display = 'block';
    document.getElementById('chartsSection').style.display = 'block';
    document.getElementById('mainContent').style.display = 'block';
    document.getElementById('gradeHistoryAndObservationsSection').style.display = 'block';
}

/**
 * Load all data for a specific year
 */
async function loadYearData(year) {
    try {
        // Load all data in parallel
        const [statsResponse, ueResponse, modulesResponse, gradeResponse, observationsResponse] = await Promise.all([
            fetch(`../../route/teacherHistoryRoute.php?action=getAnnualStats&teacher_id=${teacherId}&academic_year=${year}`),
            fetch(`../../route/teacherHistoryRoute.php?action=getUEAssignments&teacher_id=${teacherId}&academic_year=${year}`),
            fetch(`../../route/teacherHistoryRoute.php?action=getModules&teacher_id=${teacherId}&academic_year=${year}`),
            fetch(`../../route/teacherHistoryRoute.php?action=getGradeHistory&teacher_id=${teacherId}&academic_year=${year}`),
            fetch(`../../route/teacherHistoryRoute.php?action=getObservations&teacher_id=${teacherId}&academic_year=${year}`)
        ]);

        const [statsData, ueData, modulesData, gradeData, observationsData] = await Promise.all([
            statsResponse.json(),
            ueResponse.json(),
            modulesResponse.json(),
            gradeResponse.json(),
            observationsResponse.json()
        ]);

        // Display the data
        if (statsData.success) displayStatistics(statsData.data);
        if (ueData.success) displayUEAssignments(ueData.data);
        if (modulesData.success) displayModules(modulesData.data);
        if (gradeData.success) displayGradeHistory(gradeData.data);
        if (observationsData.success) displayObservations(observationsData.data);

    } catch (error) {
        console.error('Error loading year data:', error);
        showError('Failed to load data for the selected year.');
    }
}

/**
 * Display annual statistics
 */
function displayStatistics(stats) {
    const statsContainer = document.getElementById('statsContainer');

    const totalHours = stats.total_hours || 0;
    const totalModules = stats.total_modules || 0;
    const totalUEs = stats.total_ues || 0;
    const modulesWithGrades = stats.modules_with_grades || 0;

    statsContainer.innerHTML = `
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="history-stats-card">
                <i class="fas fa-clock fa-2x mb-2"></i>
                <h3>${totalHours}</h3>
                <p>Heures Totales</p>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="history-stats-card">
                <i class="fas fa-book fa-2x mb-2"></i>
                <h3>${totalModules}</h3>
                <p>Modules</p>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="history-stats-card">
                <i class="fas fa-chalkboard-teacher fa-2x mb-2"></i>
                <h3>${totalUEs}</h3>
                <p>Unités d'Enseignement</p>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="history-stats-card">
                <i class="fas fa-clipboard-check fa-2x mb-2"></i>
                <h3>${modulesWithGrades}</h3>
                <p>Notes Uploadées</p>
            </div>
        </div>
    `;

    // Show charts section and create charts
    document.getElementById('chartsSection').style.display = 'block';
    createCharts(stats);
}

/**
 * Display UE assignments
 */
function displayUEAssignments(assignments) {
    const container = document.getElementById('ueAssignments');
    const container2 = document.getElementById('ueAssignments2');

    const noDataHtml = `
        <div class="no-data">
            <i class="fas fa-chalkboard-teacher fa-3x mb-3 text-muted"></i>
            <p>Aucune unité d'enseignement assignée pour cette année.</p>
        </div>
    `;

    if (!assignments || assignments.length === 0) {
        if (container) container.innerHTML = noDataHtml;
        if (container2) container2.innerHTML = noDataHtml;
        return;
    }

    let html = '';
    assignments.forEach(assignment => {
        const typeColor = getTypeColor(assignment.type);
        html += `
            <div class="timeline-item">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1">${assignment.module_name}</h6>
                        <span class="ue-badge" style="background: ${typeColor};">
                            ${assignment.type}
                        </span>
                        <p class="small text-muted mb-1">
                            ${assignment.nom_filiere || 'N/A'} - ${assignment.niveau_name || 'N/A'}
                        </p>
                        <p class="small mb-0">
                            <i class="fas fa-clock me-1"></i>
                            ${assignment.volume_horaire}h × ${assignment.nb_groupes} groupe(s)
                        </p>
                    </div>
                </div>
            </div>
        `;
    });

    // Update both containers
    if (container) container.innerHTML = html;
    if (container2) container2.innerHTML = html;
}

/**
 * Display modules
 */
function displayModules(modules) {
    const container = document.getElementById('moduleDetails');
    const container2 = document.getElementById('moduleDetails2');

    const noDataHtml = `
        <div class="no-data">
            <i class="fas fa-book fa-3x mb-3 text-muted"></i>
            <p>Aucun module trouvé pour cette année.</p>
        </div>
    `;

    if (!modules || modules.length === 0) {
        if (container) container.innerHTML = noDataHtml;
        if (container2) container2.innerHTML = noDataHtml;
        return;
    }

    let html = '';
    modules.forEach(module => {
        const ueTypes = module.ue_types ? module.ue_types.split(',') : [];
        let typeBadges = '';
        ueTypes.forEach(type => {
            const typeColor = getTypeColor(type);
            typeBadges += `<span class="ue-badge" style="background: ${typeColor};">${type}</span>`;
        });

        html += `
            <div class="module-card">
                <h6 class="mb-2">${module.nom}</h6>
                <div class="mb-2">${typeBadges}</div>
                <p class="small text-muted mb-1">
                    <i class="fas fa-graduation-cap me-1"></i>
                    ${module.nom_filiere || 'N/A'} - ${module.niveau_name || 'N/A'}
                </p>
                <p class="small text-muted mb-1">
                    <i class="fas fa-calendar me-1"></i>
                    ${module.semestre_name || 'N/A'}
                </p>
                <p class="small mb-0">
                    <i class="fas fa-clock me-1"></i>
                    <strong>${module.total_hours || 0}h</strong> au total
                </p>
            </div>
        `;
    });

    // Update both containers
    if (container) container.innerHTML = html;
    if (container2) container2.innerHTML = html;
}

/**
 * Display grade history
 */
function displayGradeHistory(gradeData) {
    const container = document.getElementById('gradeHistory');

    if (!gradeData || (!gradeData.grade_uploads?.length && !gradeData.pdf_submissions?.length)) {
        container.innerHTML = `
            <div class="no-data">
                <i class="fas fa-clipboard-list fa-3x mb-3 text-muted"></i>
                <p>Aucun historique de notes pour cette année.</p>
            </div>
        `;
        return;
    }

    let html = '';

    // Display grade uploads
    if (gradeData.grade_uploads && gradeData.grade_uploads.length > 0) {
        gradeData.grade_uploads.forEach(upload => {
            const sessionBadge = upload.session === 'normale' ?
                '<span class="badge bg-success">Session Normale</span>' :
                '<span class="badge bg-warning">Rattrapage</span>';

            html += `
                <div class="grade-history-item">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">${upload.module_name}</h6>
                            ${sessionBadge}
                            <p class="small text-muted mb-1">
                                ${upload.nom_filiere || 'N/A'} - ${upload.niveau_name || 'N/A'}
                            </p>
                            <p class="small mb-0">
                                <i class="fas fa-users me-1"></i>
                                ${upload.student_count} étudiants -
                                <i class="fas fa-chart-line me-1"></i>
                                Moyenne: ${parseFloat(upload.average_grade).toFixed(2)}/20
                            </p>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">
                                ${formatDate(upload.date_saisie)}
                            </small>
                        </div>
                    </div>
                </div>
            `;
        });
    }

    // Display PDF submissions
    if (gradeData.pdf_submissions && gradeData.pdf_submissions.length > 0) {
        gradeData.pdf_submissions.forEach(pdf => {
            html += `
                <div class="grade-history-item">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">
                                <i class="fas fa-file-pdf me-2 text-danger"></i>
                                ${pdf.module_name}
                            </h6>
                            <span class="badge bg-info">PDF Envoyé</span>
                            <p class="small text-muted mb-1">
                                ${pdf.nom_filiere || 'N/A'} - ${pdf.niveau_name || 'N/A'}
                            </p>
                            <p class="small mb-0">
                                <i class="fas fa-paper-plane me-1"></i>
                                Envoyé au coordinateur
                            </p>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">
                                Session: ${pdf.session}
                            </small>
                        </div>
                    </div>
                </div>
            `;
        });
    }

    container.innerHTML = html;
}

/**
 * Display administrative observations
 */
function displayObservations(observations) {
    const container = document.getElementById('adminNotes');

    if (!observations || observations.length === 0) {
        container.innerHTML = `
            <div class="no-data">
                <i class="fas fa-info-circle fa-3x mb-3 text-muted"></i>
                <p>Aucune observation administrative pour cette année.</p>
            </div>
        `;
        return;
    }

    let html = '';
    observations.forEach(observation => {
        const gravityBadge = getGravityBadge(observation.niveau_gravite);

        html += `
            <div class="observation-item">
                <div class="observation-header">
                    <h6 class="observation-title">
                        <i class="fas fa-sticky-note me-2"></i>
                        Observation Administrative
                    </h6>
                    <div class="observation-date">
                        ${formatDate(observation.date_observation)}
                    </div>
                    ${gravityBadge}
                </div>
                <div class="observation-content">
                    ${observation.contenu}
                </div>
                <div class="observation-footer">
                    <i class="fas fa-user-shield me-1"></i>
                    Par: ${observation.admin_prenom} ${observation.admin_nom}
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

/**
 * Get gravity badge HTML
 */
function getGravityBadge(gravity) {
    switch (gravity) {
        case 'faible':
            return '<span class="badge bg-success">Gravité Faible</span>';
        case 'moyen':
            return '<span class="badge bg-warning">Gravité Moyenne</span>';
        case 'élevé':
            return '<span class="badge bg-danger">Gravité Élevée</span>';
        default:
            return '<span class="badge bg-secondary">Non spécifié</span>';
    }
}

/**
 * Get gravity color
 */
function getGravityColor(gravity) {
    switch (gravity) {
        case 'faible':
            return '#28a745';
        case 'moyen':
            return '#ffc107';
        case 'élevé':
            return '#dc3545';
        default:
            return '#6c757d';
    }
}

/**
 * Get color for UE type
 */
function getTypeColor(type) {
    switch (type) {
        case 'Cours':
            return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        case 'TD':
            return 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)';
        case 'TP':
            return 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)';
        default:
            return 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)';
    }
}

/**
 * Format date for display
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

/**
 * Show error message
 */
function showError(message) {
    console.error('Error:', message);
    // You can implement a toast notification or modal here
    alert('Erreur: ' + message);
}

/**
 * Create charts for data visualization
 */
let teachingTypeChart = null;
let teachingTypeChart2 = null;
let workloadChart = null;
let workloadChart2 = null;
let moduleDistributionChart = null;

function createCharts(stats) {
    // Destroy existing charts if they exist
    if (teachingTypeChart) teachingTypeChart.destroy();
    if (teachingTypeChart2) teachingTypeChart2.destroy();
    if (workloadChart) workloadChart.destroy();
    if (workloadChart2) workloadChart2.destroy();
    if (moduleDistributionChart) moduleDistributionChart.destroy();

    createTeachingTypeChart(stats);
    createWorkloadChart(stats);
    createModuleDistributionChart(stats);
}

/**
 * Create teaching type distribution pie chart
 */
function createTeachingTypeChart(stats) {
    // Create chart for first canvas
    const ctx = document.getElementById('teachingTypeChart');
    if (ctx) {
        const coursHours = stats.cours_hours || 0;
        const tdHours = stats.td_hours || 0;
        const tpHours = stats.tp_hours || 0;
        const totalHours = coursHours + tdHours + tpHours;

        // Check if there's any data to display
        if (totalHours === 0) {
            // Display a message instead of an empty chart
            const chartContainer = ctx.parentElement;
            chartContainer.innerHTML = `
                <div class="no-data">
                    <i class="fas fa-chart-pie fa-3x mb-3 text-muted"></i>
                    <p>Aucune donnée d'enseignement disponible pour cette année.</p>
                </div>
            `;
            return;
        }

        const chartData = {
            labels: ['Cours', 'TD', 'TP'],
            datasets: [{
                data: [coursHours, tdHours, tpHours],
                backgroundColor: [
                    'rgba(102, 126, 234, 0.8)',
                    'rgba(240, 147, 251, 0.8)',
                    'rgba(79, 172, 254, 0.8)'
                ],
                borderColor: [
                    'rgba(102, 126, 234, 1)',
                    'rgba(240, 147, 251, 1)',
                    'rgba(79, 172, 254, 1)'
                ],
                borderWidth: 2
            }]
        };

        const chartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ' + context.parsed + 'h';
                        }
                    }
                }
            }
        };

        teachingTypeChart = new Chart(ctx.getContext('2d'), {
            type: 'doughnut',
            data: chartData,
            options: chartOptions
        });
    }

    // Create chart for second canvas (side by side)
    const ctx2 = document.getElementById('teachingTypeChart2');
    if (ctx2) {
        const coursHours = stats.cours_hours || 0;
        const tdHours = stats.td_hours || 0;
        const tpHours = stats.tp_hours || 0;
        const totalHours = coursHours + tdHours + tpHours;

        // Check if there's any data to display
        if (totalHours === 0) {
            // Display a message instead of an empty chart
            const chartContainer = ctx2.parentElement;
            chartContainer.innerHTML = `
                <div class="no-data">
                    <i class="fas fa-chart-pie fa-3x mb-3 text-muted"></i>
                    <p>Aucune donnée d'enseignement disponible pour cette année.</p>
                </div>
            `;
            return;
        }

        const chartData2 = {
            labels: ['Cours', 'TD', 'TP'],
            datasets: [{
                data: [coursHours, tdHours, tpHours],
                backgroundColor: [
                    'rgba(102, 126, 234, 0.8)',
                    'rgba(240, 147, 251, 0.8)',
                    'rgba(79, 172, 254, 0.8)'
                ],
                borderColor: [
                    'rgba(102, 126, 234, 1)',
                    'rgba(240, 147, 251, 1)',
                    'rgba(79, 172, 254, 1)'
                ],
                borderWidth: 2
            }]
        };

        const chartOptions2 = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ' + context.parsed + 'h';
                        }
                    }
                }
            }
        };

        teachingTypeChart2 = new Chart(ctx2.getContext('2d'), {
            type: 'doughnut',
            data: chartData2,
            options: chartOptions2
        });
    }
}

/**
 * Create workload evolution line chart
 */
function createWorkloadChart(stats) {
    // Create chart for first canvas
    const ctx = document.getElementById('workloadChart');
    if (ctx) {
        const chartData = {
            labels: ['2020/2021', '2021/2022', '2022/2023', '2023/2024', currentSelectedYear],
            datasets: [{
                label: 'Heures d\'enseignement',
                data: [180, 195, 210, 225, stats.total_hours || 0],
                borderColor: 'rgba(102, 126, 234, 1)',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgba(102, 126, 234, 1)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        };

        const chartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.parsed.y + ' heures';
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return value + 'h';
                        }
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                }
            }
        };

        workloadChart = new Chart(ctx.getContext('2d'), {
            type: 'line',
            data: chartData,
            options: chartOptions
        });
    }

    // Create chart for second canvas (side by side)
    const ctx2 = document.getElementById('workloadChart2');
    if (ctx2) {
        const chartData2 = {
            labels: ['2020/2021', '2021/2022', '2022/2023', '2023/2024', currentSelectedYear],
            datasets: [{
                label: 'Heures d\'enseignement',
                data: [180, 195, 210, 225, stats.total_hours || 0],
                borderColor: 'rgba(102, 126, 234, 1)',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgba(102, 126, 234, 1)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        };

        const chartOptions2 = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.parsed.y + ' heures';
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return value + 'h';
                        }
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                }
            }
        };

        workloadChart2 = new Chart(ctx2.getContext('2d'), {
            type: 'line',
            data: chartData2,
            options: chartOptions2
        });
    }
}

/**
 * Create module distribution bar chart
 */
function createModuleDistributionChart(stats) {
    const ctx = document.getElementById('moduleDistributionChart').getContext('2d');

    // Sample data - in real implementation, this would come from module distribution data
    const data = {
        labels: ['GI1', 'GI2', 'GI3', 'CP1', 'CP2'],
        datasets: [{
            label: 'Nombre de modules',
            data: [2, 3, 1, 2, 1],
            backgroundColor: [
                'rgba(168, 216, 234, 0.8)',
                'rgba(170, 246, 131, 0.8)',
                'rgba(255, 203, 165, 0.8)',
                'rgba(255, 183, 206, 0.8)',
                'rgba(211, 181, 229, 0.8)'
            ],
            borderColor: [
                'rgba(168, 216, 234, 1)',
                'rgba(170, 246, 131, 1)',
                'rgba(255, 203, 165, 1)',
                'rgba(255, 183, 206, 1)',
                'rgba(211, 181, 229, 1)'
            ],
            borderWidth: 2,
            borderRadius: 8
        }]
    };

    moduleDistributionChart = new Chart(ctx, {
        type: 'bar',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.parsed.y + ' module(s)';
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        stepSize: 1,
                        callback: function(value) {
                            return value + ' module(s)';
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

/**
 * Download full report as PDF
 */
document.getElementById('downloadFullReport').addEventListener('click', async function() {
    if (!currentSelectedYear) {
        showError('Veuillez sélectionner une année universitaire.');
        return;
    }

    try {
        // Show loading state
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Génération...';
        this.disabled = true;

        const response = await fetch(`../../route/teacherHistoryRoute.php?action=generateReport&teacher_id=${teacherId}&academic_year=${currentSelectedYear}`);
        const data = await response.json();

        if (data.success) {
            // Generate PDF content
            generatePDFReport(data.data);
        } else {
            showError(data.error || 'Failed to generate report');
        }
    } catch (error) {
        console.error('Error generating report:', error);
        showError('Failed to generate report. Please try again.');
    } finally {
        // Reset button
        this.innerHTML = '<i class="fas fa-download me-2"></i>Rapport Complet';
        this.disabled = false;
    }
});

/**
 * Generate Professional PDF report with comprehensive information
 */
function generatePDFReport(reportData) {
    // Create a new window for the PDF content
    const printWindow = window.open('', '_blank');

    const htmlContent = `
        <!DOCTYPE html>
        <html lang="fr">
        <head>
            <meta charset="UTF-8">
            <title>Rapport d'Enseignement - ${teacherName} - ${currentSelectedYear}</title>
            <style>
                body {
                    font-family: 'Arial', 'Helvetica', sans-serif;
                    margin: 0;
                    padding: 15mm;
                    color: #2c3e50;
                    line-height: 1.4;
                    background: #fff;
                    font-size: 12px;
                }

                /* Professional Header */
                .header {
                    border-bottom: 3px solid #2c3e50;
                    padding-bottom: 15px;
                    margin-bottom: 20px;
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                }
                .header-left {
                    flex: 1;
                }
                .header-right {
                    text-align: right;
                    flex: 1;
                }
                .institution-name {
                    font-size: 16px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 0;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }
                .institution-subtitle {
                    font-size: 11px;
                    color: #7f8c8d;
                    margin: 2px 0 0 0;
                }
                .report-title {
                    font-size: 18px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 0;
                    text-transform: uppercase;
                }
                .report-meta {
                    font-size: 11px;
                    color: #7f8c8d;
                    margin: 5px 0 0 0;
                }

                /* Teacher Info Box */
                .teacher-info {
                    background: #ecf0f1;
                    border: 1px solid #bdc3c7;
                    padding: 12px;
                    margin-bottom: 20px;
                    border-radius: 3px;
                }
                .teacher-info table {
                    width: 100%;
                    border-collapse: collapse;
                }
                .teacher-info td {
                    padding: 3px 8px;
                    font-size: 11px;
                }
                .teacher-info .label {
                    font-weight: bold;
                    color: #2c3e50;
                    width: 120px;
                }
                .teacher-info .value {
                    color: #34495e;
                }



                /* Professional Section Styles */
                .section {
                    margin-bottom: 25px;
                    page-break-inside: avoid;
                }
                .section-title {
                    font-size: 14px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 0 0 12px 0;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    border-bottom: 2px solid #34495e;
                    padding-bottom: 5px;
                }

                /* Professional Table Styles */
                .data-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 10px 0;
                    font-size: 11px;
                    border: 1px solid #bdc3c7;
                }
                .data-table th {
                    background: #34495e;
                    color: white;
                    padding: 8px 6px;
                    text-align: left;
                    font-weight: bold;
                    font-size: 10px;
                    text-transform: uppercase;
                    letter-spacing: 0.3px;
                }
                .data-table td {
                    padding: 6px;
                    border-bottom: 1px solid #ecf0f1;
                    vertical-align: top;
                }
                .data-table tr:nth-child(even) {
                    background: #f8f9fa;
                }
                .data-table tr:hover {
                    background: #e8f4f8;
                }

                /* Compact List Styles */
                .compact-list {
                    margin: 0;
                    padding: 0;
                    list-style: none;
                }
                .compact-item {
                    padding: 6px 0;
                    border-bottom: 1px solid #ecf0f1;
                    font-size: 11px;
                }
                .compact-item:last-child {
                    border-bottom: none;
                }
                .item-title {
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 2px;
                }
                .item-details {
                    color: #7f8c8d;
                    font-size: 10px;
                }

                /* Status Badges */
                .status-badge {
                    display: inline-block;
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-size: 9px;
                    font-weight: bold;
                    text-transform: uppercase;
                }
                .badge-normale { background: #d5f4e6; color: #27ae60; }
                .badge-rattrapage { background: #fdeaea; color: #e74c3c; }
                .badge-pdf { background: #e8f4f8; color: #3498db; }


                /* Professional Footer */
                .footer {
                    margin-top: 30px;
                    padding-top: 15px;
                    border-top: 2px solid #bdc3c7;
                    font-size: 10px;
                    color: #7f8c8d;
                    text-align: center;
                }
                .footer-content {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .footer-left, .footer-right {
                    flex: 1;
                }
                .footer-center {
                    flex: 2;
                    text-align: center;
                }
                .footer-right {
                    text-align: right;
                }

                /* Print Optimization */
                @media print {
                    body {
                        padding: 10mm;
                        font-size: 11px;
                        line-height: 1.3;
                    }
                    .section {
                        page-break-inside: avoid;
                        margin-bottom: 15px;
                    }
                    .data-table {
                        font-size: 10px;
                    }
                    .data-table th {
                        padding: 5px 4px;
                    }
                    .data-table td {
                        padding: 4px;
                    }
                    .footer {
                        margin-top: 20px;
                        font-size: 9px;
                    }
                }
            </style>
        </head>
        <body>
            <!-- Professional Header -->
            <div class="header">
                <div class="header-left">
                    <h1 class="institution-name">École Nationale des Sciences Appliquées</h1>
                    <p class="institution-subtitle">Al Hoceima - Université Mohammed Premier</p>
                </div>
                <div class="header-right">
                    <h2 class="report-title">Rapport d'Enseignement</h2>
                    <p class="report-meta">Année Universitaire ${currentSelectedYear}</p>
                    <p class="report-meta">Généré le ${new Date().toLocaleDateString('fr-FR')}</p>
                </div>
            </div>

            <!-- Teacher Information -->
            <div class="teacher-info">
                <table>
                    <tr>
                        <td class="label">Enseignant :</td>
                        <td class="value">${teacherName}</td>
                        <td class="label">Période :</td>
                        <td class="value">${currentSelectedYear}</td>
                    </tr>
                    <tr>
                        <td class="label">Date de génération :</td>
                        <td class="value">${new Date().toLocaleDateString('fr-FR')}</td>
                        <td class="label">Type de rapport :</td>
                        <td class="value">Activités d'enseignement annuelles</td>
                    </tr>
                </table>
            </div>

            <!-- UE Assignments Table -->
            <div class="section">
                <h3 class="section-title">Unités d'Enseignement Assignées</h3>
                ${reportData.ue_assignments && reportData.ue_assignments.length > 0 ? `
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Module</th>
                                <th>Type</th>
                                <th>Filière</th>
                                <th>Niveau</th>
                                <th>Semestre</th>
                                <th>Vol. Horaire</th>
                                <th>Nb Groupes</th>
                                <th>Total (h)</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${reportData.ue_assignments.map(assignment => `
                                <tr>
                                    <td><strong>${assignment.module_name}</strong></td>
                                    <td>${assignment.type}</td>
                                    <td>${assignment.nom_filiere || 'N/A'}</td>
                                    <td>${assignment.niveau_name || 'N/A'}</td>
                                    <td>${assignment.semestre_name || 'N/A'}</td>
                                    <td>${assignment.volume_horaire}h</td>
                                    <td>${assignment.nb_groupes}</td>
                                    <td><strong>${(assignment.volume_horaire * assignment.nb_groupes)}h</strong></td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                ` : '<p style="text-align: center; color: #7f8c8d; font-style: italic;">Aucune unité d\'enseignement affectée pour cette année.</p>'}
            </div>

            <!-- Grade History Table -->
            <div class="section">
                <h3 class="section-title">Historique des Notes</h3>
                ${reportData.grade_history && (reportData.grade_history.grade_uploads || reportData.grade_history.pdf_submissions) ? `
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Module</th>
                                <th>Session</th>
                                <th>Filière</th>
                                <th>Niveau</th>
                                <th>Étudiants</th>
                                <th>Moyenne</th>
                                <th>Type</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${[
                                ...(reportData.grade_history.grade_uploads || []).map(grade => `
                                    <tr>
                                        <td><strong>${grade.module_name}</strong></td>
                                        <td><span class="status-badge badge-${grade.session}">${grade.session}</span></td>
                                        <td>${grade.nom_filiere || 'N/A'}</td>
                                        <td>${grade.niveau_name || 'N/A'}</td>
                                        <td>${grade.student_count}</td>
                                        <td><strong>${parseFloat(grade.average_grade).toFixed(2)}/20</strong></td>
                                        <td>Notes saisies</td>
                                        <td>${formatDate(grade.date_saisie)}</td>
                                    </tr>
                                `),
                                ...(reportData.grade_history.pdf_submissions || []).map(pdf => `
                                    <tr>
                                        <td><strong>${pdf.module_name}</strong></td>
                                        <td><span class="status-badge badge-${pdf.session}">${pdf.session}</span></td>
                                        <td>${pdf.nom_filiere || 'N/A'}</td>
                                        <td>${pdf.niveau_name || 'N/A'}</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td><span class="status-badge badge-pdf">PDF</span></td>
                                        <td>${formatDate(pdf.date_envoi)}</td>
                                    </tr>
                                `)
                            ].join('')}
                        </tbody>
                    </table>
                ` : '<p style="text-align: center; color: #7f8c8d; font-style: italic;">Aucun historique de notes disponible pour cette année.</p>'}
            </div>

            <!-- Professional Footer -->
            <div class="footer">
                <div class="footer-content">
                    <div class="footer-left">
                        <strong>ENSAH</strong><br>
                        École Nationale des Sciences Appliquées
                    </div>
                    <div class="footer-center">
                        <strong>Rapport d'Enseignement</strong><br>
                        ${teacherName} - ${currentSelectedYear}
                    </div>
                    <div class="footer-right">
                        Généré le ${new Date().toLocaleDateString('fr-FR')}<br>
                        Document confidentiel
                    </div>
                </div>
            </div>
        </body>
        </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();

    // Wait for content to load then print
    printWindow.onload = function() {
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 500);
    };
}
