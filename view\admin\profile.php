<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
    header('Location: ../../index.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profil Administrateur</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    
    <style>
        /* Section de profil avec thème bleu */
        .profile-section {
            background: linear-gradient(135deg, var(--royal-blue) 0%, var(--navy-blue) 100%);
            color: white;
            padding: 1.75rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(26, 115, 232, 0.2);
        }

        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid white;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,0,0,0.15);
            position: relative;
            z-index: 1;
        }

        /* Sections de formulaire */
        .form-section, .password-section {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 1px 10px rgba(0,0,0,0.05);
            margin-bottom: 2rem;
            border: 1px solid var(--gray-200);
        }

        /* Améliorations des formulaires */
        .form-control:focus, .form-select:focus {
            border-color: var(--royal-blue);
            box-shadow: 0 0 0 0.2rem rgba(26, 115, 232, 0.15);
        }

        /* Styles des boutons */
        .btn-primary {
            background-color: var(--royal-blue);
            border-color: var(--royal-blue);
        }

        .btn-primary:hover {
            background-color: var(--navy-blue);
            border-color: var(--navy-blue);
        }

        .btn-secondary {
            background-color: var(--gray-600);
            border-color: var(--gray-600);
        }

        .btn-secondary:hover {
            background-color: var(--gray-700);
            border-color: var(--gray-700);
        }

        /* Styles des alertes */
        .alert {
            display: none;
            margin-top: 1rem;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        /* Titres et textes */
        h2, h3 {
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 1.5rem;
        }

        .form-label {
            font-weight: 500;
            color: var(--gray-700);
        }

        /* Bordure en pointillés pour l'upload */
        .border-dashed {
            border: 1px dashed var(--gray-300);
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 include __DIR__ . '/../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 include __DIR__ . '/../includes/header.php'; ?>

            <div class="container-fluid p-4">
                <!-- Page Title -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0 text-gray-800">Mon Profil</h1>
                </div>

                <!-- Section Profil -->
                <div class="profile-section">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <img id="profileImage" src="../assets/img/default-profile.svg" data-src="" alt="Photo de profil" class="profile-image">

                        </div>
                        <div class="col text-start">
                            <h2 id="welcomeMessage" class="h4 mb-1">Chargement...</h2>
                            <p id="userRole" class="mb-0 text-white-50">Chargement...</p>
                        </div>
                    </div>
                </div>

        <!-- Section Principale (Formulaire) -->
        <div class="form-section">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3 class="mb-0">Informations Personnelles</h3>
                <button type="button" id="editButton" class="btn btn-outline-primary"><i class="fas fa-edit me-2"></i>Modifier</button>
            </div>
            <hr class="mb-4" style="border-color: var(--gray-200);">
            <form id="profileForm">
                <div class="row g-4">
                    <!-- Première ligne -->
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="nom" placeholder="Nom" disabled>
                            <label for="nom">Nom</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="prenom" placeholder="Prénom" disabled>
                            <label for="prenom">Prénom</label>
                        </div>
                    </div>

                    <!-- Deuxième ligne -->
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="cni" placeholder="CNI" readonly>
                            <label for="cni">CNI</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input type="email" class="form-control" id="email" placeholder="Email" required disabled>
                            <label for="email">Email</label>
                        </div>
                    </div>

                    <!-- Troisième ligne -->
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input type="tel" class="form-control" id="tele" placeholder="Téléphone" required disabled>
                            <label for="tele">Téléphone</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input type="date" class="form-control" id="date_naissance" required disabled>
                            <label for="date_naissance">Date de naissance</label>
                        </div>
                    </div>

                    <!-- Quatrième ligne -->
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="lieu_naissance" placeholder="Lieu de naissance" required disabled>
                            <label for="lieu_naissance">Lieu de naissance</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <select class="form-select" id="sexe" required disabled>
                                <option value="masculin">Masculin</option>
                                <option value="féminin">Féminin</option>
                            </select>
                            <label for="sexe">Sexe</label>
                        </div>
                    </div>

                    <!-- Cinquième ligne -->
                    <div class="col-md-4">
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="ville" placeholder="Ville" required disabled>
                            <label for="ville">Ville</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="pays" placeholder="Pays" required disabled>
                            <label for="pays">Pays</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-floating mb-3">
                            <input type="date" class="form-control" id="date_debut_travail" required disabled>
                            <label for="date_debut_travail">Date début travail</label>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-end mt-4">
                    <button type="submit" class="btn btn-primary" id="saveButton" style="display: none;">
                        <i class="fas fa-save me-2"></i>Enregistrer les modifications
                    </button>
                </div>

                <div class="alert" id="profileAlert" role="alert"></div>
            </form>
        </div>

        <!-- Section Mot de passe -->
        <div class="password-section">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3 class="mb-0">Sécurité</h3>
                <span class="badge bg-blue-100"><i class="fas fa-shield-alt me-1"></i> Protégé</span>
            </div>
            <hr class="mb-4" style="border-color: var(--gray-200);">

            <div class="card border-0 bg-light mb-4">
                <div class="card-body p-4">
                    <h5 class="card-title"><i class="fas fa-key text-primary me-2"></i>Changer le mot de passe</h5>
                    <p class="card-text text-muted mb-0">Mettez à jour régulièrement votre mot de passe pour sécuriser votre compte.</p>
                </div>
            </div>

            <form id="passwordForm">
                <div class="row g-4">
                    <div class="col-md-12">
                        <div class="form-floating mb-3">
                            <input type="password" class="form-control" id="oldPassword" placeholder="Ancien mot de passe" required>
                            <label for="oldPassword">Ancien mot de passe</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input type="password" class="form-control" id="newPassword" placeholder="Nouveau mot de passe" required>
                            <label for="newPassword">Nouveau mot de passe</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input type="password" class="form-control" id="confirmPassword" placeholder="Confirmer le nouveau mot de passe" required>
                            <label for="confirmPassword">Confirmer le nouveau mot de passe</label>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-end mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-lock me-2"></i>Changer le mot de passe
                    </button>
                </div>

                <div class="alert" id="passwordAlert" role="alert"></div>
            </form>
        </div>
            </div>
        </div>
    </div>

    <!-- Modal pour la photo de profil -->
    <div class="modal fade" id="profileImageModal" tabindex="-1" aria-labelledby="profileImageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="profileImageModalLabel">Photo de profil</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center p-3">
                    <img id="modalProfileImage" src="../assets/img/default-profile.svg" alt="Photo de profil" class="img-fluid rounded mb-3" style="max-height: 250px;">
                    <div id="imageUploadForm" style="display: none;" class="mb-3">
                        <div class="border-dashed p-3 bg-light">
                            <p class="text-muted mb-2 small">Sélectionnez une image</p>
                            <input type="file" id="profileImageUpload" class="form-control form-control-sm" accept="image/*">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" id="deleteImageBtn" class="btn btn-sm btn-outline-danger">
                        <i class="fas fa-trash me-1"></i>Supprimer
                    </button>
                    <div>
                        <button type="button" id="editImageBtn" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit me-1"></i>Modifier
                        </button>
                        <button type="button" id="saveImageBtn" class="btn btn-sm btn-primary" style="display: none;">
                            <i class="fas fa-save me-1"></i>Enregistrer
                        </button>
                        <button type="button" id="cancelImageBtn" class="btn btn-sm btn-secondary" style="display: none;">
                            <i class="fas fa-times me-1"></i>Annuler
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Fonction pour obtenir le chemin de base du projet
        function getBasePath() {
            // Obtenir le chemin de base à partir de l'URL actuelle
            const path = window.location.pathname;
            // Si nous sommes dans /view/admin/, remonter de deux niveaux
            if (path.includes('/view/admin/')) {
                return window.location.origin + path.substring(0, path.indexOf('/view/admin/'));
            }
            // Sinon, utiliser l'origine
            return window.location.origin;
        }

        // Chemin de base du projet
        const basePath = getBasePath();
        console.log('Chemin de base du projet:', basePath);
    </script>
    <script src="../assets/js/sidebar.js"></script>
    <script>
        // Fonction pour afficher les alertes
        function showAlert(elementId, message, type) {
            const alert = document.getElementById(elementId);
            alert.textContent = message;
            alert.className = `alert alert-${type}`;
            alert.style.display = 'block';
            setTimeout(() => alert.style.display = 'none', 5000);
        }

        // Charger les données de l'utilisateur
        async function loadUserData() {
            try {
                const cni = '<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $_SESSION["user"]["username"]; ?>';
                console.log('CNI:', cni);

                // Afficher un message de chargement
                showAlert('profileAlert', 'Chargement des données...', 'info');

                // Suppression de l'appel au fichier diagnostic.php qui n'existe plus

                // Utiliser un chemin relatif pour l'API
                const adminResponse = await fetch(`../../route/adminRoute.php?cni=${cni}`);
                if (!adminResponse.ok) {
                    throw new Error(`Erreur HTTP: ${adminResponse.status} ${adminResponse.statusText}`);
                }

                const data = await adminResponse.json();
                console.log('Données reçues:', data);

                if (data.success) {
                    const admin = data.admin;
                    document.getElementById('welcomeMessage').textContent = `Bienvenue ${admin.prenom} ${admin.nom}`;
                    document.getElementById('userRole').textContent = '<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $_SESSION["user"]["role"]; ?>';

                    // Remplir le formulaire
                    document.getElementById('cni').value = admin.CNI;
                    document.getElementById('nom').value = admin.nom || '';
                    document.getElementById('prenom').value = admin.prenom || '';
                    document.getElementById('email').value = admin.email;
                    document.getElementById('tele').value = admin.tele;
                    document.getElementById('date_naissance').value = admin.date_naissance;
                    document.getElementById('lieu_naissance').value = admin.lieu_naissance;
                    document.getElementById('sexe').value = admin.sexe;
                    document.getElementById('ville').value = admin.ville || '';
                    document.getElementById('pays').value = admin.pays || '';
                    document.getElementById('date_debut_travail').value = admin.date_debut_travail;

                    // Charger la photo de profil
                    const photoUrl = admin.photo_url || defaultPhotoUrl;
                    console.log('URL de la photo chargée:', photoUrl);

                    const img = document.getElementById('profileImage');
                    const modalImg = document.getElementById('modalProfileImage');

                    // Fonction pour charger une image de manière robuste en utilisant le script serve_image.php
                    function loadImageRobustly(imageUrl, imgElement) {
                        // Vérifier si l'URL de l'image est définie
                        if (!imageUrl) {
                            console.log('URL de l\'image non définie');
                            imgElement.src = defaultPhotoUrl;
                            return;
                        }

                        // Utiliser l'URL complète comme paramètre pour le script serve_image.php
                        console.log('URL de l\'image à charger:', imageUrl);

                        // Construire l'URL du script serve_image.php avec un timestamp pour éviter le cache
                        const timestamp = new Date().getTime();
                        const serviceUrl = '../../serve_image.php?file=' + encodeURIComponent(imageUrl) + '&t=' + timestamp;

                        console.log('Chargement direct de l\'image:', serviceUrl);

                        // Appliquer directement l'URL à l'image
                        imgElement.src = serviceUrl;

                        // Gérer l'échec du chargement
                        imgElement.onerror = function() {
                            console.error('Erreur de chargement de l\'image');
                            imgElement.src = defaultPhotoUrl;
                        };
                    }

                    // Charger l'image avec la nouvelle fonction
                    loadImageRobustly(photoUrl, img);
                    loadImageRobustly(photoUrl, modalImg);

                    // Les gestionnaires d'erreur sont déjà définis dans la fonction loadImageRobustly

                    // Masquer l'alerte de chargement
                    document.getElementById('profileAlert').style.display = 'none';
                } else {
                    showAlert('profileAlert', data.error || 'Erreur lors du chargement des données', 'danger');
                }
            } catch (error) {
                console.error('Erreur lors du chargement des données:', error);
                showAlert('profileAlert', `Erreur lors du chargement des données: ${error.message}`, 'danger');
            }
        }

        // Mettre à jour le profil
        document.getElementById('profileForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            try {
                const formData = {
                    nom: document.getElementById('nom').value,
                    prenom: document.getElementById('prenom').value,
                    email: document.getElementById('email').value,
                    tele: document.getElementById('tele').value,
                    date_naissance: document.getElementById('date_naissance').value,
                    lieu_naissance: document.getElementById('lieu_naissance').value,
                    sexe: document.getElementById('sexe').value,
                    ville: document.getElementById('ville').value,
                    pays: document.getElementById('pays').value,
                    date_debut_travail: document.getElementById('date_debut_travail').value
                };

                // Journaliser les données envoyées
                console.log('Données envoyées:', formData);
                console.log('Valeur du sexe:', document.getElementById('sexe').value);

                const response = await fetch(`../../route/adminRoute.php?cni=${document.getElementById('cni').value}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();
                console.log('Réponse reçue:', result);

                if (result.error) {
                    showAlert('profileAlert', result.error, 'danger');
                } else {
                    showAlert('profileAlert', result.message || 'Profil mis à jour avec succès', result.success ? 'success' : 'danger');
                }

                // Si la mise à jour a réussi, désactiver les champs et réinitialiser l'interface
                if (result.success) {
                    // Désactiver tous les champs du formulaire
                    const formInputs = document.querySelectorAll('#profileForm input:not([readonly]), #profileForm select');
                    formInputs.forEach(input => input.disabled = true);

                    // Réinitialiser le bouton d'édition
                    const editButton = document.getElementById('editButton');
                    editButton.innerHTML = '<i class="fas fa-edit"></i> Modifier';
                    editButton.classList.replace('btn-danger', 'btn-secondary');
                    editButton.setAttribute('data-editing', 'false');

                    // Masquer le bouton de sauvegarde
                    document.getElementById('saveButton').style.display = 'none';
                }
            } catch (error) {
                console.error('Erreur lors de la mise à jour:', error);
                showAlert('profileAlert', 'Erreur lors de la mise à jour du profil', 'danger');
            }
        });

        // Changer le mot de passe
        document.getElementById('passwordForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (newPassword !== confirmPassword) {
                showAlert('passwordAlert', 'Les mots de passe ne correspondent pas', 'danger');
                return;
            }

            try {
                const formData = {
                    action: 'changePassword',
                    username: document.getElementById('cni').value, // Utiliser le CNI comme nom d'utilisateur
                    oldPassword: document.getElementById('oldPassword').value,
                    newPassword: newPassword
                };

                const response = await fetch(`../../controller/authController.php`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();
                showAlert('passwordAlert', result.message || result.error || 'Mot de passe changé avec succès', result.success ? 'success' : 'danger');
                if (result.success) {
                    document.getElementById('passwordForm').reset();
                }
            } catch (error) {
                console.error('Erreur lors du changement de mot de passe:', error);
                showAlert('passwordAlert', 'Erreur lors du changement de mot de passe', 'danger');
            }
        });

        // Gestion de la photo de profil
        let currentPhotoUrl = '';
        // Définir l'URL de la photo par défaut avec un chemin relatif
        let defaultPhotoUrl = '../assets/img/default-profile.svg';

        // Vérifier si l'image par défaut existe, sinon utiliser une image de secours
        const testImg = new Image();
        testImg.onload = function() {
            console.log('Image par défaut chargée avec succès');
        };
        testImg.onerror = function() {
            console.log('Erreur de chargement de l\'image par défaut, utilisation de l\'image de secours');
            defaultPhotoUrl = 'https://via.placeholder.com/150';

            // Mettre à jour les images si elles utilisent déjà l'image par défaut
            const profileImg = document.getElementById('profileImage');
            const modalImg = document.getElementById('modalProfileImage');

            if (profileImg.src.includes('default-profile.svg')) {
                profileImg.src = defaultPhotoUrl;
            }

            if (modalImg.src.includes('default-profile.svg')) {
                modalImg.src = defaultPhotoUrl;
            }
        };

        // Ajouter un timestamp pour éviter le cache
        testImg.src = defaultPhotoUrl + '?t=' + new Date().getTime();
        let photoChanged = false;
        let selectedFile = null;

        // Ouvrir le modal lorsqu'on clique sur la photo de profil
        document.getElementById('profileImage').addEventListener('click', function() {
            const modalImage = document.getElementById('modalProfileImage');
            modalImage.src = this.src;
            const modal = new bootstrap.Modal(document.getElementById('profileImageModal'));
            modal.show();
        });

        // Bouton Modifier la photo
        document.getElementById('editImageBtn').addEventListener('click', function() {
            document.getElementById('imageUploadForm').style.display = 'block';
            this.style.display = 'none';
            document.getElementById('saveImageBtn').style.display = 'inline-block';
            document.getElementById('cancelImageBtn').style.display = 'inline-block';
        });

        // Bouton Annuler la modification
        document.getElementById('cancelImageBtn').addEventListener('click', function() {
            document.getElementById('imageUploadForm').style.display = 'none';
            document.getElementById('profileImageUpload').value = '';
            document.getElementById('editImageBtn').style.display = 'inline-block';
            this.style.display = 'none';
            document.getElementById('saveImageBtn').style.display = 'none';
            document.getElementById('modalProfileImage').src = currentPhotoUrl || defaultPhotoUrl;
            selectedFile = null;
        });

        // Prévisualisation de l'image sélectionnée
        document.getElementById('profileImageUpload').addEventListener('change', function(e) {
            if (this.files && this.files[0]) {
                selectedFile = this.files[0];
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Appliquer l'image prévisualisée au modal
                    const modalImg = document.getElementById('modalProfileImage');
                    modalImg.src = e.target.result;
                    photoChanged = true;

                    console.log('Image prévisualisée chargée');
                };
                reader.readAsDataURL(this.files[0]);
            }
        });

        // Bouton Enregistrer la photo
        document.getElementById('saveImageBtn').addEventListener('click', async function() {
            if (!selectedFile && !photoChanged) {
                return;
            }

            try {
                const formData = new FormData();
                if (selectedFile) {
                    formData.append('profileImage', selectedFile);
                }

                const cni = document.getElementById('cni').value;
                console.log('Envoi de la photo au serveur...');
                console.log('CNI:', cni);
                console.log('Fichier:', selectedFile);

                const response = await fetch(`../../photo_handler.php?cni=${cni}&action=updatePhoto`, {
                    method: 'POST',
                    body: formData
                });

                console.log('Réponse du serveur:', response.status, response.statusText);

                const result = await response.json();
                console.log('Résultat:', result);

                if (result.success) {
                    // Mettre à jour la photo dans l'interface
                    const newPhotoUrl = result.photo_url || defaultPhotoUrl;
                    console.log('URL de la photo mise à jour:', newPhotoUrl);

                    // Charger l'image avec la fonction robuste
                    const profileImg = document.getElementById('profileImage');
                    const modalImg = document.getElementById('modalProfileImage');

                    // Forcer le rechargement de l'image avec un timestamp pour éviter le cache
                    const timestamp = new Date().getTime();
                    const serviceUrl = '../../serve_image.php?file=' + encodeURIComponent(newPhotoUrl) + '&t=' + timestamp;

                    console.log('Chargement direct de la nouvelle image:', serviceUrl);

                    // Appliquer directement l'URL aux images
                    profileImg.src = serviceUrl;
                    modalImg.src = serviceUrl;

                    // Définir des gestionnaires d'erreur pour les images
                    profileImg.onerror = function() {
                        console.error('Erreur de chargement de l\'image de profil');
                        profileImg.src = defaultPhotoUrl;
                    };

                    modalImg.onerror = function() {
                        console.error('Erreur de chargement de l\'image modale');
                        modalImg.src = defaultPhotoUrl;
                    };
                    currentPhotoUrl = newPhotoUrl;

                    // Réinitialiser l'interface du modal
                    document.getElementById('imageUploadForm').style.display = 'none';
                    document.getElementById('editImageBtn').style.display = 'inline-block';
                    document.getElementById('saveImageBtn').style.display = 'none';
                    document.getElementById('cancelImageBtn').style.display = 'none';

                    // Fermer le modal
                    bootstrap.Modal.getInstance(document.getElementById('profileImageModal')).hide();

                    showAlert('profileAlert', 'Photo de profil mise à jour avec succès', 'success');
                } else {
                    showAlert('profileAlert', result.error || 'Erreur lors de la mise à jour de la photo', 'danger');
                }
            } catch (error) {
                console.error('Erreur lors de la mise à jour de la photo:', error);
                showAlert('profileAlert', 'Erreur lors de la mise à jour de la photo', 'danger');
            }
        });

        // Bouton Supprimer la photo
        document.getElementById('deleteImageBtn').addEventListener('click', async function() {
            if (confirm('Êtes-vous sûr de vouloir supprimer votre photo de profil ?')) {
                try {
                    const cni = document.getElementById('cni').value;
                    const response = await fetch(`../../photo_handler.php?cni=${cni}&action=deletePhoto`, {
                        method: 'DELETE'
                    });

                    const result = await response.json();
                    if (result.success) {
                        console.log('Suppression de la photo réussie');
                        console.log('URL de la photo par défaut:', defaultPhotoUrl);

                        // Réinitialiser la photo dans l'interface avec un timestamp pour éviter le cache
                        const timestamp = new Date().getTime();
                        const defaultPhotoUrlWithTimestamp = defaultPhotoUrl + '?t=' + timestamp;

                        document.getElementById('profileImage').src = defaultPhotoUrlWithTimestamp;
                        document.getElementById('modalProfileImage').src = defaultPhotoUrlWithTimestamp;
                        currentPhotoUrl = defaultPhotoUrl;

                        // Fermer le modal
                        bootstrap.Modal.getInstance(document.getElementById('profileImageModal')).hide();

                        showAlert('profileAlert', 'Photo de profil supprimée avec succès', 'success');
                    } else {
                        showAlert('profileAlert', result.error || 'Erreur lors de la suppression de la photo', 'danger');
                    }
                } catch (error) {
                    console.error('Erreur lors de la suppression de la photo:', error);
                    showAlert('profileAlert', 'Erreur lors de la suppression de la photo', 'danger');
                }
            }
        });

        // Charger les données au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            loadUserData();
            // Initialiser la photo actuelle
            currentPhotoUrl = document.getElementById('profileImage').src;
        });
        // Gestion du bouton de modification
        document.getElementById('editButton').addEventListener('click', function() {
            const isEditing = this.getAttribute('data-editing') === 'true';
            const formInputs = document.querySelectorAll('#profileForm input:not([readonly]), #profileForm select');
            const saveButton = document.getElementById('saveButton');

            if (!isEditing) {
                // Activer la modification
                formInputs.forEach(input => input.disabled = false);
                this.innerHTML = '<i class="fas fa-times"></i> Annuler';
                this.classList.replace('btn-secondary', 'btn-danger');
                saveButton.style.display = 'block';
                this.setAttribute('data-editing', 'true');
            } else {
                // Annuler la modification
                formInputs.forEach(input => input.disabled = true);
                this.innerHTML = '<i class="fas fa-edit"></i> Modifier';
                this.classList.replace('btn-danger', 'btn-secondary');
                saveButton.style.display = 'none';
                this.setAttribute('data-editing', 'false');
                loadUserData(); // Recharger les données originales
            }
        });
    </script>
</body>
</html>