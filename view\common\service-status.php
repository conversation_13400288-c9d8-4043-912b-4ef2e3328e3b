<?php
/**
 * Page d'état des services - Interface cohérente pour les services désactivés
 */



// Récupérer les informations du service depuis les paramètres
$serviceKey = $_GET['service'] ?? 'unknown';
$serviceName = $_GET['name'] ?? 'Service';
$reason = $_GET['reason'] ?? 'temporarily_unavailable';
$userRole = $_SESSION['user_role'] ?? 'user';

// Configuration des messages selon la raison
$statusConfig = [
    'temporarily_unavailable' => [
        'icon' => 'bi-clock-history',
        'color' => 'warning',
        'title' => 'Service Temporairement Indisponible',
        'description' => 'Ce service n\'est pas disponible pour le moment.'
    ],
    'maintenance' => [
        'icon' => 'bi-tools',
        'color' => 'info',
        'title' => 'Maintenance en Cours',
        'description' => 'Ce service est actuellement en maintenance.'
    ],
    'period_closed' => [
        'icon' => 'bi-calendar-x',
        'color' => 'warning',
        'title' => 'Période Fermée',
        'description' => 'La période d\'accès à ce service est actuellement fermée.'
    ],
    'not_activated' => [
        'icon' => 'bi-pause-circle',
        'color' => 'secondary',
        'title' => 'Service Non Activé',
        'description' => 'Ce service n\'a pas encore été activé par l\'administration.'
    ]
];

$config = $statusConfig[$reason] ?? $statusConfig['temporarily_unavailable'];

// Définir le titre de la page
$pageTitle = $serviceName . " - " . $config['title'];
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - UniAdmin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .service-status-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 2rem;
        }

        .status-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .status-header {
            padding: 2rem;
            text-align: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .status-icon {
            font-size: 3.5rem;
            margin-bottom: 1rem;
        }

        .status-body {
            padding: 2rem;
        }

        .info-section {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin: 1.5rem 0;
        }

        .contact-info {
            background-color: #e7f3ff;
            border-left: 3px solid #007bff;
            padding: 0.75rem;
            margin: 1rem 0;
            border-radius: 4px;
        }

        .action-buttons {
            text-align: center;
            margin-top: 2rem;
        }

        .refresh-button {
            background: #007bff;
            border: none;
            border-radius: 6px;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .refresh-button:hover {
            background: #0056b3;
            color: white;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid mt-4">
                <div class="service-status-container">
                    <div class="card status-card">
                        <!-- En-tête du statut -->
                        <div class="status-header">
                            <div class="status-icon text-<?php echo $config['color']; ?>">
                                <i class="<?php echo $config['icon']; ?>"></i>
                            </div>
                            <h2 class="mb-2"><?php echo $config['title']; ?></h2>
                            <h4 class="text-muted mb-0"><?php echo htmlspecialchars($serviceName); ?></h4>
                        </div>

                        <!-- Corps du message -->
                        <div class="status-body">
                            <div class="text-center mb-4">
                                <p class="lead"><?php echo $config['description']; ?></p>
                            </div>

                            <!-- Informations contextuelles -->
                            <div class="info-section">
                                <?php if ($reason === 'period_closed'): ?>
                                    <p class="mb-2"><i class="bi bi-info-circle text-primary"></i> <strong>La période de soumission est actuellement fermée.</strong></p>
                                    <p class="mb-0">Contactez votre chef de département pour plus d'informations.</p>
                                <?php elseif ($reason === 'maintenance'): ?>
                                    <p class="mb-2"><i class="bi bi-tools text-info"></i> <strong>Le service est temporairement en maintenance.</strong></p>
                                    <p class="mb-0">Réessayez dans quelques minutes.</p>
                                <?php elseif ($reason === 'not_activated'): ?>
                                    <p class="mb-2"><i class="bi bi-pause-circle text-secondary"></i> <strong>Le service n'a pas encore été activé.</strong></p>
                                    <p class="mb-0">Contactez votre chef de département si nécessaire.</p>
                                <?php else: ?>
                                    <p class="mb-2"><i class="bi bi-exclamation-circle text-warning"></i> <strong>Le service est temporairement indisponible.</strong></p>
                                    <p class="mb-0">Réessayez dans quelques minutes.</p>
                                <?php endif; ?>
                            </div>

                            <!-- Informations de contact -->
                            <div class="contact-info">
                                <p class="mb-0"><i class="bi bi-telephone text-primary"></i> <strong>Besoin d'aide ?</strong> Contactez votre chef de département.</p>
                            </div>

                            <!-- Boutons d'action -->
                            <div class="action-buttons">
                                <button onclick="location.reload()" class="btn refresh-button me-3">
                                    <i class="bi bi-arrow-clockwise"></i> Actualiser
                                </button>

                                <?php if ($userRole === 'enseignant'): ?>
                                    <a href="../enseignant/dashboard.php" class="btn btn-outline-primary">
                                        <i class="bi bi-house"></i> Tableau de Bord
                                    </a>
                                <?php elseif ($userRole === 'etudiant'): ?>
                                    <a href="../etudiant/dashboard.php" class="btn btn-outline-primary">
                                        <i class="bi bi-house"></i> Tableau de Bord
                                    </a>
                                <?php elseif ($userRole === 'chef de departement'): ?>
                                    <a href="../chef_departement/dashboard.php" class="btn btn-outline-primary">
                                        <i class="bi bi-house"></i> Tableau de Bord
                                    </a>
                                <?php elseif ($userRole === 'coordinateur'): ?>
                                    <a href="../coordinateur/dashboard.php" class="btn btn-outline-primary">
                                        <i class="bi bi-house"></i> Tableau de Bord
                                    </a>
                                <?php else: ?>
                                    <a href="../dashboard.php" class="btn btn-outline-primary">
                                        <i class="bi bi-house"></i> Tableau de Bord
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh toutes les 30 secondes pour vérifier si le service est redevenu disponible
        setInterval(function() {
            // Vérifier discrètement le statut du service
            fetch('<?php echo BASE_URL; ?>/route/serviceManagementRoute.php?action=status&service_key=<?php echo urlencode($serviceKey); ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.is_active) {
                        // Service redevenu actif, rediriger vers la page originale
                        const originalUrl = sessionStorage.getItem('originalServiceUrl');
                        if (originalUrl) {
                            window.location.href = originalUrl;
                        } else {
                            location.reload();
                        }
                    }
                })
                .catch(error => {
                    console.log('Vérification du statut du service:', error);
                });
        }, 30000);

        // Sauvegarder l'URL originale pour redirection automatique
        if (document.referrer && !sessionStorage.getItem('originalServiceUrl')) {
            sessionStorage.setItem('originalServiceUrl', document.referrer);
        }
    </script>
</body>
</html>
