<?php
// Vérifier l'authentification
require_once '../includes/auth_check_chef.php';

// Récupérer les informations du chef de département depuis la session
$userName = $_SESSION['user']['username'] ?? 'Chef de Département';
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$departmentName = $_SESSION['user']['department_name'] ?? 'Non spécifié';

// Construire le nom complet
$fullName = $prenom . ' ' . $nom;
if (trim($fullName) === '') {
    $fullName = $userName;
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chef de Département - Tableau de Bord</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard-style.css">
    <link rel="stylesheet" href="../assets/css/header-fix.css">
</head>
<body>
    <div class="dashboard-container">
        <?php
        include '../includes/sidebar.php';

        // Inclure le modèle des visites et enregistrer la visite du dashboard
        require_once '../../model/visitsModel.php';
        recordVisit('chef de departement', 'dashboard');
        ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>
            <div class="container-fluid p-4">
                <h1 class="page-title">Tableau de Bord - Chef de Département</h1>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="text-muted">Bienvenue, <strong><?php echo htmlspecialchars($fullName); ?></strong>. Vous êtes chef du département <strong><?php echo htmlspecialchars($departmentName); ?></strong>. Voici un aperçu des activités récentes.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <div class="d-inline-block me-2">
                            <span class="badge bg-primary rounded-pill">
                                <?php echo date('d M Y'); ?>
                            </span>
                        </div>
                        <div class="d-inline-block me-2">
                            <span class="badge bg-info rounded-pill">
                                <i class="fas fa-clock me-1"></i> <span id="current-time"></span>
                            </span>
                        </div>
                        <div class="d-inline-block">
                            <span class="badge bg-secondary rounded-pill">
                                <i class="fas fa-user me-1"></i> Chef de Département
                            </span>
                        </div>
                    </div>
                </div>

                <div class="row g-4">
                    <!-- Carte des Événements du Département -->
                    <div class="col-md-6 col-lg-4 animate-fade-in delay-1">
                        <a href="events.php" class="text-decoration-none">
                            <div class="card dashboard-card card-events h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="card-icon icon-events">
                                            <i class="fas fa-calendar-alt"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Événements du Département</h5>
                                            <div class="small text-muted">3 derniers événements</div>
                                        </div>
                                    </div>
                                    <ul class="dashboard-list" id="recent-events-list">
                                        <!-- Données statiques pour les événements du département -->
                                        <li><span><i class="fas fa-dot-circle me-2 text-primary"></i>Réunion de département</span><span class="date-badge">15 déc</span></li>
                                        <li><span><i class="fas fa-dot-circle me-2 text-primary"></i>Soutenance de PFE</span><span class="date-badge">20 déc</span></li>
                                        <li><span><i class="fas fa-dot-circle me-2 text-primary"></i>Conseil de département</span><span class="date-badge">25 déc</span></li>
                                    </ul>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte des Enseignants du Département -->
                    <div class="col-md-6 col-lg-4 animate-fade-in delay-2">
                        <a href="enseignants.php" class="text-decoration-none">
                            <div class="card dashboard-card card-teachers h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="card-icon icon-teachers">
                                            <i class="fas fa-chalkboard-teacher"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Enseignants</h5>
                                            <div class="small text-muted">Gestion des enseignants</div>
                                        </div>
                                    </div>
                                    <ul class="dashboard-list">
                                        <li><span><i class="fas fa-user-tie me-2 text-info"></i>Enseignants permanents</span><span class="badge bg-primary rounded-pill">12</span></li>
                                        <li><span><i class="fas fa-user-clock me-2 text-info"></i>Vacataires</span><span class="badge bg-primary rounded-pill">8</span></li>
                                        <li><span><i class="fas fa-user-graduate me-2 text-info"></i>Doctorants</span><span class="badge bg-primary rounded-pill">5</span></li>
                                    </ul>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte des Statistiques du Département -->
                    <div class="col-md-6 col-lg-4 animate-fade-in delay-3">
                        <a href="statistics.php" class="text-decoration-none">
                            <div class="card dashboard-card card-stats h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="card-icon icon-stats">
                                            <i class="fas fa-chart-bar"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Statistiques</h5>
                                            <div class="small text-muted">Aperçu du département</div>
                                        </div>
                                    </div>
                                    <div class="row g-3 mt-2">
                                        <!-- Données statiques pour les statistiques -->
                                        <div class="col-4">
                                            <div class="stat-card stat-students">
                                                <p class="stat-value">120</p>
                                                <p class="stat-label">Étudiants</p>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-card stat-professors">
                                                <p class="stat-value">20</p>
                                                <p class="stat-label">Enseignants</p>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-card stat-courses">
                                                <p class="stat-value">15</p>
                                                <p class="stat-label">Modules</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Emploi du Temps -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-4">
                        <a href="emploi-temps.php" class="text-decoration-none">
                            <div class="card dashboard-card card-schedule square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-schedule mb-3">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <h5 class="card-title">Emploi du Temps</h5>
                                    <div class="small text-muted">Gestion des horaires</div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Modules -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-5">
                        <a href="modules.php" class="text-decoration-none">
                            <div class="card dashboard-card card-modules square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-modules mb-3">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    <h5 class="card-title">Modules</h5>
                                    <div class="small text-muted">Gestion des modules</div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Examens -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-6">
                        <a href="examens.php" class="text-decoration-none">
                            <div class="card dashboard-card card-exams square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-exams mb-3">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    <h5 class="card-title">Examens</h5>
                                    <div class="small text-muted">Planification des examens</div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Réunions -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-7">
                        <a href="reunions.php" class="text-decoration-none">
                            <div class="card dashboard-card card-meetings square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-meetings mb-3">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <h5 class="card-title">Réunions</h5>
                                    <div class="small text-muted">Gestion des réunions</div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Graphique des Performances -->
                    <div class="col-md-12 animate-fade-in delay-8">
                        <div class="card dashboard-card card-performance h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="card-icon icon-performance">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div>
                                        <h5 class="card-title mb-0">Performance du Département</h5>
                                        <div class="small text-muted">Taux de réussite par module</div>
                                    </div>
                                    <div class="ms-auto">
                                        <select id="chart-type-selector" class="form-select form-select-sm">
                                            <option value="bar">Barres</option>
                                            <option value="line">Ligne</option>
                                            <option value="pie">Camembert</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="chart-container" style="position: relative; height: 300px;">
                                    <canvas id="performanceChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Dashboard Scripts -->
    <script src="../assets/js/dashboard-dynamic.js"></script>
    <script src="../assets/js/dashboard-chart.js"></script>
    <script src="../assets/js/dashboard-notifications.js"></script>
    <!-- Notifications JS -->
    <script src="../assets/js/notifications.js"></script>
    <script src="../assets/js/sidebar.js"></script>

    <!-- Script spécifique pour le graphique de performance du département -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Données de démonstration pour le graphique de performance
            const modules = ['Algorithmique', 'Programmation', 'Base de données', 'Réseaux', 'Systèmes'];
            const successRates = [85, 72, 90, 68, 78];

            // Créer le graphique
            const ctx = document.getElementById('performanceChart').getContext('2d');
            const performanceChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: modules,
                    datasets: [{
                        label: 'Taux de réussite (%)',
                        data: successRates,
                        backgroundColor: [
                            'rgba(75, 192, 192, 0.6)',
                            'rgba(54, 162, 235, 0.6)',
                            'rgba(153, 102, 255, 0.6)',
                            'rgba(255, 159, 64, 0.6)',
                            'rgba(255, 99, 132, 0.6)'
                        ],
                        borderColor: [
                            'rgba(75, 192, 192, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(153, 102, 255, 1)',
                            'rgba(255, 159, 64, 1)',
                            'rgba(255, 99, 132, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });

            // Changer le type de graphique
            document.getElementById('chart-type-selector').addEventListener('change', function() {
                performanceChart.destroy();
                const newType = this.value;

                const newChart = new Chart(ctx, {
                    type: newType,
                    data: {
                        labels: modules,
                        datasets: [{
                            label: 'Taux de réussite (%)',
                            data: successRates,
                            backgroundColor: [
                                'rgba(75, 192, 192, 0.6)',
                                'rgba(54, 162, 235, 0.6)',
                                'rgba(153, 102, 255, 0.6)',
                                'rgba(255, 159, 64, 0.6)',
                                'rgba(255, 99, 132, 0.6)'
                            ],
                            borderColor: [
                                'rgba(75, 192, 192, 1)',
                                'rgba(54, 162, 235, 1)',
                                'rgba(153, 102, 255, 1)',
                                'rgba(255, 159, 64, 1)',
                                'rgba(255, 99, 132, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100
                            }
                        }
                    }
                });
            });

            // Afficher l'heure actuelle
            function updateTime() {
                const now = new Date();
                const timeElement = document.getElementById('current-time');
                if (timeElement) {
                    timeElement.textContent = now.toLocaleTimeString();
                }
            }

            // Mettre à jour l'heure chaque seconde
            updateTime();
            setInterval(updateTime, 1000);
        });
    </script>
</body>
</html>