// Fonction globale pour récupérer dynamiquement le chemin de base
function getBasePath(routeFile = 'demandesRoute.php') {
    // Obtenir le chemin de base à partir de l'URL actuelle
    const currentPath = window.location.pathname;
    // Trouver l'index du dossier "view" ou "admin"
    const viewIndex = currentPath.indexOf('/view/');
    const adminIndex = currentPath.indexOf('/admin/');

    // Déterminer le chemin de base en fonction de la structure de l'URL
    let basePath = '';
    if (viewIndex !== -1) {
        // Si nous sommes dans un sous-dossier de "view"
        basePath = currentPath.substring(0, viewIndex);
    } else if (adminIndex !== -1) {
        // Si nous sommes dans un sous-dossier de "admin"
        basePath = currentPath.substring(0, adminIndex);
    }

    return `${basePath}/route/${routeFile}`;
}

document.addEventListener('DOMContentLoaded', function() {
    const basePath = getBasePath();

    // Function to update character count
    function updateCharCount(length) {
        document.getElementById('charCount').textContent = `${length}/500 characters`;
    }

    // Global variables for pagination
    let demandsPerPage = 8;

    function loadDemands(page = 1) {
        console.log('Fetching demands from:', `${basePath}?action=getAll&page=${page}&perPage=${demandsPerPage}`);
        fetch(`${basePath}?action=getAll&page=${page}&perPage=${demandsPerPage}`)
            .then(response => {
                if (!response.ok) {
                    return response.text().then(text => {
                        console.log('Raw server response:', text);
                        throw new Error(`HTTP error! status: ${response.status}`);
                    });
                }
                return response.text().then(text => {
                    console.log('Raw successful response:', text);
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('JSON parse error:', e);
                        throw new Error(`Invalid JSON response: ${e.message}`);
                    }
                });
            })
            .then(result => {
                console.log('Received result:', result); // Debug log
                const demandsContainer = document.getElementById('demandsList');

                // Extract demands and pagination info
                const demands = result.demands;
                const pagination = result.pagination;

                // Update pagination variables
                totalPages = pagination.totalPages;
                currentPage = pagination.currentPage;

                if (!demands || !Array.isArray(demands)) {
                    console.error('Invalid demands data received:', demands);
                    demandsContainer.innerHTML = '<p class="text-center text-danger">Invalid data received from server.</p>';
                    return;
                }

                if (!demands.length) {
                    demandsContainer.innerHTML = '<p class="text-center">No demands found.</p>';
                    return;
                }

                // Update pagination UI
                updatePagination(pagination, demands);

                // Helper function to get emoji class and icon based on demand type and status
                function getDemandTypeIcon(type) {
                    if (type === 'Student') {
                        return '<i class="bi bi-mortarboard-fill"></i>';
                    } else { // Professor or other types
                        return '<i class="bi bi-person-workspace"></i>';
                    }
                }

                function getStatusIcon(status) {
                    if (status === 'Pending') {
                        return '<i class="bi bi-hourglass-split"></i>';
                    } else if (status === 'Accepted') {
                        return '<i class="bi bi-check-circle-fill"></i>';
                    } else {
                        return '<i class="bi bi-x-circle-fill"></i>';
                    }
                }

                demandsContainer.innerHTML = demands.map(demand => {
                    const typeIcon = getDemandTypeIcon(demand.type);
                    const statusIcon = getStatusIcon(demand.status);

                    return `
                    <div class="demand-card" data-id="${demand.id}" data-author-id="${demand.author_id || ''}" data-type="${demand.type}">
                        <div class="demand-header">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="d-flex align-items-center gap-2 mb-2">
                                    <div class="demand-emoji">${typeIcon}</div>
                                    <h3>Demand #${demand.id}</h3>
                                </div>
                                <div class="demand-status-badge ${demand.status.toLowerCase()}">
                                    ${statusIcon} ${demand.status}
                                </div>
                            </div>
                            <div class="d-flex align-items-center gap-2">
                                <span class="badge bg-soft-${demand.type === 'Student' ? 'primary' : 'success'}">${demand.type}</span>
                            </div>
                        </div>

                        <div class="demand-title">
                            <i class="bi bi-chat-quote-fill text-primary"></i>
                            <p>${demand.description || 'No description provided'}</p>
                        </div>

                        <div class="demand-author">
                            <div class="author-avatar" style="background-image: url('${demand.author_avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(demand.author_name || demand.type)}&background=random`}'); background-size: cover; background-position: center;"></div>
                            <span class="author-name">${demand.author_name || (demand.type === 'Student' ? 'Student' : 'Teacher')}</span>
                            <span class="separator">·</span>
                            <span class="date">${demand.created_at ? new Date(demand.created_at).toLocaleString() : 'Date not available'}</span>
                        </div>

                        <div class="demand-actions">
                            <button class="btn btn-light"><i class="bi bi-chat-dots me-1"></i> Message</button>
                            ${demand.status === 'Pending' ? `
                                <button class="btn btn-success-soft"><i class="bi bi-check-circle me-1"></i> Accept</button>
                                <button class="btn btn-danger-soft"><i class="bi bi-x-circle me-1"></i> Reject</button>
                            ` : ``}
                        </div>
                    </div>
                    `;
                }).join('');

                addHoverEffect();
                addActionListeners();

                // Immediately load profile data for all demands
                loadProfilesForDemands();
            })
            .catch(error => {
                console.error('Fetch Error:', error);
                document.getElementById('demandsList').innerHTML =
                    `<p class="text-center text-danger">Error loading demands: ${error.message}</p>`;
            });
    }

    function addHoverEffect() {
        document.querySelectorAll('.demand-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    }

    function addActionListeners() {
        // Add profile hover cards to author avatars
        createProfileHoverCards();

        document.querySelectorAll('.btn-success-soft').forEach(button => {
            button.addEventListener('click', function() {
                const demandId = this.closest('.demand-card').dataset.id;
                // Store the demand ID in the modal
                document.getElementById('acceptModalDemandId').value = demandId;
                // Show the modal
                const acceptModal = new bootstrap.Modal(document.getElementById('acceptModal'));
                acceptModal.show();
            });
        });

        // Add event listener for the confirm accept button
        document.getElementById('confirmAcceptBtn').addEventListener('click', acceptDemand);

        // Function to accept the demand
        async function acceptDemand() {
            const demandId = document.getElementById('acceptModalDemandId').value;
            const acceptButton = document.getElementById('confirmAcceptBtn');
            const spinner = document.getElementById('acceptSpinner');

            // Disable button and show spinner
            acceptButton.disabled = true;
            spinner.classList.remove('d-none');

            try {
                const response = await fetch(`${basePath}?action=updateStatus`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        id: demandId,
                        status: 'Accepted'
                    })
                });
                const data = await response.json();
                if (data.success) {
                    // Hide the accept modal
                    bootstrap.Modal.getInstance(document.getElementById('acceptModal')).hide();

                    // Show success modal
                    document.getElementById('successTitle').textContent = 'Demand Accepted';
                    document.getElementById('successMessage').textContent = 'The demand has been accepted successfully.';
                    const successModal = new bootstrap.Modal(document.getElementById('successModal'));
                    successModal.show();

                    // Reload demands after a short delay
                    setTimeout(() => {
                        loadDemands();
                    }, 500);
                } else {
                    showToast(data.error || 'Failed to accept demand', true);
                }
            } catch (error) {
                console.error('Error:', error);
                showToast('Error accepting demand', true);
            } finally {
                // Re-enable button and hide spinner
                acceptButton.disabled = false;
                spinner.classList.add('d-none');
            }
        }

        document.querySelectorAll('.btn-danger-soft').forEach(button => {
            button.addEventListener('click', function() {
                const demandId = this.closest('.demand-card').dataset.id;
                // Store the demand ID in the modal
                document.getElementById('rejectModalDemandId').value = demandId;
                // Show the modal
                const rejectModal = new bootstrap.Modal(document.getElementById('rejectModal'));
                rejectModal.show();
            });
        });

        // Add event listener for the confirm reject button
        document.getElementById('confirmRejectBtn').addEventListener('click', rejectDemand);

        // Function to reject the demand
        async function rejectDemand() {
            const demandId = document.getElementById('rejectModalDemandId').value;
            const rejectButton = document.getElementById('confirmRejectBtn');
            const spinner = document.getElementById('rejectSpinner');

            // Disable button and show spinner
            rejectButton.disabled = true;
            spinner.classList.remove('d-none');

            try {
                const response = await fetch(`${basePath}?action=updateStatus`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        id: demandId,
                        status: 'Rejected'
                    })
                });
                const data = await response.json();
                if (data.success) {
                    // Hide the reject modal
                    bootstrap.Modal.getInstance(document.getElementById('rejectModal')).hide();

                    // Show success modal with reject message
                    document.getElementById('successTitle').textContent = 'Demand Rejected';
                    document.getElementById('successMessage').textContent = 'The demand has been rejected successfully.';
                    const successModal = new bootstrap.Modal(document.getElementById('successModal'));
                    successModal.show();

                    // Reload demands after a short delay
                    setTimeout(() => {
                        loadDemands();
                    }, 500);
                } else {
                    showToast(data.error || 'Failed to reject demand', true);
                }
            } catch (error) {
                console.error('Error:', error);
                showToast('Error rejecting demand', true);
            } finally {
                // Re-enable button and hide spinner
                rejectButton.disabled = false;
                spinner.classList.add('d-none');
            }
        }

        document.querySelectorAll('.btn-light').forEach(button => {
            button.addEventListener('click', function() {
                const demandId = this.closest('.demand-card').dataset.id;
                // Store the demand ID in the modal
                document.getElementById('messageModalDemandId').value = demandId;
                // Clear any previous message
                const messageText = document.getElementById('messageText');
                messageText.value = '';

                // Reset character counter
                updateCharCount(0);
                // Show the modal
                const messageModal = new bootstrap.Modal(document.getElementById('messageModal'));
                messageModal.show();
            });
        });

        // Add event listener for the send message button in the modal
        document.getElementById('sendMessageBtn').addEventListener('click', sendMessage);

        // Add character counter
        document.getElementById('messageText').addEventListener('input', function() {
            updateCharCount(this.value.length);
        });

        // Add keyboard support (Enter to send)
        document.getElementById('messageText').addEventListener('keydown', function(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault(); // Prevent default to avoid newline
                sendMessage();
            }
        });

        // Function to send the message
        async function sendMessage() {
            const demandId = document.getElementById('messageModalDemandId').value;
            const message = document.getElementById('messageText').value.trim();
            const sendButton = document.getElementById('sendMessageBtn');
            const spinner = document.getElementById('sendingSpinner');

            if (!message) {
                showToast('Please enter a message', true);
                return;
            }

            // Disable button and show spinner
            sendButton.disabled = true;
            spinner.classList.remove('d-none');

            try {
                const response = await fetch(`${basePath}?action=sendMessage`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        id: demandId,
                        message: message
                    })
                });
                const data = await response.json();
                if (data.success) {
                    // Hide the modal
                    bootstrap.Modal.getInstance(document.getElementById('messageModal')).hide();
                    showToast('Message sent successfully');
                } else {
                    showToast(data.error || 'Failed to send message', true);
                }
            } catch (error) {
                console.error('Error:', error);
                showToast('Error sending message', true);
            } finally {
                // Re-enable button and hide spinner
                sendButton.disabled = false;
                spinner.classList.add('d-none');
            }
        }
    }

    function showToast(message, isError = false) {
        const toast = document.createElement('div');
        toast.className = `toast ${isError ? 'error' : 'success'}`;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.classList.add('show');
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }, 100);
    }

    // Function to create profile hover cards for author avatars
    function createProfileHoverCards() {
        // First, remove any existing hover cards
        document.querySelectorAll('.profile-hover-card').forEach(card => {
            card.remove();
        });

        // Now create fresh hover cards for each avatar
        document.querySelectorAll('.author-avatar').forEach(avatar => {
            const demandCard = avatar.closest('.demand-card');
            const demandId = demandCard.dataset.id;
            const authorId = demandCard.dataset.authorId;
            const authorType = demandCard.dataset.type;

            // Create a unique hover card with a unique ID
            const hoverCard = document.createElement('div');
            hoverCard.id = `profile-hover-card-${demandId}-${authorId}`;
            hoverCard.className = 'profile-hover-card';

            // Add data attributes for debugging
            hoverCard.dataset.demandId = demandId;
            hoverCard.dataset.authorId = authorId;
            hoverCard.dataset.authorType = authorType;

            // Initial loading state
            hoverCard.innerHTML = `
                <div class="profile-loading">
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border text-primary" role="status" style="width: 1.5rem; height: 1.5rem;">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            `;

            // Add hover card to avatar
            avatar.appendChild(hoverCard);

            // Add event listener to load profile data when hovering
            avatar.addEventListener('mouseenter', function() {
                // Get the hover card by its unique ID to ensure we're updating the right one
                const currentHoverCard = document.getElementById(`profile-hover-card-${demandId}-${authorId}`);
                if (!currentHoverCard) return;

                // Show loading state
                currentHoverCard.innerHTML = `
                    <div class="profile-loading">
                        <div class="d-flex justify-content-center">
                            <div class="spinner-border text-primary" role="status" style="width: 1.5rem; height: 1.5rem;">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                `;

                // Get fresh data from the parent card
                const card = this.closest('.demand-card');
                const type = card.dataset.type;
                const id = card.dataset.authorId;
                const name = card.querySelector('.author-name').textContent.trim();

                console.log(`Fetching profile for demand #${demandId}, author ID: ${id}, type: ${type}`);

                // If we have an author_id, use it, otherwise fall back to name
                if (id) {
                    fetchProfileDataById(type, id, currentHoverCard, this);
                } else {
                    fetchProfileData(type, name, currentHoverCard, this);
                }
            });
        });
    }

    // Function to fetch profile data by ID
    async function fetchProfileDataById(type, authorId, hoverCard, avatar) {
        try {
            // Get the demand card to extract more information
            const demandCard = avatar.closest('.demand-card');
            const authorName = demandCard.querySelector('.author-name').textContent.trim();

            // Use our new API endpoint to fetch real profile data with cache busting
            const profileApiPath = getBasePath('profileRoute.php');
            const timestamp = new Date().getTime(); // Add timestamp to prevent caching
            const response = await fetch(`${profileApiPath}?action=getById&id=${authorId}&type=${type}&_=${timestamp}`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success && result.profile) {
                // Use the real profile data from the database
                const profileData = result.profile;
                console.log(`Profile data for ID ${authorId} (${type}):`, profileData);
                updateProfileHoverCard(hoverCard, profileData, type, avatar);

                // Update the author name in the demand card with the real name
                const demandCard = avatar.closest('.demand-card');
                const authorNameElement = demandCard.querySelector('.author-name');
                if (authorNameElement) {
                    authorNameElement.textContent = `${profileData.prenom} ${profileData.nom}`;
                }

                // Update the avatar if profile_picture is available
                if (profileData.profile_picture) {
                    avatar.style.backgroundImage = `url('${profileData.profile_picture}')`;
                }

                return;
            }

            // If we couldn't get the profile by ID, try by name as fallback
            await fetchProfileData(type, authorName, hoverCard, avatar);

        } catch (error) {
            console.error('Error fetching profile data by ID:', error);
            // Try by name as fallback
            try {
                // Get the name again to ensure it's in scope
                const demandCard = avatar.closest('.demand-card');
                const authorName = demandCard.querySelector('.author-name').textContent.trim();
                await fetchProfileData(type, authorName, hoverCard, avatar);
            } catch (fallbackError) {
                console.error('Fallback error:', fallbackError);
                hoverCard.innerHTML = `
                    <div class="text-center text-danger p-3">
                        <i class="bi bi-exclamation-circle"></i>
                        <p class="mb-0 mt-2">Error loading profile</p>
                    </div>
                `;
            }
        }
    }

    // Function to fetch profile data based on type and name (fallback method)
    async function fetchProfileData(type, name, hoverCard, avatar) {
        try {
            // Use our API endpoint to fetch profile data by name with cache busting
            const profileApiPath = getBasePath('profileRoute.php');
            const encodedName = encodeURIComponent(name);
            const timestamp = new Date().getTime(); // Add timestamp to prevent caching
            const response = await fetch(`${profileApiPath}?action=getByName&name=${encodedName}&type=${type}&_=${timestamp}`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success && result.profile) {
                // Use the real profile data from the database
                const profileData = result.profile;
                console.log(`Profile data for name ${name} (${type}):`, profileData);
                updateProfileHoverCard(hoverCard, profileData, type, avatar);

                // Update the author name in the demand card with the real name
                const demandCard = avatar.closest('.demand-card');
                const authorNameElement = demandCard.querySelector('.author-name');
                if (authorNameElement) {
                    authorNameElement.textContent = `${profileData.prenom} ${profileData.nom}`;
                }

                // Update the avatar if profile_picture is available
                if (profileData.profile_picture) {
                    avatar.style.backgroundImage = `url('${profileData.profile_picture}')`;
                }

                return;
            }

            // If we couldn't find the profile, use default fallback data
            const firstName = name.split(' ')[0] || '';
            const lastName = name.split(' ')[1] || '';
            let profileData;

            if (type.toLowerCase() === 'student') {
                // Default student profile
                profileData = {
                    id: 99,
                    CNE: 'CNE' + Math.floor(Math.random() * 10000),
                    nom: lastName || 'Student',
                    prenom: firstName || '',
                    email: `${firstName.toLowerCase()}${lastName ? '.' + lastName.toLowerCase() : ''}@edu.uae.ac.ma`,
                    tele: '06' + Math.floor(10000000 + Math.random() * 90000000),
                    sexe: Math.random() > 0.5 ? 'féminin' : 'masculin',
                    pays: 'Maroc',
                    ville: 'TAZA',
                    date_naissance: '2000-01-01'
                };
            } else {
                // Default teacher profile
                const roles = ['chef de departement', 'coordinateur de filiere', 'Normal', 'vacataire'];
                const role = roles[Math.floor(Math.random() * roles.length)];

                profileData = {
                    id: 199,
                    CNI: 'CNI' + Math.floor(Math.random() * 10000),
                    nom: lastName || 'Teacher',
                    prenom: firstName || '',
                    email: `${firstName.toLowerCase()}${lastName ? '.' + lastName.toLowerCase() : ''}@uae.ac.ma`,
                    tele: '06' + Math.floor(10000000 + Math.random() * 90000000),
                    sexe: 'masculin',
                    pays: 'Maroc',
                    ville: 'Fès',
                    role: role,
                    date_naissance: '1980-01-01'
                };
            }

            // Update hover card with profile data
            updateProfileHoverCard(hoverCard, profileData, type, avatar);

        } catch (error) {
            console.error('Error fetching profile data:', error);
            hoverCard.innerHTML = `
                <div class="text-center text-danger p-3">
                    <i class="bi bi-exclamation-circle"></i>
                    <p class="mb-0 mt-2">Error loading profile</p>
                </div>
            `;
        }
    }

    // Function to update hover card with profile data
    function updateProfileHoverCard(hoverCard, profile, type, avatar) {
        // Debug log to see which hover card we're updating
        console.log(`Updating hover card for demand ID ${hoverCard.dataset.demandId}, author ID ${hoverCard.dataset.authorId}:`, profile);
        // Check if profile has a profile_picture field
        let avatarUrl;

        if (profile.profile_picture) {
            // Use the profile picture from the database
            avatarUrl = profile.profile_picture;
            console.log(`Using profile picture from database: ${avatarUrl}`);
        } else {
            // Get avatar URL from the style or use a fallback
            avatarUrl = avatar.style.backgroundImage.replace(/url\(['"](.*?)['"]\)/, '$1');

            // If the URL is invalid or empty, use a generated avatar
            if (!avatarUrl || avatarUrl === 'null' || avatarUrl.includes('default.png')) {
                // Make sure we have a name to use for the avatar
                const firstName = profile.prenom || '';
                const lastName = profile.nom || '';
                const name = firstName + ' ' + lastName;
                avatarUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=random`;
                console.log(`Using generated avatar: ${avatarUrl}`);
            }
        }

        // Get role title based on role value for teachers
        let roleTitle = 'Étudiant';
        let roleIcon = '<i class="bi bi-mortarboard-fill"></i>';

        if (type.toLowerCase() !== 'student') {
            roleIcon = '<i class="bi bi-person-workspace"></i>';

            switch (profile.role) {
                case 'chef de departement':
                    roleTitle = 'Chef de Département';
                    break;
                case 'coordinateur de filiere':
                    roleTitle = 'Coordinateur de Filière';
                    break;
                case 'vacataire':
                    roleTitle = 'Enseignant Vacataire';
                    break;
                default:
                    roleTitle = 'Enseignant';
            }
        }

        // Ensure all profile properties exist to avoid errors
        const safeProfile = {
            prenom: profile.prenom || 'Unknown',
            nom: profile.nom || 'User',
            email: profile.email || 'No email available',
            tele: profile.tele || 'No phone available',
            ville: profile.ville || 'Unknown',
            pays: profile.pays || 'Unknown',
            sexe: profile.sexe || 'Unknown',
            CNE: profile.CNE || 'N/A',
            CNI: profile.CNI || 'N/A',
            profile_picture: profile.profile_picture || null
        };

        hoverCard.innerHTML = `
            <div class="profile-header">
                <div class="profile-avatar" style="background-image: url('${avatarUrl}'); background-size: cover; background-position: center;"></div>
                <div class="profile-info">
                    <h4 class="profile-name">${safeProfile.prenom} ${safeProfile.nom}</h4>
                    <p class="profile-role">
                        ${roleIcon} ${roleTitle}
                    </p>
                </div>
            </div>
            <div class="profile-details">
                <div class="profile-detail">
                    <i class="bi bi-envelope"></i>
                    <span class="profile-detail-value" title="${safeProfile.email}">${safeProfile.email}</span>
                </div>
                <div class="profile-detail">
                    <i class="bi bi-telephone"></i>
                    <span class="profile-detail-value">${safeProfile.tele}</span>
                </div>
                <div class="profile-detail">
                    <i class="bi bi-geo-alt"></i>
                    <span class="profile-detail-value">${safeProfile.ville}, ${safeProfile.pays}</span>
                </div>
                <div class="profile-detail">
                    <i class="bi bi-gender-${safeProfile.sexe === 'masculin' ? 'male' : 'female'}"></i>
                    <span class="profile-detail-value">${safeProfile.sexe}</span>
                </div>
                ${type.toLowerCase() === 'student' ?
                    `<div class="profile-detail">
                        <i class="bi bi-card-heading"></i>
                        <span class="profile-detail-label">CNE:</span>
                        <span class="profile-detail-value">${safeProfile.CNE}</span>
                    </div>` :
                    `<div class="profile-detail">
                        <i class="bi bi-card-heading"></i>
                        <span class="profile-detail-label">CNI:</span>
                        <span class="profile-detail-value">${safeProfile.CNI}</span>
                    </div>`
                }
            </div>
            <a href="#" class="view-profile-btn">View Full Profile</a>
        `;
    }

    // Function to update pagination UI
    function updatePagination(pagination, demands) {
        const paginationContainer = document.getElementById('demandsPagination');
        if (!paginationContainer) return;

        const { currentPage, totalPages, total } = pagination;

        // Create pagination HTML
        let paginationHTML = `
            <div class="pagination-info mb-2">
                <span>Showing ${demands.length} of ${total} demands</span>
            </div>
            <ul class="pagination justify-content-center">
        `;

        // Previous button
        paginationHTML += `
            <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${currentPage - 1}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
        `;

        // Page numbers
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, startPage + 4);

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `;
        }

        // Next button
        paginationHTML += `
            <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${currentPage + 1}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
        `;

        paginationHTML += `</ul>`;

        // Update the pagination container
        paginationContainer.innerHTML = paginationHTML;

        // Add event listeners to pagination links
        paginationContainer.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const page = parseInt(this.dataset.page);
                if (page >= 1 && page <= totalPages) {
                    loadDemands(page);
                }
            });
        });
    }

    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });

    // Function to load profile data for all demands
    function loadProfilesForDemands() {
        console.log('Loading profiles for all demands...');
        document.querySelectorAll('.demand-card').forEach(card => {
            const authorId = card.dataset.authorId;
            const type = card.dataset.type;
            const avatar = card.querySelector('.author-avatar');

            console.log(`Processing demand card: ID=${card.dataset.id}, authorId=${authorId}, type=${type}`);

            if (authorId && avatar) {
                // Fetch profile data for this demand
                const profileApiPath = getBasePath('profileRoute.php');
                const timestamp = new Date().getTime(); // Add timestamp to prevent caching

                console.log(`Fetching profile data for authorId=${authorId}, type=${type}`);

                fetch(`${profileApiPath}?action=getById&id=${authorId}&type=${type}&_=${timestamp}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(result => {
                        console.log(`Profile data received for authorId=${authorId}:`, result);

                        if (result.success && result.profile) {
                            const profileData = result.profile;

                            // Update the author name in the demand card
                            const authorNameElement = card.querySelector('.author-name');
                            if (authorNameElement) {
                                authorNameElement.textContent = `${profileData.prenom} ${profileData.nom}`;
                            }

                            // Update the avatar if profile_picture is available
                            if (profileData.profile_picture) {
                                console.log(`Setting profile picture for authorId=${authorId}:`, profileData.profile_picture);
                                avatar.style.backgroundImage = `url('${profileData.profile_picture}')`;

                                // Force a repaint to ensure the image is displayed
                                avatar.style.display = 'none';
                                setTimeout(() => {
                                    avatar.style.display = '';
                                }, 10);
                            } else {
                                console.log(`No profile picture available for authorId=${authorId}`);
                                // Set a default avatar if no profile picture is available
                                const name = `${profileData.prenom} ${profileData.nom}`;
                                avatar.style.backgroundImage = `url('https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=random')`;
                            }
                        }
                    })
                    .catch(error => {
                        console.error(`Error fetching profile data for authorId=${authorId}:`, error);
                    });
            } else {
                console.log(`Missing authorId or avatar element for demand ID=${card.dataset.id}`);
            }
        });
    }

    loadDemands();
});