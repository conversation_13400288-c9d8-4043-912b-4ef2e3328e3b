<?php
/**
 * <PERSON><PERSON> job script to check and deactivate expired services
 * This script should be run every minute via cron
 * 
 * Add to crontab:
 * * * * * * /usr/bin/php /path/to/your/project/cron/check_expired_services.php >> /var/log/service_cron.log 2>&1
 */

// Set the working directory to the project root
$projectRoot = dirname(__DIR__);
chdir($projectRoot);

// Include required files
require_once $projectRoot . '/model/serviceManagementModel.php';

// Log function
function logMessage($message) {
    $timestamp = date('Y-m-d H:i:s');
    echo "[$timestamp] $message\n";
}

// Main execution
try {
    logMessage("Starting expired services check...");
    
    // Ensure the service management table exists
    if (!ensureServiceManagementTable()) {
        logMessage("ERROR: Failed to ensure service management table exists");
        exit(1);
    }
    
    // Check for expired services
    $expiredServices = checkExpiredServices();
    
    if ($expiredServices === false) {
        logMessage("ERROR: Failed to check expired services");
        exit(1);
    }
    
    if (empty($expiredServices)) {
        logMessage("No expired services found");
    } else {
        logMessage("Found " . count($expiredServices) . " expired service(s): " . implode(', ', $expiredServices));
        
        // Send notifications for expired services
        foreach ($expiredServices as $serviceKey) {
            sendServiceExpiredNotification($serviceKey);
        }
    }
    
    logMessage("Expired services check completed successfully");
    
} catch (Exception $e) {
    logMessage("ERROR: " . $e->getMessage());
    exit(1);
}

/**
 * Send notification when a service expires
 */
function sendServiceExpiredNotification($serviceKey) {
    try {
        $service = getServiceByKey($serviceKey);
        
        if (!$service) {
            logMessage("WARNING: Could not find service details for $serviceKey");
            return;
        }
        
        logMessage("Sending expiration notification for service: " . $service['display_name']);
        
        // Here you can add email notifications, database notifications, etc.
        // For now, we'll just log the notification
        
        // Example: Send email to administrators
        // sendEmailNotification($service);
        
        // Example: Create database notification
        // createDatabaseNotification($service);
        
        logMessage("Expiration notification sent for service: " . $service['display_name']);
        
    } catch (Exception $e) {
        logMessage("ERROR sending notification for $serviceKey: " . $e->getMessage());
    }
}

/**
 * Send email notification (example implementation)
 */
function sendEmailNotification($service) {
    // This is a placeholder for email notification functionality
    // You would implement actual email sending here using PHPMailer or similar
    
    $subject = "Service Expired: " . $service['display_name'];
    $message = "The service '" . $service['display_name'] . "' has expired and has been automatically deactivated.";
    
    // Example email sending code:
    // mail('<EMAIL>', $subject, $message);
    
    logMessage("Email notification would be sent: $subject");
}

/**
 * Create database notification (example implementation)
 */
function createDatabaseNotification($service) {
    // This is a placeholder for database notification functionality
    // You would implement actual database notification creation here
    
    logMessage("Database notification would be created for: " . $service['display_name']);
}

?>
