-- Script pour créer la table ue_preferences si elle n'existe pas déjà

CREATE TABLE IF NOT EXISTS `ue_preferences` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_enseignant` int(11) NOT NULL,
  `id_ue` int(11) NOT NULL,
  `preference_level` int(11) DEFAULT NULL,
  `reason` text COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_enseignant_ue` (`id_enseignant`, `id_ue`),
  KEY `fk_ue_preferences_enseignant` (`id_enseignant`),
  KEY `fk_ue_preferences_ue` (`id_ue`),
  CONSTRAINT `fk_ue_preferences_enseignant` FOREIGN KEY (`id_enseignant`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_ue_preferences_ue` FOREIGN KEY (`id_ue`) REFERENCES `uniteenseignement` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
