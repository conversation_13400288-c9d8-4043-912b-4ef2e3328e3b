<?php
// Utiliser un chemin absolu pour éviter les problèmes d'inclusion
$dbPath = __DIR__ . "/../config/db.php";
if (file_exists($dbPath)) {
    require_once $dbPath;
} else {
    // Essayer un autre chemin comme solution de secours
    $altDbPath = __DIR__ . "/../db.php";
    if (file_exists($altDbPath)) {
        require_once $altDbPath;
    } else {
        die("Impossible de trouver le fichier db.php. Chemins essayés: " . $dbPath . " et " . $altDbPath);
    }
}

// Get all filieres
function getAllFilieres() {
    $conn = getConnection();
    $sql = "SELECT * FROM filiere";
    $result = mysqli_query($conn, $sql);

    $filieres = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $filieres[] = $row;
    }

    mysqli_close($conn);
    return $filieres;
}

// Get filiere by ID
function getFiliereById($id) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    $id = mysqli_real_escape_string($conn, $id);
    $sql = "SELECT * FROM filiere WHERE id_filiere = '$id'";
    $result = mysqli_query($conn, $sql);

    if ($result && mysqli_num_rows($result) > 0) {
        $filiere = mysqli_fetch_assoc($result);
        mysqli_free_result($result);
        mysqli_close($conn);
        return $filiere;
    } else {
        mysqli_close($conn);
        return ["error" => "No filiere found with this ID"];
    }
}

// Insert filiere
function insertFiliere($nom_filiere, $id_chef_filiere, $id_dep, $niveau) {
    $conn = getConnection();

    $nom_filiere = mysqli_real_escape_string($conn, $nom_filiere);
    $id_chef_filiere = mysqli_real_escape_string($conn, $id_chef_filiere);
    $id_dep = mysqli_real_escape_string($conn, $id_dep);
    $niveau = mysqli_real_escape_string($conn, $niveau);

    $sql = "INSERT INTO filiere (nom_filiere, id_chef_filiere, id_dep, niveau)
            VALUES ('$nom_filiere', '$id_chef_filiere', '$id_dep', '$niveau')";

    $result = mysqli_query($conn, $sql);
    mysqli_close($conn);
    return $result;
}

// Update filiere
function updateFiliere($id_filiere, $nom_filiere, $id_chef_filiere, $id_dep, $niveau) {
    $conn = getConnection();

    $id_filiere = mysqli_real_escape_string($conn, $id_filiere);
    $nom_filiere = mysqli_real_escape_string($conn, $nom_filiere);
    $id_chef_filiere = mysqli_real_escape_string($conn, $id_chef_filiere);
    $id_dep = mysqli_real_escape_string($conn, $id_dep);
    $niveau = mysqli_real_escape_string($conn, $niveau);

    $sql = "UPDATE filiere
            SET nom_filiere = '$nom_filiere',
                id_chef_filiere = '$id_chef_filiere',
                id_dep = '$id_dep',
                niveau = '$niveau'
            WHERE id_filiere = '$id_filiere'";

    $result = mysqli_query($conn, $sql);
    mysqli_close($conn);
    return $result;
}

// Delete filiere
function deleteFiliere($id) {
    $conn = getConnection();
    $id = mysqli_real_escape_string($conn, $id);

    $sql = "DELETE FROM filiere WHERE id_filiere = '$id'";
    $result = mysqli_query($conn, $sql);

    mysqli_close($conn);
    return $result;
}
?>