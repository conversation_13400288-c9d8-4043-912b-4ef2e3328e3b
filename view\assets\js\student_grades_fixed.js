/**
 * Student Grades JavaScript
 *
 * This file contains the JavaScript code for the student grades page.
 * It handles loading, displaying, and editing student grades.
 */
document.addEventListener('DOMContentLoaded', function() {
    // Global variables
    let allStudents = [];
    let filteredStudents = [];
    let currentPage = 1;
    const rowsPerPage = 10;
    let totalStudents = 0;

    // Get module ID and other parameters from URL
    const urlParams = new URLSearchParams(window.location.search);
    const moduleId = urlParams.get('module');
    const niveauId = urlParams.get('niveau');
    const filiereId = urlParams.get('filiere');
    const semestre = urlParams.get('semestre');
    const session = urlParams.get('session');
    const moduleName = urlParams.get('module_name') || document.getElementById('module-value')?.textContent || 'Module';
    const filiereName = urlParams.get('filiere_name') || document.getElementById('filiere-value')?.textContent || 'Filière';

    // Store teacher ID if available
    window.teacherId = document.getElementById('teacher-id')?.value;

    console.log('URL Parameters:', {
        moduleId, niveauId, filiereId, semestre, session, moduleName, filiereName
    });

    // Initialize pagination
    function updatePagination() {
        const totalPages = Math.ceil(filteredStudents.length / rowsPerPage);
        const paginationPages = document.getElementById('pagination-pages');

        if (!paginationPages) return;

        paginationPages.innerHTML = '';

        // Add page numbers
        for (let i = 1; i <= totalPages; i++) {
            const pageNumber = document.createElement('span');
            pageNumber.className = 'page-number' + (i === currentPage ? ' active' : '');
            pageNumber.textContent = i;
            pageNumber.addEventListener('click', function() {
                currentPage = i;
                displayStudents(filteredStudents, currentPage);
                updatePagination();
            });
            paginationPages.appendChild(pageNumber);
        }

        // Update prev/next buttons
        const prevBtn = document.getElementById('prev-page');
        const nextBtn = document.getElementById('next-page');

        if (prevBtn) prevBtn.disabled = currentPage === 1;
        if (nextBtn) nextBtn.disabled = currentPage === totalPages || totalPages === 0;
    }

    // Add event listeners for pagination buttons
    const prevBtn = document.getElementById('prev-page');
    const nextBtn = document.getElementById('next-page');

    if (prevBtn) {
        prevBtn.addEventListener('click', function() {
            if (currentPage > 1) {
                currentPage--;
                displayStudents(filteredStudents, currentPage);
                updatePagination();
            }
        });
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            const totalPages = Math.ceil(filteredStudents.length / rowsPerPage);
            if (currentPage < totalPages) {
                currentPage++;
                displayStudents(filteredStudents, currentPage);
                updatePagination();
            }
        });
    }

    // Function to filter students by search term
    function filterStudents(searchTerm) {
        if (!searchTerm) {
            filteredStudents = [...allStudents];
        } else {
            searchTerm = searchTerm.toLowerCase();
            filteredStudents = allStudents.filter(student => {
                return (
                    (student.nom && student.nom.toLowerCase().includes(searchTerm)) ||
                    (student.prenom && student.prenom.toLowerCase().includes(searchTerm)) ||
                    (student.CNE && student.CNE.toLowerCase().includes(searchTerm)) ||
                    (student.cne && student.cne.toLowerCase().includes(searchTerm))
                );
            });
        }

        currentPage = 1;
        displayStudents(filteredStudents, currentPage);
        updatePagination();

        // Update total students count
        document.getElementById('total-students').textContent = `Total: ${filteredStudents.length} étudiants`;
    }

    // Load student grades
    loadStudentGrades();

    // Search functionality
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterStudents(this.value);
        });
    }

    // PDF Download functionality
    const downloadBtn = document.getElementById('download-pdf');
    if (downloadBtn) {
        downloadBtn.addEventListener('click', function() {
            generatePDF(true);
        });
    }

    // Send to coordinator functionality
    const sendToCoordinatorBtn = document.getElementById('send-to-coordinator');
    if (sendToCoordinatorBtn) {
        sendToCoordinatorBtn.addEventListener('click', sendPDFToCoordinator);
    }

    // Save All Grades functionality
    const saveAllBtn = document.getElementById('save-all-grades');
    if (saveAllBtn) {
        saveAllBtn.addEventListener('click', saveAllGrades);
    }

    // Function to send PDF to coordinator
    function sendPDFToCoordinator() {
        // Show loading state
        showNotification('Préparation de l\'envoi au coordinateur...', 'info');

        try {
            // Vérifier si les éléments nécessaires existent
            const compactHeader = document.querySelector('.compact-header');
            const gradesContainer = document.querySelector('.grades-container');

            if (!compactHeader || !gradesContainer) {
                console.error('Missing required elements for PDF generation:', {
                    compactHeader: !!compactHeader,
                    gradesContainer: !!gradesContainer
                });
                showNotification('Erreur: Éléments manquants pour la génération du PDF', 'error');
                return;
            }

            // Vérifier si les éléments de filtre nécessaires existent
            const niveauElement = document.getElementById('niveau-value');
            const semestreElement = document.getElementById('semestre-value');
            const sessionElement = document.getElementById('session-value');

            if (!niveauElement || !semestreElement || !sessionElement) {
                console.error('Missing filter elements for PDF generation:', {
                    niveauElement: !!niveauElement,
                    semestreElement: !!semestreElement,
                    sessionElement: !!sessionElement
                });
                showNotification('Erreur: Informations de filtrage manquantes', 'error');
                return;
            }

            // Generate PDF as blob
            generatePDF(false).then(pdfBlob => {
                // Convert blob to base64
                const reader = new FileReader();
                reader.readAsDataURL(pdfBlob);
                reader.onloadend = function() {
                    const base64data = reader.result;

                    // Get teacher name from the teacher card
                    const teacherCard = document.querySelector('.teacher-card');
                    const teacherName = teacherCard ? teacherCard.querySelector('.filter-value').textContent : 'Enseignant';

                    // Get niveau name from the niveau card
                    const niveauValue = niveauElement.textContent;

                    // Get semestre name from the semestre card
                    const semestreValue = semestreElement.textContent;

                    // Get session value from the session card
                    const sessionValue = sessionElement.textContent;

                    // Récupérer l'ID de l'enseignant depuis la variable globale si disponible
                    const teacherId = window.teacherId || null;

                    // Vérifier que toutes les données nécessaires sont disponibles
                    if (!moduleId || !filiereId || !niveauId || !semestre) {
                        console.error('Missing required data for sending PDF:', {
                            moduleId, filiereId, niveauId, semestre, sessionValue
                        });
                        showNotification('Erreur: Données manquantes pour l\'envoi au coordinateur', 'error');
                        return;
                    }

                    // Vérifier que les noms sont disponibles
                    if (!moduleName || !filiereName) {
                        console.error('Missing name data for sending PDF:', {
                            moduleName, filiereName
                        });
                        // Utiliser des valeurs par défaut si nécessaire
                        moduleName = moduleName || 'Module';
                        filiereName = filiereName || 'Filière';
                    }

                    // Prepare data to send to server
                    const data = {
                        pdf_content: base64data,
                        module_id: moduleId,
                        filiere_id: filiereId,
                        niveau_id: niveauId,
                        semestre_id: semestre,
                        session: sessionValue,
                        module_name: moduleName,
                        filiere_name: filiereName,
                        niveau_name: niveauValue,
                        semestre_name: semestreValue,
                        teacher_name: teacherName,
                        teacher_id: teacherId
                    };

                    console.log('Sending PDF to coordinator with data:', {
                        module_id: moduleId,
                        filiere_id: filiereId,
                        niveau_id: niveauId,
                        semestre_id: semestre,
                        session: sessionValue
                    });

                    // Send data to server
                    fetch('../../route/noteRoute.php?action=send_to_coordinator', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data)
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            showNotification('Notes envoyées au coordinateur avec succès', 'success');
                        } else {
                            showNotification('Erreur lors de l\'envoi des notes: ' + (data.error || 'Erreur inconnue'), 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error sending PDF to coordinator:', error);
                        showNotification('Erreur lors de l\'envoi des notes au coordinateur', 'error');
                    });
                };
            }).catch(error => {
                console.error('Error generating PDF for coordinator:', error);
                showNotification('Erreur lors de la génération du PDF pour le coordinateur', 'error');
            });
        } catch (error) {
            console.error('Error in sendPDFToCoordinator:', error);
            showNotification('Erreur lors de la préparation de l\'envoi au coordinateur', 'error');
        }
    }

    // Function to show notification
    function showNotification(message, type = 'info') {
        // Check if notification container exists
        let notificationContainer = document.getElementById('notification-container');

        // Create container if it doesn't exist
        if (!notificationContainer) {
            notificationContainer = document.createElement('div');
            notificationContainer.id = 'notification-container';
            document.body.appendChild(notificationContainer);
        }

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        // Set icon based on type
        let icon = '';
        switch (type) {
            case 'success':
                icon = '<i class="bi bi-check-circle"></i>';
                break;
            case 'error':
                icon = '<i class="bi bi-exclamation-circle"></i>';
                break;
            default:
                icon = '<i class="bi bi-info-circle"></i>';
        }

        // Set notification content
        notification.innerHTML = `
            <div class="notification-icon">${icon}</div>
            <div class="notification-message">${message}</div>
            <div class="notification-close"><i class="bi bi-x"></i></div>
        `;

        // Add notification to container
        notificationContainer.appendChild(notification);

        // Add close event
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', function() {
            notification.style.animation = 'slideOut 0.25s ease-in forwards';
            setTimeout(() => {
                notification.remove();
            }, 250);
        });

        // Auto-remove after 4 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOut 0.25s ease-in forwards';
                setTimeout(() => {
                    notification.remove();
                }, 250);
            }
        }, 4000);
    }

    // Function to generate PDF
    function generatePDF(saveToFile = true) {
        // Show loading state
        showNotification('Génération du PDF en cours...', 'info');

        try {
            // Get the elements to include in the PDF
            const compactHeader = document.querySelector('.compact-header');
            const gradesContainer = document.querySelector('.grades-container');

            // Check if all required elements exist
            if (!compactHeader || !gradesContainer) {
                console.error('Missing required elements for PDF generation:', {
                    compactHeader: !!compactHeader,
                    gradesContainer: !!gradesContainer
                });
                showNotification('Erreur: Éléments manquants pour la génération du PDF', 'error');
                return Promise.reject(new Error('Missing required elements for PDF generation'));
            }

            // Create a clone of the elements to avoid modifying the original
            const pdfContent = document.createElement('div');
            pdfContent.appendChild(compactHeader.cloneNode(true));

            // Create a clone of the grades container without the search and pagination
            const gradesClone = gradesContainer.cloneNode(true);

            // Remove search box, buttons, and pagination from the clone
            const searchBox = gradesClone.querySelector('.search-box');
            if (searchBox) searchBox.remove();

            // Remove the download PDF button
            const downloadPdfBtn = gradesClone.querySelector('#download-pdf');
            if (downloadPdfBtn) downloadPdfBtn.remove();

            // Remove the send to coordinator button
            const sendToCoordinatorBtn = gradesClone.querySelector('#send-to-coordinator');
            if (sendToCoordinatorBtn) sendToCoordinatorBtn.remove();

            // Remove all action buttons from the grades-actions container
            const gradesActions = gradesClone.querySelector('.grades-actions');
            if (gradesActions) gradesActions.remove();

            // Remove pagination container
            const paginationContainer = gradesClone.querySelector('.pagination-container');
            if (paginationContainer) paginationContainer.remove();

            // Remove top performances section from the clone
            const topPerformancesSection = gradesClone.querySelector('.top-performances');
            if (topPerformancesSection) topPerformancesSection.remove();

            // Add all students to the table (without pagination)
            const tableBody = gradesClone.querySelector('#students-list');
            if (tableBody) {
                tableBody.innerHTML = '';

                allStudents.forEach((student, index) => {
                    const row = document.createElement('tr');

                    // Determine validation status
                    const hasGrade = student.valeur !== null && student.valeur !== undefined && student.valeur !== '';
                    const isValidated = hasGrade && parseFloat(student.valeur) >= 12;
                    const validationStatus = hasGrade ? (isValidated ? 'V' : 'R') : '-';
                    const statusClass = hasGrade ? (isValidated ? 'validated' : 'rejected') : '';

                    row.innerHTML = `
                        <td>${index + 1}</td>
                        <td>${student.CNE || student.cne || ''}</td>
                        <td>${student.nom}</td>
                        <td>${student.prenom}</td>
                        <td>${student.valeur || '-'}</td>
                        <td><span class="validation-status ${statusClass}">${validationStatus}</span></td>
                    `;

                    tableBody.appendChild(row);
                });
            }

            // Add custom styles for PDF
            const styleElement = document.createElement('style');
            styleElement.textContent = `
                .validation-status.validated {
                    color: #28a745;
                    font-weight: bold;
                }
                .validation-status.rejected {
                    color: #dc3545;
                    font-weight: bold;
                }
                .grades-table {
                    width: 100%;
                    border-collapse: collapse;
                }
                .grades-table th {
                    background-color: #4a89dc;
                    color: white;
                    padding: 10px;
                    text-align: left;
                }
                .grades-table td {
                    padding: 8px 10px;
                    border-bottom: 1px solid #ddd;
                }
                .grades-table tr:nth-child(even) {
                    background-color: #f9f9f9;
                }
            `;
            pdfContent.appendChild(styleElement);
            pdfContent.appendChild(gradesClone);

            // Set PDF options
            const options = {
                margin: 10,
                filename: 'notes_etudiants.pdf',
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { scale: 2, useCORS: true },
                jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
            };

            // Return a promise that resolves with the PDF blob or saves the file
            return new Promise((resolve, reject) => {
                if (saveToFile) {
                    // Generate PDF and save to file
                    html2pdf().from(pdfContent).set(options).save().then(() => {
                        showNotification('PDF généré avec succès', 'success');
                        resolve();
                    }).catch(error => {
                        console.error('Error generating PDF:', error);
                        showNotification('Erreur lors de la génération du PDF', 'error');
                        reject(error);
                    });
                } else {
                    // Generate PDF and return as blob
                    html2pdf().from(pdfContent).set(options).outputPdf('blob').then(blob => {
                        resolve(blob);
                    }).catch(error => {
                        console.error('Error generating PDF:', error);
                        showNotification('Erreur lors de la génération du PDF', 'error');
                        reject(error);
                    });
                }
            });
        } catch (error) {
            console.error('Error in generatePDF:', error);
            showNotification('Erreur lors de la génération du PDF', 'error');
            return Promise.reject(error);
        }
    }
