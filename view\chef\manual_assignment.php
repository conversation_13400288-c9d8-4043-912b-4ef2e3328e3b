<?php
// Vérifier l'authentification
require_once '../includes/auth_check_chef.php';

// Get the department head's ID and department ID from the session
$chefId = $_SESSION['user']['teacher_id'] ?? null;
$departmentId = $_SESSION['user']['department_id'] ?? null;

// Get department head's name from session
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$chefName = trim($prenom . ' ' . $nom);
if (empty($chefName)) {
    $chefName = $_SESSION['user']['username'] ?? 'Chef de département';
}

// Get the current academic year
require_once '../../model/affectationModel.php';
$academicYear = getCurrentAcademicYear();

// Page title
$pageTitle = "Affectation Manuelle des Modules";
$currentPage = "manual_assignment.php";
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> | Système de Gestion ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        :root {
            /* Palette de couleurs pastel */
            --primary-color: #6d8bd3;       /* Bleu pastel */
            --primary-light: rgba(109, 139, 211, 0.1);
            --secondary-color: #7bc5ae;     /* Vert pastel */
            --secondary-light: rgba(123, 197, 174, 0.1);
            --warning-color: #f8d486;       /* Jaune pastel */
            --danger-color: #e6a4a4;        /* Rouge pastel */
            --dark-color: #5a5c69;          /* Gris foncé */
            --light-color: #f8f9fc;         /* Gris très clair */
            --card-border-radius: 0.35rem;  /* Rayon de bordure réduit */
            --transition-speed: 0.2s;       /* Transition plus rapide */
            --spacing-sm: 0.5rem;           /* Petit espacement */
            --spacing-md: 0.75rem;          /* Espacement moyen */
            --spacing-lg: 1rem;             /* Grand espacement */
        }

        /* Page Layout */
        .main-content {
            background-color: #f8f9fc;
        }

        .page-title {
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.2rem;
            font-size: 1.5rem;
        }

        .page-subtitle {
            font-size: 0.9rem;
            margin-bottom: 0;
        }

        /* Container avec espacement réduit */
        .container-fluid {
            padding: var(--spacing-lg) !important;
        }

        /* Card Styles */
        .card {
            border: none;
            border-radius: var(--card-border-radius);
            box-shadow: 0 0.1rem 0.75rem 0 rgba(58, 59, 69, 0.08);
            margin-bottom: var(--spacing-lg);
            transition: box-shadow var(--transition-speed);
        }

        .card:hover {
            box-shadow: 0 0.15rem 1rem 0 rgba(58, 59, 69, 0.12);
        }

        .card-header {
            padding: var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid #e3e6f0;
            background-color: white;
            border-top-left-radius: var(--card-border-radius) !important;
            border-top-right-radius: var(--card-border-radius) !important;
        }

        /* Filter Styles */
        .filter-section {
            background-color: white;
            border-radius: var(--card-border-radius);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            box-shadow: 0 0.1rem 0.75rem 0 rgba(58, 59, 69, 0.08);
        }

        .filter-label {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: var(--spacing-sm);
            font-size: 0.85rem;
        }

        .filter-select {
            transition: border-color var(--transition-speed), box-shadow var(--transition-speed);
            cursor: pointer;
            font-size: 0.9rem;
            padding: 0.4rem 0.75rem;
            height: auto;
        }

        .filter-select:hover {
            border-color: var(--primary-color);
        }

        .filter-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.15rem rgba(109, 139, 211, 0.2);
        }

        .filter-active {
            border-color: var(--primary-color);
            background-color: var(--primary-light);
        }

        /* Module Card Styles */
        .module-card {
            border-radius: var(--card-border-radius);
            margin-bottom: var(--spacing-lg);
            transition: transform var(--transition-speed), box-shadow var(--transition-speed);
            overflow: hidden;
        }

        .module-header {
            background-color: var(--primary-light);
            color: var(--primary-color);
            padding: var(--spacing-md);
            font-weight: 600;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .module-body {
            padding: var(--spacing-md);
        }

        .ue-item {
            padding: var(--spacing-md);
            border: 1px solid rgba(0, 0, 0, 0.05);
            border-radius: var(--card-border-radius);
            margin-bottom: var(--spacing-md);
            background-color: white;
            transition: background-color var(--transition-speed);
        }

        .ue-item:hover {
            background-color: var(--light-color);
        }

        .ue-type {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: var(--spacing-sm);
        }

        .ue-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: var(--spacing-sm);
        }

        .ue-hours {
            background-color: var(--secondary-light);
            color: var(--secondary-color);
            padding: 0.2rem 0.5rem;
            border-radius: 30px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        /* Badge pour l'année académique */
        .bg-primary-light {
            background-color: var(--primary-light);
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 2rem 1rem;
        }

        .empty-state i {
            font-size: 2.5rem;
            color: #d1d1d1;
            margin-bottom: 0.75rem;
        }

        .empty-state h5 {
            font-size: 1rem;
            margin-bottom: 0.5rem;
            color: var(--dark-color);
        }

        .empty-state p {
            font-size: 0.85rem;
            color: #6c757d;
        }

        /* Animation pour les cartes */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animated-card {
            animation: fadeIn 0.5s ease-out forwards;
        }

        /* Compteur de résultats */
        .module-count {
            background-color: var(--primary-light);
            color: var(--primary-color);
            font-weight: 600;
            font-size: 0.75rem;
            padding: 0.25em 0.6em;
            border-radius: 30px;
        }

        /* Statut des filtres */
        .filter-status {
            font-size: 0.8rem;
            color: #6c757d;
        }

        /* Espacement réduit */
        .g-3 {
            --bs-gutter-y: 0.75rem;
        }

        /* Bouton d'affectation */
        .btn-assign {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: white;
        }

        .btn-assign:hover {
            background-color: #6ab09b;
            border-color: #6ab09b;
            color: white;
        }

        /* Affectation existante */
        .assigned-teacher {
            background-color: var(--secondary-light);
            color: var(--secondary-color);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--card-border-radius);
            margin-top: var(--spacing-sm);
            font-size: 0.9rem;
        }

        .assigned-teacher i {
            margin-right: var(--spacing-sm);
        }

        /* Styles pour la sélection multiple */
        .ue-checkbox {
            margin-right: var(--spacing-sm);
        }

        .ue-select-all {
            font-size: 0.85rem;
            color: var(--primary-color);
            cursor: pointer;
            margin-bottom: var(--spacing-sm);
        }

        .ue-select-all:hover {
            text-decoration: underline;
        }

        .bulk-action-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: white;
            padding: 0.75rem 1.5rem;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            display: none;
            justify-content: space-between;
            align-items: center;
            transform: translateY(100%);
            transition: transform 0.3s ease-out;
        }

        .bulk-action-bar.show {
            transform: translateY(0);
            display: flex;
        }

        .selected-count {
            font-weight: 600;
            color: var(--dark-color);
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <h1 class="page-title"><?php echo $pageTitle; ?></h1>
                        <p class="page-subtitle text-muted">Affectez manuellement les modules et unités d'enseignement aux enseignants</p>
                    </div>
                    <div>
                        <span class="badge bg-primary-light text-primary py-1 px-2">
                            <i class="bi bi-calendar-check me-1"></i> <?php echo htmlspecialchars($academicYear); ?>
                        </span>
                    </div>
                </div>

                <!-- Alert Container -->
                <div class="alert-container" id="alertContainer"></div>

                <!-- Filters Section -->
                <div class="filter-section mb-3">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="filiereFilter" class="filter-label">Filière</label>
                            <select class="form-select form-select-sm filter-select" id="filiereFilter">
                                <option value="">Toutes les filières</option>
                                <!-- Options will be populated dynamically -->
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="niveauFilter" class="filter-label">Niveau</label>
                            <select class="form-select form-select-sm filter-select" id="niveauFilter">
                                <option value="">Tous les niveaux</option>
                                <!-- Options will be populated dynamically -->
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="semestreFilter" class="filter-label">Semestre</label>
                            <select class="form-select form-select-sm filter-select" id="semestreFilter">
                                <option value="">Tous les semestres</option>
                                <!-- Options will be populated dynamically -->
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Modules Container -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <h5 class="mb-0 me-2">Modules</h5>
                            <span class="module-count" id="moduleCount">0</span>
                            <span class="ms-2 filter-status" id="filterStatus"></span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="modulesContainer">
                            <!-- Modules will be loaded dynamically -->
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Chargement...</span>
                                </div>
                                <p class="mt-2">Chargement des modules...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Action Bar -->
    <div class="bulk-action-bar" id="bulkActionBar">
        <div class="selected-count">
            <span id="selectedUECount">0</span> unité(s) d'enseignement sélectionnée(s)
        </div>
        <div>
            <button type="button" class="btn btn-outline-secondary me-2" id="cancelSelectionBtn">
                Annuler
            </button>
            <button type="button" class="btn btn-assign" id="bulkAssignBtn">
                <i class="bi bi-person-plus-fill me-1"></i> Affecter en masse
            </button>
        </div>
    </div>

    <!-- Assignment Modal -->
    <div class="modal fade" id="assignmentModal" tabindex="-1" aria-labelledby="assignmentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="assignmentModalLabel">Affecter un enseignant</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="singleAssignmentInfo" class="mb-3">
                        <p><strong>Module:</strong> <span id="modalModuleName"></span></p>
                        <p><strong>Unité d'enseignement:</strong> <span id="modalUeType"></span></p>
                        <p><strong>Volume horaire:</strong> <span id="modalUeHours"></span> heures</p>
                    </div>
                    <div id="multipleAssignmentInfo" class="mb-3 d-none">
                        <p><strong>Nombre d'UE sélectionnées:</strong> <span id="modalUeCount"></span></p>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i> Vous êtes sur le point d'affecter plusieurs unités d'enseignement au même enseignant.
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="teacherSelect" class="form-label">Enseignant</label>
                        <select class="form-select" id="teacherSelect">
                            <option value="">Sélectionner un enseignant</option>
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="assignmentComment" class="form-label">Commentaire (optionnel)</label>
                        <textarea class="form-control" id="assignmentComment" rows="2"></textarea>
                    </div>
                    <input type="hidden" id="ueId">
                    <input type="hidden" id="isMultipleAssignment" value="0">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-assign" id="saveAssignmentBtn">Enregistrer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Global variables
        const departmentId = <?php echo json_encode($departmentId); ?>;
        const academicYear = <?php echo json_encode($academicYear); ?> || '<?php echo date('Y') . '-' . (date('Y') + 1); ?>';
        let modules = [];
        let filteredModules = [];
        let teachers = [];
        let assignments = [];
        let selectedUEs = new Set(); // Pour stocker les UE sélectionnées

        // DOM elements
        const modulesContainer = document.getElementById('modulesContainer');
        const filiereFilter = document.getElementById('filiereFilter');
        const niveauFilter = document.getElementById('niveauFilter');
        const semestreFilter = document.getElementById('semestreFilter');
        const filterSelects = document.querySelectorAll('.filter-select');
        const moduleCount = document.getElementById('moduleCount');
        const filterStatus = document.getElementById('filterStatus');
        const assignmentModal = new bootstrap.Modal(document.getElementById('assignmentModal'));
        const teacherSelect = document.getElementById('teacherSelect');
        const saveAssignmentBtn = document.getElementById('saveAssignmentBtn');
        const bulkActionBar = document.getElementById('bulkActionBar');
        const selectedUECount = document.getElementById('selectedUECount');
        const cancelSelectionBtn = document.getElementById('cancelSelectionBtn');
        const bulkAssignBtn = document.getElementById('bulkAssignBtn');
        const singleAssignmentInfo = document.getElementById('singleAssignmentInfo');
        const multipleAssignmentInfo = document.getElementById('multipleAssignmentInfo');
        const modalUeCount = document.getElementById('modalUeCount');
        const isMultipleAssignment = document.getElementById('isMultipleAssignment');

        // Load data when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            // Load data
            Promise.all([
                loadModules(),
                loadTeachers(),
                loadAssignments()
            ]).then(() => {
                // Apply filters
                applyFilters();
            });

            // Add change event listeners to all filter selects
            filterSelects.forEach(select => {
                select.addEventListener('change', function() {
                    // Add visual feedback for active filters
                    if (this.value) {
                        this.classList.add('filter-active');
                    } else {
                        this.classList.remove('filter-active');
                    }

                    // Apply filters immediately
                    applyFilters();
                });
            });

            // Add event listener to save assignment button
            saveAssignmentBtn.addEventListener('click', saveAssignment);

            // Add event listeners for bulk actions
            cancelSelectionBtn.addEventListener('click', clearSelection);
            bulkAssignBtn.addEventListener('click', openBulkAssignmentModal);
        });

        // Load modules from the server
        async function loadModules() {
            if (!departmentId) {
                showAlert('ID de département non disponible. Veuillez vous reconnecter.', 'danger');
                return;
            }

            try {
                const response = await fetch(`../../route/affectationRoute.php?action=getModulesByDepartment&department_id=${departmentId}`);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.error) {
                    showAlert(`Erreur: ${data.error}`, 'danger');
                    return;
                }

                modules = data.data || [];
                populateFilters();

                return modules;
            } catch (error) {
                console.error('Error loading modules:', error);
                showAlert('Erreur lors du chargement des modules. Vérifiez la console pour plus de détails.', 'danger');
            }
        }

        // Load teachers from the server
        async function loadTeachers() {
            if (!departmentId) {
                showAlert('ID de département non disponible. Veuillez vous reconnecter.', 'danger');
                return;
            }

            try {
                const response = await fetch(`../../route/enseignantRoute.php?departement=${departmentId}`);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.error) {
                    showAlert(`Erreur: ${data.error}`, 'danger');
                    return;
                }

                // Filter out vacataires
                teachers = (data.data || []).filter(teacher => teacher.role !== 'vacataire');

                return teachers;
            } catch (error) {
                console.error('Error loading teachers:', error);
                showAlert('Erreur lors du chargement des enseignants. Vérifiez la console pour plus de détails.', 'danger');
            }
        }

        // Load existing assignments from the server
        async function loadAssignments() {
            if (!departmentId) {
                showAlert('ID de département non disponible. Veuillez vous reconnecter.', 'danger');
                return;
            }

            try {
                const response = await fetch(`../../route/affectationRoute.php?action=getAffectationsByDepartment&department_id=${departmentId}`);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.error) {
                    showAlert(`Erreur: ${data.error}`, 'danger');
                    return;
                }

                assignments = data.data || [];

                return assignments;
            } catch (error) {
                console.error('Error loading assignments:', error);
                showAlert('Erreur lors du chargement des affectations. Vérifiez la console pour plus de détails.', 'danger');
            }
        }

        // Populate filter dropdowns
        function populateFilters() {
            // Save current selections
            const currentFiliereValue = filiereFilter.value;
            const currentNiveauValue = niveauFilter.value;
            const currentSemestreValue = semestreFilter.value;

            // Clear existing options (except the first one)
            filiereFilter.innerHTML = '<option value="">Toutes les filières</option>';
            niveauFilter.innerHTML = '<option value="">Tous les niveaux</option>';
            semestreFilter.innerHTML = '<option value="">Tous les semestres</option>';

            // Create sets to track unique values
            const filieres = new Map();
            const niveaux = new Set();
            const semestres = new Set();

            // Populate sets with unique values
            modules.forEach(module => {
                if (module.nom_filiere && module.id_filiere) {
                    filieres.set(module.id_filiere, module.nom_filiere);
                }
                if (module.niveau) {
                    niveaux.add(module.niveau);
                }
                if (module.semestre) {
                    semestres.add(module.semestre);
                }
            });

            // Add filiere options
            [...filieres.entries()].sort((a, b) => a[1].localeCompare(b[1])).forEach(([id, name]) => {
                const option = document.createElement('option');
                option.value = id;
                option.textContent = name;
                option.selected = (id === currentFiliereValue);
                filiereFilter.appendChild(option);
            });

            // Add niveau options
            [...niveaux].sort().forEach(niveau => {
                const option = document.createElement('option');
                option.value = niveau;
                option.textContent = niveau;
                option.selected = (niveau === currentNiveauValue);
                niveauFilter.appendChild(option);
            });

            // Add semestre options
            [...semestres].sort().forEach(semestre => {
                const option = document.createElement('option');
                option.value = semestre;
                option.textContent = semestre;
                option.selected = (semestre === currentSemestreValue);
                semestreFilter.appendChild(option);
            });

            // Update visual feedback for active filters
            filterSelects.forEach(select => {
                if (select.value) {
                    select.classList.add('filter-active');
                } else {
                    select.classList.remove('filter-active');
                }
            });
        }

        // Apply filters to the modules
        function applyFilters() {
            const filiereValue = filiereFilter.value;
            const niveauValue = niveauFilter.value;
            const semestreValue = semestreFilter.value;

            // Check if any filter is active
            const isFiltering = filiereValue || niveauValue || semestreValue;

            // Apply filters
            filteredModules = modules.filter(module => {
                const filiereMatch = !filiereValue || module.id_filiere === filiereValue;
                const niveauMatch = !niveauValue || module.niveau === niveauValue;
                const semestreMatch = !semestreValue || module.semestre === semestreValue;
                return filiereMatch && niveauMatch && semestreMatch;
            });

            // Update filter status text
            if (isFiltering) {
                let filterText = [];
                if (filiereValue) {
                    const filiereOption = filiereFilter.querySelector(`option[value="${filiereValue}"]`);
                    if (filiereOption) {
                        filterText.push(`Filière: ${filiereOption.textContent}`);
                    }
                }
                if (niveauValue) filterText.push(`Niveau: ${niveauValue}`);
                if (semestreValue) filterText.push(`Semestre: ${semestreValue}`);

                filterStatus.textContent = `Filtres actifs: ${filterText.join(', ')}`;
                filterStatus.style.display = 'inline';
            } else {
                filterStatus.textContent = '';
                filterStatus.style.display = 'none';
            }

            // Update count
            moduleCount.textContent = filteredModules.length;

            // Render modules
            renderModules();
        }

        // Render modules with their UEs
        async function renderModules() {
            if (filteredModules.length === 0) {
                modulesContainer.innerHTML = `
                <div class="empty-state">
                    <i class="bi bi-clipboard-x"></i>
                    <h5>Aucun module trouvé</h5>
                    <p>Il n'y a pas de modules correspondant à vos critères de recherche.</p>
                </div>`;
                return;
            }

            // Show loading state
            modulesContainer.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                </div>
                <p class="mt-2">Chargement des unités d'enseignement...</p>
            </div>`;

            try {
                // Fetch UEs for each module
                const moduleUEs = await Promise.all(
                    filteredModules.map(async module => {
                        const response = await fetch(`../../route/descriptifRoute.php?action=getUnitesByModule&module_id=${module.id}`);
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        const data = await response.json();
                        return {
                            module: module,
                            ues: data.data || []
                        };
                    })
                );

                // Generate HTML for each module
                let html = '';

                moduleUEs.forEach((item, index) => {
                    const { module, ues } = item;

                    // Skip modules without UEs
                    if (ues.length === 0) return;

                    // Add animation delay based on index
                    const delay = index * 0.1;

                    html += `
                    <div class="module-card animated-card" style="animation-delay: ${delay}s">
                        <div class="module-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>${module.nom}</div>
                                <div class="badge bg-primary-light text-primary">${module.semestre || 'N/A'}</div>
                            </div>
                            <div class="text-muted small">${module.nom_filiere} - ${module.niveau || 'N/A'}</div>
                        </div>
                        <div class="module-body">
                            <div class="ue-select-all" data-module-id="${module.id}">
                                <i class="bi bi-check-square me-1"></i> Sélectionner toutes les UE
                            </div>`;

                    // Sort UEs by type (Cours, TD, TP)
                    const sortedUEs = ues.sort((a, b) => {
                        const typeOrder = { 'Cours': 1, 'TD': 2, 'TP': 3 };
                        return typeOrder[a.type] - typeOrder[b.type];
                    });

                    sortedUEs.forEach(ue => {
                        // Find existing assignment for this UE
                        const existingAssignment = assignments.find(a => a.unite_enseignement_id === ue.id);

                        const isChecked = selectedUEs.has(ue.id) ? 'checked' : '';
                        const isDisabled = existingAssignment ? 'disabled' : '';

                        html += `
                            <div class="ue-item">
                                <div class="d-flex align-items-center">
                                    <div class="form-check me-2">
                                        <input class="form-check-input ue-checkbox" type="checkbox"
                                            value="${ue.id}"
                                            data-module-id="${module.id}"
                                            data-module-name="${module.nom}"
                                            data-ue-type="${ue.type}"
                                            data-ue-hours="${ue.volume_horaire}"
                                            data-specialite-id="${module.specialite_id}"
                                            ${isChecked}
                                            ${isDisabled}
                                            onchange="toggleUESelection(this)">
                                    </div>
                                    <div class="ue-type">${ue.type}</div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mt-2">
                                    <div class="ue-hours">${ue.volume_horaire}h</div>
                                    <div>`;

                        if (existingAssignment) {
                            html += `
                                <div class="assigned-teacher">
                                    <i class="bi bi-person-check-fill"></i>
                                    ${existingAssignment.enseignant_nom} ${existingAssignment.enseignant_prenom}
                                </div>
                                <button class="btn btn-sm btn-outline-secondary mt-2"
                                    onclick="openAssignmentModal('${ue.id}', '${module.nom}', '${ue.type}', '${ue.volume_horaire}', '${module.specialite_id}')">
                                    Modifier
                                </button>`;
                        } else {
                            html += `
                                <button class="btn btn-sm btn-assign"
                                    onclick="openAssignmentModal('${ue.id}', '${module.nom}', '${ue.type}', '${ue.volume_horaire}', '${module.specialite_id}')">
                                    Affecter
                                </button>`;
                        }

                        html += `
                                    </div>
                                </div>
                            </div>`;
                    });

                    html += `
                        </div>
                    </div>`;
                });

                if (html === '') {
                    modulesContainer.innerHTML = `
                    <div class="empty-state">
                        <i class="bi bi-clipboard-x"></i>
                        <h5>Aucune unité d'enseignement trouvée</h5>
                        <p>Il n'y a pas d'unités d'enseignement pour les modules sélectionnés.</p>
                    </div>`;
                } else {
                    modulesContainer.innerHTML = html;

                    // Add event listeners to "Select all" buttons
                    document.querySelectorAll('.ue-select-all').forEach(button => {
                        button.addEventListener('click', function() {
                            const moduleId = this.dataset.moduleId;
                            const checkboxes = document.querySelectorAll(`.ue-checkbox[data-module-id="${moduleId}"]:not(:disabled)`);

                            // Check if all checkboxes are already checked
                            const allChecked = [...checkboxes].every(cb => cb.checked);

                            // Toggle all checkboxes
                            checkboxes.forEach(checkbox => {
                                checkbox.checked = !allChecked;

                                // Update selectedUEs set
                                if (!allChecked) {
                                    selectedUEs.add(checkbox.value);
                                } else {
                                    selectedUEs.delete(checkbox.value);
                                }
                            });

                            // Update bulk action bar
                            updateBulkActionBar();
                        });
                    });
                }
            } catch (error) {
                console.error('Error loading UEs:', error);
                modulesContainer.innerHTML = `
                <div class="empty-state">
                    <i class="bi bi-exclamation-triangle"></i>
                    <h5>Erreur lors du chargement</h5>
                    <p>Une erreur s'est produite lors du chargement des unités d'enseignement.</p>
                </div>`;
            }
        }

        // Open assignment modal
        function openAssignmentModal(ueId, moduleName, ueType, ueHours, specialiteId) {
            // Reset to single assignment mode
            singleAssignmentInfo.classList.remove('d-none');
            multipleAssignmentInfo.classList.add('d-none');
            isMultipleAssignment.value = "0";

            // Set modal content
            document.getElementById('modalModuleName').textContent = moduleName;
            document.getElementById('modalUeType').textContent = ueType;
            document.getElementById('modalUeHours').textContent = ueHours;
            document.getElementById('ueId').value = ueId;

            // Clear previous selection and comment
            teacherSelect.innerHTML = '<option value="">Sélectionner un enseignant</option>';
            document.getElementById('assignmentComment').value = '';

            // Filter teachers by specialty
            const filteredTeachers = teachers.filter(teacher =>
                teacher.id_specialite === specialiteId
            );

            // Add teacher options
            filteredTeachers.sort((a, b) => a.nom.localeCompare(b.nom)).forEach(teacher => {
                const option = document.createElement('option');
                option.value = teacher.id_enseignant;
                option.textContent = `${teacher.nom} ${teacher.prenom}`;
                teacherSelect.appendChild(option);
            });

            // Find existing assignment for this UE
            const existingAssignment = assignments.find(a => a.unite_enseignement_id === ueId);
            if (existingAssignment) {
                // Set selected teacher and comment
                teacherSelect.value = existingAssignment.professeur_id;
                document.getElementById('assignmentComment').value = existingAssignment.commentaire || '';
            }

            // Show modal
            assignmentModal.show();
        }

        // Save assignment
        async function saveAssignment() {
            // Check if this is a multiple assignment
            if (isMultipleAssignment.value === "1") {
                await saveMultipleAssignments();
                return;
            }

            // Single assignment
            const ueId = document.getElementById('ueId').value;
            const teacherId = teacherSelect.value;
            const comment = document.getElementById('assignmentComment').value;

            if (!ueId) {
                showAlert('ID de l\'unité d\'enseignement non disponible.', 'danger');
                return;
            }

            if (!teacherId) {
                showAlert('Veuillez sélectionner un enseignant.', 'danger');
                return;
            }

            try {
                const response = await fetch('../../route/affectationRoute.php?action=createManualAffectation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        ue_id: ueId,
                        teacher_id: teacherId,
                        academic_year: academicYear,
                        commentaire: comment
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.error) {
                    showAlert(`Erreur: ${data.error}`, 'danger');
                    return;
                }

                // Close modal
                assignmentModal.hide();

                // Show success message
                showAlert('Affectation enregistrée avec succès.', 'success');

                // Reload assignments and re-render modules
                await loadAssignments();
                renderModules();
            } catch (error) {
                console.error('Error saving assignment:', error);
                showAlert('Erreur lors de l\'enregistrement de l\'affectation. Vérifiez la console pour plus de détails.', 'danger');
            }
        }

        // Show an alert message
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertId = `alert-${Date.now()}`;

            const alertHtml = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>`;

            alertContainer.insertAdjacentHTML('beforeend', alertHtml);

            // Auto-dismiss after 4 seconds
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    alertElement.classList.remove('show');
                    setTimeout(() => alertElement.remove(), 250);
                }
            }, 4000);
        }

        // Toggle UE selection
        function toggleUESelection(checkbox) {
            const ueId = checkbox.value;

            if (checkbox.checked) {
                selectedUEs.add(ueId);
            } else {
                selectedUEs.delete(ueId);
            }

            updateBulkActionBar();
        }

        // Update bulk action bar visibility and count
        function updateBulkActionBar() {
            const count = selectedUEs.size;
            selectedUECount.textContent = count;

            if (count > 0) {
                bulkActionBar.classList.add('show');
            } else {
                bulkActionBar.classList.remove('show');
            }
        }

        // Clear all selections
        function clearSelection() {
            selectedUEs.clear();

            // Uncheck all checkboxes
            document.querySelectorAll('.ue-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });

            updateBulkActionBar();
        }

        // Open bulk assignment modal
        function openBulkAssignmentModal() {
            if (selectedUEs.size === 0) {
                showAlert('Veuillez sélectionner au moins une unité d\'enseignement.', 'warning');
                return;
            }

            // Get the first selected UE to determine the specialty
            const firstCheckbox = document.querySelector(`.ue-checkbox[value="${[...selectedUEs][0]}"]`);
            if (!firstCheckbox) return;

            const specialiteId = firstCheckbox.dataset.specialiteId;

            // Show multiple assignment info
            singleAssignmentInfo.classList.add('d-none');
            multipleAssignmentInfo.classList.remove('d-none');
            modalUeCount.textContent = selectedUEs.size;
            isMultipleAssignment.value = "1";

            // Clear previous selection and comment
            teacherSelect.innerHTML = '<option value="">Sélectionner un enseignant</option>';
            document.getElementById('assignmentComment').value = '';

            // Filter teachers by specialty
            const filteredTeachers = teachers.filter(teacher =>
                teacher.id_specialite === specialiteId
            );

            // Add teacher options
            filteredTeachers.sort((a, b) => a.nom.localeCompare(b.nom)).forEach(teacher => {
                const option = document.createElement('option');
                option.value = teacher.id_enseignant;
                option.textContent = `${teacher.nom} ${teacher.prenom}`;
                teacherSelect.appendChild(option);
            });

            // Show modal
            assignmentModal.show();
        }

        // Save multiple assignments
        async function saveMultipleAssignments() {
            const teacherId = teacherSelect.value;
            const comment = document.getElementById('assignmentComment').value;

            if (!teacherId) {
                showAlert('Veuillez sélectionner un enseignant.', 'danger');
                return;
            }

            try {
                // Create an array of promises for each assignment
                const assignmentPromises = [...selectedUEs].map(ueId =>
                    fetch('../../route/affectationRoute.php?action=createManualAffectation', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            ue_id: ueId,
                            teacher_id: teacherId,
                            academic_year: academicYear,
                            commentaire: comment
                        })
                    }).then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                );

                // Wait for all assignments to complete
                const results = await Promise.all(assignmentPromises);

                // Check if any assignment failed
                const failures = results.filter(result => result.error);

                if (failures.length > 0) {
                    showAlert(`${failures.length} affectation(s) ont échoué. Veuillez réessayer.`, 'warning');
                } else {
                    // Close modal
                    assignmentModal.hide();

                    // Show success message
                    showAlert(`${selectedUEs.size} affectation(s) enregistrée(s) avec succès.`, 'success');

                    // Clear selection
                    clearSelection();

                    // Reload assignments and re-render modules
                    await loadAssignments();
                    renderModules();
                }
            } catch (error) {
                console.error('Error saving assignments:', error);
                showAlert('Erreur lors de l\'enregistrement des affectations. Vérifiez la console pour plus de détails.', 'danger');
            }
        }

        // Make functions available globally
        window.openAssignmentModal = openAssignmentModal;
        window.toggleUESelection = toggleUESelection;
    </script>
</body>
</html>
