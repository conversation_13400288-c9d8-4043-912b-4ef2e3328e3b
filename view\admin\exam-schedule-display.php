<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exam Schedule - UniAdmin</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/timetable.css">
    <link rel="stylesheet" href="../assets/css/timetable-display.css">
    <link rel="stylesheet" href="../assets/css/exam-schedule-fixes.css">
    <link rel="stylesheet" href="../assets/css/filter-adjustments.css">
    <link rel="stylesheet" href="../assets/css/exam-form-styles.css">
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4 timetable-display-container" style="padding-top: 80px !important;">
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>Exam Schedule</h2>
                    <div>
                        <a href="exam_schedule.php" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-left me-2"></i>Back
                        </a>
                    </div>
                </div>

                <!-- Filter Summary -->
                <div class="card animate-fade-in mb-4">
                    <div class="card-header d-flex align-items-center">
                        <div class="filter-icon me-2">
                            <i class="bi bi-funnel-fill text-primary"></i>
                        </div>
                        <div class="card-title mb-0 fw-bold">
                            Applied Filters
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="applied-filters" id="applied-filters">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Error message container - will be hidden by default -->
                <div id="error-container" class="d-none"></div>

                <!-- Class Title (will be populated by JavaScript) -->
                <div id="class-title" class="alert alert-info mt-4 mb-4 d-none">
                    <!-- Will be populated by JavaScript -->
                </div>

                <!-- Schedule Container -->
                <div class="schedule-container animate-fade-in">
                    <div class="schedule-header">
                        <div class="schedule-title">
                            <i class="bi bi-calendar-week"></i>
                            <h2>Exam Schedule</h2>
                        </div>
                        <div class="schedule-actions">
                            <button class="add-session-btn" id="add-session-btn">
                                <i class="bi bi-plus"></i> Add Exam
                            </button>
                            <button class="download-pdf-btn" id="download-pdf-btn">
                                <i class="bi bi-file-pdf"></i> Download PDF
                            </button>
                        </div>
                    </div>

                    <!-- No classes message -->
                    <div id="no-classes-message" class="alert alert-info m-4 d-none">
                        No exams found for the selected filters. Please try different filters or add a new exam.
                    </div>

                    <div class="timetable-wrapper">
                        <table class="timetable">
                            <thead>
                                <tr>
                                    <th>Day / Time</th>
                                    <th class="time-header">08h-10h</th>
                                    <th class="time-header">10h-12h</th>
                                    <th class="time-header">14h-16h</th>
                                    <th class="time-header">16h-18h</th>
                                </tr>
                            </thead>
                            <tbody id="timetable-body">
                                <!-- Days as rows, time slots as columns -->
                                <tr>
                                    <td class="day-slot">Monday<div class="day-date" data-day="1"></div></td>
                                    <td class="empty-cell" data-day="1" data-time="08h-10h"></td>
                                    <td class="empty-cell" data-day="1" data-time="10h-12h"></td>
                                    <td class="empty-cell" data-day="1" data-time="14h-16h"></td>
                                    <td class="empty-cell" data-day="1" data-time="16h-18h"></td>
                                </tr>
                                <tr>
                                    <td class="day-slot">Tuesday<div class="day-date" data-day="2"></div></td>
                                    <td class="empty-cell" data-day="2" data-time="08h-10h"></td>
                                    <td class="empty-cell" data-day="2" data-time="10h-12h"></td>
                                    <td class="empty-cell" data-day="2" data-time="14h-16h"></td>
                                    <td class="empty-cell" data-day="2" data-time="16h-18h"></td>
                                </tr>
                                <tr>
                                    <td class="day-slot">Wednesday<div class="day-date" data-day="3"></div></td>
                                    <td class="empty-cell" data-day="3" data-time="08h-10h"></td>
                                    <td class="empty-cell" data-day="3" data-time="10h-12h"></td>
                                    <td class="empty-cell" data-day="3" data-time="14h-16h"></td>
                                    <td class="empty-cell" data-day="3" data-time="16h-18h"></td>
                                </tr>
                                <tr>
                                    <td class="day-slot">Thursday<div class="day-date" data-day="4"></div></td>
                                    <td class="empty-cell" data-day="4" data-time="08h-10h"></td>
                                    <td class="empty-cell" data-day="4" data-time="10h-12h"></td>
                                    <td class="empty-cell" data-day="4" data-time="14h-16h"></td>
                                    <td class="empty-cell" data-day="4" data-time="16h-18h"></td>
                                </tr>
                                <tr>
                                    <td class="day-slot">Friday<div class="day-date" data-day="5"></div></td>
                                    <td class="empty-cell" data-day="5" data-time="08h-10h"></td>
                                    <td class="empty-cell" data-day="5" data-time="10h-12h"></td>
                                    <td class="empty-cell" data-day="5" data-time="14h-16h"></td>
                                    <td class="empty-cell" data-day="5" data-time="16h-18h"></td>
                                </tr>
                                <tr>
                                    <td class="day-slot">Saturday<div class="day-date" data-day="6"></div></td>
                                    <td class="empty-cell" data-day="6" data-time="08h-10h"></td>
                                    <td class="empty-cell" data-day="6" data-time="10h-12h"></td>
                                    <td class="empty-cell" data-day="6" data-time="14h-16h"></td>
                                    <td class="empty-cell" data-day="6" data-time="16h-18h"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Hidden form fields for storing selected values -->
                <input type="hidden" id="filiere_id" name="filiere_id" value="">
                <input type="hidden" id="niveau_id" name="niveau_id" value="">
                <input type="hidden" id="groupe_id" name="groupe_id" value="">
                <input type="hidden" id="semestre" name="semestre" value="">
            </div>
        </div>
    </div>

    <!-- Add Session Modal -->
    <div class="modal fade" id="addSessionModal" tabindex="-1" aria-labelledby="addSessionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addSessionModalLabel">Add Exam</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addSessionForm">
                        <input type="hidden" id="seance_id" name="seance_id" value="">
                        <input type="hidden" id="selected_day" name="selected_day" value="">
                        <input type="hidden" id="selected_time" name="selected_time" value="">

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="module" class="form-label required">Module</label>
                                <select class="form-select" id="module" name="module" required>
                                    <option value="" disabled selected>Select module</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="enseignant" class="form-label required">Professor</label>
                                <select class="form-select" id="enseignant" name="enseignant" required>
                                    <option value="" disabled selected>Select professor</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="salle" class="form-label required">Room</label>
                                <select class="form-select" id="salle" name="salle" required>
                                    <option value="" disabled selected>Select room</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="type_seance" class="form-label required">Exam Type</label>
                                <select class="form-select" id="type_seance" name="type_exam" required>
                                    <option value="" disabled selected>Select type</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="day_display" class="form-label">Day</label>
                                <input type="text" class="form-control" id="day_display" name="day_display" readonly>
                            </div>
                            <div class="col-md-4">
                                <label for="time_display" class="form-label">Time</label>
                                <input type="text" class="form-control" id="time_display" name="time_display" readonly>
                            </div>
                            <div class="col-md-4">
                                <label for="date_exam" class="form-label required">Exam Date</label>
                                <input type="date" class="form-control" id="date_exam" name="date_exam" required>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveSessionBtn">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jsPDF for PDF generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/sidebar.js"></script>
    <script src="../assets/js/exam-schedule-view.js"></script>
    <script src="../assets/js/timetable-pdf.js"></script>
    <script src="../assets/js/filter-width-fix.js"></script>
</body>
</html>