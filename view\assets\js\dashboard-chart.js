// Script pour gérer le graphique des visites

// Fonction pour récupérer dynamiquement le chemin de base des visites
function getVisitsBasePath(routeFile = 'visitsRoute.php') {
    // Utiliser un chemin relatif pour éviter les problèmes de résolution de chemin
    return `../../route/${routeFile}`;
}

document.addEventListener('DOMContentLoaded', function() {
    // Récupérer le contexte du canvas
    const ctx = document.getElementById('visitsChart').getContext('2d');

    // Variables pour stocker les données du graphique
    let labels = [];
    let data = [];

    // Fonction pour récupérer les données de visite depuis l'API
    function fetchVisitData() {
        // Afficher un message de chargement
        const loadingMessage = document.createElement('div');
        loadingMessage.className = 'text-center text-muted my-3';
        loadingMessage.id = 'chart-loading';
        loadingMessage.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Chargement des données...';

        const chartContainer = document.querySelector('.chart-container');
        chartContainer.insertBefore(loadingMessage, chartContainer.firstChild);

        // Obtenir le chemin de l'API
        const apiPath = getVisitsBasePath();
        console.log('Chemin de l\'API pour les statistiques:', apiPath);

        // Appeler l'API pour récupérer les statistiques de visite
        fetch(`${apiPath}?action=stats`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
                return response.json();
            })
            .then(result => {
                // Supprimer le message de chargement
                const loadingMessage = document.getElementById('chart-loading');
                if (loadingMessage) {
                    loadingMessage.remove();
                }

                if (result.success) {
                    // Utiliser les données de l'API
                    labels = result.labels;
                    data = result.data;

                    // Initialiser le graphique avec les données réelles
                    initChart();
                } else {
                    console.error('Erreur lors de la récupération des données:', result.message);
                    // Utiliser des données de secours en cas d'erreur
                    useBackupData();
                }
            })
            .catch(error => {
                console.error('Erreur lors de la récupération des données:', error);

                // Supprimer le message de chargement
                const loadingMessage = document.getElementById('chart-loading');
                if (loadingMessage) {
                    loadingMessage.remove();
                }

                // Utiliser des données de secours en cas d'erreur
                useBackupData();
            });
    }

    // Fonction pour utiliser des données de secours en cas d'erreur
    function useBackupData() {
        // Générer des données aléatoires pour les 30 derniers jours
        const currentDate = new Date();
        labels = [];
        data = [];

        // Générer les étiquettes pour les 30 derniers jours
        for (let i = 29; i >= 0; i--) {
            const date = new Date();
            date.setDate(currentDate.getDate() - i);
            const day = date.getDate();
            const month = date.toLocaleString('fr-FR', { month: 'short' });
            labels.push(`${day} ${month}`);

            // Générer des données aléatoires avec une tendance à la hausse
            const baseValue = 50 + Math.floor(Math.random() * 30);
            const trend = i * 1.5; // Tendance à la hausse
            const fluctuation = Math.floor(Math.random() * 20) - 10; // Fluctuation aléatoire
            data.push(Math.max(0, Math.floor(baseValue + trend + fluctuation)));
        }

        // Initialiser le graphique avec les données de secours
        initChart();
    }

    // Récupérer les données
    fetchVisitData();

    // Définir les couleurs pastel
    const pastelBlue = 'rgba(168, 216, 234, 0.2)';
    const pastelBlueBorder = 'rgba(74, 156, 201, 1)';

    // Variable pour stocker l'instance du graphique
    let visitsChart;

    // Fonction pour initialiser le graphique
    function initChart() {
        // Créer le graphique
        visitsChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Nombre de visites',
                    data: data,
                    backgroundColor: pastelBlue,
                    borderColor: pastelBlueBorder,
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: pastelBlueBorder,
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#fff',
                        borderWidth: 1,
                        padding: 10,
                        displayColors: true,
                        callbacks: {
                            label: function(context) {
                                return `Visites: ${context.parsed.y}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            precision: 0
                        }
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                },
                animations: {
                    tension: {
                        duration: 1000,
                        easing: 'linear'
                    }
                }
            }
        });

        // Gérer le changement de type de graphique
        const chartTypeSelector = document.getElementById('chart-type-selector');
        chartTypeSelector.addEventListener('change', function() {
            const newType = this.value;

            // Mettre à jour le type de graphique
            visitsChart.config.type = newType;

            // Ajuster les options en fonction du type
            if (newType === 'bar') {
                visitsChart.data.datasets[0].tension = 0;
                visitsChart.data.datasets[0].borderRadius = 4;
            } else if (newType === 'line' || newType === 'area') {
                visitsChart.data.datasets[0].tension = 0.4;
                delete visitsChart.data.datasets[0].borderRadius;
            }

            // Mettre à jour le remplissage pour le type 'area'
            visitsChart.data.datasets[0].fill = newType === 'area';

            // Mettre à jour le graphique
            visitsChart.update();
        });
    }

    // Fonction pour enregistrer une visite
    function recordVisit() {
        // Données à envoyer
        const visitData = {
            user_type: 'admin', // Ou récupérer dynamiquement le type d'utilisateur
            page_visited: 'dashboard'
        };

        // Obtenir le chemin de l'API
        const apiPath = getVisitsBasePath();
        console.log('Chemin de l\'API pour l\'enregistrement de visite:', apiPath);

        // Envoyer les données à l'API
        fetch(`${apiPath}?action=record`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(visitData)
        })
        .then(response => response.json())
        .then(result => {
            console.log('Visite enregistrée:', result);
        })
        .catch(error => {
            console.error('Erreur lors de l\'enregistrement de la visite:', error);
        });
    }

    // Enregistrer la visite actuelle
    recordVisit();
});
