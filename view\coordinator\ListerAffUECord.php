<?php
// Vérifier l'authentification
require_once '../includes/auth_check_coordinateur.php';

// Get the coordinator's filiere ID from the session
$filiereId = $_SESSION['user']['filiere_id'] ?? null;
$filiereName = $_SESSION['user']['filiere_name'] ?? 'Non spécifié';

// Get coordinator's name from session
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$coordinatorName = trim($prenom . ' ' . $nom);
if (empty($coordinatorName)) {
    $coordinatorName = $_SESSION['user']['username'] ?? 'Coordinateur';
}

// Get the current academic year
require_once '../../model/affectationModel.php';
$academicYear = getCurrentAcademicYear();

// Page title
$pageTitle = "Teaching Units Assignments";
$currentPage = "ListerAffUECord.php";
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> | Système de Gestion ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/ListerAffUECord.css">

</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <h1 class="page-title"><?php echo $pageTitle; ?></h1>
                        <p class="page-subtitle text-muted">View teaching assignments validated by the department head for your program, organized by semester</p>
                    </div>
                    <div>
                        <span class="badge bg-primary-light text-primary py-1 px-2">
                            <i class="bi bi-calendar-check me-1"></i> <?php echo htmlspecialchars($academicYear); ?>
                        </span>
                    </div>
                </div>

                <!-- Alert Container -->
                <div class="alert-container" id="alertContainer"></div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="semestreFilter" class="form-label small text-muted">Semester</label>
                                <select id="semestreFilter" class="form-select filter-select">
                                    <option value="">All semesters</option>
                                    <!-- Options will be populated by JavaScript -->
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="moduleFilter" class="form-label small text-muted">Module</label>
                                <select id="moduleFilter" class="form-select filter-select">
                                    <option value="">All modules</option>
                                    <!-- Options will be populated by JavaScript -->
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="ueTypeFilter" class="form-label small text-muted">Teaching Unit Type</label>
                                <select id="ueTypeFilter" class="form-select filter-select">
                                    <option value="">All types</option>
                                    <option value="Cours">Lecture</option>
                                    <option value="TD">Tutorial</option>
                                    <option value="TP">Practical</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Assignments Container -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <h5 class="mb-0 me-2">Assignments</h5>
                            <span class="affectation-count" id="affectationCount">0</span>
                            <span class="ms-2 filter-status" id="filterStatus"></span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="affectationsContainer">
                            <!-- Assignments will be loaded dynamically -->
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading assignments...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/sidebar.js"></script>
    <script src="../assets/js/ListerAffUECord.js"></script>

    <script>
        // Store the filiere ID for use in JavaScript
        const filiereId = <?php echo $filiereId ?? 'null'; ?>;
        const filiereName = "<?php echo htmlspecialchars($filiereName); ?>";

        // Debug information
        console.log('Filiere ID from session:', filiereId);
        console.log('Filiere Name from session:', filiereName);
        console.log('Session data available:', <?php echo json_encode([
            'user_role' => $_SESSION['user']['role'] ?? 'unknown',
            'filiere_id' => $_SESSION['user']['filiere_id'] ?? 'not set',
            'filiere_name' => $_SESSION['user']['filiere_name'] ?? 'not set'
        ]); ?>);

        // Check if filiere ID is available
        if (!filiereId) {
            console.error('No filiere ID available');
            document.addEventListener('DOMContentLoaded', function() {
                showAlert('Program ID not available. Please log in again.', 'danger');
            });
        }
    </script>
</body>
</html>