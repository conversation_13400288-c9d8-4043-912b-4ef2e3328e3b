// Fonction pour récupérer les événements
async function fetchEvents() {
    try {
        const response = await fetch('../../route/eventsRoute.php');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const result = await response.json();
        if (result.success && Array.isArray(result.data)) {
            return result.data.slice(0, 3); // Retourne les 3 derniers événements
        }
        return [];
    } catch (error) {
        console.error('Erreur lors de la récupération des événements:', error);
        return [];
    }
}

// Fonction pour formater la date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', { 
        day: '2-digit', 
        month: 'long',
        year: 'numeric'
    });
}

// Fonction pour formater l'heure
function formatTime(timeString) {
    return timeString.substring(0, 5); // Retourne HH:mm
}

// Fonction pour mettre à jour l'affichage des événements
function updateEventsDisplay(events) {
    const eventsList = document.querySelector('.card-events .list-unstyled');
    if (!eventsList) return;

    eventsList.innerHTML = '';
    
    if (events.length === 0) {
        eventsList.innerHTML = '<li class="mb-2">Aucun événement</li>';
        return;
    }

    events.forEach(event => {
        const li = document.createElement('li');
        li.className = 'mb-2';
        const title = event.title || 'Événement';
        const date = formatDate(event.event_date);
        const time = formatTime(event.event_time);
        const category = event.category || '';
        li.innerHTML = `
            <div class="d-flex flex-column">
                <div class="d-flex justify-content-between align-items-center">
                    <span class="fw-bold">${title}</span>
                    <small class="text-muted">${date}</small>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-primary">${category}</small>
                    <small class="text-muted">${time}</small>
                </div>
            </div>
        `;
        eventsList.appendChild(li);
    });
}

// Fonction principale pour initialiser et rafraîchir les événements
async function initEvents() {
    // Première récupération des événements
    const events = await fetchEvents();
    updateEventsDisplay(events);

    // Rafraîchissement automatique toutes les 30 secondes
    setInterval(async () => {
        const updatedEvents = await fetchEvents();
        updateEventsDisplay(updatedEvents);
    }, 30000);
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', initEvents);