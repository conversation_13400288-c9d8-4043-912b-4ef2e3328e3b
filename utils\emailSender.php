<?php
/**
 * Utilitaire pour envoyer des emails
 */

// Inclure les configurations
require_once __DIR__ . '/../config/mail.php';
require_once __DIR__ . '/../config/email_provider.php';

// Inclure SendGrid
require_once __DIR__ . '/sendgridSender.php';

/**
 * Envoie un email en utilisant SendGrid ou en mode simulation
 *
 * @param string $to Adresse email du destinataire
 * @param string $subject Sujet de l'email
 * @param string $message Corps de l'email
 * @param array $attachments Pièces jointes (optionnel)
 * @return array Résultat de l'envoi avec clés 'success' et 'message'
 */
function sendEmail($to, $subject, $message, $attachments = []) {
    // Configurer le fichier de log
    $logsDir = __DIR__ . '/../logs';
    if (!file_exists($logsDir)) {
        mkdir($logsDir, 0755, true);
    }
    ini_set('error_log', $logsDir . '/php_errors.log');

    // Journaliser les informations d'envoi d'email
    error_log("Tentative d'envoi d'email à: " . $to . " - " . date('Y-m-d H:i:s'));

    try {
        // Extraire le code de réinitialisation pour le débogage
        if (strpos($subject, "Réinitialisation") !== false || strpos($subject, "Initialisation") !== false) {
            preg_match('/<div class=\'code\'>(.*?)<\/div>/', $message, $matches);
            if (isset($matches[1])) {
                $resetCode = $matches[1];
                // Stocker le code dans la session pour le débogage
                if (session_status() === PHP_SESSION_NONE) {
                    session_start();
                }
                $_SESSION['debug_reset_code'] = $resetCode;
                error_log("Code de réinitialisation: " . $resetCode);
            }
        }

        // Vérifier le mode de simulation
        if ((defined('MAIL_SIMULATE') && MAIL_SIMULATE === true) || (defined('MAIL_SIMULATE_OVERRIDE') && MAIL_SIMULATE_OVERRIDE === true)) {
            error_log("Email simulé (MAIL_SIMULATE=true)");
            error_log("À: " . $to);
            error_log("Sujet: " . $subject);
            error_log("Message: " . substr($message, 0, 100) . "...");

            // Stocker les informations dans la session pour la page de test
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }
            $_SESSION['last_email_sent'] = [
                'to' => $to,
                'subject' => $subject,
                'date' => date('Y-m-d H:i:s'),
                'success' => true,
                'simulated' => true
            ];

            return [
                'success' => true,
                'message' => 'Email simulé (mode simulation)'
            ];
        }

        // Force le mode réel (pour le débogage)
        error_log("Mode réel forcé - Tentative d'envoi d'email via SendGrid");

        // Utiliser SendGrid pour envoyer l'email
        return sendEmailViaSendGrid($to, $subject, $message, $attachments);
    } catch (Exception $e) {
        error_log("Erreur lors de l'envoi d'email: " . $e->getMessage());
        error_log("Trace: " . $e->getTraceAsString());

        return [
            'success' => false,
            'message' => 'Erreur lors de l\'envoi d\'email: ' . $e->getMessage()
        ];
    }
}

/**
 * Envoie un email de réinitialisation de mot de passe
 *
 * @param string $to Adresse email du destinataire
 * @param string $toName Nom du destinataire (optionnel)
 * @param string $code Code de réinitialisation
 * @return array Résultat de l'envoi avec clés 'success' et 'message'
 */
function sendPasswordResetEmail($to, $code, $toName = '') {
    $subject = "Réinitialisation de votre mot de passe ENSAH";

    $message = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #1a73e8; color: white; padding: 15px 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { padding: 30px; background-color: #f9f9f9; border: 1px solid #ddd; border-radius: 0 0 8px 8px; }
            .code-container { background-color: #e8f0fe; border: 2px dashed #1a73e8; padding: 20px; margin: 25px 0; text-align: center; border-radius: 8px; }
            .code { font-size: 32px; font-weight: 800; letter-spacing: 8px; color: #1a73e8; display: inline-block; padding: 10px 30px; background-color: white; border-radius: 5px; box-shadow: 0 3px 6px rgba(0,0,0,0.1); }
            .important { font-weight: bold; color: #d93025; }
            .footer { font-size: 12px; text-align: center; margin-top: 30px; color: #777; }
            .button { display: inline-block; background-color: #1a73e8; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; margin: 15px 0; }
            .note { font-size: 14px; color: #5f6368; font-style: italic; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2 style='margin: 0; font-size: 24px;'>Réinitialisation de mot de passe</h2>
            </div>
            <div class='content'>
                <p style='font-size: 18px;'><strong>Bonjour" . ($toName ? " $toName" : "") . ",</strong></p>
                <p>Vous avez demandé la réinitialisation de votre mot de passe pour votre compte ENSAH.</p>

                <p style='font-weight: bold; font-size: 18px; margin-bottom: 5px;'>Voici votre code de réinitialisation :</p>

                <div class='code-container'>
                    <div class='code'>$code</div>
                </div>

                <p class='note'>Saisissez ce code sur la page de réinitialisation de mot de passe pour confirmer votre identité.</p>

                <p><span class='important'>Important :</span> Ce code est valable pendant <strong>15 minutes</strong> seulement.</p>

                <p>Si vous n'avez pas demandé cette réinitialisation, veuillez ignorer cet email et vérifier la sécurité de votre compte.</p>

                <p>Cordialement,<br><strong>L'équipe ENSAH</strong></p>
            </div>
            <div class='footer'>
                <p>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</p>
                <p>© " . date('Y') . " École Nationale des Sciences Appliquées Al Hoceima. Tous droits réservés.</p>
            </div>
        </div>
    </body>
    </html>
    ";

    return sendEmail($to, $subject, $message);
}
?>
