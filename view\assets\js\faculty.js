// Global variables
let faculty = [];
let currentPage = 1;
let facultyPerPage = 20;
let cniSearch, roleFilter, departmentFilter, addFacultyForm, facultyTable;

document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    cniSearch = document.getElementById('cniSearch');
    roleFilter = document.getElementById('roleFilter');
    departmentFilter = document.getElementById('departmentFilter');
    addFacultyForm = document.getElementById('addFacultyForm');
    facultyTable = document.getElementById('facultyTable');

    // Load departments
    async function loadDepartments() {
        try {
            const response = await fetch('../../route/departementRoute.php');
            if (!response.ok) throw new Error('Failed to fetch departments');

            const departments = await response.json();
            if (departments.error) throw new Error(departments.error);

            updateDepartmentDropdowns(departments);
        } catch (error) {
            console.error('Error loading departments:', error);
            showAlert('Error loading departments', 'danger');
        }
    }

    // Load specialties
    async function loadSpecialties() {
        try {
            const response = await fetch('../../route/specialiteRoute.php');
            if (!response.ok) throw new Error('Failed to fetch specialties');

            const specialties = await response.json();
            if (specialties.error) throw new Error(specialties.error);

            updateSpecialtyDropdowns(specialties.data || specialties);
        } catch (error) {
            console.error('Error loading specialties:', error);
            showAlert('Error loading specialties', 'danger');
        }
    }

    // Update department dropdowns
    function updateDepartmentDropdowns(departments) {
        const filterSelect = document.getElementById('departmentFilter');
        const modalSelect = document.getElementById('department');

        filterSelect.innerHTML = '<option value="">All Departments</option>';
        modalSelect.innerHTML = '';

        departments.forEach(department => {
            filterSelect.appendChild(createOption(department.id_departement, department.nom_dep));
            modalSelect.appendChild(createOption(department.id_departement, department.nom_dep));
        });
    }

    // Update specialty dropdowns
    function updateSpecialtyDropdowns(specialties) {
        const modalSelect = document.getElementById('specialite');
        if (!modalSelect) return;

        modalSelect.innerHTML = '<option value="">Select Specialty</option>';

        specialties.forEach(specialty => {
            modalSelect.appendChild(createOption(specialty.id || specialty.id_specialite, specialty.nom || specialty.nom_specialite));
        });
    }

    function createOption(value, text) {
        const option = document.createElement('option');
        option.value = value;
        option.textContent = text;
        return option;
    }

    // Load faculty data
    async function loadFaculty() {
        try {
            const response = await fetch('../../route/enseignantRoute.php');
            if (!response.ok) throw new Error('Failed to fetch faculty');

            const data = await response.json();
            if (data.error) throw new Error(data.error);

            faculty = data.data;
            displayFaculty(faculty);
        } catch (error) {
            console.error('Error loading faculty:', error);
            showAlert('Error loading faculty data', 'danger');
        }
    }

    // Update pagination controls
    function updatePaginationControls(current, total, totalFaculty) {
        if (total === 0) return;

        const paginationContainer = document.querySelector('.card.faculty-table .card-body');
        const paginationDiv = document.createElement('div');
        paginationDiv.className = 'pagination-controls d-flex flex-wrap justify-content-between align-items-center mt-3 gap-3';
        paginationDiv.style.borderTop = '1px solid #dee2e6';
        paginationDiv.style.paddingTop = '1rem';

        // Remove existing pagination controls if any
        const existingPagination = paginationContainer.querySelector('.pagination-controls');
        if (existingPagination) {
            existingPagination.remove();
        }

        // Create page number buttons
        let pageButtons = '';
        const maxVisiblePages = 5;
        const halfVisible = Math.floor(maxVisiblePages / 2);
        let startPage = Math.max(1, current - halfVisible);
        let endPage = Math.min(total, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        // Add first page button if not visible
        if (startPage > 1) {
            pageButtons += `
                <button class="btn btn-sm btn-outline-primary mx-1" onclick="changePage(1)">1</button>
                ${startPage > 2 ? '<span class="mx-2">...</span>' : ''}`;
        }

        // Add visible page buttons
        for (let i = startPage; i <= endPage; i++) {
            pageButtons += `
                <button class="btn btn-sm ${i === current ? 'btn-primary' : 'btn-outline-primary'} mx-1"
                        onclick="changePage(${i})">
                    ${i}
                </button>`;
        }

        // Add last page button if not visible
        if (endPage < total) {
            pageButtons += `
                ${endPage < total - 1 ? '<span class="mx-2">...</span>' : ''}
                <button class="btn btn-sm btn-outline-primary mx-1" onclick="changePage(${total})">${total}</button>`;
        }

        paginationDiv.innerHTML = `
            <div class="d-flex align-items-center">
                <span>Showing ${Math.min((current - 1) * facultyPerPage + 1, totalFaculty)} - ${Math.min(current * facultyPerPage, totalFaculty)} of ${totalFaculty} faculty members</span>
            </div>
            <div class="pagination-buttons">
                <button class="btn btn-sm btn-outline-primary me-2" ${current === 1 ? 'disabled' : ''} onclick="changePage(${current - 1})">
                    <i class="bi bi-chevron-left"></i> Previous
                </button>
                ${pageButtons}
                <button class="btn btn-sm btn-outline-primary ms-2" ${current === total ? 'disabled' : ''} onclick="changePage(${current + 1})">
                    Next <i class="bi bi-chevron-right"></i>
                </button>
            </div>
        `;

        paginationContainer.appendChild(paginationDiv);
    }

    // Handle form submission
    if (addFacultyForm) {
        addFacultyForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(addFacultyForm);
            const facultyData = Object.fromEntries(formData.entries());

            try {
                const isEdit = document.querySelector('#addFacultyModal .modal-title').textContent === 'Edit Faculty';
                const cni = isEdit ? addFacultyForm.dataset.originalCni : facultyData.CNI;
                const method = isEdit ? 'PUT' : 'POST';
                const url = '../../route/enseignantRoute.php' + (isEdit ? `?cni=${cni}` : '');

                // Handle file upload if present
                let profilePicturePath = null;
                if (facultyData.profile_picture && facultyData.profile_picture instanceof File) {
                    // Create FormData for file upload
                    const fileData = new FormData();
                    fileData.append('profile_picture', facultyData.profile_picture);

                    try {
                        const uploadResponse = await fetch('../../route/uploadRoute.php', {
                            method: 'POST',
                            body: fileData
                        });

                        if (uploadResponse.ok) {
                            const uploadResult = await uploadResponse.json();
                            if (uploadResult.success && uploadResult.filePath) {
                                profilePicturePath = uploadResult.filePath;
                            }
                        }
                    } catch (error) {
                        console.error('Error uploading profile picture:', error);
                    }
                }

                // Format the data according to the database schema
                const formattedData = {
                    CNI: facultyData.CNI,
                    nom: facultyData.nom,
                    prenom: facultyData.prenom,
                    email: facultyData.email,
                    tele: facultyData.tele || null,
                    date_naissance: facultyData.date_naissance || null,
                    lieu_naissance: facultyData.lieu_naissance || null,
                    sexe: facultyData.sexe,
                    ville: facultyData.ville || null,
                    pays: facultyData.pays || null,
                    role: facultyData.role || 'enseignant',
                    // Utiliser id_departement pour correspondre au modèle
                    id_departement: facultyData.department || null,
                    // Ajouter id_specialite
                    id_specialite: facultyData.specialite || null,
                    // Traiter correctement la date de début
                    date_debut_travail: facultyData.date_debut_travail ? facultyData.date_debut_travail : null,
                    // Ajouter le chemin de l'image de profil
                    profile_picture: profilePicturePath,
                    createAccount: facultyData.createAccount === 'on' // Convert checkbox value to boolean
                };

                // Log des données pour le débogage
                console.log('Données formatées pour l\'envoi:', formattedData);

                // Log des données pour le débogage
                console.log('Sending data:', formattedData);

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formattedData)
                });

                // Log de la réponse pour le débogage
                const responseText = await response.text();
                console.log('Response text:', responseText);

                // Essayer de parser la réponse comme JSON
                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (e) {
                    console.error('Error parsing JSON response:', e);
                    throw new Error('Invalid JSON response from server: ' + responseText);
                }

                if (!response.ok) {
                    throw new Error(result.error || 'Failed to save faculty');
                }

                // Close modal and reload data
                const modal = bootstrap.Modal.getInstance(document.getElementById('addFacultyModal'));
                if (modal) {
                    modal.hide();
                }

                // Reset form
                addFacultyForm.reset();
                delete addFacultyForm.dataset.originalCni;
                document.querySelector('#addFacultyModal .modal-title').textContent = 'Add Faculty';

                // Reload faculty data
                await loadFaculty();

                // Show success message
                if (formattedData.createAccount && !isEdit) {
                    // Custom message for account creation
                    document.getElementById('successMessage').innerHTML = `
                        Faculty member saved successfully.<br>
                        <span class="text-success"><i class="bi bi-person-check me-1"></i>User account created with username: ${formattedData.CNI}</span>
                    `;
                    const successModal = new bootstrap.Modal(document.getElementById('successModal'));
                    successModal.show();
                } else {
                    showAlert('Faculty saved successfully', 'success');
                }
            } catch (error) {
                console.error('Error saving faculty:', error);
                showAlert(error.message || 'Error saving faculty', 'danger');
            }
        });
    } else {
        console.error('Form element #addFacultyForm not found');
    }

    // Filter faculty function
    async function filterFaculty() {
        const roleValue = roleFilter ? roleFilter.value : '';
        const departmentValue = departmentFilter ? departmentFilter.value : '';
        const searchValue = cniSearch ? cniSearch.value.toLowerCase() : '';

        try {
            const response = await fetch('../../route/enseignantRoute.php');
            if (!response.ok) throw new Error('Failed to fetch faculty');

            const data = await response.json();
            if (data.error) throw new Error(data.error);
            if (!Array.isArray(data.data)) throw new Error('Invalid data format');

            faculty = data.data.filter(member => {
                if (!member) return false;

                // Handle role matching (case-insensitive)
                const memberRole = (member.role || 'enseignant').toLowerCase();
                const searchRole = roleValue.toLowerCase();
                const matchesRole = !roleValue || memberRole === searchRole;

                // Handle department matching
                const memberDepartmentId = member.id_departement ? member.id_departement.toString() : '';
                const matchesDepartment = !departmentValue || memberDepartmentId === departmentValue;

                // Handle search matching
                const matchesSearch = !searchValue ||
                    ['CNI', 'nom', 'prenom', 'email'].some(field =>
                        member[field] && member[field].toString().toLowerCase().includes(searchValue)
                    );

                return matchesRole && matchesDepartment && matchesSearch;
            });

            currentPage = 1;
            displayFaculty(faculty);
        } catch (error) {
            console.error('Error filtering faculty:', error);
            showAlert('Error filtering faculty members', 'danger');
        }
    }

    // Add filter event listeners
    if (departmentFilter) departmentFilter.addEventListener('change', filterFaculty);
    if (roleFilter) roleFilter.addEventListener('change', filterFaculty);
    if (cniSearch) cniSearch.addEventListener('input', filterFaculty);

    // Fonction pour charger les rôles disponibles
    function loadRoles() {
        try {
            // Utiliser des rôles codés en dur puisque nous les connaissons déjà
            const roles = ['enseignant', 'chef de departement', 'coordinateur', 'vacataire', 'admin'];
            console.log('Roles disponibles (codés en dur):', roles);
            updateRoleDropdowns(roles);
        } catch (error) {
            console.error('Error loading roles:', error);
            showAlert('Error loading roles', 'danger');
        }
    }

    // Mettre à jour les listes déroulantes de rôles
    function updateRoleDropdowns(roles) {
        const filterSelect = document.getElementById('roleFilter');
        const modalSelect = document.getElementById('role');

        // Vider les listes déroulantes seulement si nous avons des rôles à ajouter
        if (roles && roles.length > 0) {
            filterSelect.innerHTML = '<option value="">All Roles</option>';
            modalSelect.innerHTML = '';
        }

        // Ajouter les rôles disponibles
        roles.forEach(role => {
            let displayName = '';
            // Utiliser la fonction formatRoleName pour la cohérence
            displayName = formatRoleName(role);

            console.log(`Ajout du rôle: ${role} (affiché comme: ${displayName})`);

            filterSelect.appendChild(createOption(role, displayName));
            modalSelect.appendChild(createOption(role, displayName));
        });

        console.log('Nombre d\'options dans le filtre:', filterSelect.options.length);
        console.log('Nombre d\'options dans le modal:', modalSelect.options.length);

        // Si aucune option n'a été ajoutée au modal (sauf l'option vide), ajouter des options par défaut
        if (modalSelect.options.length === 0) {
            console.log('Aucune option ajoutée, ajout des options par défaut');
            const defaultRoles = [
                { value: 'enseignant', text: 'Enseignant' },
                { value: 'chef de departement', text: 'Chef de Département' },
                { value: 'coordinateur', text: 'Coordinateur' },
                { value: 'vacataire', text: 'Vacataire' },
                { value: 'admin', text: 'Administrateur' }
            ];

            defaultRoles.forEach(role => {
                modalSelect.appendChild(createOption(role.value, role.text));
            });

            console.log('Options par défaut ajoutées, nouveau nombre d\'options:', modalSelect.options.length);
        }
    }

    // Initialize data loading
    loadDepartments();
    loadSpecialties();
    loadRoles();
    loadFaculty();

    // Add event listener for modal close to reset form
    const addFacultyModal = document.getElementById('addFacultyModal');
    if (addFacultyModal) {
        addFacultyModal.addEventListener('hidden.bs.modal', function () {
            const form = document.getElementById('addFacultyForm');
            if (form) {
                form.reset();
                document.querySelector('#addFacultyModal .modal-title').textContent = 'Add Faculty';
            }
        });
    }
});


// Fonction pour formater les noms de rôles
function formatRoleName(role) {
    switch(role) {
        case 'enseignant':
            return 'Enseignant';
        case 'chef de departement':
            return 'Chef de Département';
        case 'coordinateur':
            return 'Coordinateur';
        case 'vacataire':
            return 'Vacataire';
        case 'admin':
            return 'Administrateur';
        default:
            return role.charAt(0).toUpperCase() + role.slice(1);
    }
}

// Global functions for faculty management
async function editFaculty(facultyId) {
    try {
        const row = document.querySelector(`tr button[onclick="editFaculty(${facultyId})"]`).closest('tr');
        const cni = row.cells[0].textContent;

        if (!cni) throw new Error('Could not find faculty CNI');

        const response = await fetch(`../../route/enseignantRoute.php?cni=${cni}`);
        if (!response.ok) throw new Error('Failed to fetch faculty data');

        const data = await response.json();
        if (data.error) throw new Error(data.error);

        const faculty = data.data;
        if (!faculty) throw new Error('No faculty data received');

        const form = document.getElementById('addFacultyForm');
        form.reset();

        // Store the original CNI for update operation
        form.dataset.originalCni = cni;

        // Update modal title
        document.querySelector('#addFacultyModal .modal-title').textContent = 'Edit Faculty';

        // Populate form fields
        Object.keys(faculty).forEach(key => {
            const element = form.elements[key] || form.elements[key.toLowerCase()];
            if (element && faculty[key] !== null) {
                if (key === 'id_departement') {
                    // Handle department selection
                    const departmentSelect = form.elements['department'];
                    if (departmentSelect) {
                        departmentSelect.value = faculty[key];
                    }
                } else if (key === 'role') {
                    // Handle role selection
                    console.log('Rôle de l\'enseignant:', faculty[key]);
                    const roleSelect = form.elements['role'];
                    if (roleSelect) {
                        // Si le rôle est 'normal', le convertir en 'enseignant'
                        const roleValue = faculty[key] === 'normal' ? 'enseignant' : faculty[key];
                        roleSelect.value = roleValue;
                        console.log('Valeur du select de rôle définie à:', roleValue);
                    }
                } else if (key === 'id_specialite') {
                    // Handle specialty selection
                    console.log('Spécialité de l\'enseignant:', faculty[key]);
                    const specialiteSelect = form.elements['specialite'];
                    if (specialiteSelect && faculty[key]) {
                        specialiteSelect.value = faculty[key];
                        console.log('Valeur du select de spécialité définie à:', faculty[key]);
                    }
                } else if (key === 'profile_picture') {
                    // Handle profile picture display
                    console.log('Image de profil de l\'enseignant:', faculty[key]);
                    // We can't set the value of a file input for security reasons,
                    // but we can display the current image somewhere if needed
                    if (faculty[key]) {
                        // Create or update a preview element
                        let previewContainer = document.getElementById('profile-picture-preview');
                        if (!previewContainer) {
                            previewContainer = document.createElement('div');
                            previewContainer.id = 'profile-picture-preview';
                            previewContainer.className = 'mt-2';
                            const fileInput = form.elements['profile_picture'];
                            if (fileInput) {
                                fileInput.parentNode.appendChild(previewContainer);
                            }
                        }

                        previewContainer.innerHTML = `
                            <div class="d-flex align-items-center">
                                <img src="${faculty[key]}" alt="Profile" class="img-thumbnail me-2" style="max-width: 100px; max-height: 100px;">
                                <span class="text-muted small">Current profile picture</span>
                            </div>
                        `;
                    }
                } else if (key === 'date_debut_travail' || key === 'date_naissance') {
                    // Traiter correctement les dates
                    console.log(`Date ${key}:`, faculty[key]);
                    if (element && faculty[key]) {
                        // Formater la date au format YYYY-MM-DD pour les champs de type date
                        try {
                            const date = new Date(faculty[key]);
                            if (!isNaN(date.getTime())) {
                                const formattedDate = date.toISOString().split('T')[0];
                                element.value = formattedDate;
                                console.log(`Valeur formatée pour ${key}:`, formattedDate);
                            } else {
                                console.warn(`Date invalide pour ${key}:`, faculty[key]);
                                element.value = faculty[key]; // Utiliser la valeur telle quelle
                            }
                        } catch (e) {
                            console.error(`Erreur lors du formatage de la date ${key}:`, e);
                            element.value = faculty[key]; // Utiliser la valeur telle quelle
                        }
                    }
                } else {
                    element.value = faculty[key];
                }
            }
        });

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('addFacultyModal'));
        modal.show();
    } catch (error) {
        console.error('Error fetching faculty:', error);
        showAlert(error.message || 'Error loading faculty data', 'danger');
    }
}

async function deleteFaculty(facultyId) {
    if (!confirm('Are you sure you want to delete this faculty member?')) {
        return;
    }

    try {
        const row = document.querySelector(`tr button[onclick="deleteFaculty(${facultyId})"]`).closest('tr');
        const cni = row.cells[0].textContent;

        if (!cni) throw new Error('Could not find faculty CNI');

        const deleteResponse = await fetch(`../../route/enseignantRoute.php?cni=${cni}`, {
            method: 'DELETE'
        });

        const result = await deleteResponse.json();

        if (!deleteResponse.ok) {
            throw new Error(result.error || 'Failed to delete faculty');
        }

        // Supprimer la ligne du tableau immédiatement
        row.remove();

        // Recharger les données pour mettre à jour la pagination
        await loadFaculty();
        showAlert('Faculty deleted successfully', 'success');
    } catch (error) {
        console.error('Error deleting faculty:', error);
        showAlert(error.message || 'Error deleting faculty', 'danger');
        // Recharger les données même en cas d'erreur pour assurer la synchronisation
        await loadFaculty();
    }
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.student-management-container');
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout(() => alertDiv.remove(), 5000);
}

// Display faculty in the table with pagination
function displayFaculty(facultyData) {
    if (!facultyTable) return;

    const tbody = facultyTable.querySelector('tbody');
    tbody.innerHTML = '';

    // Clear mobile cards container
    const facultyCardsContainer = document.getElementById('facultyCards');
    if (facultyCardsContainer) {
        facultyCardsContainer.innerHTML = '';
    }

    // Calculate pagination
    const totalPages = Math.max(1, Math.ceil(facultyData.length / facultyPerPage));
    if (currentPage > totalPages) {
        currentPage = totalPages;
    }
    const start = (currentPage - 1) * facultyPerPage;
    const end = start + facultyPerPage;
    const paginatedFaculty = facultyData.slice(start, end);

    // Display paginated faculty
    paginatedFaculty.forEach(member => {
        // Create table row for desktop view
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${member.CNI}</td>
            <td>${member.nom}</td>
            <td>${member.prenom}</td>
            <td>${member.email}</td>
            <td>${member.sexe}</td>
            <td>${formatRoleName(member.role || 'enseignant')}</td>
            <td>${member.departement || ''}</td>
            <td>${member.specialite || ''}</td>
            <td class="student-actions text-center">
                <button class="btn btn-sm btn-primary" onclick="editFaculty(${member.id_enseignant})">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteFaculty(${member.id_enseignant})">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);

        // Create card for mobile view
        if (facultyCardsContainer) {
            const card = document.createElement('div');
            card.className = 'student-card';
            card.innerHTML = `
                <div class="student-card-header">
                    <h5>${member.nom} ${member.prenom}</h5>
                    <span class="badge">${formatRoleName(member.role || 'enseignant')}</span>
                </div>
                <div class="student-card-body">
                    <ul class="student-card-info">
                        <li>
                            <span class="student-card-label">CNI</span>
                            <span class="student-card-value">${member.CNI}</span>
                        </li>
                        <li>
                            <span class="student-card-label">Email</span>
                            <span class="student-card-value">${member.email}</span>
                        </li>
                        <li>
                            <span class="student-card-label">Gender</span>
                            <span class="student-card-value">${member.sexe}</span>
                        </li>
                        <li>
                            <span class="student-card-label">Department</span>
                            <span class="student-card-value">${member.departement || ''}</span>
                        </li>
                        <li>
                            <span class="student-card-label">Specialty</span>
                            <span class="student-card-value">${member.specialite || ''}</span>
                        </li>
                    </ul>
                    <div class="student-card-actions">
                        <button class="btn btn-info" onclick="editFaculty(${member.id_enseignant})">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-danger" onclick="deleteFaculty(${member.id_enseignant})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            facultyCardsContainer.appendChild(card);
        }
    });

    // Update pagination controls
    updatePaginationControls(currentPage, totalPages, facultyData.length);
}

// Update pagination controls
function updatePaginationControls(current, total, totalFaculty) {
    if (total === 0) return;

    const paginationContainer = document.querySelector('.card.student-table .card-body');
    if (!paginationContainer) return;

    const paginationDiv = document.createElement('div');
    paginationDiv.className = 'pagination-controls d-flex flex-wrap justify-content-between align-items-center mt-3 gap-3';
    paginationDiv.style.borderTop = '1px solid #dee2e6';
    paginationDiv.style.paddingTop = '1rem';

    // Remove existing pagination controls if any
    const existingPagination = paginationContainer.querySelector('.pagination-controls');
    if (existingPagination) {
        existingPagination.remove();
    }

    // Create page number buttons
    let pageButtons = '';
    const maxVisiblePages = 5;
    const halfVisible = Math.floor(maxVisiblePages / 2);
    let startPage = Math.max(1, current - halfVisible);
    let endPage = Math.min(total, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // Add first page button if not visible
    if (startPage > 1) {
        pageButtons += `
            <button class="btn btn-sm btn-outline-primary mx-1" onclick="changePage(1)">1</button>
            ${startPage > 2 ? '<span class="mx-2">...</span>' : ''}`;
    }

    // Add visible page buttons
    for (let i = startPage; i <= endPage; i++) {
        pageButtons += `
            <button class="btn btn-sm ${i === current ? 'btn-primary' : 'btn-outline-primary'} mx-1"
                    onclick="changePage(${i})">
                ${i}
            </button>`;
    }

    // Add last page button if not visible
    if (endPage < total) {
        pageButtons += `
            ${endPage < total - 1 ? '<span class="mx-2">...</span>' : ''}
            <button class="btn btn-sm btn-outline-primary mx-1" onclick="changePage(${total})">${total}</button>`;
    }

    paginationDiv.innerHTML = `
        <div class="d-flex align-items-center">
            <span>Showing ${Math.min((current - 1) * facultyPerPage + 1, totalFaculty)} - ${Math.min(current * facultyPerPage, totalFaculty)} of ${totalFaculty} faculty members</span>
        </div>
        <div class="pagination-buttons">
            <button class="btn btn-sm btn-outline-primary me-2" ${current === 1 ? 'disabled' : ''} onclick="changePage(${current - 1})">
                <i class="bi bi-chevron-left"></i> Previous
            </button>
            ${pageButtons}
            <button class="btn btn-sm btn-outline-primary ms-2" ${current === total ? 'disabled' : ''} onclick="changePage(${current + 1})">
                Next <i class="bi bi-chevron-right"></i>
            </button>
        </div>
    `;

    paginationContainer.appendChild(paginationDiv);
}

// Change page function
function changePage(newPage) {
    if (!Array.isArray(faculty) || faculty.length === 0) {
        return;
    }
    const totalPages = Math.max(1, Math.ceil(faculty.length / facultyPerPage));
    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        displayFaculty(faculty);
    }
}

// Make functions globally accessible
window.editFaculty = editFaculty;
window.deleteFaculty = deleteFaculty;
window.changePage = changePage;
