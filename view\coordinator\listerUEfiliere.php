<?php
// Temporarily disable authentication for testing
// require_once '../includes/auth_check_coordinateur.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Debug information
error_log("[DEBUG] Coordinator session info in listerUEfiliere.php:");
error_log("[DEBUG] Username: " . ($_SESSION['user']['username'] ?? 'not set'));
error_log("[DEBUG] Role: " . ($_SESSION['user']['role'] ?? 'not set'));
error_log("[DEBUG] Filiere ID: " . ($_SESSION['user']['filiere_id'] ?? 'not set'));
error_log("[DEBUG] Filiere Name: " . ($_SESSION['user']['filiere_name'] ?? 'not set'));

// For testing purposes, set a default filiere_id if not set
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = [
        'username' => 'test_coordinator',
        'role' => 'coordinateur',
        'filiere_id' => 1, // Default to filiere ID 1 (informatique)
        'filiere_name' => 'Informatique'
    ];
    error_log("[DEBUG] Created test coordinator session");
}

// Get the coordinator's filiere_id from the session
$coordinatorFiliereId = $_SESSION['user']['filiere_id'] ?? 1; // Default to 1 if not set

// If filiere_id is not in session, get it from the database
if (empty($coordinatorFiliereId) && isset($_SESSION['user']['username'])) {
    require_once '../../model/listerUEfiliereModel.php';
    $filiereId = getCoordinatorFiliereId($_SESSION['user']['username']);

    if (!is_array($filiereId)) {
        $coordinatorFiliereId = $filiereId;
        $_SESSION['user']['filiere_id'] = $filiereId;
        error_log("[DEBUG] Updated filiere_id in session: " . $filiereId);
    } else {
        error_log("[ERROR] Could not get filiere_id for coordinator: " . json_encode($filiereId));
        // Set a default filiere_id if not found
        $coordinatorFiliereId = 1;
        $_SESSION['user']['filiere_id'] = 1;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teaching Units by Filière - UniAdmin</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/listerUEfiliere.css">

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <style>
        /* Custom responsive sidebar styles */
        @media (min-width: 992px) {
            .sidebar {
                width: 280px;
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                z-index: 1030;
                overflow-y: auto;
            }

            .main-content {
                margin-left: 280px;
                width: calc(100% - 280px);
            }
        }

        @media (max-width: 991.98px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: -280px;
                width: 280px;
                height: 100vh;
                z-index: 1030;
                overflow-y: auto;
                transition: transform 0.3s ease;
            }

            .sidebar.show {
                transform: translateX(280px);
            }

            .main-content {
                margin-left: 0;
                width: 100%;
            }

            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(3px);
                z-index: 1020;
                display: none;
            }

            .sidebar-overlay.show {
                display: block;
            }
        }
    </style>
</head>

<body>
    <div class="dashboard-container">
        <!-- Sidebar overlay for mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay"></div>

        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <?php include_once '../includes/sidebar.php'; ?>
        </div>

        <!-- Main content -->
        <div class="main-content">
            <!-- Header -->
            <?php include_once '../includes/header.php'; ?>

            <div class="content-wrapper">
                <div class="pt-3 pb-2 mb-3">
                    <h1 class="h2 mb-0"><i class="bi bi-book me-2"></i>Modules of Field</h1>
                    <div class="border-bottom mt-2" style="width: 100px;"></div>
                </div>

                <!-- Alert container for messages -->
                <div id="alertContainer"></div>

                <!-- Hidden input field for coordinator's filiere_id -->
                <input type="hidden" id="coordinator-filiere-id" value="<?php echo htmlspecialchars($coordinatorFiliereId); ?>">

                <!-- Teaching Units Container -->
                <div class="card mt-4">
                    <div class="card-header d-flex justify-content-between align-items-center bg-white">
                        <h5 class="mb-0"><i class="bi bi-grid-3x3-gap me-2"></i>Teaching Units</h5>
                        <div>
                            <button type="button" class="btn btn-sm btn-pastel-blue" onclick="window.location.href='descriptif.php'">
                                <i class="bi bi-plus-circle"></i> Add Unit
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Filters -->
                        <div class="row mb-3">
                            <div class="col-md-3 mb-2 mb-md-0">
                                <div class="input-group">
                                    <span class="input-group-text bg-pastel-blue border-0"><i class="bi bi-bar-chart"></i></span>
                                    <select id="filterNiveau" class="form-select border-start-0">
                                        <option value="">All Levels</option>
                                        <!-- Levels will be populated dynamically -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3 mb-2 mb-md-0">
                                <div class="input-group">
                                    <span class="input-group-text bg-pastel-blue border-0"><i class="bi bi-calendar3"></i></span>
                                    <select id="filterSemestre" class="form-select border-start-0">
                                        <option value="">All Semesters</option>
                                        <!-- Semesters will be populated dynamically -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3 mb-2 mb-md-0">
                                <div class="input-group">
                                    <span class="input-group-text bg-pastel-blue border-0"><i class="bi bi-layers"></i></span>
                                    <select id="filterType" class="form-select border-start-0">
                                        <option value="">All Types</option>
                                        <option value="Cours">Cours</option>
                                        <option value="TD">TD</option>
                                        <option value="TP">TP</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <span class="input-group-text bg-pastel-blue border-0"><i class="bi bi-search"></i></span>
                                    <input type="text" id="searchInput" class="form-control border-start-0" placeholder="Search...">
                                </div>
                            </div>
                        </div>

                        <!-- Teaching Units Table -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Module</th>
                                        <th>Type</th>
                                        <th>Volume Horaire</th>
                                        <th>Level</th>
                                        <th>Semester</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="unitesList">
                                    <!-- Teaching units will be loaded here -->
                                    <tr>
                                        <td colspan="6" class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- Pagination will be generated dynamically -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/listerUEfiliere.js"></script>

    <!-- Sidebar toggle functionality -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile sidebar toggle
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebarOverlay');

            function toggleSidebar() {
                sidebar.classList.toggle('show');
                sidebarOverlay.classList.toggle('show');

                // Prevent body scrolling when sidebar is open
                if (sidebar.classList.contains('show')) {
                    document.body.style.overflow = 'hidden';
                } else {
                    document.body.style.overflow = 'auto';
                }
            }

            if (sidebarToggle && sidebar && sidebarOverlay) {
                sidebarToggle.addEventListener('click', toggleSidebar);
                sidebarOverlay.addEventListener('click', toggleSidebar);
            }
        });
    </script>
</body>
</html>