/* Styles personnalisés pour la page import_grades.php - UNIQUEMENT pour le contenu principal */

/* Variables de couleurs */
:root {
    --primary-gradient: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    --secondary-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --success-gradient: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
    --info-gradient: linear-gradient(135deg, #e0c3fc 0%, #8ec5fc 100%);
    --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    --light-gradient: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    --dark-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Correction du header pour qu'il soit identique à celui du dashboard */
.header {
    height: 100px !important;
    background-color: white !important;
    border-bottom: 1px solid var(--gray-200) !important;
}

.header .search-bar form {
    position: relative !important;
}

.header .search-icon {
    left: 10px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    color: var(--gray-500) !important;
}

.header .search-input {
    padding-left: 2rem !important;
    background-color: var(--gray-100) !important;
    border: 1px solid var(--gray-300) !important;
    border-radius: 0.25rem !important;
    height: calc(1.5em + 0.75rem + 2px) !important;
    padding: 0.375rem 0.75rem 0.375rem 2rem !important;
    font-size: 1rem !important;
}

.header .notification-badge {
    position: absolute !important;
    top: -5px !important;
    right: -5px !important;
    width: 18px !important;
    height: 18px !important;
    font-size: 10px !important;
    background-color: var(--royal-blue) !important;
    color: white !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.header .btn {
    padding: 0.375rem 0.75rem !important;
    font-size: 1rem !important;
    line-height: 1.5 !important;
    border-radius: 0.25rem !important;
    background: none !important;
    border: none !important;
    box-shadow: none !important;
}

.header .btn-link {
    color: #212529 !important;
    text-decoration: none !important;
}

.header .dropdown-toggle::after {
    display: inline-block !important;
    margin-left: 0.255em !important;
    vertical-align: 0.255em !important;
    content: "" !important;
    border-top: 0.3em solid !important;
    border-right: 0.3em solid transparent !important;
    border-bottom: 0 !important;
    border-left: 0.3em solid transparent !important;
}

.header .dropdown-menu {
    padding: 0.5rem 0 !important;
    margin: 0.125rem 0 0 !important;
    font-size: 1rem !important;
    color: #212529 !important;
    background-color: #fff !important;
    border: 1px solid rgba(0, 0, 0, 0.15) !important;
    border-radius: 0.25rem !important;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Styles du contenu principal uniquement */
.main-content .page-title {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 700;
    margin-bottom: 0.5rem;
    animation: fadeIn 1s ease-in-out;
}

/* Styles des cartes de filtres */
.main-content .filter-card {
    background: var(--light-gradient);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: none;
    animation: slideIn 0.5s ease-in-out;
}

.main-content .filter-card:hover {
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.main-content .filter-title {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: #4a4a4a;
    position: relative;
    padding-bottom: 10px;
}

.main-content .filter-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 3px;
}

/* Styles des formulaires - uniquement dans le contenu principal */
.main-content .form-select,
.main-content .form-control {
    border-radius: 10px;
    border: 1px solid #e0e0e0;
    padding: 12px 15px;
    transition: all 0.3s ease;
    box-shadow: none;
}

.main-content .form-select:focus,
.main-content .form-control:focus {
    border-color: #8ec5fc;
    box-shadow: 0 0 0 0.25rem rgba(142, 197, 252, 0.25);
}

.main-content .form-label {
    font-weight: 600;
    color: #555;
    margin-bottom: 8px;
}

/* Styles des boutons - uniquement dans le contenu principal */
.main-content .container-fluid .btn {
    border-radius: 10px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
}

.main-content .container-fluid .btn-primary {
    background: var(--primary-gradient);
}

.main-content .container-fluid .btn-primary:hover {
    background: linear-gradient(135deg, #5a01cb 0%, #1565fc 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

/* Styles des alertes */
.main-content .alert-upload {
    display: none;
    margin-top: 20px;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    opacity: 0;
}

.main-content .alert-success {
    background: var(--success-gradient);
    border: none;
    color: #1e6641;
}

.main-content .alert-danger {
    background: var(--danger-gradient);
    border: none;
    color: #721c24;
}

.main-content .alert-info {
    background: var(--info-gradient);
    border: none;
    color: #0c5460;
}

/* Styles des cartes de module */
.main-content .module-card {
    background: var(--info-gradient);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    animation: fadeIn 0.5s ease-in-out;
}

.main-content .module-card h4 {
    color: #4a4a4a;
    font-weight: 700;
    margin-bottom: 15px;
}

.main-content .teacher-card {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 10px;
    padding: 15px;
    margin-top: 15px;
    transition: all 0.3s ease;
}

.main-content .coordinator-card {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 10px;
    padding: 15px;
    margin-top: 15px;
    transition: all 0.3s ease;
}

/* Styles de la section d'affichage des PDFs */
.main-content .pdf-display-section {
    background: var(--light-gradient);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    animation: fadeIn 0.5s ease-in-out;
}

.main-content .section-title {
    color: #4a4a4a;
    font-weight: 700;
    border-bottom: 2px solid rgba(0, 0, 0, 0.1);
    padding-bottom: 15px;
    margin-bottom: 25px;
    position: relative;
}

.main-content .section-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 80px;
    height: 2px;
    background: var(--primary-gradient);
}

.main-content .pdf-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    animation: slideIn 0.5s ease-in-out;
}

.main-content .pdf-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.main-content .pdf-icon {
    color: #e74c3c;
    font-size: 2.5rem;
    margin-right: 20px;
    transition: all 0.3s ease;
}

.main-content .pdf-card:hover .pdf-icon {
    transform: scale(1.1);
}

.main-content .pdf-info {
    flex: 1;
}

.main-content .pdf-title {
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.main-content .pdf-meta {
    color: #777;
    font-size: 0.9rem;
}

.main-content .pdf-actions {
    display: flex;
    gap: 15px;
}

.main-content .pdf-view-btn,
.main-content .pdf-download-btn {
    background: var(--info-gradient);
    color: #333;
    border: none;
    border-radius: 8px;
    padding: 8px 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.main-content .pdf-view-btn:hover {
    background: linear-gradient(135deg, #d0b3fc 0%, #7eb5fc 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

.main-content .pdf-download-btn {
    background: var(--success-gradient);
}

.main-content .pdf-download-btn:hover {
    background: linear-gradient(135deg, #74eaa0 0%, #7ec3e4 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

/* Styles pour le spinner de chargement */
.main-content .loading-spinner {
    display: none;
    text-align: center;
    margin: 30px 0;
    animation: fadeIn 0.5s ease-in-out;
}

.main-content .spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.25rem;
    color: #8ec5fc;
}
