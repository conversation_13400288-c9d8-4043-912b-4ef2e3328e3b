html, body {
  overflow-y: auto;
}

.notification-container {
  max-width: 1000px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eaeaea;
}

.notification-title {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.mark-all-btn {
  background-color: #1e88e5;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.mark-all-btn:hover {
  background-color: #1565c0;
}

/* Notification Item Styles */
.notification-item {
  padding: 12px;
  margin-bottom: 10px;
  min-height: 60px;
  display: flex;
  position: relative;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
}

.notification-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.notification-item.expanded {
  min-height: auto;
}

.notification-item.expanded .notification-message {
  max-height: none;
  overflow: visible;
  -webkit-line-clamp: unset;
}

/* Enhanced Notification Types with Vibrant Colors */
.system-notification {
  background: linear-gradient(135deg, #f0f7ff 0%, #e3f2fd 100%);
  border-left: 4px solid #2196f3;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);
}

.update-notification {
  background: linear-gradient(135deg, #f1f8e9 0%, #dcedc8 100%);
  border-left: 4px solid #4caf50;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.15);
}

.message-notification {
  background: linear-gradient(135deg, #fce4ec 0%, #f8bbd0 100%);
  border-left: 4px solid #e91e63;
  box-shadow: 0 2px 8px rgba(233, 30, 99, 0.15);
}

.assignment-notification {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  border-left: 4px solid #ff9800;
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.15);
}

.alert-notification {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  border-left: 4px solid #f44336;
  box-shadow: 0 2px 8px rgba(244, 67, 54, 0.15);
}

.default-notification {
  background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 100%);
  border-left: 4px solid #9e9e9e;
  box-shadow: 0 2px 8px rgba(158, 158, 158, 0.15);
}

/* Enhanced Icon Styles with Animations */
.notification-icon {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: white;
  flex-shrink: 0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.notification-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(-100%) rotate(45deg);
  transition: transform 0.6s;
}

.notification-item:hover .notification-icon::before {
  transform: translateX(100%) rotate(45deg);
}

.notification-icon i {
  font-size: 20px;
  transition: transform 0.3s ease;
}

.notification-item:hover .notification-icon i {
  transform: scale(1.1);
}

/* Enhanced Icon Colors with Gradients */
.notification-icon.system { 
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.2);
}

.notification-icon.update { 
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
  box-shadow: 0 4px 8px rgba(76, 175, 80, 0.2);
}

.notification-icon.message { 
  background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%);
  box-shadow: 0 4px 8px rgba(233, 30, 99, 0.2);
}

.notification-icon.assignment { 
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  box-shadow: 0 4px 8px rgba(255, 152, 0, 0.2);
}

.notification-icon.alert { 
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  box-shadow: 0 4px 8px rgba(244, 67, 54, 0.2);
}

.notification-icon.default { 
  background: linear-gradient(135deg, #9e9e9e 0%, #757575 100%);
  box-shadow: 0 4px 8px rgba(158, 158, 158, 0.2);
}

.notification-content {
  flex-grow: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  min-height: 36px;
}

.notification-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
}

.notification-item-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 5px 0;
}

.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
}

.modal.show {
  display: block;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background-color: #ffffff;
  margin: 5% auto;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 90%;
  position: relative;
  transform: translateY(20px);
  opacity: 0;
  animation: slideUp 0.3s ease forwards;
}

.modal-close-btn {
  position: absolute;
  right: 10px;
  top: 10px;
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  padding: 5px;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.modal-close-btn:hover {
  background-color: #f0f0f0;
  color: #333;
}

#modal-title {
  font-size: 28px;
  font-weight: 600;
  color: #1a237e;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

#modal-title i {
  font-size: 24px;
}

#modal-message {
  font-size: 16px;
  color: #37474f;
  line-height: 1.8;
  margin-bottom: 25px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

#modal-footer {
  margin-top: 25px;
  padding-top: 20px;
  border-top: 2px solid #e3f2fd;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.modal-btn {
  padding: 8px 20px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-btn.primary {
  background-color: #2196f3;
  color: white;
  border: none;
}

.modal-btn.primary:hover {
  background-color: #1976d2;
  transform: translateY(-1px);
}

.modal-btn.secondary {
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1px solid #90caf9;
}

.modal-btn.secondary:hover {
  background-color: #bbdefb;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.notification-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  font-size: 12px;
}

.notification-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  background: none;
  border: none;
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background-color: rgba(0,0,0,0.05);
}

.action-btn.mark-read {
  color: #2196f3;
}

.action-btn.delete {
  color: #dc2626;
}

.notification-time-info {
  color: #6b7280;
  display: flex;
  gap: 8px;
}

.unread {
  background-color: rgba(255,255,255,0.9);
}

.empty-state {
  text-align: center;
  color: #6b7280;
  padding: 20px;
  font-size: 16px;
}