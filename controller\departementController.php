<?php
require_once "../model/departementModel.php";
require_once "../utils/response.php";

function getAllDepartementsAPI() {
    try {
        $departements = getAllDepartements();
        jsonResponse($departements);
    } catch (Exception $e) {
        jsonResponse(['error' => $e->getMessage()], 500);
    }
}

function getDepartementByIdAPI($id) {
    try {
        $departement = getDepartementById($id);
        if ($departement) {
            jsonResponse($departement);
        } else {
            jsonResponse(['error' => 'Département non trouvé'], 404);
        }
    } catch (Exception $e) {
        jsonResponse(['error' => $e->getMessage()], 500);
    }
}

function createDepartementAPI() {
    try {
        $data = json_decode(file_get_contents('php://input'), true);
        if (!isset($data['nom'])) {
            jsonResponse(['error' => 'Le nom du département est requis'], 400);
            return;
        }

        $result = createDepartement($data['nom']);
        jsonResponse(['message' => 'Département créé avec succès', 'id' => $result]);
    } catch (Exception $e) {
        jsonResponse(['error' => $e->getMessage()], 500);
    }
}

function updateDepartementAPI($id) {
    try {
        $data = json_decode(file_get_contents('php://input'), true);
        if (!isset($data['nom'])) {
            jsonResponse(['error' => 'Le nom du département est requis'], 400);
            return;
        }

        $result = updateDepartement($id, $data['nom']);
        if ($result) {
            jsonResponse(['message' => 'Département mis à jour avec succès']);
        } else {
            jsonResponse(['error' => 'Département non trouvé'], 404);
        }
    } catch (Exception $e) {
        jsonResponse(['error' => $e->getMessage()], 500);
    }
}

function deleteDepartementAPI($id) {
    try {
        $result = deleteDepartement($id);
        if ($result) {
            jsonResponse(['message' => 'Département supprimé avec succès']);
        } else {
            jsonResponse(['error' => 'Département non trouvé'], 404);
        }
    } catch (Exception $e) {
        jsonResponse(['error' => $e->getMessage()], 500);
    }
}
?>