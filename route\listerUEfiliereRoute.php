<?php
require_once "../controller/listerUEfiliereController.php";

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log the request for debugging
error_log("listerUEfiliereRoute.php called with: " . json_encode($_GET));

// Set the content type to JSON
header('Content-Type: application/json');

// Handle CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Get the request method
$method = $_SERVER['REQUEST_METHOD'];

// Only allow GET requests
if ($method !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

// Check if an action is specified
if (!isset($_GET['action'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Action parameter is required']);
    exit();
}

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// For testing purposes, set a default user if not set
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = [
        'username' => 'test_coordinator',
        'role' => 'coordinateur',
        'filiere_id' => 1, // Default to filiere ID 1 (informatique)
        'filiere_name' => 'Informatique'
    ];
    error_log("[DEBUG] Created test coordinator session in route file");
}

// Handle the action
$action = $_GET['action'];

switch ($action) {
    case 'getForCoordinator':
        // Get teaching units for the coordinator's filiere
        getUniteEnseignementForCoordinatorAPI();
        break;

    case 'getModules':
        // Get modules for the coordinator's filiere
        getModulesForCoordinatorAPI();
        break;

    case 'getNiveaux':
        // Get all niveaux for the coordinator's filiere
        getNiveauxForCoordinatorAPI();
        break;

    default:
        http_response_code(400);
        echo json_encode(['error' => 'Invalid action']);
        break;
}