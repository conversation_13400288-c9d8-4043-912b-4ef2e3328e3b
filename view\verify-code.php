<?php
// Inclure les constantes
require_once __DIR__ . "/../config/constants.php";
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vérification du code - ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <style>
        :root {
            --royal-blue: #1a73e8;
            --azure: #3a8ff7;
            --sky-blue: #64b5f6;
            --light-blue: #e8f0fe;
            --navy-blue: #0d47a1;
        }

        body {
            background-color: #f8f9fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }

        .reset-container {
            max-width: 450px;
            width: 100%;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: none;
        }

        .card-header {
            background-color: var(--royal-blue);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            padding: 20px;
            text-align: center;
        }

        .card-body {
            padding: 30px;
        }

        .form-control {
            border-radius: 5px;
            padding: 12px 15px;
            border: 1px solid #ced4da;
            margin-bottom: 20px;
        }

        .btn-primary {
            background-color: var(--royal-blue);
            border-color: var(--royal-blue);
            border-radius: 5px;
            padding: 12px 15px;
            font-weight: 500;
            width: 100%;
        }

        .btn-primary:hover {
            background-color: var(--navy-blue);
            border-color: var(--navy-blue);
        }

        .alert {
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .logo {
            max-width: 100px;
            margin-bottom: 15px;
        }

        .steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 33%;
        }

        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .step.active .step-number {
            background-color: var(--royal-blue);
            color: white;
        }

        .step-text {
            font-size: 12px;
            color: #6c757d;
            text-align: center;
        }

        .step.active .step-text {
            color: var(--royal-blue);
            font-weight: 500;
        }

        .step-line {
            flex-grow: 1;
            height: 2px;
            background-color: #e9ecef;
            margin: 15px 5px 0;
        }

        .back-to-login {
            text-align: center;
            margin-top: 20px;
        }

        .back-to-login a {
            color: var(--royal-blue);
            text-decoration: none;
        }

        .back-to-login a:hover {
            text-decoration: underline;
        }

        .code-inputs {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .code-input {
            width: 50px;
            height: 60px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            border: 2px solid #ced4da;
            border-radius: 8px;
        }

        .code-input:focus {
            border-color: var(--royal-blue);
            box-shadow: 0 0 0 0.25rem rgba(26, 115, 232, 0.25);
        }

        .resend-code {
            text-align: center;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        .resend-code button {
            background: none;
            border: none;
            color: var(--royal-blue);
            text-decoration: underline;
            cursor: pointer;
        }

        .resend-code button:disabled {
            color: #6c757d;
            cursor: not-allowed;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="card">
            <div class="card-header">
                <img src="<?php echo BASE_URL; ?>/view/assets/img/logo.png" alt="ENSAH Logo" class="logo">
                <h4>Vérification du code</h4>
            </div>
            <div class="card-body">
                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-text">Identification</div>
                    </div>
                    <div class="step-line"></div>
                    <div class="step active">
                        <div class="step-number">2</div>
                        <div class="step-text">Vérification</div>
                    </div>
                    <div class="step-line"></div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-text">Nouveau mot de passe</div>
                    </div>
                </div>

                <div class="alert alert-info" role="alert">
                    <i class="bi bi-info-circle-fill me-2"></i>
                    Entrez le code à 6 caractères que nous avons envoyé à votre adresse email.
                </div>

                <div class="alert alert-danger" id="errorAlert" style="display: none;" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <span id="errorMessage"></span>
                </div>

                <form id="verifyCodeForm">
                    <div class="code-inputs">
                        <input type="text" class="code-input" maxlength="1" pattern="[A-Za-z0-9]" required>
                        <input type="text" class="code-input" maxlength="1" pattern="[A-Za-z0-9]" required>
                        <input type="text" class="code-input" maxlength="1" pattern="[A-Za-z0-9]" required>
                        <input type="text" class="code-input" maxlength="1" pattern="[A-Za-z0-9]" required>
                        <input type="text" class="code-input" maxlength="1" pattern="[A-Za-z0-9]" required>
                        <input type="text" class="code-input" maxlength="1" pattern="[A-Za-z0-9]" required>
                    </div>

                    <div class="resend-code">
                        <button type="button" id="resendBtn" disabled>
                            Renvoyer le code dans <span id="countdown">60</span> secondes
                        </button>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-2"></i>
                        Vérifier le code
                    </button>
                </form>

                <div class="back-to-login">
                    <a href="<?php echo BASE_URL; ?>/view/reset-password.php">
                        <i class="bi bi-arrow-left me-1"></i>
                        Retour à l'étape précédente
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Vérifier si l'identifiant est stocké
            const identifier = sessionStorage.getItem('resetIdentifier');
            if (!identifier) {
                // Rediriger vers la page de demande de réinitialisation
                window.location.href = '<?php echo BASE_URL; ?>/view/reset-password.php';
                return;
            }

            const verifyCodeForm = document.getElementById('verifyCodeForm');
            const codeInputs = document.querySelectorAll('.code-input');
            const errorAlert = document.getElementById('errorAlert');
            const errorMessage = document.getElementById('errorMessage');
            const resendBtn = document.getElementById('resendBtn');
            const countdownSpan = document.getElementById('countdown');

            // Configurer les champs de code
            codeInputs.forEach((input, index) => {
                // Passer au champ suivant après la saisie
                input.addEventListener('input', function() {
                    if (this.value.length === 1) {
                        if (index < codeInputs.length - 1) {
                            codeInputs[index + 1].focus();
                        }
                    }
                });

                // Gérer la touche Backspace
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Backspace' && this.value.length === 0 && index > 0) {
                        codeInputs[index - 1].focus();
                    }
                });

                // Forcer les caractères en majuscules
                input.addEventListener('input', function() {
                    this.value = this.value.toUpperCase();
                });
            });

            // Mettre le focus sur le premier champ
            codeInputs[0].focus();

            // Gérer le compte à rebours pour le renvoi du code
            let countdown = 60;
            const countdownInterval = setInterval(function() {
                countdown--;
                countdownSpan.textContent = countdown;

                if (countdown <= 0) {
                    clearInterval(countdownInterval);
                    resendBtn.disabled = false;
                    resendBtn.textContent = 'Renvoyer le code';
                }
            }, 1000);

            // Gérer le renvoi du code
            resendBtn.addEventListener('click', function() {
                if (this.disabled) return;

                this.disabled = true;
                this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Envoi en cours...';

                // Envoyer la requête
                fetch('<?php echo BASE_URL; ?>/route/reset-password-index.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'request',
                        identifier: identifier
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Réinitialiser le compte à rebours
                        countdown = 60;
                        countdownSpan.textContent = countdown;
                        resendBtn.innerHTML = `Renvoyer le code dans <span id="countdown">${countdown}</span> secondes`;

                        // Redémarrer l'intervalle
                        clearInterval(countdownInterval);
                        const newInterval = setInterval(function() {
                            countdown--;
                            document.getElementById('countdown').textContent = countdown;

                            if (countdown <= 0) {
                                clearInterval(newInterval);
                                resendBtn.disabled = false;
                                resendBtn.textContent = 'Renvoyer le code';
                            }
                        }, 1000);

                        // Afficher un message de succès
                        showSuccess('Un nouveau code a été envoyé à votre adresse email.');
                    } else {
                        resendBtn.disabled = false;
                        resendBtn.textContent = 'Renvoyer le code';
                        showError(data.error);
                    }
                })
                .catch(error => {
                    resendBtn.disabled = false;
                    resendBtn.textContent = 'Renvoyer le code';
                    showError('Une erreur est survenue. Veuillez réessayer plus tard.');
                    console.error('Error:', error);
                });
            });

            // Gérer la soumission du formulaire
            verifyCodeForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Récupérer le code
                let code = '';
                codeInputs.forEach(input => {
                    code += input.value;
                });

                if (code.length !== 6) {
                    showError('Veuillez entrer le code complet à 6 caractères.');
                    return;
                }

                // Désactiver le bouton pendant la requête
                const submitBtn = verifyCodeForm.querySelector('button[type="submit"]');
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Vérification en cours...';

                // Masquer les erreurs précédentes
                errorAlert.style.display = 'none';

                // Envoyer la requête
                fetch('<?php echo BASE_URL; ?>/route/reset-password-index.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'verify',
                        identifier: identifier,
                        code: code
                    })
                })
                .then(response => response.json())
                .then(data => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="bi bi-check-circle me-2"></i>Vérifier le code';

                    if (data.success) {
                        // Stocker le token dans sessionStorage
                        sessionStorage.setItem('resetToken', data.token);

                        // Rediriger vers la page de réinitialisation du mot de passe
                        window.location.href = '<?php echo BASE_URL; ?>/view/new-password.php';
                    } else {
                        showError(data.error);
                    }
                })
                .catch(error => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="bi bi-check-circle me-2"></i>Vérifier le code';
                    showError('Une erreur est survenue. Veuillez réessayer plus tard.');
                    console.error('Error:', error);
                });
            });

            function showError(message) {
                errorMessage.textContent = message;
                errorAlert.className = 'alert alert-danger';
                errorAlert.style.display = 'block';
            }

            function showSuccess(message) {
                errorMessage.textContent = message;
                errorAlert.className = 'alert alert-success';
                errorAlert.style.display = 'block';
            }
        });
    </script>
</body>
</html>
