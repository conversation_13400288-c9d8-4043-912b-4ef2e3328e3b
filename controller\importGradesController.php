<?php
require_once "../model/enseignantModel.php";
require_once "../model/niveauModel.php";
require_once "../model/semestreModel.php";
require_once "../model/moduleModel.php";
require_once "../model/noteModel.php";
require_once "../utils/response.php";

/**
 * Get teachers associated with a coordinator's field
 *
 * @return array Array of teachers
 */
function getTeachersByCoordinatorFieldAPI() {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a coordinator
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'coordinateur') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    // Get the coordinator's field ID from the session
    $filiereId = $_SESSION['user']['filiere_id'] ?? null;

    if (!$filiereId) {
        jsonResponse(['error' => 'Coordinator field not found'], 400);
        exit;
    }

    // Get the database connection
    $conn = getConnection();
    if (!$conn) {
        jsonResponse(['error' => 'Database connection error'], 500);
        exit;
    }

    // Sanitize input
    $filiereId = mysqli_real_escape_string($conn, $filiereId);

    // Query to get teachers associated with the coordinator's field using the affectation table
    // This query finds all teachers who are assigned to teaching units in modules that belong to the coordinator's field
    $query = "SELECT DISTINCT e.id_enseignant, e.CNI, e.nom, e.prenom
              FROM enseignant e
              JOIN affectation a ON e.id_enseignant = a.professeur_id
              JOIN uniteenseignement ue ON a.unite_enseignement_id = ue.id
              JOIN module m ON ue.module_id = m.id
              WHERE m.filiere_id = '$filiereId'
              ORDER BY e.nom, e.prenom";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getTeachersByCoordinatorFieldAPI: " . $error);

        // Try a fallback query with more flexible column names
        error_log("Trying fallback query with flexible column names");

        // Check module table columns
        $checkModuleColumns = mysqli_query($conn, "SHOW COLUMNS FROM module");
        $moduleColumns = [];
        while ($col = mysqli_fetch_assoc($checkModuleColumns)) {
            $moduleColumns[] = $col['Field'];
        }

        // Determine the correct field names
        $moduleIdField = in_array('id', $moduleColumns) ? 'id' : 'id_module';
        $filiereField = in_array('filiere_id', $moduleColumns) ? 'filiere_id' : 'id_filiere';

        // Check uniteenseignement table columns
        $checkUEColumns = mysqli_query($conn, "SHOW COLUMNS FROM uniteenseignement");
        $ueColumns = [];
        while ($col = mysqli_fetch_assoc($checkUEColumns)) {
            $ueColumns[] = $col['Field'];
        }

        $ueIdField = in_array('id', $ueColumns) ? 'id' : 'id_ue';
        $ueModuleIdField = in_array('module_id', $ueColumns) ? 'module_id' : 'id_module';

        $fallbackQuery = "SELECT DISTINCT e.id_enseignant, e.CNI, e.nom, e.prenom
                         FROM enseignant e
                         JOIN affectation a ON e.id_enseignant = a.professeur_id
                         JOIN uniteenseignement ue ON a.unite_enseignement_id = ue.$ueIdField
                         JOIN module m ON ue.$ueModuleIdField = m.$moduleIdField
                         WHERE m.$filiereField = '$filiereId'
                         ORDER BY e.nom, e.prenom";

        error_log("Executing fallback query: " . $fallbackQuery);
        $fallbackResult = mysqli_query($conn, $fallbackQuery);

        if (!$fallbackResult) {
            $fallbackError = mysqli_error($conn);
            error_log("Error in fallback query: " . $fallbackError);
            mysqli_close($conn);
            jsonResponse(['error' => 'Error fetching teachers: ' . $fallbackError], 500);
            exit;
        }

        $teachers = [];
        while ($row = mysqli_fetch_assoc($fallbackResult)) {
            $teachers[] = $row;
        }
    } else {
        $teachers = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $teachers[] = $row;
        }
    }

    mysqli_close($conn);
    jsonResponse(['data' => $teachers], 200);
}

/**
 * Get levels for a teacher and field
 *
 * @param int $teacherId The teacher ID
 * @return array Array of levels
 */
function getLevelsByTeacherAndFieldAPI($teacherId) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a coordinator
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'coordinateur') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    // Get the coordinator's field ID from the session
    $filiereId = $_SESSION['user']['filiere_id'] ?? null;

    if (!$filiereId || !$teacherId) {
        jsonResponse(['error' => 'Missing required parameters'], 400);
        exit;
    }

    // Get the database connection
    $conn = getConnection();
    if (!$conn) {
        jsonResponse(['error' => 'Database connection error'], 500);
        exit;
    }

    // Sanitize inputs
    $filiereId = mysqli_real_escape_string($conn, $filiereId);
    $teacherId = mysqli_real_escape_string($conn, $teacherId);

    // Check module table columns
    $checkModuleColumns = mysqli_query($conn, "SHOW COLUMNS FROM module");
    $moduleColumns = [];
    while ($col = mysqli_fetch_assoc($checkModuleColumns)) {
        $moduleColumns[] = $col['Field'];
    }

    // Determine the correct field names
    $moduleIdField = in_array('id', $moduleColumns) ? 'id' : 'id_module';
    $filiereField = in_array('filiere_id', $moduleColumns) ? 'filiere_id' : 'id_filiere';
    $niveauField = in_array('id_niveau', $moduleColumns) ? 'id_niveau' :
                  (in_array('niveau_id', $moduleColumns) ? 'niveau_id' : 'id_niveau');

    // Check uniteenseignement table columns
    $checkUEColumns = mysqli_query($conn, "SHOW COLUMNS FROM uniteenseignement");
    $ueColumns = [];
    while ($col = mysqli_fetch_assoc($checkUEColumns)) {
        $ueColumns[] = $col['Field'];
    }

    $ueIdField = in_array('id', $ueColumns) ? 'id' : 'id_ue';
    $ueModuleIdField = in_array('module_id', $ueColumns) ? 'module_id' : 'id_module';

    // Query to get levels for the teacher and field using the affectation table
    $query = "SELECT DISTINCT n.id as id_niveau, n.nom as niveau
              FROM niveaux n
              JOIN module m ON n.id = m.$niveauField
              JOIN uniteenseignement ue ON m.$moduleIdField = ue.$ueModuleIdField
              JOIN affectation a ON ue.$ueIdField = a.unite_enseignement_id
              WHERE a.professeur_id = '$teacherId'
              AND m.$filiereField = '$filiereId'
              ORDER BY n.nom";

    error_log("Executing query to get levels: " . $query);
    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getLevelsByTeacherAndFieldAPI: " . $error);

        // Try a simpler fallback query
        error_log("Trying fallback query to get levels");
        $fallbackQuery = "SELECT DISTINCT n.id as id_niveau, n.nom as niveau
                         FROM niveaux n
                         JOIN module m ON n.id = m.$niveauField
                         WHERE m.$filiereField = '$filiereId'
                         ORDER BY n.nom";

        error_log("Executing fallback query: " . $fallbackQuery);
        $fallbackResult = mysqli_query($conn, $fallbackQuery);

        if (!$fallbackResult) {
            $fallbackError = mysqli_error($conn);
            error_log("Error in fallback query: " . $fallbackError);
            mysqli_close($conn);
            jsonResponse(['error' => 'Error fetching levels: ' . $fallbackError], 500);
            exit;
        }

        $levels = [];
        while ($row = mysqli_fetch_assoc($fallbackResult)) {
            $levels[] = $row;
        }
    } else {
        $levels = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $levels[] = $row;
        }
    }

    error_log("Found " . count($levels) . " levels for teacher ID: $teacherId and field ID: $filiereId");
    mysqli_close($conn);
    jsonResponse(['data' => $levels], 200);
}

/**
 * Get semesters for a level
 *
 * @param int $levelId The level ID
 * @return array Array of semesters
 */
function getSemestersByLevelAPI($levelId) {
    if (!$levelId) {
        jsonResponse(['error' => 'Level ID is required'], 400);
        exit;
    }

    $semesters = getSemestresByNiveau($levelId);

    if (isset($semesters['error'])) {
        jsonResponse(['error' => $semesters['error']], 500);
        exit;
    }

    jsonResponse(['data' => $semesters], 200);
}

/**
 * Get all modules for a teacher without filtering
 * Only returns modules where the teacher is assigned to teach the lecture component ('cours')
 *
 * @param int $teacherId The teacher ID
 * @return array Array of modules
 */
function getAllTeacherModulesAPI($teacherId) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a teacher
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'enseignant') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    if (!$teacherId) {
        jsonResponse(['error' => 'Teacher ID is required'], 400);
        exit;
    }

    error_log("Getting all modules for teacher ID: $teacherId");
    error_log("Note: Only returning modules where the teacher is assigned to teach the lecture component ('cours')");

    // Use the new getAllTeacherModules function to get all modules for the teacher
    $modules = getAllTeacherModules($teacherId);

    if (isset($modules['error'])) {
        error_log("Error in getAllTeacherModules: " . $modules['error']);
        jsonResponse(['error' => $modules['error']], 500);
        exit;
    }

    // Format the modules for the response
    $formattedModules = [];
    foreach ($modules as $module) {
        // Make sure we have the required fields
        if (isset($module['id']) || isset($module['id_module'])) {
            $moduleId = isset($module['id']) ? $module['id'] : $module['id_module'];
            $formattedModules[] = [
                'id' => $moduleId,
                'nom' => $module['nom'],
                // Include additional information that might be useful for the frontend
                'filiere_id' => $module['filiere_id'],
                'niveau_id' => $module['id_niveau'],
                'semestre_id' => $module['semestre_id'] ?? null,
                'semestre_nom' => $module['semestre_nom'] ?? null,
                'filiere_nom' => $module['nom_filiere'] ?? null,
                'niveau_nom' => $module['niveau'] ?? null
            ];
        }
    }

    error_log("Found " . count($formattedModules) . " modules for teacher ID: $teacherId");
    jsonResponse(['data' => $formattedModules], 200);
}

/**
 * Get modules for a teacher, level, semester, and field
 * Only returns modules where the teacher is assigned to teach the lecture component ('cours')
 *
 * @param int $teacherId The teacher ID
 * @param int $levelId The level ID
 * @param string $semester The semester
 * @return array Array of modules
 */
function getModulesByFiltersAPI($teacherId, $levelId, $semester) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a coordinator or teacher
    if (!isset($_SESSION['user']) || ($_SESSION['user']['role'] !== 'coordinateur' && $_SESSION['user']['role'] !== 'enseignant')) {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    // Get the field ID based on user role
    $filiereId = null;

    if ($_SESSION['user']['role'] === 'coordinateur') {
        // For coordinators, get field ID from session
        $filiereId = $_SESSION['user']['filiere_id'] ?? null;
    } else if ($_SESSION['user']['role'] === 'enseignant') {
        // For teachers, get field ID from the request parameters
        if (isset($_GET['filiere'])) {
            $filiereId = $_GET['filiere'];
        }
    }

    if (!$filiereId || !$teacherId || !$levelId || !$semester) {
        jsonResponse(['error' => 'Missing required parameters'], 400);
        exit;
    }

    error_log("Getting modules for teacher ID: $teacherId, field ID: $filiereId, level ID: $levelId, semester: $semester");
    error_log("Note: Only returning modules where the teacher is assigned to teach the lecture component ('cours')");

    // Use the improved getTeacherModules function to get modules
    // The function now filters for teaching units of type 'Cours' only
    $modules = getTeacherModules($teacherId, $filiereId, $levelId, $semester);

    if (isset($modules['error'])) {
        error_log("Error in getTeacherModules: " . $modules['error']);
        jsonResponse(['error' => $modules['error']], 500);
        exit;
    }

    // Format the modules for the response
    $formattedModules = [];
    foreach ($modules as $module) {
        // Make sure we have the required fields
        if (isset($module['id']) || isset($module['id_module'])) {
            $moduleId = isset($module['id']) ? $module['id'] : $module['id_module'];
            $formattedModules[] = [
                'id' => $moduleId,
                'nom' => $module['nom'],
                // Include additional information that might be useful for the frontend
                'filiere_id' => $module['filiere_id'],
                'niveau_id' => $module['id_niveau'],
                'semestre_id' => $module['semestre_id'] ?? null,
                'semestre_nom' => $module['semestre_nom'] ?? null
            ];
        }
    }

    error_log("Found " . count($formattedModules) . " modules for the specified criteria");

    // If no modules found, try a fallback approach without semester filter
    // Still maintaining the filter for teaching units of type 'Cours'
    if (empty($formattedModules)) {
        error_log("No modules found with semester filter, trying without semester filter");
        error_log("Still filtering for teaching units of type 'Cours' only");

        $modulesWithoutSemester = getTeacherModules($teacherId, $filiereId, $levelId);

        if (!isset($modulesWithoutSemester['error'])) {
            foreach ($modulesWithoutSemester as $module) {
                if (isset($module['id']) || isset($module['id_module'])) {
                    $moduleId = isset($module['id']) ? $module['id'] : $module['id_module'];
                    $formattedModules[] = [
                        'id' => $moduleId,
                        'nom' => $module['nom'],
                        'filiere_id' => $module['filiere_id'],
                        'niveau_id' => $module['id_niveau'],
                        'semestre_id' => $module['semestre_id'] ?? null,
                        'semestre_nom' => $module['semestre_nom'] ?? null
                    ];
                }
            }

            error_log("Fallback query returned " . count($formattedModules) . " modules");
        }
    }

    jsonResponse(['data' => $formattedModules], 200);
}

/**
 * Handle file upload for grade import
 */
function importGradesFileAPI() {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a coordinator
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'coordinateur') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    // Check if file was uploaded
    if (!isset($_FILES['gradeFile']) || $_FILES['gradeFile']['error'] !== UPLOAD_ERR_OK) {
        jsonResponse(['error' => 'No file uploaded or upload error'], 400);
        exit;
    }

    // Check if required parameters are provided
    if (!isset($_POST['teacherId']) || !isset($_POST['moduleId']) || !isset($_POST['levelId']) ||
        !isset($_POST['semester']) || !isset($_POST['session'])) {
        jsonResponse(['error' => 'Missing required parameters'], 400);
        exit;
    }

    // Get the coordinator's field ID from the session
    $filiereId = $_SESSION['user']['filiere_id'] ?? null;
    if (!$filiereId) {
        jsonResponse(['error' => 'Coordinator field not found'], 400);
        exit;
    }

    // Get parameters
    $teacherId = $_POST['teacherId'];
    $moduleId = $_POST['moduleId'];
    $levelId = $_POST['levelId'];
    $semester = $_POST['semester'];
    $session = $_POST['session'];

    // Create upload directory if it doesn't exist
    $uploadDir = "../uploads/grades/";
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    // Generate a unique filename
    $filename = "grades_" . $moduleId . "_" . $levelId . "_" . $semester . "_" . $session . "_" . time() . ".pdf";
    $filePath = $uploadDir . $filename;

    // Move the uploaded file
    if (!move_uploaded_file($_FILES['gradeFile']['tmp_name'], $filePath)) {
        jsonResponse(['error' => 'Failed to save the file'], 500);
        exit;
    }

    // Create a record in the database
    $conn = getConnection();
    if (!$conn) {
        jsonResponse(['error' => 'Database connection error'], 500);
        exit;
    }

    // Sanitize inputs
    $filiereId = mysqli_real_escape_string($conn, $filiereId);
    $teacherId = mysqli_real_escape_string($conn, $teacherId);
    $moduleId = mysqli_real_escape_string($conn, $moduleId);
    $levelId = mysqli_real_escape_string($conn, $levelId);
    $semester = mysqli_real_escape_string($conn, $semester);
    $session = mysqli_real_escape_string($conn, $session);
    $filename = mysqli_real_escape_string($conn, $filename);

    // Check if the imported_grades table exists, create it if not
    $checkTableQuery = "SHOW TABLES LIKE 'imported_grades'";
    $tableExists = mysqli_query($conn, $checkTableQuery);

    if (mysqli_num_rows($tableExists) == 0) {
        $createTableQuery = "CREATE TABLE imported_grades (
            id INT AUTO_INCREMENT PRIMARY KEY,
            id_filiere INT NOT NULL,
            id_enseignant INT NOT NULL,
            id_module INT NOT NULL,
            id_niveau INT NOT NULL,
            semestre VARCHAR(50) NOT NULL,
            session VARCHAR(50) NOT NULL,
            filename VARCHAR(255) NOT NULL,
            import_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX (id_filiere),
            INDEX (id_enseignant),
            INDEX (id_module)
        )";

        if (!mysqli_query($conn, $createTableQuery)) {
            $error = mysqli_error($conn);
            error_log("Error creating imported_grades table: " . $error);
            mysqli_close($conn);
            jsonResponse(['error' => 'Error creating database table: ' . $error], 500);
            exit;
        }
    }

    // Insert the record
    $insertQuery = "INSERT INTO imported_grades (id_filiere, id_enseignant, id_module, id_niveau, semestre, session, filename)
                    VALUES ('$filiereId', '$teacherId', '$moduleId', '$levelId', '$semester', '$session', '$filename')";

    if (!mysqli_query($conn, $insertQuery)) {
        $error = mysqli_error($conn);
        error_log("Error inserting imported grade record: " . $error);
        mysqli_close($conn);
        jsonResponse(['error' => 'Error saving record: ' . $error], 500);
        exit;
    }

    mysqli_close($conn);
    jsonResponse(['success' => true, 'message' => 'File imported successfully', 'filename' => $filename], 200);
}

// Handle API requests
if (isset($_GET['action'])) {
    $action = $_GET['action'];

    switch ($action) {
        case 'getTeachers':
            getTeachersByCoordinatorFieldAPI();
            break;

        case 'getLevels':
            if (!isset($_GET['teacherId'])) {
                jsonResponse(['error' => 'Teacher ID is required'], 400);
                exit;
            }
            getLevelsByTeacherAndFieldAPI($_GET['teacherId']);
            break;

        case 'getSemesters':
            if (!isset($_GET['levelId'])) {
                jsonResponse(['error' => 'Level ID is required'], 400);
                exit;
            }
            getSemestersByLevelAPI($_GET['levelId']);
            break;

        case 'getAllTeacherModules':
            if (!isset($_GET['teacherId'])) {
                jsonResponse(['error' => 'Teacher ID is required'], 400);
                exit;
            }
            getAllTeacherModulesAPI($_GET['teacherId']);
            break;

        case 'getModules':
            if (!isset($_GET['teacherId']) || !isset($_GET['levelId']) || !isset($_GET['semester'])) {
                jsonResponse(['error' => 'Teacher ID, Level ID, and Semester are required'], 400);
                exit;
            }
            getModulesByFiltersAPI($_GET['teacherId'], $_GET['levelId'], $_GET['semester']);
            break;

        case 'importGrades':
            importGradesFileAPI();
            break;

        default:
            jsonResponse(['error' => 'Invalid action'], 400);
            exit;
    }
}
?>
