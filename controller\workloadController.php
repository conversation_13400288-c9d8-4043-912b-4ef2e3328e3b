<?php
/**
 * Dynamic Workload Controller
 * Handles API requests for dynamic workload tracking and historical data
 */

require_once __DIR__ . '/../model/affectationModel.php';
require_once __DIR__ . '/../utils/response.php';

/**
 * Get teacher's workload for a specific academic year
 */
function getTeacherWorkloadByYearAPI() {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in
    if (!isset($_SESSION['user'])) {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    // Get parameters
    $teacherId = $_GET['teacher_id'] ?? '';
    $academicYear = $_GET['academic_year'] ?? '';

    // Validate required parameters
    if (empty($teacherId)) {
        jsonResponse(['error' => 'Teacher ID is required'], 400);
        exit;
    }

    if (empty($academicYear)) {
        jsonResponse(['error' => 'Academic year is required'], 400);
        exit;
    }

    // Validate academic year format
    if (!preg_match('/^\d{4}-\d{4}$/', $academicYear)) {
        jsonResponse(['error' => 'Invalid academic year format. Use YYYY-YYYY'], 400);
        exit;
    }

    $workload = calculateTeacherWorkloadByYear($teacherId, $academicYear);

    if (isset($workload['error'])) {
        jsonResponse(['error' => $workload['error']], 500);
        exit;
    }

    jsonResponse(['success' => true, 'data' => ['total_hours' => $workload]], 200);
}

/**
 * Get teacher's workload history across multiple academic years
 */
function getTeacherWorkloadHistoryAPI() {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in
    if (!isset($_SESSION['user'])) {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    // Get parameters
    $teacherId = $_GET['teacher_id'] ?? '';

    // Validate required parameters
    if (empty($teacherId)) {
        jsonResponse(['error' => 'Teacher ID is required'], 400);
        exit;
    }

    $history = getTeacherWorkloadHistory($teacherId);

    if (isset($history['error'])) {
        jsonResponse(['error' => $history['error']], 500);
        exit;
    }

    jsonResponse(['success' => true, 'data' => $history], 200);
}

/**
 * Get department workload overview with dynamic calculation by academic year
 */
function getDepartmentWorkloadByYearAPI() {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a department head
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'chef de departement') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    // Get parameters
    $departmentId = $_GET['department_id'] ?? $_SESSION['user']['id_departement'] ?? '';
    $academicYear = $_GET['academic_year'] ?? '';

    // Validate required parameters
    if (empty($departmentId)) {
        jsonResponse(['error' => 'Department ID is required'], 400);
        exit;
    }

    // Use current academic year if not provided
    if (empty($academicYear)) {
        $currentYear = date('Y');
        $nextYear = $currentYear + 1;
        $academicYear = "$currentYear-$nextYear";
    }

    // Validate academic year format
    if (!preg_match('/^\d{4}-\d{4}$/', $academicYear)) {
        jsonResponse(['error' => 'Invalid academic year format. Use YYYY-YYYY'], 400);
        exit;
    }

    $workload = getDepartmentWorkloadByYear($departmentId, $academicYear);

    if (isset($workload['error'])) {
        jsonResponse(['error' => $workload['error']], 500);
        exit;
    }

    jsonResponse(['success' => true, 'data' => $workload], 200);
}

/**
 * Get available academic years from affectation table
 */
function getAvailableAcademicYearsAPI() {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in
    if (!isset($_SESSION['user'])) {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    $conn = getConnection();

    if (!$conn) {
        jsonResponse(['error' => 'Database connection error'], 500);
        exit;
    }

    try {
        // Get distinct academic years from affectation table
        $query = "SELECT DISTINCT annee_academique 
                  FROM affectation 
                  WHERE annee_academique IS NOT NULL 
                  ORDER BY annee_academique DESC";

        $result = mysqli_query($conn, $query);

        if (!$result) {
            throw new Exception("Error getting academic years: " . mysqli_error($conn));
        }

        $years = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $years[] = $row['annee_academique'];
        }

        // Add current year if not in list
        $currentYear = date('Y');
        $nextYear = $currentYear + 1;
        $currentAcademicYear = "$currentYear-$nextYear";
        
        if (!in_array($currentAcademicYear, $years)) {
            array_unshift($years, $currentAcademicYear);
        }

        mysqli_close($conn);
        jsonResponse(['success' => true, 'data' => $years], 200);

    } catch (Exception $e) {
        mysqli_close($conn);
        error_log("Error in getAvailableAcademicYearsAPI: " . $e->getMessage());
        jsonResponse(['error' => $e->getMessage()], 500);
    }
}

/**
 * Get department workload summary statistics
 */
function getDepartmentWorkloadSummaryAPI() {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a department head
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'chef de departement') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    // Get parameters
    $departmentId = $_GET['department_id'] ?? $_SESSION['user']['id_departement'] ?? '';
    $academicYear = $_GET['academic_year'] ?? '';

    // Validate required parameters
    if (empty($departmentId)) {
        jsonResponse(['error' => 'Department ID is required'], 400);
        exit;
    }

    // Use current academic year if not provided
    if (empty($academicYear)) {
        $currentYear = date('Y');
        $nextYear = $currentYear + 1;
        $academicYear = "$currentYear-$nextYear";
    }

    $workload = getDepartmentWorkloadByYear($departmentId, $academicYear);

    if (isset($workload['error'])) {
        jsonResponse(['error' => $workload['error']], 500);
        exit;
    }

    // Calculate summary statistics
    $totalTeachers = count($workload);
    $totalHours = array_sum(array_column($workload, 'total_hours'));
    $totalAssignments = array_sum(array_column($workload, 'total_assignments'));
    $averageHours = $totalTeachers > 0 ? round($totalHours / $totalTeachers, 2) : 0;

    // Count teachers by role
    $roleStats = [];
    foreach ($workload as $teacher) {
        $role = $teacher['role'];
        if (!isset($roleStats[$role])) {
            $roleStats[$role] = ['count' => 0, 'total_hours' => 0];
        }
        $roleStats[$role]['count']++;
        $roleStats[$role]['total_hours'] += $teacher['total_hours'];
    }

    $summary = [
        'academic_year' => $academicYear,
        'total_teachers' => $totalTeachers,
        'total_hours' => $totalHours,
        'total_assignments' => $totalAssignments,
        'average_hours' => $averageHours,
        'role_statistics' => $roleStats
    ];

    jsonResponse(['success' => true, 'data' => $summary], 200);
}

// Handle API requests based on action parameter
if (isset($_GET['action'])) {
    $action = $_GET['action'];

    switch ($action) {
        case 'getTeacherWorkloadByYear':
            getTeacherWorkloadByYearAPI();
            break;

        case 'getTeacherWorkloadHistory':
            getTeacherWorkloadHistoryAPI();
            break;

        case 'getDepartmentWorkloadByYear':
            getDepartmentWorkloadByYearAPI();
            break;

        case 'getAvailableAcademicYears':
            getAvailableAcademicYearsAPI();
            break;

        case 'getDepartmentWorkloadSummary':
            getDepartmentWorkloadSummaryAPI();
            break;

        default:
            jsonResponse(['error' => 'Invalid action'], 400);
            break;
    }
} else {
    jsonResponse(['error' => 'Action parameter is required'], 400);
}

?>
