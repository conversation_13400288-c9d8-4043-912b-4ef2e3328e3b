<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
require_once '../../config/constants.php';

$pageTitle = "Gestion des Services";
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - UniAdmin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        /* Style pour les en-têtes de carte */
        .card-header.bg-pastel-blue {
            background-color: #81D4FA !important;
            color: #0D47A1;
        }

        .service-card {
            transition: all 0.3s ease;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            margin-bottom: 20px;
        }

        .service-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .service-status {
            font-weight: bold;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
        }

        .status-active {
            background-color: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }

        .remaining-time {
            background-color: #fff3cd;
            color: #856404;
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 0.8em;
            margin-left: 8px;
        }

        .service-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .duration-input {
            width: 80px;
        }

        .service-description {
            color: #6c757d;
            font-size: 0.9em;
            margin-bottom: 15px;
        }

        .service-stats {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
            margin-top: 10px;
        }

        .stat-item {
            display: inline-block;
            margin-right: 15px;
            font-size: 0.85em;
            color: #6c757d;
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert-custom {
            border-radius: 8px;
            border: none;
            padding: 12px 16px;
        }

        .datetime-input {
            width: 200px;
        }

        .activation-form {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
            display: none;
        }

        .activation-form.show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid mt-4">
                <div class="row">
                    <div class="col-12">
                        <div class="card mb-4">
                            <div class="card-header bg-pastel-blue">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0"><?php echo $pageTitle; ?></h5>
                                    <div>
                                        <button class="btn btn-outline-light btn-sm" onclick="refreshServices()">
                                            <i class="bi bi-arrow-clockwise"></i> Actualiser
                                        </button>
                                        <button class="btn btn-outline-light btn-sm" onclick="checkExpiredServices()">
                                            <i class="bi bi-clock-history"></i> Vérifier Expirés
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <p class="text-muted mb-4">Contrôlez la disponibilité des services système avec activation temporisée</p>

                                <!-- Alert container -->
                                <div id="alertContainer"></div>

                                <!-- Services container -->
                                <div id="servicesContainer">
                                    <div class="text-center py-5">
                                        <div class="loading-spinner mx-auto" style="display: block;"></div>
                                        <p class="text-muted mt-3">Chargement des services...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Service Logs Modal -->
                <div class="modal fade" id="logsModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Journaux des Services</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div id="logsContainer">
                                    <div class="text-center py-3">
                                        <div class="loading-spinner mx-auto" style="display: block;"></div>
                                        <p class="text-muted mt-2">Chargement des journaux...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/dashboard-dynamic.js"></script>
    <script src="../assets/js/dashboard-chart.js"></script>
    <script src="../assets/js/dashboard-notifications.js"></script>
    <!-- Notifications JS -->
    <script src="../assets/js/notifications.js"></script>
    <script src="../assets/js/sidebar.js"></script>
    <script>
        const BASE_URL = '<?php echo BASE_URL; ?>';
        let services = [];
        let refreshInterval;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadServices();
            startAutoRefresh();
        });

        // Load all services
        async function loadServices() {
            try {
                console.log('Loading services from:', `${BASE_URL}/route/serviceManagementRoute.php`);

                const response = await fetch(`${BASE_URL}/route/serviceManagementRoute.php`);
                console.log('Response status:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const responseText = await response.text();
                console.log('Response text:', responseText);

                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('JSON parse error:', parseError);
                    throw new Error('Invalid JSON response: ' + responseText.substring(0, 100));
                }

                console.log('Parsed data:', data);

                if (data.success) {
                    services = data.services || [];
                    console.log('Services loaded:', services.length);
                    renderServices();
                } else if (data.services) {
                    // Handle case where API returns services directly without success flag
                    services = Array.isArray(data.services) ? data.services : data;
                    console.log('Services loaded (direct):', services.length);
                    renderServices();
                } else {
                    showAlert('Erreur lors du chargement des services: ' + (data.error || 'Format de réponse invalide'), 'danger');
                }
            } catch (error) {
                console.error('Error loading services:', error);
                showAlert('Erreur de connexion lors du chargement des services: ' + error.message, 'danger');
            }
        }

        // Render services
        function renderServices() {
            const container = document.getElementById('servicesContainer');

            if (services.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="bi bi-gear" style="font-size: 3rem; color: #ccc;"></i>
                        <p class="text-muted mt-3">Aucun service configuré</p>
                    </div>
                `;
                return;
            }

            const servicesHTML = services.map(service => {
                // Debug log pour chaque service
                console.log(`Service: ${service.service_key}, is_active: ${service.is_active}, type: ${typeof service.is_active}`);

                // Convertir is_active en booléen si c'est une chaîne
                const isActive = service.is_active === true || service.is_active === 1 || service.is_active === '1';

                return `
                <div class="service-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div>
                                <h5 class="card-title mb-1">${service.display_name}</h5>
                                <span class="service-status ${isActive ? 'status-active' : 'status-inactive'}">
                                    <i class="bi bi-${isActive ? 'check-circle' : 'x-circle'}"></i>
                                    ${isActive ? 'Actif' : 'Inactif'}
                                </span>
                                ${service.remaining_time ? `
                                    <span class="remaining-time">
                                        <i class="bi bi-clock"></i>
                                        ${formatRemainingTime(service.remaining_time)}
                                    </span>
                                ` : ''}
                            </div>
                            <div class="service-controls">
                                ${isActive ? `
                                    <button class="btn btn-sm btn-outline-warning" onclick="toggleExtensionForm('${service.service_key}')">
                                        <i class="bi bi-clock-history"></i> Prolonger
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deactivateService('${service.service_key}')">
                                        <i class="bi bi-stop-circle"></i> Désactiver
                                    </button>
                                ` : `
                                    <button class="btn btn-sm btn-outline-success" onclick="toggleActivationForm('${service.service_key}')">
                                        <i class="bi bi-play-circle"></i> Activer
                                    </button>
                                `}
                                <button class="btn btn-sm btn-outline-secondary" onclick="showServiceLogs('${service.service_key}')">
                                    <i class="bi bi-journal-text"></i>
                                </button>
                            </div>
                        </div>

                        <p class="service-description">${service.description}</p>

                        <!-- Activation Form -->
                        <div id="activationForm_${service.service_key}" class="activation-form">
                            <h6>Paramètres d'activation</h6>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">Durée (heures)</label>
                                    <input type="number" class="form-control duration-input"
                                           id="duration_${service.service_key}"
                                           min="1" max="8760" placeholder="24">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Ou fin programmée</label>
                                    <input type="datetime-local" class="form-control datetime-input"
                                           id="endTime_${service.service_key}">
                                </div>
                                <div class="col-12">
                                    <button class="btn btn-success btn-sm" onclick="activateService('${service.service_key}')">
                                        <i class="bi bi-play-circle"></i> Activer le service
                                    </button>
                                    <button class="btn btn-secondary btn-sm" onclick="toggleActivationForm('${service.service_key}')">
                                        Annuler
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Extension Form -->
                        <div id="extensionForm_${service.service_key}" class="activation-form">
                            <h6>Prolonger le service</h6>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">Heures supplémentaires</label>
                                    <input type="number" class="form-control duration-input"
                                           id="extensionHours_${service.service_key}"
                                           min="1" max="8760" placeholder="12">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Ou nouvelle fin programmée</label>
                                    <input type="datetime-local" class="form-control datetime-input"
                                           id="newEndTime_${service.service_key}">
                                </div>
                                <div class="col-12">
                                    <button class="btn btn-warning btn-sm" onclick="extendService('${service.service_key}')">
                                        <i class="bi bi-clock-history"></i> Prolonger le service
                                    </button>
                                    <button class="btn btn-secondary btn-sm" onclick="toggleExtensionForm('${service.service_key}')">
                                        Annuler
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="service-stats">
                            <div class="stat-item">
                                <i class="bi bi-key"></i> <strong>Clé:</strong> ${service.service_key}
                            </div>
                            <div class="stat-item">
                                <i class="bi bi-arrow-repeat"></i> <strong>Activations:</strong> ${service.activation_count}
                            </div>
                            ${service.last_activated_at ? `
                                <div class="stat-item">
                                    <i class="bi bi-clock-history"></i> <strong>Dernière activation:</strong>
                                    ${formatDateTime(service.last_activated_at)}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
                `;
            }).join('');

            container.innerHTML = servicesHTML;
        }

        // Toggle activation form
        function toggleActivationForm(serviceKey) {
            const form = document.getElementById(`activationForm_${serviceKey}`);
            const extensionForm = document.getElementById(`extensionForm_${serviceKey}`);

            // Hide extension form if open
            if (extensionForm) {
                extensionForm.classList.remove('show');
            }

            form.classList.toggle('show');
        }

        // Toggle extension form
        function toggleExtensionForm(serviceKey) {
            const form = document.getElementById(`extensionForm_${serviceKey}`);
            const activationForm = document.getElementById(`activationForm_${serviceKey}`);

            // Hide activation form if open
            if (activationForm) {
                activationForm.classList.remove('show');
            }

            form.classList.toggle('show');
        }

        // Activate service
        async function activateService(serviceKey) {
            console.log('activateService called with serviceKey:', serviceKey);

            const durationInput = document.getElementById(`duration_${serviceKey}`);
            const endTimeInput = document.getElementById(`endTime_${serviceKey}`);

            console.log('durationInput:', durationInput);
            console.log('endTimeInput:', endTimeInput);

            if (!durationInput || !endTimeInput) {
                console.error('Input elements not found for serviceKey:', serviceKey);
                showAlert('Erreur: Formulaire d\'activation non trouvé. Veuillez d\'abord cliquer sur "Activer".', 'danger');
                return;
            }

            const durationHours = durationInput.value ? parseInt(durationInput.value) : null;
            const endTime = endTimeInput.value || null;

            console.log('durationHours:', durationHours);
            console.log('endTime:', endTime);

            if (!durationHours && !endTime) {
                showAlert('Veuillez spécifier une durée ou une heure de fin', 'warning');
                return;
            }

            // Show loading state
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Activation...';
            button.disabled = true;

            try {
                console.log('Activating service:', serviceKey, 'Duration:', durationHours, 'End time:', endTime);

                const requestData = {
                    service_key: serviceKey,
                    duration_hours: durationHours,
                    end_time: endTime || null,
                    activated_by: 'admin'
                };

                console.log('Request data:', requestData);
                console.log('Request JSON:', JSON.stringify(requestData));

                const response = await fetch(`${BASE_URL}/route/serviceManagementRoute.php?action=activate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Error response text:', errorText);
                    throw new Error(`HTTP error! status: ${response.status}, response: ${errorText}`);
                }

                const responseText = await response.text();
                console.log('Response text:', responseText);

                let data;
                try {
                    data = JSON.parse(responseText);
                    console.log('Response data:', data);
                } catch (parseError) {
                    console.error('JSON parse error:', parseError);
                    throw new Error(`Invalid JSON response: ${responseText}`);
                }

                if (data.success) {
                    showAlert(`Service "${serviceKey}" activé avec succès`, 'success');
                    await loadServices(); // Wait for services to reload
                    toggleActivationForm(serviceKey); // Hide the form
                } else {
                    showAlert('Erreur lors de l\'activation: ' + (data.error || 'Erreur inconnue'), 'danger');
                }
            } catch (error) {
                console.error('Error activating service:', error);
                showAlert('Erreur de connexion lors de l\'activation: ' + error.message, 'danger');
            } finally {
                // Restore button state
                if (button) {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            }
        }

        // Extend service
        async function extendService(serviceKey) {
            const extensionHoursInput = document.getElementById(`extensionHours_${serviceKey}`);
            const newEndTimeInput = document.getElementById(`newEndTime_${serviceKey}`);

            const additionalHours = extensionHoursInput.value ? parseInt(extensionHoursInput.value) : null;
            const newEndTime = newEndTimeInput.value || null;

            if (!additionalHours && !newEndTime) {
                showAlert('Veuillez spécifier des heures supplémentaires ou une nouvelle heure de fin', 'warning');
                return;
            }

            // Show loading state
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Extension...';
            button.disabled = true;

            try {
                console.log('Extending service:', serviceKey, 'Additional hours:', additionalHours, 'New end time:', newEndTime);

                const response = await fetch(`${BASE_URL}/route/serviceManagementRoute.php?action=extend`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        service_key: serviceKey,
                        additional_hours: additionalHours,
                        new_end_time: newEndTime || null,
                        extended_by: 'admin'
                    })
                });

                console.log('Response status:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('Response data:', data);

                if (data.success) {
                    showAlert(`Service "${serviceKey}" prolongé avec succès`, 'success');
                    await loadServices(); // Wait for services to reload
                    toggleExtensionForm(serviceKey); // Hide the form
                } else {
                    showAlert('Erreur lors de la prolongation: ' + (data.error || 'Erreur inconnue'), 'danger');
                }
            } catch (error) {
                console.error('Error extending service:', error);
                showAlert('Erreur de connexion lors de la prolongation: ' + error.message, 'danger');
            } finally {
                // Restore button state
                if (button) {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            }
        }

        // Deactivate service
        async function deactivateService(serviceKey) {
            if (!confirm('Êtes-vous sûr de vouloir désactiver ce service ?')) {
                return;
            }

            // Show loading state
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Désactivation...';
            button.disabled = true;

            try {
                console.log('Deactivating service:', serviceKey);

                const response = await fetch(`${BASE_URL}/route/serviceManagementRoute.php?action=deactivate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        service_key: serviceKey,
                        reason: 'manual',
                        deactivated_by: 'admin'
                    })
                });

                console.log('Response status:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('Response data:', data);

                if (data.success) {
                    showAlert(`Service "${serviceKey}" désactivé avec succès`, 'success');
                    await loadServices(); // Wait for services to reload
                } else {
                    showAlert('Erreur lors de la désactivation: ' + (data.error || 'Erreur inconnue'), 'danger');
                }
            } catch (error) {
                console.error('Error deactivating service:', error);
                showAlert('Erreur de connexion lors de la désactivation: ' + error.message, 'danger');
            } finally {
                // Restore button state
                if (button) {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            }
        }

        // Show service logs
        async function showServiceLogs(serviceKey) {
            const modal = new bootstrap.Modal(document.getElementById('logsModal'));
            modal.show();

            try {
                const response = await fetch(`${BASE_URL}/route/serviceManagementRoute.php?action=logs&service_key=${serviceKey}`);
                const data = await response.json();

                if (data.success) {
                    renderLogs(data.logs);
                } else {
                    document.getElementById('logsContainer').innerHTML = `
                        <div class="alert alert-danger">Erreur lors du chargement des journaux: ${data.error}</div>
                    `;
                }
            } catch (error) {
                console.error('Error loading logs:', error);
                document.getElementById('logsContainer').innerHTML = `
                    <div class="alert alert-danger">Erreur de connexion lors du chargement des journaux</div>
                `;
            }
        }

        // Render logs
        function renderLogs(logs) {
            const container = document.getElementById('logsContainer');

            if (logs.length === 0) {
                container.innerHTML = '<p class="text-muted text-center">Aucun journal trouvé</p>';
                return;
            }

            const logsHTML = logs.map(log => `
                <div class="border-bottom pb-2 mb-2">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <strong>${log.action}</strong> - ${log.display_name || log.service_key}
                            ${log.performed_by ? `<small class="text-muted">par ${log.performed_by}</small>` : ''}
                        </div>
                        <small class="text-muted">${formatDateTime(log.created_at)}</small>
                    </div>
                    ${log.details ? `<small class="text-muted">${JSON.stringify(log.details)}</small>` : ''}
                </div>
            `).join('');

            container.innerHTML = logsHTML;
        }

        // Check expired services
        async function checkExpiredServices() {
            try {
                const response = await fetch(`${BASE_URL}/route/serviceManagementRoute.php?action=check_expired`);
                const data = await response.json();

                if (data.success) {
                    if (data.count > 0) {
                        showAlert(`${data.count} service(s) expiré(s) désactivé(s)`, 'info');
                        loadServices();
                    } else {
                        showAlert('Aucun service expiré trouvé', 'info');
                    }
                } else {
                    showAlert('Erreur lors de la vérification: ' + data.error, 'danger');
                }
            } catch (error) {
                console.error('Error checking expired services:', error);
                showAlert('Erreur de connexion lors de la vérification', 'danger');
            }
        }

        // Refresh services
        function refreshServices() {
            loadServices();
        }

        // Start auto refresh
        function startAutoRefresh() {
            refreshInterval = setInterval(() => {
                loadServices();
            }, 30000); // Refresh every 30 seconds
        }

        // Utility functions
        function formatRemainingTime(remainingTime) {
            if (remainingTime.days > 0) {
                return `${remainingTime.days}j ${remainingTime.hours}h ${remainingTime.minutes}m`;
            } else if (remainingTime.hours > 0) {
                return `${remainingTime.hours}h ${remainingTime.minutes}m`;
            } else {
                return `${remainingTime.minutes}m`;
            }
        }

        function formatDateTime(dateTimeString) {
            const date = new Date(dateTimeString);
            return date.toLocaleString('fr-FR');
        }

        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertHTML = `
                <div class="alert alert-${type} alert-custom alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            alertContainer.innerHTML = alertHTML;

            // Auto dismiss after 5 seconds
            setTimeout(() => {
                const alert = alertContainer.querySelector('.alert');
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 5000);
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });
    </script>
</body>
</html>
