<?php
require_once "../model/examModel.php";
require_once "../utils/response.php";

// Get all exams
function getAllExamsAPI() {
    $exams = getAllExams();

    if (isset($exams['error'])) {
        jsonResponse(['error' => $exams['error']], 404);
    }

    jsonResponse(['data' => $exams], 200);
}

// Get exams by filters
function getExamsByFiltersAPI($filiere = null, $niveau = null, $groupe = null, $semestre = null) {
    error_log("getExamsByFiltersAPI called with: filiere=$filiere, niveau=$niveau, groupe=$groupe, semestre=$semestre");

    // Normalize semestre to lowercase for case-insensitive comparison
    if ($semestre) {
        $semestre = strtolower($semestre);
        error_log("Normalized semestre to lowercase: $semestre");
    }

    // Determine if this is an AP class or a cycle class
    $isAPClass = false;
    $isCycleClass = false;

    if ($niveau) {
        // Check if this is an AP class (niveau 1 or 2)
        if ($niveau == 1 || $niveau == 2) {
            $isAPClass = true;
            error_log("This is an AP class (niveau $niveau)");
        } else {
            $isCycleClass = true;
            error_log("This is a cycle class (niveau $niveau)");
        }
    }

    $exams = getExamsByFilters($filiere, $niveau, $groupe, $semestre);

    if (isset($exams['error'])) {
        error_log("Error in getExamsByFilters: " . $exams['error']);
        jsonResponse(['error' => $exams['error']], 404);
    }

    error_log("Found " . count($exams) . " exams matching the filters");

    // Add class type information to the response
    $classInfo = [
        'type' => $isAPClass ? 'AP' : ($isCycleClass ? 'Cycle' : 'Unknown'),
        'niveau' => $niveau,
        'filiere' => $filiere,
        'groupe' => $groupe,
        'semestre' => $semestre
    ];

    jsonResponse(['data' => $exams, 'class_info' => $classInfo], 200);
}

// Get exam by ID
function getExamByIdAPI($id) {
    error_log("getExamByIdAPI called with ID: $id");

    $exam = getExamById($id);

    if (isset($exam['error'])) {
        error_log("Error in getExamById: " . $exam['error']);
        jsonResponse(['error' => $exam['error']], 404);
    }

    jsonResponse(['data' => $exam], 200);
}

// Add a new exam
function addExamAPI($data) {
    error_log("addExamAPI called with data: " . print_r($data, true));

    // Validate required fields
    $requiredFields = ['id_module', 'id_enseignant', 'id_salle', 'type', 'date_examen', 'heure_debut', 'heure_fin', 'id_niveau', 'semestre'];

    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            error_log("Missing required field: $field");
            jsonResponse(['error' => "Missing required field: $field"], 400);
            return;
        }
    }

    try {
        $result = addExam($data);
        error_log("addExam result: " . print_r($result, true));

        if (isset($result['error'])) {
            error_log("Error in addExam: " . $result['error']);
            jsonResponse(['error' => $result['error']], 400);
            return;
        }

        jsonResponse(['data' => $result, 'message' => 'Exam added successfully'], 201);
    } catch (Exception $e) {
        error_log("Exception in addExam: " . $e->getMessage());
        jsonResponse(['error' => 'Server error: ' . $e->getMessage()], 500);
    }
}

// Update an exam
function updateExamAPI($data) {
    error_log("updateExamAPI called with data: " . print_r($data, true));

    // Validate required fields
    $requiredFields = ['id_examen', 'id_module', 'id_enseignant', 'id_salle', 'type', 'date_examen', 'heure_debut', 'heure_fin'];

    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            error_log("Missing required field for update: $field");
            jsonResponse(['error' => "Missing required field: $field"], 400);
            return;
        }
    }

    try {
        $result = updateExam($data);
        error_log("updateExam result: " . print_r($result, true));

        if (isset($result['error'])) {
            error_log("Error in updateExam: " . $result['error']);
            jsonResponse(['error' => $result['error']], 400);
            return;
        }

        jsonResponse(['data' => $result, 'message' => 'Exam updated successfully'], 200);
    } catch (Exception $e) {
        error_log("Exception in updateExam: " . $e->getMessage());
        jsonResponse(['error' => 'Server error: ' . $e->getMessage()], 500);
    }
}

// Delete an exam
function deleteExamAPI($id) {
    error_log("deleteExamAPI called with id: $id");

    if (!$id) {
        error_log("Missing ID for delete");
        jsonResponse(['error' => "Missing ID parameter"], 400);
        return;
    }

    try {
        $result = deleteExam($id);
        error_log("deleteExam result: " . print_r($result, true));

        if (isset($result['error'])) {
            error_log("Error in deleteExam: " . $result['error']);
            jsonResponse(['error' => $result['error']], 400);
            return;
        }

        jsonResponse(['data' => $result, 'message' => 'Exam deleted successfully'], 200);
    } catch (Exception $e) {
        error_log("Exception in deleteExam: " . $e->getMessage());
        jsonResponse(['error' => 'Server error: ' . $e->getMessage()], 500);
    }
}
?>