// Initialize the events
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM content loaded - exam-schedule.js');
  
    // Check if the display button exists
    const displayButton = document.getElementById('display-button');
    console.log('Display button found:', displayButton);
  
    try {
      const displayButton = document.getElementById('display-button');
      if (displayButton) {
        displayButton.addEventListener('click', function(event) {
          // Prevent the default action
          event.preventDefault();
  
          try {
            console.log('Display button clicked in exam-schedule.js');
  
            // Safely get filter values with error handling
            let filiere = '';
            let niveau = '';
            let groupe = '';
            let semestre = '';
            let semaine = '';
  
            // Safely get filiere
            const filiereElement = document.getElementById('filiere');
            if (filiereElement && filiereElement.value) {
              filiere = filiereElement.value;
            }
  
            // Safely get niveau
            const niveauElement = document.getElementById('niveau');
            if (niveauElement && niveauElement.value) {
              niveau = niveauElement.value;
            }
  
            // Safely get groupe
            const groupeElement = document.getElementById('groupe');
            if (groupeElement && groupeElement.value) {
              groupe = groupeElement.value;
            }
  
            // Safely get semestre
            const semestreElement = document.getElementById('semestre');
            if (semestreElement && semestreElement.value) {
              semestre = semestreElement.value;
            }
  
            // Safely get semaine
            const semaineElement = document.getElementById('semaine');
            if (semaineElement && semaineElement.value) {
              semaine = semaineElement.value;
            }
  
            console.log('Display button clicked with filters:', { filiere, niveau, groupe, semestre, semaine });
  
            // Validate required filters
            if (!niveau) {
              alert('Please select a level');
              return;
            }
  
            if (!semestre) {
              alert('Please select a semester');
              return;
            }
  
            // Build query string for the URL
            const queryParams = new URLSearchParams();
  
            if (filiere && filiere !== 'none') {
              queryParams.append('filiere', filiere);
              // Safely add the filiere name for display
              if (filiereElement && filiereElement.options && filiereElement.selectedIndex >= 0) {
                const selectedOption = filiereElement.options[filiereElement.selectedIndex];
                if (selectedOption && selectedOption.textContent) {
                  queryParams.append('filiereName', selectedOption.textContent);
                }
              }
            }
  
            if (niveau) {
              queryParams.append('niveau', niveau);
              // Safely add the niveau name for display
              if (niveauElement && niveauElement.options && niveauElement.selectedIndex >= 0) {
                const selectedOption = niveauElement.options[niveauElement.selectedIndex];
                if (selectedOption && selectedOption.textContent) {
                  queryParams.append('niveauName', selectedOption.textContent);
                }
              }
            }
  
            if (groupe && groupe !== 'all') {
              queryParams.append('groupe', groupe);
              // Safely add the groupe name for display
              if (groupeElement && groupeElement.options && groupeElement.selectedIndex >= 0) {
                const selectedOption = groupeElement.options[groupeElement.selectedIndex];
                if (selectedOption && selectedOption.textContent) {
                  queryParams.append('groupeName', selectedOption.textContent);
                }
              }
            }
  
            if (semestre) {
              queryParams.append('semestre', semestre);
            }
  
            if (semaine) {
              queryParams.append('semaine', semaine);
            }
  
            // Redirect to the exam schedule display page
            const url = `exam-schedule-display.php?${queryParams.toString()}`;
            console.log('Redirecting to:', url);
  
            // Simple redirect
            window.location.href = url;
          } catch (clickError) {
            console.error('Error in click handler:', clickError);
            // Fallback to a simple redirect
            window.location.href = 'exam-schedule-display.php';
          }
  
          return false;
        });
      } else {
        console.warn('Display button not found in the DOM');
      }
    } catch (error) {
      console.error('Error setting up click handler:', error);
    }
  });