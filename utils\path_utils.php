<?php
/**
 * Utilitaires pour la gestion des chemins
 */

/**
 * Obtient le chemin de base du projet
 * 
 * @return string Le chemin de base du projet (ex: /Projet-Web)
 */
function getBasePath() {
    // Récupérer le chemin de base à partir de la configuration du serveur
    $scriptName = $_SERVER['SCRIPT_NAME'];
    $scriptDir = dirname($scriptName);
    
    // Si le script est directement dans la racine du projet
    if ($scriptDir === '/' || $scriptDir === '\\') {
        return '';
    }
    
    // Trouver le nom du projet dans le chemin
    $parts = explode('/', $scriptDir);
    $projectName = '';
    
    // Chercher le nom du projet (généralement le premier dossier après la racine)
    foreach ($parts as $part) {
        if (!empty($part)) {
            $projectName = $part;
            break;
        }
    }
    
    // Si on a trouvé un nom de projet, construire le chemin de base
    if (!empty($projectName)) {
        return '/' . $projectName;
    }
    
    // Fallback: utiliser le chemin du script
    return $scriptDir;
}

/**
 * Construit une URL absolue à partir d'un chemin relatif
 * 
 * @param string $relativePath Chemin relatif (ex: view/assets/img/profile/image.jpg)
 * @return string URL absolue (ex: /Projet-Web/view/assets/img/profile/image.jpg)
 */
function buildAbsolutePath($relativePath) {
    $basePath = getBasePath();
    
    // Si le chemin relatif commence déjà par un slash, ne pas en ajouter un autre
    if (strpos($relativePath, '/') === 0) {
        return $basePath . $relativePath;
    }
    
    return $basePath . '/' . $relativePath;
}

/**
 * Construit une URL complète à partir d'un chemin relatif
 * 
 * @param string $relativePath Chemin relatif (ex: view/assets/img/profile/image.jpg)
 * @return string URL complète (ex: http://localhost/Projet-Web/view/assets/img/profile/image.jpg)
 */
function buildFullUrl($relativePath) {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $absolutePath = buildAbsolutePath($relativePath);
    
    return $protocol . '://' . $host . $absolutePath;
}
?>
