/* Update these styles in messages.css */
body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    color: #1f2937;
    overflow: auto; /* Changed from hidden to auto */
}

.main-content {
    flex: 1;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: 100vh;
    position: relative; /* Added to ensure proper stacking context */
}


.dashboards-container {
    display: flex;
    min-height: 100vh;
    overflow: auto; /* Changed from hidden to auto */
}

/* Messages Section */
.messages-content {
    flex: 1;
    padding: 16px 24px;
    max-width: 1000px;
    margin: 0 auto;
    width: 100%;
    overflow-y: visible; /* Changed from auto to visible */
}

.messages-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #eaeaea;
}

.messages-header h1 {
    font-size: 24px;
    margin: 0;
    color: #111827;
    font-weight: 600;
}

.messages-actions {
    display: flex;
    gap: 10px;
}

.create-message-btn {
    background-color: #10b981;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.2s;
}

.create-message-btn:hover {
    background-color: #059669;
}

.mark-all-btn {
    background-color: #2673ff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.2s;
}

.mark-all-btn:hover {
    background-color: #1e5dcc;
}

.notifications-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: calc(100vh - 150px); /* Hauteur maximale réduite */
    overflow-y: auto; /* Défilement vertical */
    padding-right: 5px; /* Espace pour la barre de défilement */
}

/* Message Toolbar */
.message-toolbar {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    padding: 5px 0;
    border-bottom: 1px solid #eaeaea;
}

.message-toolbar .btn-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: none;
    background-color: #f5f5f5;
    color: #6b7280;
    transition: all 0.2s;
    padding: 0;
}

.message-toolbar .btn-icon:hover {
    background-color: #e5e7eb;
    color: #4b5563;
}

.message-toolbar .btn-icon i {
    font-size: 16px;
}

.message-attachment-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    margin-top: 8px;
    margin-right: 8px;
    padding: 3px 8px;
    background-color: #f3f4f6;
    border-radius: 4px;
    font-size: 12px;
    color: #4b5563;
}

.message-attachment-indicator i {
    color: #2673ff;
}

.message-attachment-indicator .btn-sm {
    padding: 0;
    margin-left: 5px;
    background: none;
    border: none;
    color: #9ca3af;
    font-size: 12px;
}

.message-attachment-indicator .btn-sm:hover {
    color: #4b5563;
}

/* Success Modal styles */
.modal-header.bg-success {
    background-color: #28a745 !important;
}

.modal-header.bg-danger {
    background-color: #dc3545 !important;
}

.btn-close-white {
    filter: invert(1) grayscale(100%) brightness(200%);
}

.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

/* Autocomplete styles */
.autocomplete-container {
    position: relative;
    width: 100%;
}

.autocomplete-items {
    position: absolute;
    border: 1px solid #ddd;
    border-top: none;
    z-index: 99;
    top: 100%;
    left: 0;
    right: 0;
    max-height: 200px;
    overflow-y: auto;
    background-color: #fff;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.autocomplete-item {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid #f1f1f1;
}

.autocomplete-item:last-child {
    border-bottom: none;
}

.autocomplete-item:hover {
    background-color: #f1f1f1;
}

.autocomplete-item.active {
    background-color: #e9ecef;
}

.autocomplete-item .username {
    font-weight: bold;
}

.autocomplete-item .user-info {
    font-size: 12px;
    color: #6c757d;
}

/* Notification Cards */
.me-card {
    display: flex;
    background-color: white;
    border-radius: 12px;
    padding: 12px 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    cursor: pointer;
    height: auto;
    min-height: 0;
}

.me-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    background-color: #f9fbff;
}

.me-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 4px;
    background-color: #2673ff;
    opacity: 0.7;
}

.me-card.read:before {
    display: none;
}

/* Compact Card Style */
.me-card.compact {
    padding: 8px 15px;
    margin-bottom: 8px;
    height: auto;
    min-height: 0;
}

.me-card.compact .notification-content {
    min-height: 0;
    display: grid;
    grid-template-columns: 70% 30%;
    width: 100%;
    padding: 0 5px;
    align-items: center;
    grid-gap: 10px;
}

.notification-title-container {
    display: flex;
    align-items: center;
    gap: 6px;
    justify-content: flex-start;
    text-align: left;
    padding-right: 8px;
    justify-self: start;
    width: 100%;
    height: auto;
}

.me-card.compact .notification-title {
    margin: 0;
    font-size: 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    width: 100%;
    line-height: 1.2;
}

.notification-meta-compact {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    text-align: right;
}

.attachment-indicator {
    color: #6b7280;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.me-card.compact .notification-time {
    margin: 0;
    font-size: 11px;
    color: #6b7280;
    text-align: right;
    white-space: nowrap;
    width: 100%;
    justify-self: end;
    grid-column: 2;
    display: block;
    line-height: 1;
}

.notification-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e6f3ff;
    border-radius: 8px;
    width: 40px;
    height: 40px;
    margin-right: 12px;
    flex-shrink: 0;
    color: #2673ff;
}

.notification-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    position: relative;
}

.notification-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: #1f2937;
}

.notification-text {
    color: #4b5563;
    margin: 0 0 8px 0;
    font-size: 14px;
    line-height: 1.4;
}

.notification-actions {
    display: flex;
    gap: 10px;
}
.notification-text a {
    text-decoration: none;
}

.action-link {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    margin-top: 5px;
    background-color: #e8f1fd;
    color: #2673ff;
    border: 1px solid #d0e3fc;
    padding: 6px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s;
    text-decoration: none;
    margin-right: 10px;
}

.action-link:hover {
    background-color: #d0e3fc;
    color: #2673ff;
}

.download-file-btn:before {
    content: '\f019';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 5px;
}

.visit-link-btn:before {
    content: '\f0c1';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 5px;
}

/* Message Detail Modal Styles */
.message-detail-header {
    margin-bottom: 20px;
    border-bottom: 1px solid #eaeaea;
    padding-bottom: 15px;
}

.message-detail-header h3 {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #1f2937;
}

.message-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    color: #6b7280;
    font-size: 14px;
}

.message-date {
    display: flex;
    align-items: center;
}

.message-date:before {
    content: '\f017';
    font-family: 'Font Awesome 5 Free';
    font-weight: 400;
    margin-right: 5px;
}

.message-receiver {
    display: flex;
    align-items: center;
}

.message-receiver:before {
    content: '\f007';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 5px;
}

.message-detail-content {
    margin-bottom: 20px;
    line-height: 1.6;
    color: #4b5563;
}

.message-detail-content p {
    margin-bottom: 15px;
}

.message-attachments {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
    padding-top: 15px;
    border-top: 1px solid #eaeaea;
}

.message-actions {
    display: flex;
    gap: 10px;
    padding-top: 15px;
    border-top: 1px solid #eaeaea;
}
.mark-read-btn {
    background-color: #e8f1fd;
    color: #2673ff;
    border: 1px solid #d0e3fc;
    padding: 6px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s;
    display: flex;
    align-items: center;
}

.mark-read-btn:hover {
    background-color: #d0e3fc;
}

.delete-btn {
    background-color: #fff;
    color: #ef4444;
    border: 1px solid #fecaca;
    padding: 6px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s;
}

.delete-btn:hover {
    background-color: #fef2f2;
}

.notification-time {
    color: #6b7280;
    font-size: 13px;
    flex-shrink: 0;
    margin-left: 15px;
    align-self: flex-start;
    margin-top: 10px;

}

/* Toast notifications */
.toast-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #323232;
    color: white;
    padding: 12px 24px;
    border-radius: 6px;
    min-width: 250px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    animation: slideIn 0.3s ease;
    display: block;
}

.toast.fade-out {
    opacity: 0;
}
.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
    padding: 1rem;
}

.pagination-btn {
    background-color: #2673ff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.pagination-btn:hover {
    background-color: #1e5dcc;
}

.page-info {
    color: #6b7280;
    font-size: 14px;
}
/* Responsive adjustments */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }

    .notification-card {
        flex-direction: column;
    }

    .notification-time {
        align-self: flex-end;
        margin-top: 10px;
    }

    .messages-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .messages-header h1 {
        font-size: 24px;
    }

    .messages-actions {
        flex-direction: column;
        width: 100%;
        gap: 8px;
    }

    .create-message-btn,
    .mark-all-btn {
        width: 100%;
    }

    .notification-actions {
        flex-direction: column;
        width: 100%;
    }

    .mark-read-btn, .delete-btn {
        width: 100%;
        text-align: center;
    }
}


@media (max-width: 480px) {
    .notification-title {
        font-size: 16px;
    }

    .notification-text {
        font-size: 13px;
    }

    .notification-card {
        padding: 15px;
    }
}

/* Styles personnalisés pour la barre de défilement */
.notifications-list::-webkit-scrollbar {
    width: 8px;
}

.notifications-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.notifications-list::-webkit-scrollbar-thumb {
    background: #2673ff;
    border-radius: 10px;
    opacity: 0.7;
}

.notifications-list::-webkit-scrollbar-thumb:hover {
    background: #1e5dcc;
}

/* Pour Firefox */
.notifications-list {
    scrollbar-width: thin;
    scrollbar-color: #2673ff #f1f1f1;
}
