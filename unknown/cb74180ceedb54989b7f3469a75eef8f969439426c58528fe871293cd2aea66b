<?php
/**
 * Script de test pour l'API des observations
 */

// Simuler une session d'enseignant
session_start();
$_SESSION['user'] = [
    'role' => 'enseignant',
    'teacher_id' => 1,
    'nom' => 'Test',
    'prenom' => 'Enseignant'
];

// Inclure le contrôleur
require_once 'controller/teacherHistoryController.php';

echo "=== Test de l'API des observations ===\n\n";

// Test 1: Récupérer les observations pour l'année 2024/2025
echo "Test 1: Récupération des observations pour 2024/2025\n";
echo "URL: route/teacherHistoryRoute.php?action=getObservations&teacher_id=1&academic_year=2024/2025\n\n";

// Simuler la requête GET
$_GET['action'] = 'getObservations';
$_GET['teacher_id'] = '1';
$_GET['academic_year'] = '2024/2025';
$_SERVER['REQUEST_METHOD'] = 'GET';

// Capturer la sortie
ob_start();
getTeacherObservationsAPI($_GET['teacher_id'], $_GET['academic_year']);
$output = ob_get_clean();

echo "Réponse API:\n";
echo $output . "\n\n";

// Test 2: Récupérer les observations pour l'année 2023/2024
echo "Test 2: Récupération des observations pour 2023/2024\n";
echo "URL: route/teacherHistoryRoute.php?action=getObservations&teacher_id=1&academic_year=2023/2024\n\n";

$_GET['academic_year'] = '2023/2024';

ob_start();
getTeacherObservationsAPI($_GET['teacher_id'], $_GET['academic_year']);
$output = ob_get_clean();

echo "Réponse API:\n";
echo $output . "\n\n";

// Test 3: Test avec un enseignant inexistant
echo "Test 3: Test avec un enseignant inexistant\n";
echo "URL: route/teacherHistoryRoute.php?action=getObservations&teacher_id=999&academic_year=2024/2025\n\n";

$_GET['teacher_id'] = '999';
$_GET['academic_year'] = '2024/2025';

ob_start();
getTeacherObservationsAPI($_GET['teacher_id'], $_GET['academic_year']);
$output = ob_get_clean();

echo "Réponse API:\n";
echo $output . "\n\n";

// Test 4: Test de la fonction du modèle directement
echo "Test 4: Test direct de la fonction du modèle\n";
require_once 'model/teacherHistoryModel.php';

$observations = getTeacherObservations(1, '2024/2025');
echo "Résultat du modèle:\n";
print_r($observations);
echo "\n";

echo "=== Fin des tests ===\n";
?>
