/**
 * Vacant Teaching Units Management JavaScript
 * Handles the frontend interactions for managing vacant teaching units
 */

let currentDepartmentId = null;
let currentChefId = null;
let unitsData = [];
let currentFilter = 'all';

/**
 * Initialize the vacant units page
 */
function initializeVacantUnitsPage(departmentId, chefId) {
    currentDepartmentId = departmentId;
    currentChefId = chefId;

    // Load department information
    loadDepartmentInfo(departmentId);

    // Load unassigned teaching units
    loadUnassignedUnits(departmentId);
}

/**
 * Load department information
 */
function loadDepartmentInfo(departmentId) {
    // Try to load department info, but don't fail if endpoint doesn't exist
    fetch(`../../route/vacantUERoute.php?action=getDepartmentInfo&department_id=${departmentId}`)
        .then(response => {
            if (!response.ok) {
                // If endpoint doesn't exist, just show a default name
                document.getElementById('departmentName').textContent = 'Département ' + departmentId;
                return null;
            }
            return response.json();
        })
        .then(data => {
            if (data && data.data && data.data.nom_dep) {
                document.getElementById('departmentName').textContent = data.data.nom_dep;
            } else {
                document.getElementById('departmentName').textContent = 'Département ' + departmentId;
            }
        })
        .catch(() => {
            // Silently fall back to default name
            document.getElementById('departmentName').textContent = 'Département ' + departmentId;
        });
}

/**
 * Load unassigned teaching units
 */
function loadUnassignedUnits(departmentId) {
    showLoading(true);

    fetch(`../../route/vacantUERoute.php?action=getUnassignedUnits&department_id=${departmentId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                showError('Error loading unassigned units: ' + data.error);
                return;
            }

            unitsData = data.data || [];
            updateStatistics();
            renderUnits();
        })
        .catch(error => {
            console.error('Error loading unassigned units:', error);
            showError('Error loading unassigned units. Please try again.');
        })
        .finally(() => {
            showLoading(false);
        });
}

/**
 * Update statistics cards
 */
function updateStatistics() {
    const total = unitsData.length;
    const vacant = unitsData.filter(unit => unit.is_vacant === '1').length;
    const notVacant = unitsData.filter(unit => unit.is_vacant === '0').length;
    const unmarked = unitsData.filter(unit => unit.is_vacant === null).length;

    document.getElementById('totalUnits').textContent = total;
    document.getElementById('vacantUnits').textContent = vacant;
    document.getElementById('notVacantUnits').textContent = notVacant;
    document.getElementById('unmarkedUnits').textContent = unmarked;
}

/**
 * Render teaching units
 */
function renderUnits() {
    const container = document.getElementById('unitsContainer');

    if (unitsData.length === 0) {
        container.innerHTML = `
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle me-2"></i>
                Aucune unité d'enseignement non assignée trouvée pour ce département.
            </div>
        `;
        return;
    }

    // Filter units based on current filter
    let filteredUnits = unitsData;
    if (currentFilter !== 'all') {
        filteredUnits = unitsData.filter(unit => {
            switch (currentFilter) {
                case 'vacant':
                    return unit.is_vacant === '1';
                case 'not-vacant':
                    return unit.is_vacant === '0';
                case 'unmarked':
                    return unit.is_vacant === null;
                default:
                    return true;
            }
        });
    }

    if (filteredUnits.length === 0) {
        container.innerHTML = `
            <div class="alert alert-warning text-center">
                <i class="fas fa-filter me-2"></i>
                Aucune unité d'enseignement ne correspond au filtre actuel.
            </div>
        `;
        return;
    }

    // Create table structure matching the established design
    let html = `
        <div class="row">
            <div class="col-12">
                <div class="card" style="border-radius: 12px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.06); border: none;">
                    <div class="card-header" style="background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); border-bottom: 1px solid rgba(0, 0, 0, 0.05); border-radius: 12px 12px 0 0; position: relative;">
                        <div style="position: absolute; top: 0; left: 0; width: 5px; height: 100%; background: linear-gradient(to bottom, #4e73df, #36b9cc);"></div>
                        <div class="d-flex align-items-center" style="padding-left: 15px;">
                            <div class="me-3">
                                <div style="width: 40px; height: 40px; border-radius: 50%; background: linear-gradient(135deg, #4e73df 0%, #224abe 100%); display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 8px rgba(78, 115, 223, 0.25);">
                                    <i class="fas fa-exclamation-triangle" style="font-size: 16px; color: white;"></i>
                                </div>
                            </div>
                            <div>
                                <h5 class="mb-1" style="color: #343a40; font-weight: 600;">
                                    Unités d'Enseignement Non Assignées
                                </h5>
                                <p class="mb-0" style="color: #6c757d; font-size: 0.9rem;">
                                    <i class="fas fa-list me-2"></i>
                                    ${filteredUnits.length} unité${filteredUnits.length > 1 ? 's' : ''} trouvée${filteredUnits.length > 1 ? 's' : ''}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="vacantUnitsTable">
                                <thead style="background-color: rgba(78, 115, 223, 0.03);">
                                    <tr>
                                        <th style="border: none; padding: 15px; color: #4e73df; font-weight: 600;">Module</th>
                                        <th style="border: none; padding: 15px; color: #4e73df; font-weight: 600;">Type</th>
                                        <th style="border: none; padding: 15px; color: #4e73df; font-weight: 600;">Filière</th>
                                        <th style="border: none; padding: 15px; color: #4e73df; font-weight: 600;">Niveau</th>
                                        <th style="border: none; padding: 15px; color: #4e73df; font-weight: 600;">Volume</th>
                                        <th style="border: none; padding: 15px; color: #4e73df; font-weight: 600;">Groupes</th>
                                        <th style="border: none; padding: 15px; color: #4e73df; font-weight: 600;">Statut</th>
                                        <th style="border: none; padding: 15px; color: #4e73df; font-weight: 600;">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
    `;

    filteredUnits.forEach((unit, index) => {
        const statusBadge = getStatusBadge(unit.is_vacant);
        const rowClass = index % 2 === 0 ? '' : 'table-light';

        html += `
            <tr class="${rowClass}" data-unit-id="${unit.ue_id}" style="transition: background-color 0.2s ease;">
                <td style="border: none; padding: 15px; vertical-align: middle;">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-book me-2" style="color: #4e73df; font-size: 0.9rem;"></i>
                        <strong>${escapeHtml(unit.module_name)}</strong>
                    </div>
                </td>
                <td style="border: none; padding: 15px; vertical-align: middle;">
                    <span style="color: #6c757d; font-weight: 500; font-size: 0.9rem;">
                        ${escapeHtml(unit.ue_type)}
                    </span>
                </td>
                <td style="border: none; padding: 15px; vertical-align: middle;">
                    <span style="color: #6c757d; font-weight: 500; font-size: 0.9rem;">
                        ${escapeHtml(unit.nom_filiere)}
                    </span>
                </td>
                <td style="border: none; padding: 15px; vertical-align: middle;">
                    <span style="color: #6c757d; font-weight: 500; font-size: 0.9rem;">
                        ${escapeHtml(unit.niveau)} - S${escapeHtml(unit.semestre)}
                    </span>
                </td>
                <td style="border: none; padding: 15px; vertical-align: middle;">
                    <span style="font-weight: 500;">${unit.volume_horaire}h</span>
                </td>
                <td style="border: none; padding: 15px; vertical-align: middle;">
                    <span style="font-weight: 500;">${unit.nb_groupes}</span>
                </td>
                <td style="border: none; padding: 15px; vertical-align: middle;">
                    ${statusBadge}
                </td>
                <td style="border: none; padding: 15px; vertical-align: middle;">
                    <div class="d-flex gap-2">
                        <button class="btn btn-vacant btn-sm" onclick="markVacancy(${unit.ue_id}, true)" title="Marquer comme vacante">
                            <i class="fas fa-exclamation-triangle me-1"></i>Vacante
                        </button>
                        <button class="btn btn-not-vacant btn-sm" onclick="markVacancy(${unit.ue_id}, false)" title="Marquer comme non vacante">
                            <i class="fas fa-check-circle me-1"></i>Non vacante
                        </button>
                        <button class="btn btn-sm" style="background-color: #f8f9fa; color: #6c757d; border: 1px solid #dee2e6;" onclick="addComment(${unit.ue_id})" title="Ajouter un commentaire">
                            <i class="fas fa-comment"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;

        // Add comment row if exists
        if (unit.comments) {
            html += `
                <tr class="${rowClass}">
                    <td colspan="8" style="border: none; padding: 0 15px 15px 15px;">
                        <div class="alert alert-light mb-0" style="margin-left: 30px; font-size: 0.9rem;">
                            <i class="fas fa-comment me-2"></i>
                            <strong>Commentaire:</strong> ${escapeHtml(unit.comments)}
                            ${unit.marked_at ? `
                                <br><small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    Mis à jour le ${formatDate(unit.marked_at)} par ${escapeHtml(unit.marked_by_name || 'Inconnu')}
                                </small>
                            ` : ''}
                        </div>
                    </td>
                </tr>
            `;
        }
    });

    html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;

    // Initialize DataTable if available
    if (typeof $.fn.DataTable !== 'undefined') {
        $('#vacantUnitsTable').DataTable({
            responsive: true,
            pageLength: 10,
            language: {
                "sProcessing": "Traitement en cours...",
                "sSearch": "Rechercher :",
                "sLengthMenu": "Afficher _MENU_ éléments",
                "sInfo": "Affichage de l'élément _START_ à _END_ sur _TOTAL_ éléments",
                "sInfoEmpty": "Affichage de l'élément 0 à 0 sur 0 élément",
                "sInfoFiltered": "(filtré de _MAX_ éléments au total)",
                "sInfoPostFix": "",
                "sLoadingRecords": "Chargement en cours...",
                "sZeroRecords": "Aucun élément à afficher",
                "sEmptyTable": "Aucune donnée disponible dans le tableau",
                "oPaginate": {
                    "sFirst": "Premier",
                    "sPrevious": "Précédent",
                    "sNext": "Suivant",
                    "sLast": "Dernier"
                },
                "oAria": {
                    "sSortAscending": ": activer pour trier la colonne par ordre croissant",
                    "sSortDescending": ": activer pour trier la colonne par ordre décroissant"
                }
            },
            columnDefs: [
                { orderable: false, targets: [6, 7] } // Disable sorting for Status and Actions columns
            ]
        });
    }
}

/**
 * Get status class for unit card
 */
function getStatusClass(isVacant) {
    if (isVacant === '1') return 'is-vacant';
    if (isVacant === '0') return 'not-vacant';
    return 'unmarked';
}

/**
 * Get status badge HTML
 */
function getStatusBadge(isVacant) {
    if (isVacant === '1') {
        return '<span class="status-badge status-vacant"><i class="fas fa-exclamation-triangle me-1"></i>Vacante</span>';
    }
    if (isVacant === '0') {
        return '<span class="status-badge status-not-vacant"><i class="fas fa-check-circle me-1"></i>Non Vacante</span>';
    }
    return '<span class="status-badge status-unmarked"><i class="fas fa-question-circle me-1"></i>Non Marquée</span>';
}

/**
 * Mark teaching unit vacancy status
 */
function markVacancy(ueId, isVacant) {
    if (!confirm(`Êtes-vous sûr de vouloir marquer cette unité d'enseignement comme ${isVacant ? 'vacante' : 'non vacante'} ?`)) {
        return;
    }

    const data = {
        ue_id: ueId,
        department_id: currentDepartmentId,
        is_vacant: isVacant,
        marked_by: currentChefId,
        academic_year: null // Will use current academic year
    };

    fetch('../../route/vacantUERoute.php?action=updateVacancyStatus', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showError('Erreur lors de la mise à jour du statut : ' + data.error);
            return;
        }

        showSuccess(`Unité d'enseignement marquée comme ${isVacant ? 'vacante' : 'non vacante'} avec succès.`);

        // Refresh the data
        loadUnassignedUnits(currentDepartmentId);
    })
    .catch(error => {
        console.error('Error updating vacancy status:', error);
        showError('Erreur lors de la mise à jour du statut. Veuillez réessayer.');
    });
}

/**
 * Add comment to teaching unit
 */
function addComment(ueId) {
    const unit = unitsData.find(u => u.ue_id == ueId);
    const currentComment = unit ? unit.comments : '';

    const comment = prompt('Entrez un commentaire pour cette unité d\'enseignement :', currentComment || '');

    if (comment === null) return; // User cancelled

    const data = {
        ue_id: ueId,
        department_id: currentDepartmentId,
        is_vacant: unit ? (unit.is_vacant === '1' ? true : (unit.is_vacant === '0' ? false : null)) : null,
        marked_by: currentChefId,
        comments: comment,
        academic_year: null
    };

    // If no vacancy status is set, we need to set one
    if (data.is_vacant === null) {
        if (!confirm('Cette unité d\'enseignement n\'a pas de statut de vacance. Voulez-vous la marquer comme vacante ?')) {
            data.is_vacant = false;
        } else {
            data.is_vacant = true;
        }
    }

    fetch('../../route/vacantUERoute.php?action=updateVacancyStatus', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showError('Erreur lors de la mise à jour du commentaire : ' + data.error);
            return;
        }

        showSuccess('Commentaire mis à jour avec succès.');
        loadUnassignedUnits(currentDepartmentId);
    })
    .catch(error => {
        console.error('Error updating comment:', error);
        showError('Erreur lors de la mise à jour du commentaire. Veuillez réessayer.');
    });
}

/**
 * Filter units by status
 */
function filterUnits(filter, buttonElement) {
    currentFilter = filter;

    // Update active button
    document.querySelectorAll('.btn-group button').forEach(btn => {
        btn.classList.remove('active');
    });

    // If buttonElement is provided, use it; otherwise find the button by filter
    if (buttonElement) {
        buttonElement.classList.add('active');
    } else {
        // Find button by onclick attribute or data attribute
        const buttons = document.querySelectorAll('.btn-group button');
        buttons.forEach(btn => {
            if (btn.onclick && btn.onclick.toString().includes(`'${filter}'`)) {
                btn.classList.add('active');
            }
        });
    }

    renderUnits();
}

/**
 * Refresh data
 */
function refreshData() {
    loadUnassignedUnits(currentDepartmentId);
}

/**
 * Show/hide loading spinner
 */
function showLoading(show) {
    const spinner = document.getElementById('loadingSpinner');
    const container = document.getElementById('unitsContainer');

    if (show) {
        spinner.style.display = 'block';
        container.style.display = 'none';
    } else {
        spinner.style.display = 'none';
        container.style.display = 'block';
    }
}

/**
 * Show error message
 */
function showError(message) {
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = container.querySelector('.alert-danger');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

/**
 * Show success message
 */
function showSuccess(message) {
    const alertHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // Auto-dismiss after 3 seconds
    setTimeout(() => {
        const alert = container.querySelector('.alert-success');
        if (alert) {
            alert.remove();
        }
    }, 3000);
}

/**
 * Escape HTML to prevent XSS
 */
function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Format date string
 */
function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}
