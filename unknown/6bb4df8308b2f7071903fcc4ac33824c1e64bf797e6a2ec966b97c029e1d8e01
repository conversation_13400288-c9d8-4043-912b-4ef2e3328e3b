<?php
// Vérifier l'authentification
require_once '../includes/auth_check_enseignant.php';

// Récupérer les informations de l'enseignant depuis la session
$userName = $_SESSION['user']['username'] ?? 'Enseignant';
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$departmentName = $_SESSION['user']['department_name'] ?? 'Non spécifié';
$specialtyName = $_SESSION['user']['specialty_name'] ?? 'Non spécifié';

// Construire le nom complet
$fullName = $prenom . ' ' . $nom;
if (trim($fullName) === '') {
    $fullName = $userName;
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enseignant - Tableau de Bord</title>

     <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard-style.css">
    <link rel="stylesheet" href="../assets/css/header-fix.css">
</head>
<body>
    <div class="dashboard-container">
        <?php
        include '../includes/sidebar.php';

        // Inclure le modèle des visites et enregistrer la visite du dashboard
        require_once '../../model/visitsModel.php';
        recordVisit('enseignant', 'dashboard');
        ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>
            <div class="container-fluid p-4">
                <h1 class="page-title">Tableau de Bord - Enseignant</h1>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="text-muted">Bienvenue, <strong><?php echo htmlspecialchars($fullName); ?></strong>. Voici un aperçu de vos activités d'enseignement.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <div class="d-inline-block me-2">
                            <span class="badge bg-primary rounded-pill">
                                <?php echo date('d M Y'); ?>
                            </span>
                        </div>
                        <div class="d-inline-block me-2">
                            <span class="badge bg-info rounded-pill">
                                <i class="fas fa-clock me-1"></i> <span id="current-time"></span>
                            </span>
                        </div>
                        <div class="d-inline-block">
                            <span class="badge bg-secondary rounded-pill">
                                <i class="fas fa-user me-1"></i> Enseignant
                            </span>
                        </div>
                    </div>
                </div>

                <div class="row g-4">
                    <!-- Carte des Modules Assignés -->
                    <div class="col-md-6 col-lg-4 animate-fade-in delay-1">
                        <a href="assigned_modules.php" class="text-decoration-none">
                            <div class="card dashboard-card card-modules h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="card-icon icon-modules">
                                            <i class="fas fa-book"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Modules Assignés</h5>
                                            <div class="small text-muted">Vos modules actuels</div>
                                        </div>
                                    </div>
                                    <ul class="dashboard-list">
                                        <!-- Données statiques pour les modules assignés -->
                                        <li><span><i class="fas fa-book-open me-2 text-primary"></i>Programmation Web</span></li>
                                        <li><span><i class="fas fa-book-open me-2 text-primary"></i>Bases de Données</span></li>
                                        <li><span><i class="fas fa-book-open me-2 text-primary"></i>Algorithmes et Structures de Données</span></li>
                                    </ul>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte des Préférences d'Enseignement -->
                    <div class="col-md-6 col-lg-4 animate-fade-in delay-2">
                        <a href="module_preferences.php" class="text-decoration-none">
                            <div class="card dashboard-card card-preferences h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="card-icon icon-preferences">
                                            <i class="fas fa-list-check"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Préférences de Modules</h5>
                                            <div class="small text-muted">Gérer vos préférences</div>
                                        </div>
                                    </div>
                                    <div class="text-center py-4">
                                        <p>Sélectionnez les modules que vous préférez enseigner pour la prochaine année académique.</p>
                                        <button class="btn btn-outline-primary mt-2">Gérer mes préférences</button>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte des Informations Personnelles -->
                    <div class="col-md-6 col-lg-4 animate-fade-in delay-3">
                        <a href="profile.php" class="text-decoration-none">
                            <div class="card dashboard-card card-profile h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="card-icon icon-profile">
                                            <i class="fas fa-user-circle"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Profil</h5>
                                            <div class="small text-muted">Vos informations</div>
                                        </div>
                                    </div>
                                    <ul class="dashboard-list">
                                        <li>
                                            <span class="fw-bold">Département:</span>
                                            <span><?php echo htmlspecialchars($departmentName); ?></span>
                                        </li>
                                        <li>
                                            <span class="fw-bold">Spécialité:</span>
                                            <span><?php echo htmlspecialchars($specialtyName); ?></span>
                                        </li>
                                        <li>
                                            <span class="fw-bold">CNI:</span>
                                            <span><?php echo htmlspecialchars($userName); ?></span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Charge d'Enseignement -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-4">
                        <a href="calculate_load.php" class="text-decoration-none">
                            <div class="card dashboard-card card-load square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-load mb-3">
                                        <i class="fas fa-calculator"></i>
                                    </div>
                                    <h5 class="card-title">Charge d'Enseignement</h5>
                                    <div class="small text-muted">Calculer votre charge</div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Emploi du Temps -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-5">
                        <a href="schedule.php" class="text-decoration-none">
                            <div class="card dashboard-card card-schedule square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-schedule mb-3">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <h5 class="card-title">Emploi du Temps</h5>
                                    <div class="small text-muted">Consulter votre planning</div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Notes -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-6">
                        <a href="upload_grades.php" class="text-decoration-none">
                            <div class="card dashboard-card card-grades square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-grades mb-3">
                                        <i class="fas fa-graduation-cap"></i>
                                    </div>
                                    <h5 class="card-title">Notes</h5>
                                    <div class="small text-muted">Gérer les notes</div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Historique -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-7">
                        <a href="history.php" class="text-decoration-none">
                            <div class="card dashboard-card card-history square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-history mb-3">
                                        <i class="fas fa-history"></i>
                                    </div>
                                    <h5 class="card-title">Historique</h5>
                                    <div class="small text-muted">Consulter votre historique</div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Carte Communication -->
                    <div class="col-md-6 col-lg-3 animate-fade-in delay-8">
                        <a href="messages.php" class="text-decoration-none">
                            <div class="card dashboard-card card-messages square-card">
                                <div class="card-body text-center d-flex flex-column align-items-center justify-content-center">
                                    <div class="card-icon icon-messages mb-3">
                                        <i class="fas fa-comments"></i>
                                    </div>
                                    <h5 class="card-title">Messages</h5>
                                    <div class="small text-muted">Communiquer avec les collègues</div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Dashboard Scripts -->
    <script src="../assets/js/dashboard-dynamic.js"></script>
    <script src="../assets/js/dashboard-chart.js"></script>
    <script src="../assets/js/dashboard-notifications.js"></script>
    <!-- Notifications JS -->
    <script src="../assets/js/notifications.js"></script>
    <script src="../assets/js/sidebar.js"></script>

</body>
</html>