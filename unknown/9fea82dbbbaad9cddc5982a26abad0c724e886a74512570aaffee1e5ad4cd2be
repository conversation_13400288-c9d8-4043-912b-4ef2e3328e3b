<?php
/**
 * Database structure check for affectation functionality
 */

require_once 'config/db.php';

echo "<h1>Database Structure Check</h1>";

$conn = getConnection();
if (!$conn) {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
    exit;
}

// Check tables and their structures
$tables = [
    'filiere' => ['id_filiere', 'nom_filiere', 'id_departement'],
    'module' => ['id', 'nom', 'filiere_id', 'id_niveau', 'id_semestre'],
    'uniteenseignement' => ['id', 'type', 'volume_horaire', 'nb_groupes', 'module_id'],
    'enseignant' => ['id_enseignant', 'CNI', 'nom', 'prenom', 'role', 'id_departement'],
    'affectation' => ['id', 'annee_academique', 'statut'],
    'ue_vacantes' => ['id', 'ue_id', 'academic_year', 'is_vacant']
];

foreach ($tables as $tableName => $expectedColumns) {
    echo "<h2>Table: $tableName</h2>";
    
    // Check if table exists
    $result = mysqli_query($conn, "SHOW TABLES LIKE '$tableName'");
    if (mysqli_num_rows($result) == 0) {
        echo "<p style='color: red;'>❌ Table '$tableName' does not exist</p>";
        continue;
    }
    
    echo "<p style='color: green;'>✅ Table '$tableName' exists</p>";
    
    // Get table structure
    $result = mysqli_query($conn, "DESCRIBE $tableName");
    if (!$result) {
        echo "<p style='color: red;'>❌ Cannot describe table '$tableName'</p>";
        continue;
    }
    
    echo "<h3>Columns:</h3><ul>";
    $actualColumns = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $actualColumns[] = $row['Field'];
        echo "<li><strong>{$row['Field']}</strong> - {$row['Type']} " . 
             ($row['Null'] == 'YES' ? '(NULL)' : '(NOT NULL)') . 
             ($row['Key'] ? " [{$row['Key']}]" : '') . "</li>";
    }
    echo "</ul>";
    
    // Check for expected columns
    $missingColumns = array_diff($expectedColumns, $actualColumns);
    if (!empty($missingColumns)) {
        echo "<p style='color: orange;'>⚠️ Missing expected columns: " . implode(', ', $missingColumns) . "</p>";
    }
    
    // Get row count
    $countResult = mysqli_query($conn, "SELECT COUNT(*) as count FROM $tableName");
    if ($countResult) {
        $countRow = mysqli_fetch_assoc($countResult);
        echo "<p>Row count: <strong>{$countRow['count']}</strong></p>";
    }
}

// Check specific data
echo "<h2>Data Checks</h2>";

// Check filiere data
echo "<h3>Filiere Data:</h3>";
$result = mysqli_query($conn, "SELECT id_filiere, nom_filiere FROM filiere LIMIT 5");
if ($result && mysqli_num_rows($result) > 0) {
    echo "<ul>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<li>ID: {$row['id_filiere']} - {$row['nom_filiere']}</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: red;'>❌ No filiere data found</p>";
}

// Check module data for filiere 1
echo "<h3>Module Data for Filiere 1:</h3>";
$result = mysqli_query($conn, "SELECT id, nom FROM module WHERE filiere_id = 1 LIMIT 5");
if ($result && mysqli_num_rows($result) > 0) {
    echo "<ul>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<li>ID: {$row['id']} - {$row['nom']}</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: red;'>❌ No module data found for filiere 1</p>";
}

// Check UE data
echo "<h3>Teaching Units (UE) Data:</h3>";
$result = mysqli_query($conn, "SELECT ue.id, ue.type, m.nom as module_name 
                               FROM uniteenseignement ue 
                               LEFT JOIN module m ON ue.module_id = m.id 
                               WHERE m.filiere_id = 1 LIMIT 5");
if ($result && mysqli_num_rows($result) > 0) {
    echo "<ul>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<li>UE ID: {$row['id']} - {$row['type']} ({$row['module_name']})</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: red;'>❌ No UE data found for filiere 1</p>";
}

// Check enseignant data
echo "<h3>Enseignant Data:</h3>";
$result = mysqli_query($conn, "SELECT id_enseignant, nom, prenom, role FROM enseignant LIMIT 5");
if ($result && mysqli_num_rows($result) > 0) {
    echo "<ul>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<li>ID: {$row['id_enseignant']} - {$row['prenom']} {$row['nom']} ({$row['role']})</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: red;'>❌ No enseignant data found</p>";
}

// Check vacataire data specifically
echo "<h3>Vacataire Data:</h3>";
$result = mysqli_query($conn, "SELECT id_enseignant, nom, prenom FROM enseignant WHERE role = 'vacataire' LIMIT 5");
if ($result && mysqli_num_rows($result) > 0) {
    echo "<ul>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<li>ID: {$row['id_enseignant']} - {$row['prenom']} {$row['nom']}</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: orange;'>⚠️ No vacataire data found</p>";
}

// Check affectation table structure specifically
echo "<h3>Affectation Table Structure:</h3>";
$result = mysqli_query($conn, "SHOW COLUMNS FROM affectation");
if ($result) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$row['Key']}</td>";
        echo "<td>{$row['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Check ue_vacantes table
echo "<h3>UE Vacantes Data:</h3>";
$result = mysqli_query($conn, "SELECT COUNT(*) as count FROM ue_vacantes WHERE is_vacant = 1");
if ($result) {
    $row = mysqli_fetch_assoc($result);
    echo "<p>Vacant UEs marked: <strong>{$row['count']}</strong></p>";
} else {
    echo "<p style='color: red;'>❌ Cannot check ue_vacantes data</p>";
}

mysqli_close($conn);
echo "<h2>Check Complete</h2>";
?>
