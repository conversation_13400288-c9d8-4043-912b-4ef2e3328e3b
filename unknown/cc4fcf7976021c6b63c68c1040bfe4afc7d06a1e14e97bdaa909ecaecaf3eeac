-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Hôte : 127.0.0.1
-- <PERSON><PERSON><PERSON><PERSON> le : jeu. 29 mai 2025 à 01:23
-- Version du serveur : 10.4.32-MariaDB
-- Version de PHP : 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de données : `ensah`
--

-- --------------------------------------------------------

--
-- Structure de la table `activities`
--

CREATE TABLE `activities` (
  `id` int(11) NOT NULL,
  `type` enum('message','notification','cours','evenement') NOT NULL,
  `id_user` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `related_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Structure de la table `admin`
--

CREATE TABLE `admin` (
  `id_admin` int(11) NOT NULL,
  `CNI` varchar(20) NOT NULL,
  `nom` varchar(50) NOT NULL,
  `prenom` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `tele` varchar(15) DEFAULT NULL,
  `date_naissance` date NOT NULL,
  `lieu_naissance` varchar(100) DEFAULT NULL,
  `sexe` enum('masculin','féminin') NOT NULL,
  `ville` varchar(50) DEFAULT NULL,
  `pays` varchar(50) DEFAULT NULL,
  `date_debut_travail` date DEFAULT NULL,
  `photo_url` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `admin`
--

INSERT INTO `admin` (`id_admin`, `CNI`, `nom`, `prenom`, `email`, `tele`, `date_naissance`, `lieu_naissance`, `sexe`, `ville`, `pays`, `date_debut_travail`, `photo_url`) VALUES
(6, 'CN12345', 'Chiboub', 'Salmaa', '<EMAIL>', '06234567810', '1999-02-10', 'Fès', 'féminin', 'Fes', 'Maroc', '2025-03-27', 'view/assets/img/profile/CN12345_1745374463.jpg'),
(7, 'CNI12121', 'Solving', 'Problem', '<EMAIL>', '0641800125', '2000-11-12', 'TAZA', 'féminin', 'TAZA', 'Maroc', '2025-03-27', ''),
(10, 'CN1987', 'chiboub', 'Salma', '<EMAIL>', '0654331211', '2000-02-20', 'TAZA', 'masculin', 'Taza', 'Maroc', '2025-03-13', ''),
(16, 'CN123454', 'Chiboub', 'Salma', '<EMAIL>', '0623456789', '1998-02-20', 'Fès', 'masculin', 'Fes', 'Maroc', '2023-11-12', '');

-- --------------------------------------------------------

--
-- Structure de la table `affectation`
--

CREATE TABLE `affectation` (
  `id` int(11) NOT NULL,
  `professeur_id` int(11) NOT NULL,
  `unite_enseignement_id` int(11) NOT NULL,
  `annee_academique` varchar(9) NOT NULL,
  `valide` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `affectation`
--

INSERT INTO `affectation` (`id`, `professeur_id`, `unite_enseignement_id`, `annee_academique`, `valide`) VALUES
(1, 29, 2, '2025', 1),
(2, 29, 3, '2025', 1),
(3, 1, 2, '2025', 0),
(4, 1, 2, '2025', 1);

-- --------------------------------------------------------

--
-- Structure de la table `ai_generated_schedules`
--

CREATE TABLE `ai_generated_schedules` (
  `id` int(11) NOT NULL,
  `schedule_name` varchar(100) NOT NULL,
  `generation_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `generated_by` int(11) DEFAULT NULL,
  `status` enum('draft','published','archived') DEFAULT 'draft',
  `algorithm_version` varchar(50) DEFAULT NULL,
  `algorithm_parameters` text DEFAULT NULL,
  `quality_score` float DEFAULT NULL,
  `constraint_violations` text DEFAULT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Structure de la table `ai_schedule_items`
--

CREATE TABLE `ai_schedule_items` (
  `id` int(11) NOT NULL,
  `schedule_id` int(11) NOT NULL,
  `id_module` int(11) NOT NULL,
  `id_enseignant` int(11) NOT NULL,
  `id_salle` int(11) NOT NULL,
  `type` enum('Cours','TD','TP') NOT NULL,
  `day` enum('lundi','mardi','mercredi','jeudi','vendredi','samedi') NOT NULL,
  `heure_debut` time NOT NULL,
  `heure_fin` time NOT NULL,
  `id_niveau` int(11) NOT NULL,
  `semestre` varchar(5) NOT NULL,
  `id_filiere` int(11) DEFAULT NULL,
  `id_groupe` int(11) DEFAULT NULL,
  `quality_score` float DEFAULT NULL,
  `preference_satisfaction` float DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Structure de la table `choixue`
--

CREATE TABLE `choixue` (
  `id` int(11) NOT NULL,
  `professeur_id` int(11) NOT NULL,
  `unite_enseignement_id` int(11) NOT NULL,
  `ordre_de_choix` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Structure de la table `club`
--

CREATE TABLE `club` (
  `id_club` int(11) NOT NULL,
  `nom` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `date_creation` date NOT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `president_id` varchar(20) DEFAULT NULL,
  `vice_president_id` varchar(20) DEFAULT NULL,
  `secretary_id` varchar(20) DEFAULT NULL,
  `treasurer_id` varchar(20) DEFAULT NULL,
  `statut` enum('actif','inactif') DEFAULT 'actif',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `club`
--

INSERT INTO `club` (`id_club`, `nom`, `description`, `date_creation`, `logo`, `president_id`, `vice_president_id`, `secretary_id`, `treasurer_id`, `statut`, `created_at`, `updated_at`) VALUES
(1, '01 club', 'club d\'informatique', '2005-02-02', 'https://cdn-icons-png.flaticon.com/512/2721/2721620.png', '12344', 'CNI12123', 'P1234567800', 'P123456786', 'actif', '2025-04-27 02:41:57', '2025-04-29 01:02:04'),
(3, 'Club Chess', 'club for entertainement ', '2025-04-29', 'https://cdn-icons-png.flaticon.com/512/3588/3588592.png', 'CNI12121', '12344', 'P123456786', 'P123456788', 'actif', '2025-04-29 01:00:55', '2025-05-04 16:15:49'),
(1, '01 club', 'club d\'informatique', '2005-02-02', 'https://cdn-icons-png.flaticon.com/512/2721/2721620.png', '12344', 'CNI12123', 'P1234567800', 'P123456786', 'actif', '2025-04-27 02:41:57', '2025-04-29 01:02:04'),
(3, 'Club Chess', 'club for entertainement ', '2025-04-29', 'https://cdn-icons-png.flaticon.com/512/3588/3588592.png', 'CNI12121', '12344', 'P123456786', 'P123456788', 'actif', '2025-04-29 01:00:55', '2025-05-04 16:15:49');

-- --------------------------------------------------------

--
-- Structure de la table `club_programme`
--

CREATE TABLE `club_programme` (
  `id_programme` int(11) NOT NULL,
  `id_club` int(11) NOT NULL,
  `titre` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `date_debut` datetime NOT NULL,
  `date_fin` datetime NOT NULL,
  `lieu` varchar(100) DEFAULT NULL,
  `type` enum('evenement','sortie','formation','reunion','autre') NOT NULL DEFAULT 'evenement',
  `statut` enum('planifie','en_cours','termine','annule') NOT NULL DEFAULT 'planifie',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `club_programme`
--

INSERT INTO `club_programme` (`id_programme`, `id_club`, `titre`, `description`, `date_debut`, `date_fin`, `lieu`, `type`, `statut`, `created_at`, `updated_at`) VALUES
(2, 1, 'Atelier de programmation Python', 'Initiation à la programmation Python pour débutants', '2023-12-20 09:00:00', '2023-12-20 12:00:00', 'Salle informatique B', 'formation', 'planifie', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(3, 1, 'Visite de l\'entreprise Tech Solutions', 'Visite guidée des locaux et rencontre avec les professionnels', '2024-01-10 10:00:00', '2024-01-10 15:00:00', 'Tech Solutions, Zone Industrielle', 'sortie', 'planifie', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(4, 1, 'Réunion de planification annuelle', 'Planification des activités pour l\'année 2024', '2023-12-05 16:00:00', '2023-12-05 18:00:00', 'Salle de réunion C', 'reunion', 'termine', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(5, 1, 'Hackathon de développement web', 'Compétition de développement web sur 48 heures', '2024-02-15 09:00:00', '2024-02-17 09:00:00', 'Campus universitaire', 'evenement', 'planifie', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(6, 3, 'Tournoi d\'échecs interne', 'Compétition amicale entre les membres du club', '2023-12-18 14:00:00', '2023-12-18 18:00:00', 'Salle de jeux', 'evenement', 'planifie', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(7, 3, 'Cours d\'ouvertures pour débutants', 'Apprendre les principes fondamentaux des ouvertures aux échecs', '2024-01-05 15:00:00', '2024-01-05 17:00:00', 'Salle d\'étude', 'formation', 'planifie', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(8, 3, 'Visite du championnat régional d\'échecs', 'Observation des parties et analyse avec des maîtres', '2024-01-20 09:00:00', '2024-01-20 17:00:00', 'Centre culturel municipal', 'sortie', 'planifie', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(9, 3, 'Réunion mensuelle du club', 'Discussion sur les progrès et les événements à venir', '2023-12-10 17:00:00', '2023-12-10 19:00:00', 'Salle de réunion A', 'reunion', 'termine', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(10, 3, 'Simultanée avec un Maître International', 'Un maître international affrontera plusieurs joueurs simultanément', '2024-03-01 14:00:00', '2024-03-01 18:00:00', 'Grand hall du campus', 'evenement', 'planifie', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(11, 3, 'Atelier de tactiques et combinaisons', 'Améliorer sa vision tactique et sa capacité à calculer les variantes', '2024-02-10 14:00:00', '2024-02-10 16:30:00', 'Salle d\'étude B', 'formation', 'en_cours', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(12, 3, 'Analyse de parties célèbres', 'Étude des parties historiques des grands champions', '2024-02-25 15:00:00', '2024-02-25 17:00:00', 'Bibliothèque universitaire', 'formation', 'planifie', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(13, 3, 'Tournoi rapide (Blitz)', 'Compétition de parties rapides (5 minutes par joueur)', '2024-01-15 18:00:00', '2024-01-15 21:00:00', 'Cafétéria du campus', 'evenement', 'planifie', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(14, 1, 'exple', 'tes test ', '2025-04-29 10:00:00', '2025-04-30 00:00:00', 'Amphi A', 'formation', 'en_cours', '2025-04-29 12:51:41', '2025-04-29 12:51:41'),
(2, 1, 'Atelier de programmation Python', 'Initiation à la programmation Python pour débutants', '2023-12-20 09:00:00', '2023-12-20 12:00:00', 'Salle informatique B', 'formation', 'planifie', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(3, 1, 'Visite de l\'entreprise Tech Solutions', 'Visite guidée des locaux et rencontre avec les professionnels', '2024-01-10 10:00:00', '2024-01-10 15:00:00', 'Tech Solutions, Zone Industrielle', 'sortie', 'planifie', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(4, 1, 'Réunion de planification annuelle', 'Planification des activités pour l\'année 2024', '2023-12-05 16:00:00', '2023-12-05 18:00:00', 'Salle de réunion C', 'reunion', 'termine', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(5, 1, 'Hackathon de développement web', 'Compétition de développement web sur 48 heures', '2024-02-15 09:00:00', '2024-02-17 09:00:00', 'Campus universitaire', 'evenement', 'planifie', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(6, 3, 'Tournoi d\'échecs interne', 'Compétition amicale entre les membres du club', '2023-12-18 14:00:00', '2023-12-18 18:00:00', 'Salle de jeux', 'evenement', 'planifie', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(7, 3, 'Cours d\'ouvertures pour débutants', 'Apprendre les principes fondamentaux des ouvertures aux échecs', '2024-01-05 15:00:00', '2024-01-05 17:00:00', 'Salle d\'étude', 'formation', 'planifie', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(8, 3, 'Visite du championnat régional d\'échecs', 'Observation des parties et analyse avec des maîtres', '2024-01-20 09:00:00', '2024-01-20 17:00:00', 'Centre culturel municipal', 'sortie', 'planifie', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(9, 3, 'Réunion mensuelle du club', 'Discussion sur les progrès et les événements à venir', '2023-12-10 17:00:00', '2023-12-10 19:00:00', 'Salle de réunion A', 'reunion', 'termine', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(10, 3, 'Simultanée avec un Maître International', 'Un maître international affrontera plusieurs joueurs simultanément', '2024-03-01 14:00:00', '2024-03-01 18:00:00', 'Grand hall du campus', 'evenement', 'planifie', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(11, 3, 'Atelier de tactiques et combinaisons', 'Améliorer sa vision tactique et sa capacité à calculer les variantes', '2024-02-10 14:00:00', '2024-02-10 16:30:00', 'Salle d\'étude B', 'formation', 'en_cours', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(12, 3, 'Analyse de parties célèbres', 'Étude des parties historiques des grands champions', '2024-02-25 15:00:00', '2024-02-25 17:00:00', 'Bibliothèque universitaire', 'formation', 'planifie', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(13, 3, 'Tournoi rapide (Blitz)', 'Compétition de parties rapides (5 minutes par joueur)', '2024-01-15 18:00:00', '2024-01-15 21:00:00', 'Cafétéria du campus', 'evenement', 'planifie', '2025-04-29 12:47:57', '2025-04-29 12:47:57'),
(14, 1, 'exple', 'tes test ', '2025-04-29 10:00:00', '2025-04-30 00:00:00', 'Amphi A', 'formation', 'en_cours', '2025-04-29 12:51:41', '2025-04-29 12:51:41');

-- --------------------------------------------------------

--
-- Structure de la table `configuration_charge`
--

CREATE TABLE `configuration_charge` (
  `id_config` int(11) NOT NULL,
  `annee_universitaire` varchar(9) NOT NULL,
  `charge_minimale` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Structure de la table `cycle`
--

CREATE TABLE `cycle` (
  `id` int(11) NOT NULL,
  `nom` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `cycle`
--

INSERT INTO `cycle` (`id`, `nom`) VALUES
(1, 'Cycle préparatoire'),
(2, 'Cycle d\'ingenieur');

-- --------------------------------------------------------

--
-- Structure de la table `demands`
--

CREATE TABLE `demands` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `type` enum('Student','Teacher') NOT NULL,
  `author_id` varchar(20) DEFAULT NULL,
  `author_avatar` varchar(255) DEFAULT '../assets/images/avatars/default.png',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `status` enum('Pending','Accepted','Rejected') DEFAULT 'Pending',
  `message` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `demands`
--

INSERT INTO `demands` (`id`, `title`, `description`, `type`, `author_id`, `author_avatar`, `created_at`, `status`, `message`) VALUES
(0, 'qwertyu', 'ertyuio', 'Student', '59', '../assets/images/avatars/default.png', '2025-04-24 23:28:07', 'Accepted', 'wertyu'),
(1, 'Request for Project Approval', 'Request for approval of final year project topic (AI-powered chatbot assistant).', 'Student', '56', '../assets/images/avatars/avatar1.jpg', '2025-04-21 02:22:54', 'Accepted', 'demande'),
(2, 'Laboratory Equipment Request', ' NEW Application for new laboratory equipment and budget allocation for research.', 'Student', '60', '../assets/images/avatars/avatar2.jpg', '2025-04-21 02:22:54', 'Rejected', 'demande'),
(3, 'test test', 'just a test', 'Student', '64', '../assets/images/avatars/default.png', '2025-04-24 01:04:25', 'Pending', 'demande'),
(5, 'attestation de stage ', ' besoin d\'attestation ', 'Student', '61', '', '2025-04-24 02:18:21', 'Accepted', 'demande'),
(6, 'DEMANDE DE LA SALLE DE TP', 'JE VEUX LA SALLE DE TP DE 14:00 A 18:00', 'Teacher', '5', '../assets/images/avatars/default.png', '2025-04-24 01:24:13', 'Pending', 'demande');

-- --------------------------------------------------------

--
-- Structure de la table `departement`
--

CREATE TABLE `departement` (
  `id_departement` int(11) NOT NULL,
  `nom_dep` varchar(100) NOT NULL,
  `id_chef_departement` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `departement`
--

INSERT INTO `departement` (`id_departement`, `nom_dep`, `id_chef_departement`) VALUES
(1, 'Informatique', 3),
(2, 'physique', NULL),
(4, 'langue', NULL);

--
-- Déclencheurs `departement`
--
DELIMITER $$
CREATE TRIGGER `check_chef_departement_before_insert` BEFORE INSERT ON `departement` FOR EACH ROW BEGIN
    DECLARE role_chef_departement VARCHAR(255);

    -- Get the role of the enseignant
    SELECT role INTO role_chef_departement
    FROM enseignant
    WHERE id_enseignant = NEW.id_chef_departement;

    -- Check if the role is 'chef de département'
    IF role_chef_departement != 'chef de département' THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'id_chef_departement must be a "chef de département"';
    END IF;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Structure de la table `emploi_temps`
--

CREATE TABLE `emploi_temps` (
  `id` int(11) NOT NULL,
  `affectation_id` int(11) DEFAULT NULL,
  `salle_id` int(11) DEFAULT NULL,
  `jour` enum('lundi','mardi','mercredi','jeudi','vendredi','samedi') DEFAULT NULL,
  `heure_debut` time DEFAULT NULL,
  `heure_fin` time DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Structure de la table `enseignant`
--

CREATE TABLE `enseignant` (
  `id_enseignant` int(11) NOT NULL,
  `CNI` varchar(20) NOT NULL,
  `nom` varchar(50) NOT NULL,
  `prenom` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `tele` varchar(15) DEFAULT NULL,
  `date_naissance` date NOT NULL,
  `lieu_naissance` varchar(100) DEFAULT NULL,
  `sexe` enum('masculin','féminin') NOT NULL,
  `ville` varchar(50) DEFAULT NULL,
  `pays` varchar(50) DEFAULT NULL,
  `role` enum('enseignant','chef de departement','coordinateur','vacataire','admin') NOT NULL,
  `id_specialite` int(11) DEFAULT NULL,
  `id_departement` int(11) DEFAULT NULL,
  `date_debut_travail` date DEFAULT NULL,
  `profile_picture` varchar(255) NOT NULL,
  `charge_horaire_accomplie` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `enseignant`
--

INSERT INTO `enseignant` (`id_enseignant`, `CNI`, `nom`, `prenom`, `email`, `tele`, `date_naissance`, `lieu_naissance`, `sexe`, `ville`, `pays`, `role`, `id_specialite`, `id_departement`, `date_debut_travail`, `profile_picture`, `charge_horaire_accomplie`) VALUES
(1, 'salmaEns', 'Chiboub', 'Salma', '<EMAIL>', '0641800125', '2004-05-01', 'Taza', 'féminin', 'TAZA', 'MAROC', 'enseignant', 1, 1, '2025-03-02', '', 0),
(2, 'salmaCor', 'Chiboub', 'Salma', '<EMAIL>', '0641800125', '2004-11-08', 'Taza', 'féminin', 'Taza', 'Maroc', 'coordinateur', 2, 1, '2025-05-01', '', 0),
(3, 'salmaChef', 'Chiboub', 'Salma', '<EMAIL>', '0641800125', '2004-05-01', 'Taza', 'féminin', 'TAZA', 'MAROC', 'chef de departement', 1, 1, '2025-03-02', '', 0),
(4, 'CNI170', 'badi', 'mohammed', '<EMAIL>', '0612345678', '1980-07-20', 'Marrakech', 'masculin', 'Marrakech', 'Morocco', 'enseignant', NULL, 2, '2024-02-13', '', 0),
(5, 'CNI147', 'Bahri', 'abdelhak', '<EMAIL>', '0623456789', '1985-02-14', 'Fès', 'masculin', 'Fès', 'Morocco', 'chef de departement', NULL, 1, '2025-02-15', '', 0),
(25, 'CNI028', 'bahjari', 'imane ', '<EMAIL>', '0612323678', '1980-07-20', 'Marrakech', 'féminin', 'Marrakech', 'Morocco', 'enseignant', NULL, 4, '2025-02-11', '', 0),
(27, 'douae', 'Douae', 'Abdouni', '<EMAIL>', '1212121212124', '2004-05-01', 'Taza', 'féminin', 'Tetouan', 'Maroc', 'enseignant', 1, 1, '2025-05-01', '', 0),
(29, 'CN12345', 'Salma', 'Chiboub', '<EMAIL>', '0641800125', '2004-11-08', 'Taza', 'féminin', 'Taza', 'Maroc', 'enseignant', NULL, 1, '0000-00-00', '', 0),
(33, 'CNI09844', 'najeh', 'asmaa ', '<EMAIL>', '0609345678', '1980-07-20', 'Marrakech', 'masculin', 'Marrakech', 'Morocco', 'enseignant', NULL, 1, '2025-05-05', '', 0);

-- --------------------------------------------------------

--
-- Structure de la table `enseignant_filiere`
--

CREATE TABLE `enseignant_filiere` (
  `id_enseignant` int(11) NOT NULL,
  `id_filiere` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `enseignant_filiere`
--

INSERT INTO `enseignant_filiere` (`id_enseignant`, `id_filiere`) VALUES
(25, 1);

-- --------------------------------------------------------

--
-- Structure de la table `etudiant`
--

CREATE TABLE `etudiant` (
  `id_etudiant` int(11) NOT NULL,
  `CNE` varchar(20) NOT NULL,
  `nom` varchar(50) NOT NULL,
  `prenom` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `tele` varchar(15) DEFAULT NULL,
  `sexe` enum('masculin','féminin') NOT NULL,
  `pays` varchar(50) DEFAULT NULL,
  `ville` varchar(50) DEFAULT NULL,
  `date_naissance` date NOT NULL,
  `lieu_naissance` varchar(100) DEFAULT NULL,
  `coordonne_parental` text DEFAULT NULL,
  `id_filiere` int(11) DEFAULT NULL,
  `date_inscription` date DEFAULT NULL,
  `id_niveau` int(11) DEFAULT NULL,
  `profile_picture` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `etudiant`
--

INSERT INTO `etudiant` (`id_etudiant`, `CNE`, `nom`, `prenom`, `email`, `tele`, `sexe`, `pays`, `ville`, `date_naissance`, `lieu_naissance`, `coordonne_parental`, `id_filiere`, `date_inscription`, `id_niveau`, `profile_picture`) VALUES
(56, 'CN12345', 'Salma', 'Chiboub', '<EMAIL>', '0641800125', 'féminin', 'Maroc', 'TAZA', '2004-08-08', 'TAZA', '0641909774', 1, '2025-03-29', 3, ''),
(59, 'CNI12123', 'Ahmed', 'Elhajouji', '<EMAIL>', '0641800125', 'féminin', 'Maroc', 'TAZA', '2004-08-08', 'TAZA', '0641909774', 1, '2025-03-29', 3, ''),
(60, 'P123456789', 'Dupont', 'Jean', '<EMAIL>', '0612345678', 'féminin', 'France', 'Paris', '2000-01-01', 'Paris', '0687654321', 1, '2023-09-15', 3, ''),
(61, 's130059429', 'Problem Solving', 'Salma', '<EMAIL>', '0623456789', 'masculin', 'Maroc', 'TAZA', '1998-05-04', 'TAZA', '0641909774', 1, '2025-04-03', 3, ''),
(62, 'P123456788', 'Dupont', 'Jean', '<EMAIL>', '0612345678', 'féminin', 'France', 'Paris', '2005-01-01', 'Paris', '0687654321', 1, '2025-04-03', 1, ''),
(63, 'P123456786', 'Dupont', 'Jean', '<EMAIL>', '0612345678', 'féminin', 'France', 'Paris', '2000-02-08', 'Paris', '0687654321', 1, '2025-04-03', 1, ''),
(64, 'P1234567800', 'Dupont', 'Jean', '<EMAIL>', '0612345678', 'féminin', 'France', 'Paris', '1998-07-08', 'Paris', '0687654321', 2, '2025-04-04', 1, ''),
(83, 'salma123', 'Salma', 'Chiboub', '<EMAIL>', '0641800125', 'masculin', 'Maroc', 'TAZA', '2004-08-08', 'TAZA', '0641909774', 1, '2025-03-29', 1, ''),
(86, 's130059428', 'Salma', 'Chiboub', '<EMAIL>', '0641800125', 'masculin', 'Maroc', 'TAZA', '2004-08-08', 'TAZA', '0641909774', 2, '2025-03-29', 1, '');

-- --------------------------------------------------------

--
-- Structure de la table `events`
--

CREATE TABLE `events` (
  `id_event` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `category` enum('Administrative','Academic','Other') NOT NULL,
  `event_date` date NOT NULL,
  `event_time` time NOT NULL,
  `location` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `send_notification` tinyint(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `events`
--

INSERT INTO `events` (`id_event`, `title`, `description`, `category`, `event_date`, `event_time`, `location`, `created_at`, `send_notification`) VALUES
(18, 'Conférence sur ', 'Découvrez les dernières avancées en intelligence artificielle', 'Other', '2023-12-15', '14:00:00', 'Amphithéâtre A', '2025-04-24 17:02:44', 0),
(19, 'Journée Portes Ouvertes', 'Venez découvrir notre université', 'Academic', '2023-12-20', '09:00:00', 'Campus principal', '2025-04-24 17:02:44', 0),
(20, 'Remise des Diplômes', 'Cérémonie de remise des diplômes pour les étudiants de dernière année', 'Administrative', '2023-12-25', '16:00:00', 'Grand Hall', '2025-04-24 17:02:44', 1);

-- --------------------------------------------------------

--
-- Structure de la table `events_image`
--

CREATE TABLE `events_image` (
  `id` int(11) NOT NULL,
  `id_event` int(11) DEFAULT NULL,
  `photo_url` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `events_image`
--

INSERT INTO `events_image` (`id`, `id_event`, `photo_url`) VALUES
(14, 20, 'uploads/events/681770f4810d0_ensah2.jpg');

-- --------------------------------------------------------

--
-- Structure de la table `filiere`
--

CREATE TABLE `filiere` (
  `id_filiere` int(11) NOT NULL,
  `nom_filiere` varchar(100) NOT NULL,
  `id_coordinateur` int(11) DEFAULT NULL,
  `id_cycle` int(10) NOT NULL,
  `id_dep` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `filiere`
--

INSERT INTO `filiere` (`id_filiere`, `nom_filiere`, `id_coordinateur`, `id_cycle`, `id_dep`) VALUES
(1, 'Génie Informatique', 29, 2, 1),
(2, 'Génie Civil', 4, 2, 2),
(3, 'Génie de l\'eau et de l\'environement', 5, 2, 1),
(4, 'CP', 5, 1, 1);

-- --------------------------------------------------------

--
-- Structure de la table `messages`
--

CREATE TABLE `messages` (
  `id` int(11) NOT NULL,
  `sender_id` int(11) NOT NULL,
  `receiver_id` int(11) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `content` text DEFAULT NULL,
  `media_url` varchar(255) DEFAULT NULL,
  `file_path` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_read` tinyint(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `messages`
--

INSERT INTO `messages` (`id`, `sender_id`, `receiver_id`, `title`, `content`, `media_url`, `file_path`, `created_at`, `updated_at`, `is_read`) VALUES
(31, 17, 27, 'hello', 'hello world !', 'https://ensah.ma/', NULL, '2025-05-14 13:58:31', '2025-05-14 13:58:31', 0);

-- --------------------------------------------------------

--
-- Structure de la table `module`
--

CREATE TABLE `module` (
  `id` int(11) NOT NULL,
  `nom` varchar(100) NOT NULL,
  `volume_total` int(11) NOT NULL,
  `specialite_id` int(11) DEFAULT NULL,
  `filiere_id` int(11) DEFAULT NULL,
  `id_niveau` int(11) DEFAULT NULL,
  `id_semestre` int(10) NOT NULL,
  `is_cours` tinyint(1) NOT NULL DEFAULT 1,
  `is_td` tinyint(1) NOT NULL DEFAULT 1,
  `is_tp` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `module`
--

INSERT INTO `module` (`id`, `nom`, `volume_total`, `specialite_id`, `filiere_id`, `id_niveau`, `id_semestre`, `is_cours`, `is_td`, `is_tp`) VALUES
(1, 'Programmation C++', 130, 1, 1, 3, 5, 1, 0, 0),
(2, 'TEST', 130, 1, 2, 3, 6, 1, 1, 0),
(3, 'Base de donnees', 108, 1, 1, 3, 5, 1, 1, 0),
(4, 'Algorithmes', 100, 2, 1, 4, 7, 1, 1, 0),
(5, 'Language C', 90, 1, 1, 4, 8, 1, 1, 0);

-- --------------------------------------------------------

--
-- Structure de la table `module_preferences`
--

CREATE TABLE `module_preferences` (
  `id` int(11) NOT NULL,
  `id_enseignant` int(11) NOT NULL,
  `id_module` int(11) NOT NULL,
  `preference_level` int(11) DEFAULT 5,
  `reason` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `module_preferences`
--

INSERT INTO `module_preferences` (`id`, `id_enseignant`, `id_module`, `preference_level`, `reason`, `created_at`, `updated_at`) VALUES
(9, 27, 1, 10, '', '2025-05-13 22:08:10', '2025-05-13 22:08:10');

-- --------------------------------------------------------

--
-- Structure de la table `niveaux`
--

CREATE TABLE `niveaux` (
  `id` int(11) NOT NULL,
  `nom` varchar(10) NOT NULL,
  `cycle_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `niveaux`
--

INSERT INTO `niveaux` (`id`, `nom`, `cycle_id`) VALUES
(1, 'AP1', 1),
(2, 'AP2', 1),
(3, 'CI1', 2),
(4, 'CI2', 2),
(5, 'CI3', 2);

-- --------------------------------------------------------

--
-- Structure de la table `note`
--

CREATE TABLE `note` (
  `id_note` int(11) NOT NULL,
  `id_etudiant` int(11) NOT NULL,
  `id_module` int(11) NOT NULL,
  `id_niveau` int(11) NOT NULL,
  `semestre` varchar(2) DEFAULT NULL,
  `session` enum('normale','rattrapage') NOT NULL,
  `valeur` decimal(5,2) NOT NULL CHECK (`valeur` between 0 and 20),
  `date_saisie` date DEFAULT curdate(),
  `id_filiere` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `note`
--

INSERT INTO `note` (`id_note`, `id_etudiant`, `id_module`, `id_niveau`, `semestre`, `session`, `valeur`, `date_saisie`, `id_filiere`) VALUES
(1, 59, 1, 3, '5', 'normale', 16.00, '2025-05-21', 1),
(2, 60, 1, 3, '5', 'normale', 17.00, '2025-05-20', 1),
(3, 61, 1, 3, '5', 'normale', 9.00, '2025-05-17', 1),
(4, 56, 1, 3, '5', 'normale', 12.00, '2025-05-17', 1);

-- --------------------------------------------------------

--
-- Structure de la table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `media_url` varchar(255) DEFAULT NULL,
  `file_path` varchar(255) DEFAULT NULL,
  `type` enum('assignment','message','meeting','system','event') DEFAULT 'message',
  `is_read` tinyint(1) DEFAULT 0,
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `notifications`
--

INSERT INTO `notifications` (`id`, `title`, `message`, `media_url`, `file_path`, `type`, `is_read`, `created_at`) VALUES
(2, 'Math Homework Due', 'Complete exercises 1-10 from chapter 4 by Friday', NULL, NULL, 'system', 1, '2023-05-20 14:30:00'),
(3, 'update your profile', 'the university maintenance system invit you to update yout=r profle information', 'https://www.w3schools.com/', NULL, 'assignment', 1, '2025-04-25 23:30:30'),
(4, 'Professor\'s meeting', 'the Maintenace system of the university invit all the proffesors to an Urgent meeting the afternoon 26/04/2025', NULL, NULL, 'meeting', 1, '2025-04-25 23:34:52'),
(5, 'Bienvenue sur la plateforme', 'Bienvenue sur la plateforme de gestion universitaire. Nous sommes ravis de vous avoir parmi nous.', NULL, NULL, 'system', 1, '2025-04-24 16:14:15'),
(8, 'semaine d\'integration', 'le Bureau d\'ADE vous invite a participer a la semaine d\'integration pour l\'annee universitaire 2025/2026', NULL, NULL, 'event', 1, '2025-04-26 11:20:21'),
(10, 'Événement : Remise des Diplômes', 'Événement : Remise des Diplômes\nDate : 25/12/2023\nHeure : 16:00\nLieu : Grand Hall\n\nDescription :\nCérémonie de remise des diplômes pour les étudiants de dernière année', 'view/admin/events.php?view=20', NULL, 'event', 1, '2025-05-04 15:02:14'),
(11, 'Nouvelles notes pour le module Programmation C', 'L\'enseignant Chiboub Salma a envoyé les notes des étudiants pour le module Programmation C de la filière Génie Informatique.', NULL, 'notes_1_20250517170850.pdf', '', 0, '2025-05-17 16:08:50'),
(12, 'Nouvelles notes pour le module Programmation C', 'L\'enseignant Chiboub Salma a envoyé les notes des étudiants pour le module Programmation C de la filière Génie Informatique.', NULL, 'notes_1_20250517174146.pdf', '', 0, '2025-05-17 16:41:46'),
(13, 'Nouvelles notes pour le module Programmation C', 'L\'enseignant Chiboub Salma a envoyé les notes des étudiants pour le module Programmation C de la filière Génie Informatique.', NULL, 'notes_1_20250517174610.pdf', '', 0, '2025-05-17 16:46:10'),
(14, 'Nouvelles notes pour le module Programmation C', 'L\'enseignant Chiboub Salma a envoyé les notes des étudiants pour le module Programmation C de la filière Génie Informatique.', NULL, 'notes_1_20250520160532.pdf', '', 0, '2025-05-20 15:05:32'),
(15, 'Nouvelles notes pour le module Programmation C', 'L\'enseignant Chiboub Salma a envoyé les notes des étudiants pour le module Programmation C de la filière Génie Informatique.', NULL, 'notes_1_20250520160839.pdf', '', 0, '2025-05-20 15:08:39');

-- --------------------------------------------------------

--
-- Structure de la table `password_reset_codes`
--

CREATE TABLE `password_reset_codes` (
  `id` int(11) NOT NULL,
  `identifier` varchar(50) NOT NULL,
  `code` varchar(10) NOT NULL,
  `email` varchar(100) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `expires_at` timestamp NULL DEFAULT NULL,
  `used` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `password_reset_codes`
--

INSERT INTO `password_reset_codes` (`id`, `identifier`, `code`, `email`, `created_at`, `expires_at`, `used`) VALUES
(1, 'CN12345', '90KUTU', '<EMAIL>', '2025-04-26 14:52:18', '2025-04-26 16:07:18', 1),
(2, 'CN12345', 'RNW8WI', '<EMAIL>', '2025-04-26 14:53:35', '2025-04-26 16:08:35', 1),
(3, 'CN12345', '7YUKGP', '<EMAIL>', '2025-04-26 14:54:11', '2025-04-26 16:09:11', 1),
(4, 'CN12345', 'LSHCE9', '<EMAIL>', '2025-04-26 14:56:51', '2025-04-26 16:11:51', 1),
(5, 'CN12345', 'CS3HDF', '<EMAIL>', '2025-04-26 14:57:29', '2025-04-26 16:12:29', 1),
(6, 'CN12345', 'GMNNSM', '<EMAIL>', '2025-04-26 14:58:02', '2025-04-26 16:13:02', 1),
(7, 'CN12345', '3F0XYS', '<EMAIL>', '2025-04-26 16:13:02', '2025-04-26 17:28:02', 1),
(8, 'CN12345', 'M3TJEW', '<EMAIL>', '2025-04-26 16:16:00', '2025-04-26 17:31:00', 1),
(9, 'CN12345', '6MVE0R', '<EMAIL>', '2025-04-26 16:21:08', '2025-04-26 17:36:08', 1),
(10, 'CN12345', 'E7OQM4', '<EMAIL>', '2025-04-26 16:23:41', '2025-04-26 17:38:41', 1),
(11, 'CN12345', '9W2SRE', '<EMAIL>', '2025-04-26 16:27:35', '2025-04-26 17:42:35', 1),
(12, 'CN12345', '1PYPXZ', '<EMAIL>', '2025-04-26 17:02:17', '2025-04-26 18:17:17', 1),
(13, 'CN12345', 'RQ3SD7', '<EMAIL>', '2025-04-26 17:56:45', '2025-04-26 19:11:45', 1),
(14, 'CN12345', 'JU656P', '<EMAIL>', '2025-05-05 00:19:58', '2025-05-05 01:34:58', 1),
(15, 'CN12345', 'HWIFTF', '<EMAIL>', '2025-05-05 01:09:55', '2025-05-05 02:24:55', 1),
(16, 'CN12345', 'W27Q50', '<EMAIL>', '2025-05-05 02:38:30', '2025-05-05 03:53:30', 1),
(17, 'CN12345', 'GUWQKY', '<EMAIL>', '2025-05-05 03:04:55', '2025-05-05 04:19:55', 1),
(18, 'CN12345', '7K5RMV', '<EMAIL>', '2025-05-05 03:20:13', '2025-05-05 04:35:13', 1),
(19, 'CN12345', 'QGJ532', '<EMAIL>', '2025-05-05 11:26:33', '2025-05-05 12:41:33', 0);

-- --------------------------------------------------------

--
-- Structure de la table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `token` varchar(100) NOT NULL,
  `expiry` datetime NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `password_reset_tokens`
--

INSERT INTO `password_reset_tokens` (`id`, `username`, `token`, `expiry`, `created_at`) VALUES
(1, 'salma123', '1234b8fa22e5ecedda7b2dc5728fb8e523470353090194edcf858b6ce2f4c88e', '2025-05-05 23:37:19', '2025-05-04 21:37:19'),
(3, 'Z683653', '9a4e3b71070ab505ef27e0a1ecc55e7ac2c88524848dbfbf3fae98c5e1bcc519', '2025-05-06 00:49:28', '2025-05-04 22:49:28'),
(4, 'CNI09847', '0fb115649de9f827f830fa57cd1ad3b36e112517bc992f951216c5b53f83123d', '2025-05-06 01:16:13', '2025-05-04 23:16:13'),
(5, 'CNI098408', 'd46c02fbc6837a0a465485bd4a72360a7e0d9cf901da2397e3644efb9c583e7b', '2025-05-06 01:18:18', '2025-05-04 23:18:18'),
(7, 'CNI098498', '8aef3110719dbc9e8c33dd595c5220327ff6e1eb494a15820a81bbdfc467a898', '2025-05-06 02:25:39', '2025-05-05 00:25:39');

-- --------------------------------------------------------

--
-- Structure de la table `pdf_grades`
--

CREATE TABLE `pdf_grades` (
  `id` int(11) NOT NULL,
  `id_filiere` int(11) NOT NULL,
  `id_enseignant` int(11) NOT NULL,
  `id_module` int(11) NOT NULL,
  `id_niveau` int(11) NOT NULL,
  `id_semestre` int(11) NOT NULL,
  `session` varchar(50) NOT NULL,
  `file_path` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `pdf_grades`
--

INSERT INTO `pdf_grades` (`id`, `id_filiere`, `id_enseignant`, `id_module`, `id_niveau`, `id_semestre`, `session`, `file_path`) VALUES
(3, 1, 29, 1, 3, 5, 'normale', 'notes_1_20250517174610.pdf'),
(4, 1, 29, 1, 3, 5, 'normale', 'notes_1_20250520160532.pdf'),
(5, 1, 29, 1, 3, 5, 'normale', 'notes_1_20250520160839.pdf');

-- --------------------------------------------------------

--
-- Structure de la table `salles`
--

CREATE TABLE `salles` (
  `id` int(11) NOT NULL,
  `nom` varchar(20) DEFAULT NULL,
  `type` enum('amphi','tp','td','labo','ordinaire') DEFAULT NULL,
  `capacite` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `salles`
--

INSERT INTO `salles` (`id`, `nom`, `type`, `capacite`) VALUES
(1, 'Amphi A', 'amphi', 300),
(2, 'Amphi B', 'amphi', 300),
(3, 'Labo A1 bloc A', 'labo', 25),
(4, 'Labo A2 bloc A', 'labo', 25),
(5, 'TP 1 bloc A', 'tp', 30),
(6, 'TP 2 bloc A', 'tp', 30),
(7, 'TP 3 bloc A', 'tp', 30),
(8, 'TP 4 bloc A', 'tp', 30),
(9, 'TP 5 bloc A', 'tp', 30),
(10, 'TP 6 bloc A', 'tp', 30),
(11, 'Salle 1 bloc A', 'ordinaire', 40),
(12, 'Salle 2 bloc A', 'ordinaire', 40),
(13, 'Salle 3 bloc A', 'ordinaire', 40),
(14, 'Salle 4 bloc A', 'ordinaire', 40),
(15, 'Salle 5 bloc A', 'ordinaire', 40),
(16, 'Salle 6 bloc A', 'ordinaire', 40),
(17, 'Salle 7 bloc A', 'ordinaire', 40),
(18, 'Salle 8 bloc A', 'ordinaire', 40);

-- --------------------------------------------------------

--
-- Structure de la table `schedule_generation_logs`
--

CREATE TABLE `schedule_generation_logs` (
  `id` int(11) NOT NULL,
  `schedule_id` int(11) DEFAULT NULL,
  `log_timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `log_level` enum('info','warning','error') DEFAULT 'info',
  `message` text NOT NULL,
  `details` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Structure de la table `scheduling_constraints`
--

CREATE TABLE `scheduling_constraints` (
  `id` int(11) NOT NULL,
  `constraint_name` varchar(100) NOT NULL,
  `constraint_type` enum('global','teacher','room','module') NOT NULL,
  `constraint_value` text NOT NULL,
  `is_hard_constraint` tinyint(1) DEFAULT 1,
  `weight` int(11) DEFAULT 10,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `scheduling_constraints`
--

INSERT INTO `scheduling_constraints` (`id`, `constraint_name`, `constraint_type`, `constraint_value`, `is_hard_constraint`, `weight`, `created_at`, `updated_at`) VALUES
(1, 'no_teacher_conflict', 'teacher', 'A teacher cannot be scheduled in two places at the same time', 1, 10, '2025-05-13 19:39:05', '2025-05-13 19:39:05'),
(2, 'no_room_conflict', 'room', 'A room cannot be used by two classes at the same time', 1, 10, '2025-05-13 19:39:05', '2025-05-13 19:39:05'),
(3, 'respect_teacher_unavailability', 'teacher', 'Teachers cannot be scheduled during their unavailable times', 1, 10, '2025-05-13 19:39:05', '2025-05-13 19:39:05'),
(4, 'prefer_teacher_preferences', 'teacher', 'Try to schedule teachers according to their preferred times', 0, 8, '2025-05-13 19:39:05', '2025-05-13 19:39:05'),
(5, 'max_hours_per_day', 'teacher', 'Teachers should not exceed their maximum hours per day', 0, 7, '2025-05-13 19:39:05', '2025-05-13 19:39:05'),
(6, 'consecutive_classes_preferred', 'teacher', 'Teachers prefer consecutive classes without large gaps', 0, 5, '2025-05-13 19:39:05', '2025-05-13 19:39:05'),
(7, 'room_capacity', 'room', 'Room capacity must be sufficient for the class size', 1, 10, '2025-05-13 19:39:05', '2025-05-13 19:39:05'),
(8, 'room_equipment', 'room', 'Room must have the required equipment for the class', 0, 6, '2025-05-13 19:39:05', '2025-05-13 19:39:05');

-- --------------------------------------------------------

--
-- Structure de la table `semestre`
--

CREATE TABLE `semestre` (
  `id` int(11) NOT NULL,
  `nom` varchar(10) NOT NULL,
  `niveau_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `semestre`
--

INSERT INTO `semestre` (`id`, `nom`, `niveau_id`) VALUES
(1, 'S1', 1),
(2, 'S2', 1),
(3, 'S3', 2),
(4, 'S4', 2),
(5, 'S1', 3),
(6, 'S2', 3),
(7, 'S3', 4),
(8, 'S4', 4),
(9, 'S5', 5),
(10, 'S6', 5);

-- --------------------------------------------------------

--
-- Structure de la table `service_logs`
--

CREATE TABLE `service_logs` (
  `id` int(11) NOT NULL,
  `service_key` varchar(50) NOT NULL,
  `action` varchar(50) NOT NULL,
  `performed_by` varchar(50) DEFAULT NULL,
  `details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`details`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `service_logs`
--

INSERT INTO `service_logs` (`id`, `service_key`, `action`, `performed_by`, `details`, `created_at`) VALUES
(1, 'ue_preferences', 'activated', 'admin', '{\"duration_hours\":null,\"end_time\":\"2025-05-28 12:44:00\"}', '2025-05-27 11:44:36'),
(2, 'ue_preferences', 'deactivated', 'admin', '{\"reason\":\"manual\"}', '2025-05-27 11:44:56'),
(3, 'ue_preferences', 'activated', 'admin', '{\"duration_hours\":null,\"end_time\":\"2025-05-28 12:47:00\"}', '2025-05-27 11:48:00');

-- --------------------------------------------------------

--
-- Structure de la table `service_management`
--

CREATE TABLE `service_management` (
  `id` int(11) NOT NULL,
  `service_name` varchar(100) NOT NULL,
  `service_key` varchar(50) NOT NULL,
  `display_name` varchar(150) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 0,
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `duration_hours` int(11) DEFAULT NULL,
  `auto_deactivate` tinyint(1) DEFAULT 1,
  `created_by` varchar(50) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `last_activated_at` datetime DEFAULT NULL,
  `last_deactivated_at` datetime DEFAULT NULL,
  `activation_count` int(11) DEFAULT 0,
  `settings` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`settings`))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `service_management`
--

INSERT INTO `service_management` (`id`, `service_name`, `service_key`, `display_name`, `description`, `is_active`, `start_time`, `end_time`, `duration_hours`, `auto_deactivate`, `created_by`, `created_at`, `updated_at`, `last_activated_at`, `last_deactivated_at`, `activation_count`, `settings`) VALUES
(1, 'UE Preferences', 'ue_preferences', 'Collecte des Préférences UE', 'Permet aux enseignants de soumettre leurs préférences pour les unités d\'enseignement', 1, '2025-05-27 13:48:00', '2025-05-28 12:47:00', NULL, 1, NULL, '2025-05-27 11:43:40', '2025-05-27 12:48:00', '2025-05-27 13:48:00', '2025-05-27 13:44:56', 2, '{\"notification_enabled\":true,\"reminder_hours\":[24,6,1],\"department_notification\":true}'),
(2, 'Grade Submission', 'grade_submission', 'Soumission des Notes', 'Période de soumission des notes par les enseignants', 0, NULL, NULL, NULL, 1, NULL, '2025-05-27 11:43:40', '2025-05-27 11:43:40', NULL, NULL, 0, '{\"notification_enabled\":true,\"reminder_hours\":[48,24,6],\"auto_lock\":true}'),
(3, 'Course Evaluation', 'course_evaluation', 'Évaluation des Cours', 'Période d\'évaluation des cours par les étudiants', 0, NULL, NULL, NULL, 1, NULL, '2025-05-27 11:43:40', '2025-05-27 11:43:40', NULL, NULL, 0, '{\"notification_enabled\":true,\"anonymous_feedback\":true,\"reminder_hours\":[72,24]}'),
(4, 'Schedule Modification', 'schedule_modification', 'Modification d\'Emploi du Temps', 'Période de modification des emplois du temps', 0, NULL, NULL, NULL, 1, NULL, '2025-05-27 11:43:40', '2025-05-27 11:43:40', NULL, NULL, 0, '{\"notification_enabled\":true,\"approval_required\":true,\"reminder_hours\":[24,6]}');

-- --------------------------------------------------------

--
-- Structure de la table `specialite`
--

CREATE TABLE `specialite` (
  `id` int(11) NOT NULL,
  `nom` enum('Informatique','Mathématiques','Mécanique','Chimie','Génie Électrique et Automatique','Génie Civil','Réseaux et Télécommunications','Biologie','Environnement','Économie','Gestion') DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `specialite`
--

INSERT INTO `specialite` (`id`, `nom`) VALUES
(1, 'Informatique'),
(2, 'Mathématiques');

-- --------------------------------------------------------

--
-- Structure de la table `staff`
--

CREATE TABLE `staff` (
  `CNI` varchar(10) NOT NULL,
  `nom` varchar(50) NOT NULL,
  `prenom` varchar(50) NOT NULL,
  `sexe` enum('masculin','feminin') DEFAULT NULL,
  `phone` varchar(15) NOT NULL,
  `role` varchar(30) DEFAULT NULL CHECK (`role` in ('Sécurité','Ménage','Maintenance','Secrétariat','Technicien Labo','Support IT','Bibliothécaire','Cafétéria','Chauffeur'))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `staff`
--

INSERT INTO `staff` (`CNI`, `nom`, `prenom`, `sexe`, `phone`, `role`) VALUES
('AB123456', 'Alami', 'Ahmed', 'masculin', '+212612345678', 'Sécurité'),
('CD789012', 'Bennani', 'Fatima', 'feminin', '+212600123456', 'Ménage'),
('EF345678', 'Elouardi', 'Karim', 'masculin', '+212670987654', 'Maintenance'),
('GH901234', 'Rami', 'Leila', 'feminin', '+212661234567', 'Secrétariat'),
('IJ567890', 'Idrissi', 'Youssef', 'masculin', '+212650112233', 'Technicien Labo');

-- --------------------------------------------------------

--
-- Structure de la table `statistics`
--

CREATE TABLE `statistics` (
  `id` int(11) NOT NULL,
  `metric` varchar(50) NOT NULL,
  `value` int(11) NOT NULL,
  `period` varchar(20) NOT NULL,
  `recorded_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `statistics`
--

INSERT INTO `statistics` (`id`, `metric`, `value`, `period`, `recorded_at`) VALUES
(1, 'students', 10, '2024', '2025-03-18 01:31:05'),
(2, 'faculty', 3, '2024', '2024-03-18 01:31:05'),
(3, 'courses', 4, '02', '2025-03-18 01:31:15'),
(4, 'events', 2, '02', '2025-03-18 01:31:15'),
(5, 'students', 2, '2025', '2025-03-18 01:31:20'),
(6, 'faculty', 4, '2024', '2024-03-18 01:31:20'),
(7, 'courses', 3, '03', '2025-03-18 01:31:20'),
(8, 'events', 0, '03', '2025-03-18 01:31:20'),
(9, 'students', 2, '2025', '2025-03-18 01:37:01'),
(10, 'faculty', 4, '2025', '2025-03-18 01:37:01'),
(11, 'courses', 7, '03', '2025-03-18 01:37:01'),
(12, 'events', 0, '03', '2025-03-18 01:37:01'),
(13, 'students', 2, '2025', '2025-03-18 01:44:58'),
(14, 'faculty', 9, '2024', '2024-03-18 01:44:58'),
(15, 'courses', 7, '03', '2025-03-18 01:44:58'),
(16, 'events', 0, '03', '2025-03-18 01:44:58'),
(17, 'students', 2, '2025', '2025-03-18 01:45:31'),
(18, 'faculty', 9, '2024', '2025-03-18 01:45:31'),
(19, 'courses', 7, '03', '2025-03-18 01:45:31'),
(20, 'events', 0, '03', '2025-03-18 01:45:31'),
(21, 'students', 2, '2025', '2025-03-18 01:45:33'),
(22, 'faculty', 9, '2025', '2025-03-18 01:45:33'),
(23, 'courses', 7, '03', '2025-03-18 01:45:33'),
(24, 'events', 0, '03', '2025-03-18 01:45:33'),
(25, 'students', 2, '2025', '2025-03-18 02:48:22'),
(26, 'faculty', 9, '2025', '2025-03-18 02:48:22'),
(27, 'courses', 7, '03', '2025-03-18 02:48:22'),
(28, 'events', 6, '03', '2025-03-18 02:48:22'),
(29, 'students', 2, '2025', '2025-03-18 02:50:19'),
(30, 'faculty', 9, '2025', '2025-03-18 02:50:19'),
(31, 'courses', 7, '03', '2025-03-18 02:50:19'),
(32, 'events', 6, '03', '2025-03-18 02:50:19'),
(33, 'students', 2, '2025', '2025-03-18 02:50:49'),
(34, 'faculty', 9, '2025', '2025-03-18 02:50:49'),
(35, 'courses', 7, '03', '2025-03-18 02:50:49'),
(36, 'events', 6, '03', '2025-03-18 02:50:49'),
(37, 'students', 2, '2025', '2025-03-18 02:51:34'),
(38, 'faculty', 9, '2025', '2025-03-18 02:51:34'),
(39, 'courses', 7, '03', '2025-03-18 02:51:34'),
(40, 'events', 6, '03', '2025-03-18 02:51:34'),
(41, 'students', 2, '2025', '2025-03-18 02:56:40'),
(42, 'faculty', 9, '2025', '2025-03-18 02:56:40'),
(43, 'courses', 7, '03', '2025-03-18 02:56:40'),
(44, 'events', 6, '03', '2025-03-18 02:56:40'),
(45, 'students', 2, '2025', '2025-03-18 02:56:44'),
(46, 'faculty', 9, '2025', '2025-03-18 02:56:44'),
(47, 'courses', 7, '03', '2025-03-18 02:56:44'),
(48, 'events', 6, '03', '2025-03-18 02:56:44'),
(49, 'students', 2, '2025', '2025-03-18 02:57:26'),
(50, 'faculty', 9, '2025', '2025-03-18 02:57:26'),
(51, 'courses', 7, '03', '2025-03-18 02:57:26'),
(52, 'events', 6, '03', '2025-03-18 02:57:26'),
(53, 'students', 12, '2025', '2025-03-18 03:11:19'),
(54, 'faculty', 9, '2025', '2025-03-18 03:11:19'),
(55, 'courses', 7, '03', '2025-03-18 03:11:19'),
(56, 'events', 6, '03', '2025-03-18 03:11:19'),
(57, 'students', 12, '2025', '2025-03-18 03:13:08'),
(58, 'faculty', 9, '2025', '2025-03-18 03:13:08'),
(59, 'courses', 7, '03', '2025-03-18 03:13:08'),
(60, 'events', 6, '03', '2025-03-18 03:13:08'),
(61, 'students', 12, '2025', '2025-03-18 03:13:12'),
(62, 'faculty', 9, '2025', '2025-03-18 03:13:12'),
(63, 'courses', 7, '03', '2025-03-18 03:13:12'),
(64, 'events', 6, '03', '2025-03-18 03:13:12'),
(65, 'students', 17, '2025', '2025-03-18 03:29:39'),
(66, 'faculty', 9, '2025', '2025-03-18 03:29:39'),
(67, 'courses', 7, '03', '2025-03-18 03:29:39'),
(68, 'events', 6, '03', '2025-03-18 03:29:39'),
(69, 'students', 17, '2025', '2025-03-18 03:30:59'),
(70, 'faculty', 9, '2025', '2025-03-18 03:30:59'),
(71, 'courses', 7, '03', '2025-03-18 03:30:59'),
(72, 'events', 6, '03', '2025-03-18 03:30:59'),
(73, 'students', 17, '2025', '2025-03-18 03:31:56'),
(74, 'faculty', 9, '2025', '2025-03-18 03:31:56'),
(75, 'courses', 7, '03', '2025-03-18 03:31:56'),
(76, 'events', 6, '03', '2025-03-18 03:31:56'),
(77, 'students', 17, '2025', '2025-03-18 03:33:47'),
(78, 'faculty', 9, '2025', '2025-03-18 03:33:47'),
(79, 'courses', 7, '03', '2025-03-18 03:33:47'),
(80, 'events', 6, '03', '2025-03-18 03:33:47'),
(81, 'students', 17, '2025', '2025-03-18 03:35:08'),
(82, 'faculty', 9, '2025', '2025-03-18 03:35:08'),
(83, 'courses', 7, '03', '2025-03-18 03:35:08'),
(84, 'events', 6, '03', '2025-03-18 03:35:08'),
(85, 'students', 17, '2025', '2025-03-18 03:35:49'),
(86, 'faculty', 9, '2025', '2025-03-18 03:35:49'),
(87, 'courses', 7, '03', '2025-03-18 03:35:49'),
(88, 'events', 6, '03', '2025-03-18 03:35:49'),
(89, 'students', 17, '2025', '2025-03-18 03:51:19'),
(90, 'faculty', 9, '2025', '2025-03-18 03:51:19'),
(91, 'courses', 7, '03', '2025-03-18 03:51:19'),
(92, 'events', 6, '03', '2025-03-18 03:51:19'),
(93, 'students', 17, '2025', '2025-03-18 03:53:20'),
(94, 'faculty', 9, '2025', '2025-03-18 03:53:20'),
(95, 'courses', 7, '03', '2025-03-18 03:53:20'),
(96, 'events', 6, '03', '2025-03-18 03:53:20'),
(97, 'students', 17, '2025', '2025-03-18 03:53:47'),
(98, 'faculty', 9, '2025', '2025-03-18 03:53:47'),
(99, 'courses', 7, '03', '2025-03-18 03:53:47'),
(100, 'events', 6, '03', '2025-03-18 03:53:47'),
(101, 'students', 17, '2025', '2025-03-18 04:13:49'),
(102, 'faculty', 9, '2025', '2025-03-18 04:13:49'),
(103, 'courses', 3, '03', '2025-03-18 04:13:49'),
(104, 'events', 6, '03', '2025-03-18 04:13:49'),
(105, 'students', 17, '2025', '2025-03-19 22:04:52'),
(106, 'faculty', 9, '2025', '2025-03-19 22:04:52'),
(107, 'courses', 3, '03', '2025-03-19 22:04:52'),
(108, 'events', 6, '03', '2025-03-19 22:04:52'),
(109, 'students', 0, '2025', '2025-03-19 23:55:54'),
(110, 'faculty', 7, '2025', '2025-03-19 23:55:54'),
(111, 'courses', 0, '03', '2025-03-19 23:55:54'),
(112, 'events', 1, '03', '2025-03-19 23:55:54'),
(113, 'students', 0, '2025', '2025-03-19 23:57:11'),
(114, 'faculty', 7, '2025', '2025-03-19 23:57:11'),
(115, 'courses', 0, '03', '2025-03-19 23:57:11'),
(116, 'events', 1, '03', '2025-03-19 23:57:11'),
(117, 'students', 0, '2025', '2025-03-19 23:58:20'),
(118, 'faculty', 7, '2025', '2025-03-19 23:58:20'),
(119, 'courses', 0, '03', '2025-03-19 23:58:20'),
(120, 'events', 1, '03', '2025-03-19 23:58:20'),
(121, 'students', 0, '2025', '2025-03-20 00:00:29'),
(122, 'faculty', 7, '2025', '2025-03-20 00:00:29'),
(123, 'courses', 0, '03', '2025-03-20 00:00:29'),
(124, 'events', 1, '03', '2025-03-20 00:00:29'),
(125, 'students', 0, '2025', '2025-03-20 00:00:50'),
(126, 'faculty', 7, '2025', '2025-03-20 00:00:50'),
(127, 'courses', 0, '03', '2025-03-20 00:00:50'),
(128, 'events', 1, '03', '2025-03-20 00:00:50'),
(129, 'students', 0, '2025', '2025-03-20 00:01:22'),
(130, 'faculty', 7, '2025', '2025-03-20 00:01:22'),
(131, 'courses', 0, '03', '2025-03-20 00:01:22'),
(132, 'events', 1, '03', '2025-03-20 00:01:22'),
(133, 'students', 0, '2025', '2025-03-20 00:04:58'),
(134, 'faculty', 7, '2025', '2025-03-20 00:04:58'),
(135, 'courses', 0, '03', '2025-03-20 00:04:58'),
(136, 'events', 1, '03', '2025-03-20 00:04:58'),
(137, 'students', 0, '2025', '2025-03-20 00:04:59'),
(138, 'faculty', 7, '2025', '2025-03-20 00:04:59'),
(139, 'courses', 0, '03', '2025-03-20 00:04:59'),
(140, 'events', 1, '03', '2025-03-20 00:04:59'),
(141, 'students', 0, '2025', '2025-03-20 00:05:04'),
(142, 'faculty', 7, '2025', '2025-03-20 00:05:04'),
(143, 'courses', 0, '03', '2025-03-20 00:05:04'),
(144, 'events', 1, '03', '2025-03-20 00:05:04'),
(145, 'students', 0, '2025', '2025-03-20 00:05:09'),
(146, 'faculty', 7, '2025', '2025-03-20 00:05:09'),
(147, 'courses', 0, '03', '2025-03-20 00:05:09'),
(148, 'events', 1, '03', '2025-03-20 00:05:09'),
(149, 'students', 0, '2025', '2025-03-20 00:05:14'),
(150, 'faculty', 7, '2025', '2025-03-20 00:05:14'),
(151, 'courses', 0, '03', '2025-03-20 00:05:14'),
(152, 'events', 1, '03', '2025-03-20 00:05:14'),
(153, 'students', 0, '2025', '2025-03-20 00:05:18'),
(154, 'faculty', 7, '2025', '2025-03-20 00:05:18'),
(155, 'courses', 0, '03', '2025-03-20 00:05:18'),
(156, 'events', 1, '03', '2025-03-20 00:05:18'),
(157, 'students', 0, '2025', '2025-03-20 00:05:18'),
(158, 'faculty', 7, '2025', '2025-03-20 00:05:18'),
(159, 'courses', 0, '03', '2025-03-20 00:05:18'),
(160, 'events', 1, '03', '2025-03-20 00:05:18'),
(161, 'students', 0, '2025', '2025-03-20 00:05:23'),
(162, 'faculty', 7, '2025', '2025-03-20 00:05:23'),
(163, 'courses', 0, '03', '2025-03-20 00:05:23'),
(164, 'events', 1, '03', '2025-03-20 00:05:23'),
(165, 'students', 0, '2025', '2025-03-20 00:05:28'),
(166, 'faculty', 7, '2025', '2025-03-20 00:05:28'),
(167, 'courses', 0, '03', '2025-03-20 00:05:28'),
(168, 'events', 1, '03', '2025-03-20 00:05:28'),
(169, 'students', 0, '2025', '2025-03-20 00:05:34'),
(170, 'faculty', 7, '2025', '2025-03-20 00:05:34'),
(171, 'courses', 0, '03', '2025-03-20 00:05:34'),
(172, 'events', 1, '03', '2025-03-20 00:05:34'),
(173, 'students', 0, '2025', '2025-03-20 00:05:39'),
(174, 'faculty', 7, '2025', '2025-03-20 00:05:39'),
(175, 'courses', 0, '03', '2025-03-20 00:05:39'),
(176, 'events', 1, '03', '2025-03-20 00:05:39'),
(177, 'students', 0, '2025', '2025-03-20 00:05:44'),
(178, 'faculty', 7, '2025', '2025-03-20 00:05:44'),
(179, 'courses', 0, '03', '2025-03-20 00:05:44'),
(180, 'events', 1, '03', '2025-03-20 00:05:44'),
(181, 'students', 0, '2025', '2025-03-20 00:05:49'),
(182, 'faculty', 7, '2025', '2025-03-20 00:05:49'),
(183, 'courses', 0, '03', '2025-03-20 00:05:49'),
(184, 'events', 1, '03', '2025-03-20 00:05:49'),
(185, 'students', 0, '2025', '2025-03-20 00:05:54'),
(186, 'faculty', 7, '2025', '2025-03-20 00:05:54'),
(187, 'courses', 0, '03', '2025-03-20 00:05:54'),
(188, 'events', 1, '03', '2025-03-20 00:05:54'),
(189, 'students', 0, '2025', '2025-03-20 00:05:59'),
(190, 'faculty', 7, '2025', '2025-03-20 00:05:59'),
(191, 'courses', 0, '03', '2025-03-20 00:05:59'),
(192, 'events', 1, '03', '2025-03-20 00:05:59'),
(193, 'students', 0, '2025', '2025-03-20 00:06:04'),
(194, 'faculty', 7, '2025', '2025-03-20 00:06:04'),
(195, 'courses', 0, '03', '2025-03-20 00:06:04'),
(196, 'events', 1, '03', '2025-03-20 00:06:04'),
(197, 'students', 0, '2025', '2025-03-20 00:06:09'),
(198, 'faculty', 7, '2025', '2025-03-20 00:06:09'),
(199, 'courses', 0, '03', '2025-03-20 00:06:09'),
(200, 'events', 1, '03', '2025-03-20 00:06:09'),
(201, 'students', 0, '2025', '2025-03-20 00:06:14'),
(202, 'faculty', 7, '2025', '2025-03-20 00:06:14'),
(203, 'courses', 0, '03', '2025-03-20 00:06:14'),
(204, 'events', 1, '03', '2025-03-20 00:06:14'),
(205, 'students', 0, '2025', '2025-03-20 00:06:19'),
(206, 'faculty', 7, '2025', '2025-03-20 00:06:19'),
(207, 'courses', 0, '03', '2025-03-20 00:06:19'),
(208, 'events', 1, '03', '2025-03-20 00:06:19'),
(209, 'students', 0, '2025', '2025-03-20 00:06:24'),
(210, 'faculty', 7, '2025', '2025-03-20 00:06:24'),
(211, 'courses', 0, '03', '2025-03-20 00:06:24'),
(212, 'events', 1, '03', '2025-03-20 00:06:24'),
(213, 'students', 0, '2025', '2025-03-20 00:06:29'),
(214, 'faculty', 7, '2025', '2025-03-20 00:06:29'),
(215, 'courses', 0, '03', '2025-03-20 00:06:29'),
(216, 'events', 1, '03', '2025-03-20 00:06:29'),
(217, 'students', 0, '2025', '2025-03-20 00:06:47'),
(218, 'faculty', 7, '2025', '2025-03-20 00:06:47'),
(219, 'courses', 0, '03', '2025-03-20 00:06:47'),
(220, 'events', 1, '03', '2025-03-20 00:06:47'),
(221, 'students', 0, '2025', '2025-03-20 00:07:47'),
(222, 'faculty', 7, '2025', '2025-03-20 00:07:47'),
(223, 'courses', 0, '03', '2025-03-20 00:07:47'),
(224, 'events', 1, '03', '2025-03-20 00:07:47'),
(225, 'students', 0, '2025', '2025-03-20 00:08:00'),
(226, 'faculty', 7, '2025', '2025-03-20 00:08:00'),
(227, 'courses', 0, '03', '2025-03-20 00:08:00'),
(228, 'events', 1, '03', '2025-03-20 00:08:00'),
(229, 'students', 0, '2025', '2025-03-20 00:08:02'),
(230, 'faculty', 7, '2025', '2025-03-20 00:08:02'),
(231, 'courses', 0, '03', '2025-03-20 00:08:02'),
(232, 'events', 1, '03', '2025-03-20 00:08:02'),
(233, 'students', 0, '2025', '2025-03-20 00:08:02'),
(234, 'faculty', 7, '2025', '2025-03-20 00:08:02'),
(235, 'courses', 0, '03', '2025-03-20 00:08:02'),
(236, 'events', 1, '03', '2025-03-20 00:08:02'),
(237, 'students', 0, '2025', '2025-03-20 00:08:07'),
(238, 'faculty', 7, '2025', '2025-03-20 00:08:07'),
(239, 'courses', 0, '03', '2025-03-20 00:08:07'),
(240, 'events', 1, '03', '2025-03-20 00:08:07'),
(241, 'students', 0, '2025', '2025-03-20 00:08:13'),
(242, 'faculty', 7, '2025', '2025-03-20 00:08:13'),
(243, 'courses', 0, '03', '2025-03-20 00:08:13'),
(244, 'events', 1, '03', '2025-03-20 00:08:13'),
(245, 'students', 0, '2025', '2025-03-20 00:08:14'),
(246, 'faculty', 7, '2025', '2025-03-20 00:08:14'),
(247, 'courses', 0, '03', '2025-03-20 00:08:14'),
(248, 'events', 1, '03', '2025-03-20 00:08:14'),
(249, 'students', 0, '2025', '2025-03-20 00:08:14'),
(250, 'faculty', 7, '2025', '2025-03-20 00:08:14'),
(251, 'courses', 0, '03', '2025-03-20 00:08:14'),
(252, 'events', 1, '03', '2025-03-20 00:08:14'),
(253, 'students', 0, '2025', '2025-03-20 00:08:19'),
(254, 'faculty', 7, '2025', '2025-03-20 00:08:19'),
(255, 'courses', 0, '03', '2025-03-20 00:08:19'),
(256, 'events', 1, '03', '2025-03-20 00:08:19'),
(257, 'students', 0, '2025', '2025-03-20 00:08:25'),
(258, 'faculty', 7, '2025', '2025-03-20 00:08:25'),
(259, 'courses', 0, '03', '2025-03-20 00:08:25'),
(260, 'events', 1, '03', '2025-03-20 00:08:25'),
(261, 'students', 0, '2025', '2025-03-20 00:08:26'),
(262, 'faculty', 7, '2025', '2025-03-20 00:08:26'),
(263, 'courses', 0, '03', '2025-03-20 00:08:26'),
(264, 'events', 1, '03', '2025-03-20 00:08:26'),
(265, 'students', 0, '2025', '2025-03-20 00:08:26'),
(266, 'faculty', 7, '2025', '2025-03-20 00:08:26'),
(267, 'courses', 0, '03', '2025-03-20 00:08:26'),
(268, 'events', 1, '03', '2025-03-20 00:08:26'),
(269, 'students', 0, '2025', '2025-03-20 00:08:32'),
(270, 'faculty', 7, '2025', '2025-03-20 00:08:32'),
(271, 'courses', 0, '03', '2025-03-20 00:08:32'),
(272, 'events', 1, '03', '2025-03-20 00:08:32'),
(273, 'students', 0, '2025', '2025-03-20 00:08:35'),
(274, 'faculty', 7, '2025', '2025-03-20 00:08:35'),
(275, 'courses', 0, '03', '2025-03-20 00:08:35'),
(276, 'events', 1, '03', '2025-03-20 00:08:35'),
(277, 'students', 0, '2025', '2025-03-20 00:08:36'),
(278, 'faculty', 7, '2025', '2025-03-20 00:08:36'),
(279, 'courses', 0, '03', '2025-03-20 00:08:36'),
(280, 'events', 1, '03', '2025-03-20 00:08:36'),
(281, 'students', 0, '2025', '2025-03-20 00:08:41'),
(282, 'faculty', 7, '2025', '2025-03-20 00:08:41'),
(283, 'courses', 0, '03', '2025-03-20 00:08:41'),
(284, 'events', 1, '03', '2025-03-20 00:08:41'),
(285, 'students', 0, '2025', '2025-03-20 00:08:46'),
(286, 'faculty', 7, '2025', '2025-03-20 00:08:46'),
(287, 'courses', 0, '03', '2025-03-20 00:08:46'),
(288, 'events', 1, '03', '2025-03-20 00:08:46'),
(289, 'students', 0, '2025', '2025-03-20 00:08:51'),
(290, 'faculty', 7, '2025', '2025-03-20 00:08:51'),
(291, 'courses', 0, '03', '2025-03-20 00:08:51'),
(292, 'events', 1, '03', '2025-03-20 00:08:51'),
(293, 'students', 0, '2025', '2025-03-20 00:08:56'),
(294, 'faculty', 7, '2025', '2025-03-20 00:08:56'),
(295, 'courses', 0, '03', '2025-03-20 00:08:56'),
(296, 'events', 1, '03', '2025-03-20 00:08:56'),
(297, 'students', 0, '2025', '2025-03-20 00:09:01'),
(298, 'faculty', 7, '2025', '2025-03-20 00:09:01'),
(299, 'courses', 0, '03', '2025-03-20 00:09:01'),
(300, 'events', 1, '03', '2025-03-20 00:09:01'),
(301, 'students', 0, '2025', '2025-03-20 00:09:06'),
(302, 'faculty', 7, '2025', '2025-03-20 00:09:06'),
(303, 'courses', 0, '03', '2025-03-20 00:09:06'),
(304, 'events', 1, '03', '2025-03-20 00:09:06'),
(305, 'students', 0, '2025', '2025-03-20 00:09:11'),
(306, 'faculty', 7, '2025', '2025-03-20 00:09:11'),
(307, 'courses', 0, '03', '2025-03-20 00:09:11'),
(308, 'events', 1, '03', '2025-03-20 00:09:11'),
(309, 'students', 0, '2025', '2025-03-20 00:09:16'),
(310, 'faculty', 7, '2025', '2025-03-20 00:09:16'),
(311, 'courses', 0, '03', '2025-03-20 00:09:16'),
(312, 'events', 1, '03', '2025-03-20 00:09:16'),
(313, 'students', 0, '2025', '2025-03-20 00:09:21'),
(314, 'faculty', 7, '2025', '2025-03-20 00:09:21'),
(315, 'courses', 0, '03', '2025-03-20 00:09:21'),
(316, 'events', 1, '03', '2025-03-20 00:09:21'),
(317, 'students', 0, '2025', '2025-03-20 00:09:26'),
(318, 'faculty', 7, '2025', '2025-03-20 00:09:26'),
(319, 'courses', 0, '03', '2025-03-20 00:09:26'),
(320, 'events', 1, '03', '2025-03-20 00:09:26'),
(321, 'students', 0, '2025', '2025-03-20 00:09:31'),
(322, 'faculty', 7, '2025', '2025-03-20 00:09:31'),
(323, 'courses', 0, '03', '2025-03-20 00:09:31'),
(324, 'events', 1, '03', '2025-03-20 00:09:31'),
(325, 'students', 0, '2025', '2025-03-20 00:09:36'),
(326, 'faculty', 7, '2025', '2025-03-20 00:09:36'),
(327, 'courses', 0, '03', '2025-03-20 00:09:36'),
(328, 'events', 1, '03', '2025-03-20 00:09:36'),
(329, 'students', 0, '2025', '2025-03-20 00:09:41'),
(330, 'faculty', 7, '2025', '2025-03-20 00:09:41'),
(331, 'courses', 0, '03', '2025-03-20 00:09:41'),
(332, 'events', 1, '03', '2025-03-20 00:09:41'),
(333, 'students', 0, '2025', '2025-03-20 00:09:46'),
(334, 'faculty', 7, '2025', '2025-03-20 00:09:46'),
(335, 'courses', 0, '03', '2025-03-20 00:09:46'),
(336, 'events', 1, '03', '2025-03-20 00:09:46'),
(337, 'students', 0, '2025', '2025-03-20 00:09:51'),
(338, 'faculty', 7, '2025', '2025-03-20 00:09:51'),
(339, 'courses', 0, '03', '2025-03-20 00:09:51'),
(340, 'events', 1, '03', '2025-03-20 00:09:51'),
(341, 'students', 0, '2025', '2025-03-20 00:09:56'),
(342, 'faculty', 7, '2025', '2025-03-20 00:09:56'),
(343, 'courses', 0, '03', '2025-03-20 00:09:56'),
(344, 'events', 1, '03', '2025-03-20 00:09:56'),
(345, 'students', 0, '2025', '2025-03-20 00:10:01'),
(346, 'faculty', 7, '2025', '2025-03-20 00:10:01'),
(347, 'courses', 0, '03', '2025-03-20 00:10:01'),
(348, 'events', 1, '03', '2025-03-20 00:10:01'),
(349, 'students', 0, '2025', '2025-03-20 00:10:06'),
(350, 'faculty', 7, '2025', '2025-03-20 00:10:06'),
(351, 'courses', 0, '03', '2025-03-20 00:10:06'),
(352, 'events', 1, '03', '2025-03-20 00:10:06'),
(353, 'students', 0, '2025', '2025-03-20 00:10:11'),
(354, 'faculty', 7, '2025', '2025-03-20 00:10:11'),
(355, 'courses', 0, '03', '2025-03-20 00:10:11'),
(356, 'events', 1, '03', '2025-03-20 00:10:11'),
(357, 'students', 0, '2025', '2025-03-20 00:10:16'),
(358, 'faculty', 7, '2025', '2025-03-20 00:10:16'),
(359, 'courses', 0, '03', '2025-03-20 00:10:16'),
(360, 'events', 1, '03', '2025-03-20 00:10:16'),
(361, 'students', 0, '2025', '2025-03-20 00:10:21'),
(362, 'faculty', 7, '2025', '2025-03-20 00:10:21'),
(363, 'courses', 0, '03', '2025-03-20 00:10:21'),
(364, 'events', 1, '03', '2025-03-20 00:10:21'),
(365, 'students', 0, '2025', '2025-03-20 00:10:26'),
(366, 'faculty', 7, '2025', '2025-03-20 00:10:26'),
(367, 'courses', 0, '03', '2025-03-20 00:10:26'),
(368, 'events', 1, '03', '2025-03-20 00:10:26'),
(369, 'students', 0, '2025', '2025-03-20 00:10:31'),
(370, 'faculty', 7, '2025', '2025-03-20 00:10:31'),
(371, 'courses', 0, '03', '2025-03-20 00:10:31'),
(372, 'events', 1, '03', '2025-03-20 00:10:31'),
(373, 'students', 0, '2025', '2025-03-20 00:10:36'),
(374, 'faculty', 7, '2025', '2025-03-20 00:10:36'),
(375, 'courses', 0, '03', '2025-03-20 00:10:36'),
(376, 'events', 1, '03', '2025-03-20 00:10:36'),
(377, 'students', 0, '2025', '2025-03-20 00:10:41'),
(378, 'faculty', 7, '2025', '2025-03-20 00:10:41'),
(379, 'courses', 0, '03', '2025-03-20 00:10:41'),
(380, 'events', 1, '03', '2025-03-20 00:10:41'),
(381, 'students', 0, '2025', '2025-03-20 00:10:46'),
(382, 'faculty', 7, '2025', '2025-03-20 00:10:46'),
(383, 'courses', 0, '03', '2025-03-20 00:10:46'),
(384, 'events', 1, '03', '2025-03-20 00:10:46'),
(385, 'students', 0, '2025', '2025-03-20 00:10:51'),
(386, 'faculty', 7, '2025', '2025-03-20 00:10:51'),
(387, 'courses', 0, '03', '2025-03-20 00:10:51'),
(388, 'events', 1, '03', '2025-03-20 00:10:51'),
(389, 'students', 0, '2025', '2025-03-20 00:11:47'),
(390, 'faculty', 7, '2025', '2025-03-20 00:11:47'),
(391, 'courses', 0, '03', '2025-03-20 00:11:47'),
(392, 'events', 1, '03', '2025-03-20 00:11:47'),
(393, 'students', 0, '2025', '2025-03-20 00:12:47'),
(394, 'faculty', 7, '2025', '2025-03-20 00:12:47'),
(395, 'courses', 0, '03', '2025-03-20 00:12:47'),
(396, 'events', 1, '03', '2025-03-20 00:12:47'),
(397, 'students', 0, '2025', '2025-03-20 00:13:47'),
(398, 'faculty', 7, '2025', '2025-03-20 00:13:47'),
(399, 'courses', 0, '03', '2025-03-20 00:13:47'),
(400, 'events', 1, '03', '2025-03-20 00:13:47'),
(401, 'students', 0, '2025', '2025-03-20 00:14:47'),
(402, 'faculty', 7, '2025', '2025-03-20 00:14:47'),
(403, 'courses', 0, '03', '2025-03-20 00:14:47'),
(404, 'events', 1, '03', '2025-03-20 00:14:47'),
(405, 'students', 0, '2025', '2025-03-20 00:15:47'),
(406, 'faculty', 7, '2025', '2025-03-20 00:15:47'),
(407, 'courses', 0, '03', '2025-03-20 00:15:47'),
(408, 'events', 1, '03', '2025-03-20 00:15:47'),
(409, 'students', 0, '2025', '2025-03-20 00:16:37'),
(410, 'faculty', 7, '2025', '2025-03-20 00:16:37'),
(411, 'courses', 0, '03', '2025-03-20 00:16:37'),
(412, 'events', 1, '03', '2025-03-20 00:16:37'),
(413, 'students', 0, '2025', '2025-03-20 00:16:41'),
(414, 'faculty', 7, '2025', '2025-03-20 00:16:41'),
(415, 'courses', 0, '03', '2025-03-20 00:16:41'),
(416, 'events', 1, '03', '2025-03-20 00:16:41'),
(417, 'students', 0, '2025', '2025-03-20 00:16:42'),
(418, 'faculty', 7, '2025', '2025-03-20 00:16:42'),
(419, 'courses', 0, '03', '2025-03-20 00:16:42'),
(420, 'events', 1, '03', '2025-03-20 00:16:42'),
(421, 'students', 0, '2025', '2025-03-20 00:16:42'),
(422, 'faculty', 7, '2025', '2025-03-20 00:16:42'),
(423, 'courses', 0, '03', '2025-03-20 00:16:42'),
(424, 'events', 1, '03', '2025-03-20 00:16:42'),
(425, 'students', 0, '2025', '2025-03-20 00:16:47'),
(426, 'faculty', 7, '2025', '2025-03-20 00:16:47'),
(427, 'courses', 0, '03', '2025-03-20 00:16:47'),
(428, 'events', 1, '03', '2025-03-20 00:16:47'),
(429, 'students', 0, '2025', '2025-03-20 00:16:50'),
(430, 'faculty', 7, '2025', '2025-03-20 00:16:50'),
(431, 'courses', 0, '03', '2025-03-20 00:16:50'),
(432, 'events', 1, '03', '2025-03-20 00:16:50'),
(433, 'students', 0, '2025', '2025-03-20 00:16:58'),
(434, 'faculty', 7, '2025', '2025-03-20 00:16:58'),
(435, 'courses', 0, '03', '2025-03-20 00:16:58'),
(436, 'events', 1, '03', '2025-03-20 00:16:58'),
(437, 'students', 0, '2025', '2025-03-20 00:17:03'),
(438, 'faculty', 7, '2025', '2025-03-20 00:17:03'),
(439, 'courses', 0, '03', '2025-03-20 00:17:03'),
(440, 'events', 1, '03', '2025-03-20 00:17:03'),
(441, 'students', 0, '2025', '2025-03-20 00:17:08'),
(442, 'faculty', 7, '2025', '2025-03-20 00:17:08'),
(443, 'courses', 0, '03', '2025-03-20 00:17:08'),
(444, 'events', 1, '03', '2025-03-20 00:17:08'),
(445, 'students', 0, '2025', '2025-03-20 00:17:13'),
(446, 'faculty', 7, '2025', '2025-03-20 00:17:13'),
(447, 'courses', 0, '03', '2025-03-20 00:17:13'),
(448, 'events', 1, '03', '2025-03-20 00:17:13'),
(449, 'students', 0, '2025', '2025-03-20 00:17:18'),
(450, 'faculty', 7, '2025', '2025-03-20 00:17:18'),
(451, 'courses', 0, '03', '2025-03-20 00:17:18'),
(452, 'events', 1, '03', '2025-03-20 00:17:18'),
(453, 'students', 0, '2025', '2025-03-20 00:17:23'),
(454, 'faculty', 7, '2025', '2025-03-20 00:17:23'),
(455, 'courses', 0, '03', '2025-03-20 00:17:23'),
(456, 'events', 1, '03', '2025-03-20 00:17:23'),
(457, 'students', 0, '2025', '2025-03-20 00:17:28'),
(458, 'faculty', 7, '2025', '2025-03-20 00:17:28'),
(459, 'courses', 0, '03', '2025-03-20 00:17:28'),
(460, 'events', 1, '03', '2025-03-20 00:17:28'),
(461, 'students', 0, '2025', '2025-03-20 00:17:33'),
(462, 'faculty', 7, '2025', '2025-03-20 00:17:33'),
(463, 'courses', 0, '03', '2025-03-20 00:17:33'),
(464, 'events', 1, '03', '2025-03-20 00:17:33'),
(465, 'students', 0, '2025', '2025-03-20 00:17:38'),
(466, 'faculty', 7, '2025', '2025-03-20 00:17:38'),
(467, 'courses', 0, '03', '2025-03-20 00:17:38'),
(468, 'events', 1, '03', '2025-03-20 00:17:38'),
(469, 'students', 0, '2025', '2025-03-20 00:17:43'),
(470, 'faculty', 7, '2025', '2025-03-20 00:17:43'),
(471, 'courses', 0, '03', '2025-03-20 00:17:43'),
(472, 'events', 1, '03', '2025-03-20 00:17:43'),
(473, 'students', 0, '2025', '2025-03-20 00:17:48'),
(474, 'faculty', 7, '2025', '2025-03-20 00:17:48'),
(475, 'courses', 0, '03', '2025-03-20 00:17:48'),
(476, 'events', 1, '03', '2025-03-20 00:17:48'),
(477, 'students', 0, '2025', '2025-03-20 00:17:53'),
(478, 'faculty', 7, '2025', '2025-03-20 00:17:53'),
(479, 'courses', 0, '03', '2025-03-20 00:17:53'),
(480, 'events', 1, '03', '2025-03-20 00:17:53'),
(481, 'students', 0, '2025', '2025-03-20 00:17:58'),
(482, 'faculty', 7, '2025', '2025-03-20 00:17:58'),
(483, 'courses', 0, '03', '2025-03-20 00:17:58'),
(484, 'events', 1, '03', '2025-03-20 00:17:58'),
(485, 'students', 0, '2025', '2025-03-20 00:18:03'),
(486, 'faculty', 7, '2025', '2025-03-20 00:18:03'),
(487, 'courses', 0, '03', '2025-03-20 00:18:03'),
(488, 'events', 1, '03', '2025-03-20 00:18:03'),
(489, 'students', 0, '2025', '2025-03-20 00:18:08'),
(490, 'faculty', 7, '2025', '2025-03-20 00:18:08'),
(491, 'courses', 0, '03', '2025-03-20 00:18:08'),
(492, 'events', 1, '03', '2025-03-20 00:18:08'),
(493, 'students', 0, '2025', '2025-03-20 00:18:13'),
(494, 'faculty', 7, '2025', '2025-03-20 00:18:13'),
(495, 'courses', 0, '03', '2025-03-20 00:18:13'),
(496, 'events', 1, '03', '2025-03-20 00:18:13'),
(497, 'students', 0, '2025', '2025-03-20 00:18:20'),
(498, 'faculty', 7, '2025', '2025-03-20 00:18:20'),
(499, 'courses', 0, '03', '2025-03-20 00:18:20'),
(500, 'events', 1, '03', '2025-03-20 00:18:20'),
(501, 'students', 0, '2025', '2025-03-20 00:18:23'),
(502, 'faculty', 7, '2025', '2025-03-20 00:18:23'),
(503, 'courses', 0, '03', '2025-03-20 00:18:23'),
(504, 'events', 1, '03', '2025-03-20 00:18:23'),
(505, 'students', 0, '2025', '2025-03-20 00:18:28'),
(506, 'faculty', 7, '2025', '2025-03-20 00:18:28'),
(507, 'courses', 0, '03', '2025-03-20 00:18:28'),
(508, 'events', 1, '03', '2025-03-20 00:18:28'),
(509, 'students', 0, '2025', '2025-03-20 00:18:33'),
(510, 'faculty', 7, '2025', '2025-03-20 00:18:33'),
(511, 'courses', 0, '03', '2025-03-20 00:18:33'),
(512, 'events', 1, '03', '2025-03-20 00:18:33'),
(513, 'students', 0, '2025', '2025-03-20 00:18:38'),
(514, 'faculty', 7, '2025', '2025-03-20 00:18:38'),
(515, 'courses', 0, '03', '2025-03-20 00:18:38'),
(516, 'events', 1, '03', '2025-03-20 00:18:38'),
(517, 'students', 0, '2025', '2025-03-20 00:18:43'),
(518, 'faculty', 7, '2025', '2025-03-20 00:18:43'),
(519, 'courses', 0, '03', '2025-03-20 00:18:43'),
(520, 'events', 1, '03', '2025-03-20 00:18:43'),
(521, 'students', 0, '2025', '2025-03-20 00:18:48'),
(522, 'faculty', 7, '2025', '2025-03-20 00:18:48'),
(523, 'courses', 0, '03', '2025-03-20 00:18:48'),
(524, 'events', 1, '03', '2025-03-20 00:18:48'),
(525, 'students', 0, '2025', '2025-03-20 00:18:53'),
(526, 'faculty', 7, '2025', '2025-03-20 00:18:53'),
(527, 'courses', 0, '03', '2025-03-20 00:18:53'),
(528, 'events', 1, '03', '2025-03-20 00:18:53'),
(529, 'students', 0, '2025', '2025-03-20 00:18:58'),
(530, 'faculty', 7, '2025', '2025-03-20 00:18:58'),
(531, 'courses', 0, '03', '2025-03-20 00:18:58'),
(532, 'events', 1, '03', '2025-03-20 00:18:58'),
(533, 'students', 0, '2025', '2025-03-20 00:19:03'),
(534, 'faculty', 7, '2025', '2025-03-20 00:19:03'),
(535, 'courses', 0, '03', '2025-03-20 00:19:03'),
(536, 'events', 1, '03', '2025-03-20 00:19:03'),
(537, 'students', 0, '2025', '2025-03-20 00:19:08'),
(538, 'faculty', 7, '2025', '2025-03-20 00:19:08'),
(539, 'courses', 0, '03', '2025-03-20 00:19:08'),
(540, 'events', 1, '03', '2025-03-20 00:19:08'),
(541, 'students', 0, '2025', '2025-03-20 00:19:13'),
(542, 'faculty', 7, '2025', '2025-03-20 00:19:13'),
(543, 'courses', 0, '03', '2025-03-20 00:19:13'),
(544, 'events', 1, '03', '2025-03-20 00:19:13'),
(545, 'students', 0, '2025', '2025-03-20 00:19:18'),
(546, 'faculty', 7, '2025', '2025-03-20 00:19:18'),
(547, 'courses', 0, '03', '2025-03-20 00:19:18'),
(548, 'events', 1, '03', '2025-03-20 00:19:18'),
(549, 'students', 0, '2025', '2025-03-20 00:19:23'),
(550, 'faculty', 7, '2025', '2025-03-20 00:19:23'),
(551, 'courses', 0, '03', '2025-03-20 00:19:23'),
(552, 'events', 1, '03', '2025-03-20 00:19:23'),
(553, 'students', 0, '2025', '2025-03-20 00:19:47'),
(554, 'faculty', 7, '2025', '2025-03-20 00:19:47'),
(555, 'courses', 0, '03', '2025-03-20 00:19:47'),
(556, 'events', 1, '03', '2025-03-20 00:19:47'),
(557, 'students', 0, '2025', '2025-03-20 00:19:54'),
(558, 'faculty', 7, '2025', '2025-03-20 00:19:54'),
(559, 'courses', 0, '03', '2025-03-20 00:19:54'),
(560, 'events', 1, '03', '2025-03-20 00:19:54'),
(561, 'students', 0, '2025', '2025-03-20 00:19:58'),
(562, 'faculty', 7, '2025', '2025-03-20 00:19:58'),
(563, 'courses', 0, '03', '2025-03-20 00:19:58'),
(564, 'events', 1, '03', '2025-03-20 00:19:58'),
(565, 'students', 0, '2025', '2025-03-20 00:20:02'),
(566, 'faculty', 7, '2025', '2025-03-20 00:20:02'),
(567, 'courses', 0, '03', '2025-03-20 00:20:02'),
(568, 'events', 1, '03', '2025-03-20 00:20:02'),
(569, 'students', 0, '2025', '2025-03-20 00:20:02'),
(570, 'faculty', 7, '2025', '2025-03-20 00:20:02'),
(571, 'courses', 0, '03', '2025-03-20 00:20:02'),
(572, 'events', 1, '03', '2025-03-20 00:20:02'),
(573, 'students', 0, '2025', '2025-03-20 00:20:07'),
(574, 'faculty', 7, '2025', '2025-03-20 00:20:07'),
(575, 'courses', 0, '03', '2025-03-20 00:20:07'),
(576, 'events', 1, '03', '2025-03-20 00:20:07'),
(577, 'students', 0, '2025', '2025-03-20 00:20:12'),
(578, 'faculty', 7, '2025', '2025-03-20 00:20:12'),
(579, 'courses', 0, '03', '2025-03-20 00:20:12'),
(580, 'events', 1, '03', '2025-03-20 00:20:12'),
(581, 'students', 0, '2025', '2025-03-20 00:20:17'),
(582, 'faculty', 7, '2025', '2025-03-20 00:20:17'),
(583, 'courses', 0, '03', '2025-03-20 00:20:17'),
(584, 'events', 1, '03', '2025-03-20 00:20:17'),
(585, 'students', 0, '2025', '2025-03-20 00:20:22'),
(586, 'faculty', 7, '2025', '2025-03-20 00:20:22'),
(587, 'courses', 0, '03', '2025-03-20 00:20:22'),
(588, 'events', 1, '03', '2025-03-20 00:20:22'),
(589, 'students', 0, '2025', '2025-03-20 00:20:27'),
(590, 'faculty', 7, '2025', '2025-03-20 00:20:27'),
(591, 'courses', 0, '03', '2025-03-20 00:20:27'),
(592, 'events', 1, '03', '2025-03-20 00:20:27'),
(593, 'students', 0, '2025', '2025-03-20 00:20:32'),
(594, 'faculty', 7, '2025', '2025-03-20 00:20:32'),
(595, 'courses', 0, '03', '2025-03-20 00:20:32'),
(596, 'events', 1, '03', '2025-03-20 00:20:32'),
(597, 'students', 0, '2025', '2025-03-20 00:20:38'),
(598, 'faculty', 7, '2025', '2025-03-20 00:20:38'),
(599, 'courses', 0, '03', '2025-03-20 00:20:38'),
(600, 'events', 1, '03', '2025-03-20 00:20:38'),
(601, 'students', 0, '2025', '2025-03-20 00:20:43'),
(602, 'faculty', 7, '2025', '2025-03-20 00:20:43'),
(603, 'courses', 0, '03', '2025-03-20 00:20:43'),
(604, 'events', 1, '03', '2025-03-20 00:20:43'),
(605, 'students', 0, '2025', '2025-03-20 00:20:48'),
(606, 'faculty', 7, '2025', '2025-03-20 00:20:48'),
(607, 'courses', 0, '03', '2025-03-20 00:20:48'),
(608, 'events', 1, '03', '2025-03-20 00:20:48'),
(609, 'students', 0, '2025', '2025-03-20 00:20:53'),
(610, 'faculty', 7, '2025', '2025-03-20 00:20:53'),
(611, 'courses', 0, '03', '2025-03-20 00:20:53'),
(612, 'events', 1, '03', '2025-03-20 00:20:53'),
(613, 'students', 0, '2025', '2025-03-20 00:20:58'),
(614, 'faculty', 7, '2025', '2025-03-20 00:20:58'),
(615, 'courses', 0, '03', '2025-03-20 00:20:58'),
(616, 'events', 1, '03', '2025-03-20 00:20:58'),
(617, 'students', 0, '2025', '2025-03-20 00:21:03'),
(618, 'faculty', 7, '2025', '2025-03-20 00:21:03'),
(619, 'courses', 0, '03', '2025-03-20 00:21:03'),
(620, 'events', 1, '03', '2025-03-20 00:21:03'),
(621, 'students', 0, '2025', '2025-03-20 00:21:08'),
(622, 'faculty', 7, '2025', '2025-03-20 00:21:08'),
(623, 'courses', 0, '03', '2025-03-20 00:21:08'),
(624, 'events', 1, '03', '2025-03-20 00:21:08'),
(625, 'students', 0, '2025', '2025-03-20 00:21:13'),
(626, 'faculty', 7, '2025', '2025-03-20 00:21:13'),
(627, 'courses', 0, '03', '2025-03-20 00:21:13'),
(628, 'events', 1, '03', '2025-03-20 00:21:13'),
(629, 'students', 0, '2025', '2025-03-20 00:21:18'),
(630, 'faculty', 7, '2025', '2025-03-20 00:21:18'),
(631, 'courses', 0, '03', '2025-03-20 00:21:18'),
(632, 'events', 1, '03', '2025-03-20 00:21:18'),
(633, 'students', 0, '2025', '2025-03-20 00:21:23'),
(634, 'faculty', 7, '2025', '2025-03-20 00:21:23'),
(635, 'courses', 0, '03', '2025-03-20 00:21:23'),
(636, 'events', 1, '03', '2025-03-20 00:21:23'),
(637, 'students', 0, '2025', '2025-03-20 00:21:28'),
(638, 'faculty', 7, '2025', '2025-03-20 00:21:28'),
(639, 'courses', 0, '03', '2025-03-20 00:21:28'),
(640, 'events', 1, '03', '2025-03-20 00:21:28'),
(641, 'students', 0, '2025', '2025-03-20 00:21:33'),
(642, 'faculty', 7, '2025', '2025-03-20 00:21:33'),
(643, 'courses', 0, '03', '2025-03-20 00:21:33'),
(644, 'events', 1, '03', '2025-03-20 00:21:33'),
(645, 'students', 0, '2025', '2025-03-20 00:21:47'),
(646, 'faculty', 7, '2025', '2025-03-20 00:21:47'),
(647, 'courses', 0, '03', '2025-03-20 00:21:47'),
(648, 'events', 1, '03', '2025-03-20 00:21:47'),
(649, 'students', 0, '2025', '2025-03-20 00:22:47'),
(650, 'faculty', 7, '2025', '2025-03-20 00:22:47'),
(651, 'courses', 0, '03', '2025-03-20 00:22:47'),
(652, 'events', 1, '03', '2025-03-20 00:22:47'),
(653, 'students', 0, '2025', '2025-03-20 00:23:42'),
(654, 'faculty', 7, '2025', '2025-03-20 00:23:42'),
(655, 'courses', 0, '03', '2025-03-20 00:23:42'),
(656, 'events', 1, '03', '2025-03-20 00:23:42'),
(657, 'students', 0, '2025', '2025-03-20 00:23:48'),
(658, 'faculty', 7, '2025', '2025-03-20 00:23:48'),
(659, 'courses', 0, '03', '2025-03-20 00:23:48'),
(660, 'events', 1, '03', '2025-03-20 00:23:48'),
(661, 'students', 0, '2025', '2025-03-20 00:23:53'),
(662, 'faculty', 7, '2025', '2025-03-20 00:23:53'),
(663, 'courses', 0, '03', '2025-03-20 00:23:53'),
(664, 'events', 1, '03', '2025-03-20 00:23:53'),
(665, 'students', 0, '2025', '2025-03-20 00:23:58'),
(666, 'faculty', 7, '2025', '2025-03-20 00:23:58'),
(667, 'courses', 0, '03', '2025-03-20 00:23:58'),
(668, 'events', 1, '03', '2025-03-20 00:23:58'),
(669, 'students', 0, '2025', '2025-03-20 00:24:03'),
(670, 'faculty', 7, '2025', '2025-03-20 00:24:03'),
(671, 'courses', 0, '03', '2025-03-20 00:24:03'),
(672, 'events', 1, '03', '2025-03-20 00:24:03'),
(673, 'students', 0, '2025', '2025-03-20 00:24:08'),
(674, 'faculty', 7, '2025', '2025-03-20 00:24:08'),
(675, 'courses', 0, '03', '2025-03-20 00:24:08'),
(676, 'events', 1, '03', '2025-03-20 00:24:08'),
(677, 'students', 0, '2025', '2025-03-20 00:24:13'),
(678, 'faculty', 7, '2025', '2025-03-20 00:24:13'),
(679, 'courses', 0, '03', '2025-03-20 00:24:13'),
(680, 'events', 1, '03', '2025-03-20 00:24:13'),
(681, 'students', 0, '2025', '2025-03-20 00:24:18'),
(682, 'faculty', 7, '2025', '2025-03-20 00:24:18'),
(683, 'courses', 0, '03', '2025-03-20 00:24:18'),
(684, 'events', 1, '03', '2025-03-20 00:24:18'),
(685, 'students', 0, '2025', '2025-03-20 00:24:23'),
(686, 'faculty', 7, '2025', '2025-03-20 00:24:23'),
(687, 'courses', 0, '03', '2025-03-20 00:24:23'),
(688, 'events', 1, '03', '2025-03-20 00:24:23'),
(689, 'students', 0, '2025', '2025-03-20 00:24:28'),
(690, 'faculty', 7, '2025', '2025-03-20 00:24:28'),
(691, 'courses', 0, '03', '2025-03-20 00:24:28'),
(692, 'events', 1, '03', '2025-03-20 00:24:28'),
(693, 'students', 0, '2025', '2025-03-20 00:24:33'),
(694, 'faculty', 7, '2025', '2025-03-20 00:24:33'),
(695, 'courses', 0, '03', '2025-03-20 00:24:33'),
(696, 'events', 1, '03', '2025-03-20 00:24:33'),
(697, 'students', 0, '2025', '2025-03-20 00:24:38'),
(698, 'faculty', 7, '2025', '2025-03-20 00:24:38'),
(699, 'courses', 0, '03', '2025-03-20 00:24:38'),
(700, 'events', 1, '03', '2025-03-20 00:24:38'),
(701, 'students', 0, '2025', '2025-03-20 00:24:43'),
(702, 'faculty', 7, '2025', '2025-03-20 00:24:43'),
(703, 'courses', 0, '03', '2025-03-20 00:24:43'),
(704, 'events', 1, '03', '2025-03-20 00:24:43'),
(705, 'students', 0, '2025', '2025-03-20 00:25:44'),
(706, 'faculty', 7, '2025', '2025-03-20 00:25:44'),
(707, 'courses', 0, '03', '2025-03-20 00:25:44'),
(708, 'events', 1, '03', '2025-03-20 00:25:44'),
(709, 'students', 0, '2025', '2025-03-20 00:27:16'),
(710, 'faculty', 7, '2025', '2025-03-20 00:27:16'),
(711, 'courses', 0, '03', '2025-03-20 00:27:16'),
(712, 'events', 1, '03', '2025-03-20 00:27:16'),
(713, 'students', 0, '2025', '2025-03-20 00:27:17'),
(714, 'faculty', 7, '2025', '2025-03-20 00:27:17'),
(715, 'courses', 0, '03', '2025-03-20 00:27:17'),
(716, 'events', 1, '03', '2025-03-20 00:27:17'),
(717, 'students', 0, '2025', '2025-03-20 00:28:30'),
(718, 'faculty', 7, '2025', '2025-03-20 00:28:30'),
(719, 'courses', 0, '03', '2025-03-20 00:28:30'),
(720, 'events', 1, '03', '2025-03-20 00:28:30'),
(721, 'students', 0, '2025', '2025-03-20 00:28:30'),
(722, 'faculty', 7, '2025', '2025-03-20 00:28:30'),
(723, 'courses', 0, '03', '2025-03-20 00:28:30'),
(724, 'events', 1, '03', '2025-03-20 00:28:30'),
(725, 'students', 0, '2025', '2025-03-20 00:28:35'),
(726, 'faculty', 7, '2025', '2025-03-20 00:28:35'),
(727, 'courses', 0, '03', '2025-03-20 00:28:35'),
(728, 'events', 1, '03', '2025-03-20 00:28:35'),
(729, 'students', 0, '2025', '2025-03-20 00:28:40'),
(730, 'faculty', 7, '2025', '2025-03-20 00:28:40'),
(731, 'courses', 0, '03', '2025-03-20 00:28:40'),
(732, 'events', 1, '03', '2025-03-20 00:28:40'),
(733, 'students', 0, '2025', '2025-03-20 00:28:45'),
(734, 'faculty', 7, '2025', '2025-03-20 00:28:45'),
(735, 'courses', 0, '03', '2025-03-20 00:28:45'),
(736, 'events', 1, '03', '2025-03-20 00:28:45'),
(737, 'students', 0, '2025', '2025-03-20 00:28:50'),
(738, 'faculty', 7, '2025', '2025-03-20 00:28:50'),
(739, 'courses', 0, '03', '2025-03-20 00:28:50'),
(740, 'events', 1, '03', '2025-03-20 00:28:50'),
(741, 'students', 0, '2025', '2025-03-20 00:28:56'),
(742, 'faculty', 7, '2025', '2025-03-20 00:28:56'),
(743, 'courses', 0, '03', '2025-03-20 00:28:56'),
(744, 'events', 1, '03', '2025-03-20 00:28:56'),
(745, 'students', 0, '2025', '2025-03-20 00:29:01'),
(746, 'faculty', 7, '2025', '2025-03-20 00:29:01'),
(747, 'courses', 0, '03', '2025-03-20 00:29:01'),
(748, 'events', 1, '03', '2025-03-20 00:29:01'),
(749, 'students', 0, '2025', '2025-03-20 00:29:06'),
(750, 'faculty', 7, '2025', '2025-03-20 00:29:06'),
(751, 'courses', 0, '03', '2025-03-20 00:29:06'),
(752, 'events', 1, '03', '2025-03-20 00:29:06'),
(753, 'students', 0, '2025', '2025-03-20 00:29:11'),
(754, 'faculty', 7, '2025', '2025-03-20 00:29:11'),
(755, 'courses', 0, '03', '2025-03-20 00:29:11'),
(756, 'events', 1, '03', '2025-03-20 00:29:11'),
(757, 'students', 0, '2025', '2025-03-20 00:29:16'),
(758, 'faculty', 7, '2025', '2025-03-20 00:29:16'),
(759, 'courses', 0, '03', '2025-03-20 00:29:16'),
(760, 'events', 1, '03', '2025-03-20 00:29:16'),
(761, 'students', 0, '2025', '2025-03-20 00:29:21'),
(762, 'faculty', 7, '2025', '2025-03-20 00:29:21'),
(763, 'courses', 0, '03', '2025-03-20 00:29:21'),
(764, 'events', 1, '03', '2025-03-20 00:29:21'),
(765, 'students', 0, '2025', '2025-03-20 00:29:26'),
(766, 'faculty', 7, '2025', '2025-03-20 00:29:26'),
(767, 'courses', 0, '03', '2025-03-20 00:29:26'),
(768, 'events', 1, '03', '2025-03-20 00:29:26'),
(769, 'students', 0, '2025', '2025-03-20 00:29:31'),
(770, 'faculty', 7, '2025', '2025-03-20 00:29:31'),
(771, 'courses', 0, '03', '2025-03-20 00:29:31'),
(772, 'events', 1, '03', '2025-03-20 00:29:31'),
(773, 'students', 0, '2025', '2025-03-20 00:29:36'),
(774, 'faculty', 7, '2025', '2025-03-20 00:29:36'),
(775, 'courses', 0, '03', '2025-03-20 00:29:36'),
(776, 'events', 1, '03', '2025-03-20 00:29:36'),
(777, 'students', 0, '2025', '2025-03-20 00:29:41'),
(778, 'faculty', 7, '2025', '2025-03-20 00:29:41'),
(779, 'courses', 0, '03', '2025-03-20 00:29:41'),
(780, 'events', 1, '03', '2025-03-20 00:29:41'),
(781, 'students', 0, '2025', '2025-03-20 00:29:46'),
(782, 'faculty', 7, '2025', '2025-03-20 00:29:46'),
(783, 'courses', 0, '03', '2025-03-20 00:29:46'),
(784, 'events', 1, '03', '2025-03-20 00:29:46'),
(785, 'students', 0, '2025', '2025-03-20 00:29:51'),
(786, 'faculty', 7, '2025', '2025-03-20 00:29:51'),
(787, 'courses', 0, '03', '2025-03-20 00:29:51'),
(788, 'events', 1, '03', '2025-03-20 00:29:51'),
(789, 'students', 0, '2025', '2025-03-20 00:30:05'),
(790, 'faculty', 7, '2025', '2025-03-20 00:30:05'),
(791, 'courses', 0, '03', '2025-03-20 00:30:05'),
(792, 'events', 1, '03', '2025-03-20 00:30:05'),
(793, 'students', 0, '2025', '2025-03-20 00:30:05'),
(794, 'faculty', 7, '2025', '2025-03-20 00:30:05'),
(795, 'courses', 0, '03', '2025-03-20 00:30:05'),
(796, 'events', 1, '03', '2025-03-20 00:30:05'),
(797, 'students', 0, '2025', '2025-03-20 00:30:07'),
(798, 'faculty', 7, '2025', '2025-03-20 00:30:07'),
(799, 'courses', 0, '03', '2025-03-20 00:30:07'),
(800, 'events', 1, '03', '2025-03-20 00:30:07'),
(801, 'students', 0, '2025', '2025-03-20 00:30:07'),
(802, 'faculty', 7, '2025', '2025-03-20 00:30:07'),
(803, 'courses', 0, '03', '2025-03-20 00:30:07'),
(804, 'events', 1, '03', '2025-03-20 00:30:07'),
(805, 'students', 0, '2025', '2025-03-20 00:30:12'),
(806, 'faculty', 7, '2025', '2025-03-20 00:30:12'),
(807, 'courses', 0, '03', '2025-03-20 00:30:12'),
(808, 'events', 1, '03', '2025-03-20 00:30:12'),
(809, 'students', 0, '2025', '2025-03-20 00:30:14'),
(810, 'faculty', 7, '2025', '2025-03-20 00:30:14'),
(811, 'courses', 0, '03', '2025-03-20 00:30:14'),
(812, 'events', 1, '03', '2025-03-20 00:30:14'),
(813, 'students', 0, '2025', '2025-03-20 00:30:14'),
(814, 'faculty', 7, '2025', '2025-03-20 00:30:14'),
(815, 'courses', 0, '03', '2025-03-20 00:30:14'),
(816, 'events', 1, '03', '2025-03-20 00:30:14'),
(817, 'students', 0, '2025', '2025-03-20 00:30:19'),
(818, 'faculty', 7, '2025', '2025-03-20 00:30:19'),
(819, 'courses', 0, '03', '2025-03-20 00:30:19'),
(820, 'events', 1, '03', '2025-03-20 00:30:19'),
(821, 'students', 0, '2025', '2025-03-20 00:30:24'),
(822, 'faculty', 7, '2025', '2025-03-20 00:30:24'),
(823, 'courses', 0, '03', '2025-03-20 00:30:24'),
(824, 'events', 1, '03', '2025-03-20 00:30:24'),
(825, 'students', 0, '2025', '2025-03-20 00:30:30'),
(826, 'faculty', 7, '2025', '2025-03-20 00:30:30'),
(827, 'courses', 0, '03', '2025-03-20 00:30:30'),
(828, 'events', 1, '03', '2025-03-20 00:30:30'),
(829, 'students', 0, '2025', '2025-03-20 00:30:35'),
(830, 'faculty', 7, '2025', '2025-03-20 00:30:35'),
(831, 'courses', 0, '03', '2025-03-20 00:30:35'),
(832, 'events', 1, '03', '2025-03-20 00:30:35'),
(833, 'students', 0, '2025', '2025-03-20 00:30:40'),
(834, 'faculty', 7, '2025', '2025-03-20 00:30:40'),
(835, 'courses', 0, '03', '2025-03-20 00:30:40'),
(836, 'events', 1, '03', '2025-03-20 00:30:40'),
(837, 'students', 0, '2025', '2025-03-20 00:30:45'),
(838, 'faculty', 7, '2025', '2025-03-20 00:30:45'),
(839, 'courses', 0, '03', '2025-03-20 00:30:45'),
(840, 'events', 1, '03', '2025-03-20 00:30:45'),
(841, 'students', 0, '2025', '2025-03-20 00:30:50'),
(842, 'faculty', 7, '2025', '2025-03-20 00:30:50'),
(843, 'courses', 0, '03', '2025-03-20 00:30:50'),
(844, 'events', 1, '03', '2025-03-20 00:30:50'),
(845, 'students', 0, '2025', '2025-03-20 00:30:55'),
(846, 'faculty', 7, '2025', '2025-03-20 00:30:55'),
(847, 'courses', 0, '03', '2025-03-20 00:30:55'),
(848, 'events', 1, '03', '2025-03-20 00:30:55'),
(849, 'students', 0, '2025', '2025-03-20 00:31:00'),
(850, 'faculty', 7, '2025', '2025-03-20 00:31:00'),
(851, 'courses', 0, '03', '2025-03-20 00:31:00'),
(852, 'events', 1, '03', '2025-03-20 00:31:00'),
(853, 'students', 0, '2025', '2025-03-20 00:31:05'),
(854, 'faculty', 7, '2025', '2025-03-20 00:31:05'),
(855, 'courses', 0, '03', '2025-03-20 00:31:05'),
(856, 'events', 1, '03', '2025-03-20 00:31:05'),
(857, 'students', 0, '2025', '2025-03-20 00:31:10'),
(858, 'faculty', 7, '2025', '2025-03-20 00:31:10'),
(859, 'courses', 0, '03', '2025-03-20 00:31:10'),
(860, 'events', 1, '03', '2025-03-20 00:31:10'),
(861, 'students', 0, '2025', '2025-03-20 00:31:15'),
(862, 'faculty', 7, '2025', '2025-03-20 00:31:15'),
(863, 'courses', 0, '03', '2025-03-20 00:31:15'),
(864, 'events', 1, '03', '2025-03-20 00:31:15'),
(865, 'students', 0, '2025', '2025-03-20 00:31:20'),
(866, 'faculty', 7, '2025', '2025-03-20 00:31:20'),
(867, 'courses', 0, '03', '2025-03-20 00:31:20'),
(868, 'events', 1, '03', '2025-03-20 00:31:20'),
(869, 'students', 0, '2025', '2025-03-20 00:31:47'),
(870, 'faculty', 7, '2025', '2025-03-20 00:31:47'),
(871, 'courses', 0, '03', '2025-03-20 00:31:47'),
(872, 'events', 1, '03', '2025-03-20 00:31:47'),
(873, 'students', 0, '2025', '2025-03-20 00:31:56'),
(874, 'faculty', 7, '2025', '2025-03-20 00:31:56'),
(875, 'courses', 0, '03', '2025-03-20 00:31:56'),
(876, 'events', 1, '03', '2025-03-20 00:31:56'),
(877, 'students', 0, '2025', '2025-03-20 00:31:58'),
(878, 'faculty', 7, '2025', '2025-03-20 00:31:58'),
(879, 'courses', 0, '03', '2025-03-20 00:31:58'),
(880, 'events', 1, '03', '2025-03-20 00:31:58'),
(881, 'students', 0, '2025', '2025-03-20 00:31:58'),
(882, 'faculty', 7, '2025', '2025-03-20 00:31:58'),
(883, 'courses', 0, '03', '2025-03-20 00:31:58'),
(884, 'events', 1, '03', '2025-03-20 00:31:58'),
(885, 'students', 0, '2025', '2025-03-20 00:31:59'),
(886, 'faculty', 7, '2025', '2025-03-20 00:31:59'),
(887, 'courses', 0, '03', '2025-03-20 00:31:59'),
(888, 'events', 1, '03', '2025-03-20 00:31:59'),
(889, 'students', 0, '2025', '2025-03-20 00:31:59'),
(890, 'faculty', 7, '2025', '2025-03-20 00:31:59'),
(891, 'courses', 0, '03', '2025-03-20 00:31:59'),
(892, 'events', 1, '03', '2025-03-20 00:31:59'),
(893, 'students', 0, '2025', '2025-03-20 00:32:04'),
(894, 'faculty', 7, '2025', '2025-03-20 00:32:04'),
(895, 'courses', 0, '03', '2025-03-20 00:32:04'),
(896, 'events', 1, '03', '2025-03-20 00:32:04'),
(897, 'students', 0, '2025', '2025-03-20 00:32:10'),
(898, 'faculty', 7, '2025', '2025-03-20 00:32:10'),
(899, 'courses', 0, '03', '2025-03-20 00:32:10'),
(900, 'events', 1, '03', '2025-03-20 00:32:10'),
(901, 'students', 0, '2025', '2025-03-20 00:32:15'),
(902, 'faculty', 7, '2025', '2025-03-20 00:32:15'),
(903, 'courses', 0, '03', '2025-03-20 00:32:15'),
(904, 'events', 1, '03', '2025-03-20 00:32:15'),
(905, 'students', 0, '2025', '2025-03-20 00:32:20'),
(906, 'faculty', 7, '2025', '2025-03-20 00:32:20'),
(907, 'courses', 0, '03', '2025-03-20 00:32:20'),
(908, 'events', 1, '03', '2025-03-20 00:32:20'),
(909, 'students', 0, '2025', '2025-03-20 00:32:25'),
(910, 'faculty', 7, '2025', '2025-03-20 00:32:25'),
(911, 'courses', 0, '03', '2025-03-20 00:32:25'),
(912, 'events', 1, '03', '2025-03-20 00:32:25'),
(913, 'students', 0, '2025', '2025-03-20 00:32:30'),
(914, 'faculty', 7, '2025', '2025-03-20 00:32:30'),
(915, 'courses', 0, '03', '2025-03-20 00:32:30'),
(916, 'events', 1, '03', '2025-03-20 00:32:30'),
(917, 'students', 0, '2025', '2025-03-20 00:32:35'),
(918, 'faculty', 7, '2025', '2025-03-20 00:32:35'),
(919, 'courses', 0, '03', '2025-03-20 00:32:35'),
(920, 'events', 1, '03', '2025-03-20 00:32:35'),
(921, 'students', 0, '2025', '2025-03-20 00:32:40'),
(922, 'faculty', 7, '2025', '2025-03-20 00:32:40'),
(923, 'courses', 0, '03', '2025-03-20 00:32:40'),
(924, 'events', 1, '03', '2025-03-20 00:32:40'),
(925, 'students', 0, '2025', '2025-03-20 00:32:45'),
(926, 'faculty', 7, '2025', '2025-03-20 00:32:45'),
(927, 'courses', 0, '03', '2025-03-20 00:32:45'),
(928, 'events', 1, '03', '2025-03-20 00:32:45'),
(929, 'students', 0, '2025', '2025-03-20 00:32:47'),
(930, 'faculty', 7, '2025', '2025-03-20 00:32:47'),
(931, 'courses', 0, '03', '2025-03-20 00:32:47'),
(932, 'events', 1, '03', '2025-03-20 00:32:47'),
(933, 'students', 0, '2025', '2025-03-20 00:32:47'),
(934, 'faculty', 7, '2025', '2025-03-20 00:32:47'),
(935, 'courses', 0, '03', '2025-03-20 00:32:47'),
(936, 'events', 1, '03', '2025-03-20 00:32:47'),
(937, 'students', 0, '2025', '2025-03-20 00:32:54'),
(938, 'faculty', 7, '2025', '2025-03-20 00:32:54'),
(939, 'courses', 0, '03', '2025-03-20 00:32:54'),
(940, 'events', 1, '03', '2025-03-20 00:32:54'),
(941, 'students', 0, '2025', '2025-03-20 00:32:58'),
(942, 'faculty', 7, '2025', '2025-03-20 00:32:58'),
(943, 'courses', 0, '03', '2025-03-20 00:32:58'),
(944, 'events', 1, '03', '2025-03-20 00:32:58'),
(945, 'students', 0, '2025', '2025-03-20 00:33:03'),
(946, 'faculty', 7, '2025', '2025-03-20 00:33:03'),
(947, 'courses', 0, '03', '2025-03-20 00:33:03'),
(948, 'events', 1, '03', '2025-03-20 00:33:03'),
(949, 'students', 0, '2025', '2025-03-20 00:33:08'),
(950, 'faculty', 7, '2025', '2025-03-20 00:33:08'),
(951, 'courses', 0, '03', '2025-03-20 00:33:08'),
(952, 'events', 1, '03', '2025-03-20 00:33:08'),
(953, 'students', 0, '2025', '2025-03-20 00:33:13'),
(954, 'faculty', 7, '2025', '2025-03-20 00:33:13'),
(955, 'courses', 0, '03', '2025-03-20 00:33:13'),
(956, 'events', 1, '03', '2025-03-20 00:33:13'),
(957, 'students', 0, '2025', '2025-03-20 00:33:18'),
(958, 'faculty', 7, '2025', '2025-03-20 00:33:18'),
(959, 'courses', 0, '03', '2025-03-20 00:33:18'),
(960, 'events', 1, '03', '2025-03-20 00:33:18'),
(961, 'students', 0, '2025', '2025-03-20 00:33:23'),
(962, 'faculty', 7, '2025', '2025-03-20 00:33:23'),
(963, 'courses', 0, '03', '2025-03-20 00:33:23'),
(964, 'events', 1, '03', '2025-03-20 00:33:23'),
(965, 'students', 0, '2025', '2025-03-20 00:33:28'),
(966, 'faculty', 7, '2025', '2025-03-20 00:33:28'),
(967, 'courses', 0, '03', '2025-03-20 00:33:28'),
(968, 'events', 1, '03', '2025-03-20 00:33:28'),
(969, 'students', 0, '2025', '2025-03-20 00:33:33'),
(970, 'faculty', 7, '2025', '2025-03-20 00:33:33'),
(971, 'courses', 0, '03', '2025-03-20 00:33:33'),
(972, 'events', 1, '03', '2025-03-20 00:33:33'),
(973, 'students', 0, '2025', '2025-03-20 00:33:38'),
(974, 'faculty', 7, '2025', '2025-03-20 00:33:38'),
(975, 'courses', 0, '03', '2025-03-20 00:33:38'),
(976, 'events', 1, '03', '2025-03-20 00:33:38'),
(977, 'students', 0, '2025', '2025-03-20 00:33:43'),
(978, 'faculty', 7, '2025', '2025-03-20 00:33:43'),
(979, 'courses', 0, '03', '2025-03-20 00:33:43'),
(980, 'events', 1, '03', '2025-03-20 00:33:43'),
(981, 'students', 0, '2025', '2025-03-20 00:33:48'),
(982, 'faculty', 7, '2025', '2025-03-20 00:33:48'),
(983, 'courses', 0, '03', '2025-03-20 00:33:48'),
(984, 'events', 1, '03', '2025-03-20 00:33:48'),
(985, 'students', 0, '2025', '2025-03-20 00:33:53'),
(986, 'faculty', 7, '2025', '2025-03-20 00:33:53'),
(987, 'courses', 0, '03', '2025-03-20 00:33:53'),
(988, 'events', 1, '03', '2025-03-20 00:33:53'),
(989, 'students', 0, '2025', '2025-03-20 00:34:29'),
(990, 'faculty', 7, '2025', '2025-03-20 00:34:29'),
(991, 'courses', 0, '03', '2025-03-20 00:34:29'),
(992, 'events', 1, '03', '2025-03-20 00:34:29'),
(993, 'students', 0, '2025', '2025-03-20 00:34:31'),
(994, 'faculty', 7, '2025', '2025-03-20 00:34:31'),
(995, 'courses', 0, '03', '2025-03-20 00:34:31'),
(996, 'events', 1, '03', '2025-03-20 00:34:31'),
(997, 'students', 0, '2025', '2025-03-20 00:34:44'),
(998, 'faculty', 7, '2025', '2025-03-20 00:34:44'),
(999, 'courses', 0, '03', '2025-03-20 00:34:44'),
(1000, 'events', 1, '03', '2025-03-20 00:34:44'),
(1001, 'students', 0, '2025', '2025-03-20 00:34:44'),
(1002, 'faculty', 7, '2025', '2025-03-20 00:34:44'),
(1003, 'courses', 0, '03', '2025-03-20 00:34:44'),
(1004, 'events', 1, '03', '2025-03-20 00:34:44'),
(1005, 'students', 0, '2025', '2025-03-20 00:34:50'),
(1006, 'faculty', 7, '2025', '2025-03-20 00:34:50'),
(1007, 'courses', 0, '03', '2025-03-20 00:34:50'),
(1008, 'events', 1, '03', '2025-03-20 00:34:50'),
(1009, 'students', 0, '2025', '2025-03-20 00:34:55'),
(1010, 'faculty', 7, '2025', '2025-03-20 00:34:55'),
(1011, 'courses', 0, '03', '2025-03-20 00:34:55'),
(1012, 'events', 1, '03', '2025-03-20 00:34:55'),
(1013, 'students', 0, '2025', '2025-03-20 00:35:00'),
(1014, 'faculty', 7, '2025', '2025-03-20 00:35:00'),
(1015, 'courses', 0, '03', '2025-03-20 00:35:00'),
(1016, 'events', 1, '03', '2025-03-20 00:35:00'),
(1017, 'students', 0, '2025', '2025-03-20 00:35:05'),
(1018, 'faculty', 7, '2025', '2025-03-20 00:35:05'),
(1019, 'courses', 0, '03', '2025-03-20 00:35:05'),
(1020, 'events', 1, '03', '2025-03-20 00:35:05');
INSERT INTO `statistics` (`id`, `metric`, `value`, `period`, `recorded_at`) VALUES
(1021, 'students', 0, '2025', '2025-03-20 00:35:10'),
(1022, 'faculty', 7, '2025', '2025-03-20 00:35:10'),
(1023, 'courses', 0, '03', '2025-03-20 00:35:10'),
(1024, 'events', 1, '03', '2025-03-20 00:35:10'),
(1025, 'students', 0, '2025', '2025-03-20 00:35:14'),
(1026, 'faculty', 7, '2025', '2025-03-20 00:35:14'),
(1027, 'courses', 0, '03', '2025-03-20 00:35:14'),
(1028, 'events', 1, '03', '2025-03-20 00:35:14'),
(1029, 'students', 0, '2025', '2025-03-20 00:35:19'),
(1030, 'faculty', 7, '2025', '2025-03-20 00:35:19'),
(1031, 'courses', 0, '03', '2025-03-20 00:35:19'),
(1032, 'events', 1, '03', '2025-03-20 00:35:19'),
(1033, 'students', 0, '2025', '2025-03-20 00:35:24'),
(1034, 'faculty', 7, '2025', '2025-03-20 00:35:24'),
(1035, 'courses', 0, '03', '2025-03-20 00:35:24'),
(1036, 'events', 1, '03', '2025-03-20 00:35:24'),
(1037, 'students', 0, '2025', '2025-03-20 00:35:27'),
(1038, 'faculty', 7, '2025', '2025-03-20 00:35:27'),
(1039, 'courses', 0, '03', '2025-03-20 00:35:27'),
(1040, 'events', 1, '03', '2025-03-20 00:35:27'),
(1041, 'students', 0, '2025', '2025-03-20 00:35:28'),
(1042, 'faculty', 7, '2025', '2025-03-20 00:35:28'),
(1043, 'courses', 0, '03', '2025-03-20 00:35:28'),
(1044, 'events', 1, '03', '2025-03-20 00:35:28'),
(1045, 'students', 0, '2025', '2025-03-20 00:35:33'),
(1046, 'faculty', 7, '2025', '2025-03-20 00:35:33'),
(1047, 'courses', 0, '03', '2025-03-20 00:35:33'),
(1048, 'events', 1, '03', '2025-03-20 00:35:33'),
(1049, 'students', 0, '2025', '2025-03-20 00:35:38'),
(1050, 'faculty', 7, '2025', '2025-03-20 00:35:38'),
(1051, 'courses', 0, '03', '2025-03-20 00:35:38'),
(1052, 'events', 1, '03', '2025-03-20 00:35:38'),
(1053, 'students', 0, '2025', '2025-03-20 00:35:43'),
(1054, 'faculty', 7, '2025', '2025-03-20 00:35:43'),
(1055, 'courses', 0, '03', '2025-03-20 00:35:43'),
(1056, 'events', 1, '03', '2025-03-20 00:35:43'),
(1057, 'students', 0, '2025', '2025-03-20 00:35:48'),
(1058, 'faculty', 7, '2025', '2025-03-20 00:35:48'),
(1059, 'courses', 0, '03', '2025-03-20 00:35:48'),
(1060, 'events', 1, '03', '2025-03-20 00:35:48'),
(1061, 'students', 0, '2025', '2025-03-20 00:35:53'),
(1062, 'faculty', 7, '2025', '2025-03-20 00:35:53'),
(1063, 'courses', 0, '03', '2025-03-20 00:35:53'),
(1064, 'events', 1, '03', '2025-03-20 00:35:53'),
(1065, 'students', 0, '2025', '2025-03-20 00:35:58'),
(1066, 'faculty', 7, '2025', '2025-03-20 00:35:58'),
(1067, 'courses', 0, '03', '2025-03-20 00:35:58'),
(1068, 'events', 1, '03', '2025-03-20 00:35:58'),
(1069, 'students', 0, '2025', '2025-03-20 00:36:03'),
(1070, 'faculty', 7, '2025', '2025-03-20 00:36:03'),
(1071, 'courses', 0, '03', '2025-03-20 00:36:03'),
(1072, 'events', 1, '03', '2025-03-20 00:36:03'),
(1073, 'students', 0, '2025', '2025-03-20 00:36:08'),
(1074, 'faculty', 7, '2025', '2025-03-20 00:36:08'),
(1075, 'courses', 0, '03', '2025-03-20 00:36:08'),
(1076, 'events', 1, '03', '2025-03-20 00:36:08'),
(1077, 'students', 0, '2025', '2025-03-20 00:36:13'),
(1078, 'faculty', 7, '2025', '2025-03-20 00:36:13'),
(1079, 'courses', 0, '03', '2025-03-20 00:36:13'),
(1080, 'events', 1, '03', '2025-03-20 00:36:13'),
(1081, 'students', 0, '2025', '2025-03-20 00:36:18'),
(1082, 'faculty', 7, '2025', '2025-03-20 00:36:18'),
(1083, 'courses', 0, '03', '2025-03-20 00:36:18'),
(1084, 'events', 1, '03', '2025-03-20 00:36:18'),
(1085, 'students', 0, '2025', '2025-03-20 00:36:23'),
(1086, 'faculty', 7, '2025', '2025-03-20 00:36:23'),
(1087, 'courses', 0, '03', '2025-03-20 00:36:23'),
(1088, 'events', 1, '03', '2025-03-20 00:36:23'),
(1089, 'students', 0, '2025', '2025-03-20 00:36:24'),
(1090, 'faculty', 7, '2025', '2025-03-20 00:36:24'),
(1091, 'courses', 0, '03', '2025-03-20 00:36:24'),
(1092, 'events', 1, '03', '2025-03-20 00:36:24'),
(1093, 'students', 0, '2025', '2025-03-20 00:36:24'),
(1094, 'faculty', 7, '2025', '2025-03-20 00:36:24'),
(1095, 'courses', 0, '03', '2025-03-20 00:36:24'),
(1096, 'events', 1, '03', '2025-03-20 00:36:24'),
(1097, 'students', 0, '2025', '2025-03-20 00:36:25'),
(1098, 'faculty', 7, '2025', '2025-03-20 00:36:25'),
(1099, 'courses', 0, '03', '2025-03-20 00:36:25'),
(1100, 'events', 1, '03', '2025-03-20 00:36:25'),
(1101, 'students', 0, '2025', '2025-03-20 00:36:25'),
(1102, 'faculty', 7, '2025', '2025-03-20 00:36:25'),
(1103, 'courses', 0, '03', '2025-03-20 00:36:25'),
(1104, 'events', 1, '03', '2025-03-20 00:36:25'),
(1105, 'students', 0, '2025', '2025-03-20 00:36:30'),
(1106, 'faculty', 7, '2025', '2025-03-20 00:36:30'),
(1107, 'courses', 0, '03', '2025-03-20 00:36:30'),
(1108, 'events', 1, '03', '2025-03-20 00:36:30'),
(1109, 'students', 0, '2025', '2025-03-20 00:36:35'),
(1110, 'faculty', 7, '2025', '2025-03-20 00:36:35'),
(1111, 'courses', 0, '03', '2025-03-20 00:36:35'),
(1112, 'events', 1, '03', '2025-03-20 00:36:35'),
(1113, 'students', 0, '2025', '2025-03-20 00:36:39'),
(1114, 'faculty', 7, '2025', '2025-03-20 00:36:39'),
(1115, 'courses', 0, '03', '2025-03-20 00:36:39'),
(1116, 'events', 1, '03', '2025-03-20 00:36:39'),
(1117, 'students', 0, '2025', '2025-03-20 00:36:39'),
(1118, 'faculty', 7, '2025', '2025-03-20 00:36:39'),
(1119, 'courses', 0, '03', '2025-03-20 00:36:39'),
(1120, 'events', 1, '03', '2025-03-20 00:36:39'),
(1121, 'students', 0, '2025', '2025-03-20 00:36:44'),
(1122, 'faculty', 7, '2025', '2025-03-20 00:36:44'),
(1123, 'courses', 0, '03', '2025-03-20 00:36:44'),
(1124, 'events', 1, '03', '2025-03-20 00:36:44'),
(1125, 'students', 0, '2025', '2025-03-20 00:36:49'),
(1126, 'faculty', 7, '2025', '2025-03-20 00:36:49'),
(1127, 'courses', 0, '03', '2025-03-20 00:36:49'),
(1128, 'events', 1, '03', '2025-03-20 00:36:49'),
(1129, 'students', 0, '2025', '2025-03-20 00:36:54'),
(1130, 'faculty', 7, '2025', '2025-03-20 00:36:54'),
(1131, 'courses', 0, '03', '2025-03-20 00:36:54'),
(1132, 'events', 1, '03', '2025-03-20 00:36:54'),
(1133, 'students', 0, '2025', '2025-03-20 00:37:00'),
(1134, 'faculty', 7, '2025', '2025-03-20 00:37:00'),
(1135, 'courses', 0, '03', '2025-03-20 00:37:00'),
(1136, 'events', 1, '03', '2025-03-20 00:37:00'),
(1137, 'students', 0, '2025', '2025-03-20 00:37:05'),
(1138, 'faculty', 7, '2025', '2025-03-20 00:37:05'),
(1139, 'courses', 0, '03', '2025-03-20 00:37:05'),
(1140, 'events', 1, '03', '2025-03-20 00:37:05'),
(1141, 'students', 0, '2025', '2025-03-20 00:37:10'),
(1142, 'faculty', 7, '2025', '2025-03-20 00:37:10'),
(1143, 'courses', 0, '03', '2025-03-20 00:37:10'),
(1144, 'events', 1, '03', '2025-03-20 00:37:10'),
(1145, 'students', 0, '2025', '2025-03-20 00:37:15'),
(1146, 'faculty', 7, '2025', '2025-03-20 00:37:15'),
(1147, 'courses', 0, '03', '2025-03-20 00:37:15'),
(1148, 'events', 1, '03', '2025-03-20 00:37:15'),
(1149, 'students', 0, '2025', '2025-03-20 00:37:20'),
(1150, 'faculty', 7, '2025', '2025-03-20 00:37:20'),
(1151, 'courses', 0, '03', '2025-03-20 00:37:20'),
(1152, 'events', 1, '03', '2025-03-20 00:37:20'),
(1153, 'students', 0, '2025', '2025-03-20 00:37:25'),
(1154, 'faculty', 7, '2025', '2025-03-20 00:37:25'),
(1155, 'courses', 0, '03', '2025-03-20 00:37:25'),
(1156, 'events', 1, '03', '2025-03-20 00:37:25'),
(1157, 'students', 0, '2025', '2025-03-20 00:37:30'),
(1158, 'faculty', 7, '2025', '2025-03-20 00:37:30'),
(1159, 'courses', 0, '03', '2025-03-20 00:37:30'),
(1160, 'events', 1, '03', '2025-03-20 00:37:30'),
(1161, 'students', 0, '2025', '2025-03-20 00:37:35'),
(1162, 'faculty', 7, '2025', '2025-03-20 00:37:35'),
(1163, 'courses', 0, '03', '2025-03-20 00:37:35'),
(1164, 'events', 1, '03', '2025-03-20 00:37:35'),
(1165, 'students', 0, '2025', '2025-03-20 00:37:40'),
(1166, 'faculty', 7, '2025', '2025-03-20 00:37:40'),
(1167, 'courses', 0, '03', '2025-03-20 00:37:40'),
(1168, 'events', 1, '03', '2025-03-20 00:37:40'),
(1169, 'students', 0, '2025', '2025-03-20 00:37:45'),
(1170, 'faculty', 7, '2025', '2025-03-20 00:37:45'),
(1171, 'courses', 0, '03', '2025-03-20 00:37:45'),
(1172, 'events', 1, '03', '2025-03-20 00:37:45'),
(1173, 'students', 0, '2025', '2025-03-20 00:37:50'),
(1174, 'faculty', 7, '2025', '2025-03-20 00:37:50'),
(1175, 'courses', 0, '03', '2025-03-20 00:37:50'),
(1176, 'events', 1, '03', '2025-03-20 00:37:50'),
(1177, 'students', 0, '2025', '2025-03-20 00:37:55'),
(1178, 'faculty', 7, '2025', '2025-03-20 00:37:55'),
(1179, 'courses', 0, '03', '2025-03-20 00:37:55'),
(1180, 'events', 1, '03', '2025-03-20 00:37:55'),
(1181, 'students', 0, '2025', '2025-03-20 00:38:47'),
(1182, 'faculty', 7, '2025', '2025-03-20 00:38:47'),
(1183, 'courses', 0, '03', '2025-03-20 00:38:47'),
(1184, 'events', 1, '03', '2025-03-20 00:38:47'),
(1185, 'students', 0, '2025', '2025-03-20 00:39:12'),
(1186, 'faculty', 7, '2025', '2025-03-20 00:39:12'),
(1187, 'courses', 0, '03', '2025-03-20 00:39:12'),
(1188, 'events', 1, '03', '2025-03-20 00:39:12'),
(1189, 'students', 0, '2025', '2025-03-20 00:39:14'),
(1190, 'faculty', 7, '2025', '2025-03-20 00:39:14'),
(1191, 'courses', 0, '03', '2025-03-20 00:39:14'),
(1192, 'events', 1, '03', '2025-03-20 00:39:14'),
(1193, 'students', 0, '2025', '2025-03-20 00:39:14'),
(1194, 'faculty', 7, '2025', '2025-03-20 00:39:14'),
(1195, 'courses', 0, '03', '2025-03-20 00:39:14'),
(1196, 'events', 1, '03', '2025-03-20 00:39:14'),
(1197, 'students', 0, '2025', '2025-03-20 00:39:14'),
(1198, 'faculty', 7, '2025', '2025-03-20 00:39:14'),
(1199, 'courses', 0, '03', '2025-03-20 00:39:14'),
(1200, 'events', 1, '03', '2025-03-20 00:39:14'),
(1201, 'students', 0, '2025', '2025-03-20 00:39:20'),
(1202, 'faculty', 7, '2025', '2025-03-20 00:39:20'),
(1203, 'courses', 0, '03', '2025-03-20 00:39:20'),
(1204, 'events', 1, '03', '2025-03-20 00:39:20'),
(1205, 'students', 0, '2025', '2025-03-20 00:39:25'),
(1206, 'faculty', 7, '2025', '2025-03-20 00:39:25'),
(1207, 'courses', 0, '03', '2025-03-20 00:39:25'),
(1208, 'events', 1, '03', '2025-03-20 00:39:25'),
(1209, 'students', 0, '2025', '2025-03-20 00:39:30'),
(1210, 'faculty', 7, '2025', '2025-03-20 00:39:30'),
(1211, 'courses', 0, '03', '2025-03-20 00:39:30'),
(1212, 'events', 1, '03', '2025-03-20 00:39:30'),
(1213, 'students', 0, '2025', '2025-03-20 00:39:35'),
(1214, 'faculty', 7, '2025', '2025-03-20 00:39:35'),
(1215, 'courses', 0, '03', '2025-03-20 00:39:35'),
(1216, 'events', 1, '03', '2025-03-20 00:39:35'),
(1217, 'students', 0, '2025', '2025-03-20 00:39:40'),
(1218, 'faculty', 7, '2025', '2025-03-20 00:39:40'),
(1219, 'courses', 0, '03', '2025-03-20 00:39:40'),
(1220, 'events', 1, '03', '2025-03-20 00:39:40'),
(1221, 'students', 0, '2025', '2025-03-20 00:39:45'),
(1222, 'faculty', 7, '2025', '2025-03-20 00:39:45'),
(1223, 'courses', 0, '03', '2025-03-20 00:39:45'),
(1224, 'events', 1, '03', '2025-03-20 00:39:45'),
(1225, 'students', 0, '2025', '2025-03-20 00:39:50'),
(1226, 'faculty', 7, '2025', '2025-03-20 00:39:50'),
(1227, 'courses', 0, '03', '2025-03-20 00:39:50'),
(1228, 'events', 1, '03', '2025-03-20 00:39:50'),
(1229, 'students', 0, '2025', '2025-03-20 00:39:55'),
(1230, 'faculty', 7, '2025', '2025-03-20 00:39:55'),
(1231, 'courses', 0, '03', '2025-03-20 00:39:55'),
(1232, 'events', 1, '03', '2025-03-20 00:39:55'),
(1233, 'students', 0, '2025', '2025-03-20 00:40:00'),
(1234, 'faculty', 7, '2025', '2025-03-20 00:40:00'),
(1235, 'courses', 0, '03', '2025-03-20 00:40:00'),
(1236, 'events', 1, '03', '2025-03-20 00:40:00'),
(1237, 'students', 0, '2025', '2025-03-20 00:40:05'),
(1238, 'faculty', 7, '2025', '2025-03-20 00:40:05'),
(1239, 'courses', 0, '03', '2025-03-20 00:40:05'),
(1240, 'events', 1, '03', '2025-03-20 00:40:05'),
(1241, 'students', 0, '2025', '2025-03-20 00:40:10'),
(1242, 'faculty', 7, '2025', '2025-03-20 00:40:10'),
(1243, 'courses', 0, '03', '2025-03-20 00:40:10'),
(1244, 'events', 1, '03', '2025-03-20 00:40:10'),
(1245, 'students', 0, '2025', '2025-03-20 00:40:15'),
(1246, 'faculty', 7, '2025', '2025-03-20 00:40:15'),
(1247, 'courses', 0, '03', '2025-03-20 00:40:15'),
(1248, 'events', 1, '03', '2025-03-20 00:40:15'),
(1249, 'students', 0, '2025', '2025-03-20 00:40:47'),
(1250, 'faculty', 7, '2025', '2025-03-20 00:40:47'),
(1251, 'courses', 0, '03', '2025-03-20 00:40:47'),
(1252, 'events', 1, '03', '2025-03-20 00:40:47'),
(1253, 'students', 0, '2025', '2025-03-20 00:41:47'),
(1254, 'faculty', 7, '2025', '2025-03-20 00:41:47'),
(1255, 'courses', 0, '03', '2025-03-20 00:41:47'),
(1256, 'events', 1, '03', '2025-03-20 00:41:47'),
(1257, 'students', 0, '2025', '2025-03-20 00:42:47'),
(1258, 'faculty', 7, '2025', '2025-03-20 00:42:47'),
(1259, 'courses', 0, '03', '2025-03-20 00:42:47'),
(1260, 'events', 1, '03', '2025-03-20 00:42:47'),
(1261, 'students', 0, '2025', '2025-03-20 00:43:47'),
(1262, 'faculty', 7, '2025', '2025-03-20 00:43:47'),
(1263, 'courses', 0, '03', '2025-03-20 00:43:47'),
(1264, 'events', 1, '03', '2025-03-20 00:43:47'),
(1265, 'students', 0, '2025', '2025-03-20 00:44:16'),
(1266, 'faculty', 7, '2025', '2025-03-20 00:44:16'),
(1267, 'courses', 0, '03', '2025-03-20 00:44:16'),
(1268, 'events', 1, '03', '2025-03-20 00:44:16'),
(1269, 'students', 0, '2025', '2025-03-20 00:44:20'),
(1270, 'faculty', 7, '2025', '2025-03-20 00:44:20'),
(1271, 'courses', 0, '03', '2025-03-20 00:44:20'),
(1272, 'events', 1, '03', '2025-03-20 00:44:20'),
(1273, 'students', 0, '2025', '2025-03-20 00:44:25'),
(1274, 'faculty', 7, '2025', '2025-03-20 00:44:25'),
(1275, 'courses', 0, '03', '2025-03-20 00:44:25'),
(1276, 'events', 1, '03', '2025-03-20 00:44:25'),
(1277, 'students', 0, '2025', '2025-03-20 00:44:30'),
(1278, 'faculty', 7, '2025', '2025-03-20 00:44:30'),
(1279, 'courses', 0, '03', '2025-03-20 00:44:30'),
(1280, 'events', 1, '03', '2025-03-20 00:44:30'),
(1281, 'students', 0, '2025', '2025-03-20 00:44:35'),
(1282, 'faculty', 7, '2025', '2025-03-20 00:44:35'),
(1283, 'courses', 0, '03', '2025-03-20 00:44:35'),
(1284, 'events', 1, '03', '2025-03-20 00:44:35'),
(1285, 'students', 0, '2025', '2025-03-20 00:44:40'),
(1286, 'faculty', 7, '2025', '2025-03-20 00:44:40'),
(1287, 'courses', 0, '03', '2025-03-20 00:44:40'),
(1288, 'events', 1, '03', '2025-03-20 00:44:40'),
(1289, 'students', 0, '2025', '2025-03-20 00:44:45'),
(1290, 'faculty', 7, '2025', '2025-03-20 00:44:45'),
(1291, 'courses', 0, '03', '2025-03-20 00:44:45'),
(1292, 'events', 1, '03', '2025-03-20 00:44:45'),
(1293, 'students', 0, '2025', '2025-03-20 00:44:50'),
(1294, 'faculty', 7, '2025', '2025-03-20 00:44:50'),
(1295, 'courses', 0, '03', '2025-03-20 00:44:50'),
(1296, 'events', 1, '03', '2025-03-20 00:44:50'),
(1297, 'students', 0, '2025', '2025-03-20 00:44:55'),
(1298, 'faculty', 7, '2025', '2025-03-20 00:44:55'),
(1299, 'courses', 0, '03', '2025-03-20 00:44:55'),
(1300, 'events', 1, '03', '2025-03-20 00:44:55'),
(1301, 'students', 0, '2025', '2025-03-20 00:45:00'),
(1302, 'faculty', 7, '2025', '2025-03-20 00:45:00'),
(1303, 'courses', 0, '03', '2025-03-20 00:45:00'),
(1304, 'events', 1, '03', '2025-03-20 00:45:00'),
(1305, 'students', 0, '2025', '2025-03-20 00:45:05'),
(1306, 'faculty', 7, '2025', '2025-03-20 00:45:05'),
(1307, 'courses', 0, '03', '2025-03-20 00:45:05'),
(1308, 'events', 2, '03', '2025-03-20 00:45:05'),
(1309, 'students', 0, '2025', '2025-03-20 00:45:09'),
(1310, 'faculty', 7, '2025', '2025-03-20 00:45:09'),
(1311, 'courses', 0, '03', '2025-03-20 00:45:09'),
(1312, 'events', 2, '03', '2025-03-20 00:45:09'),
(1313, 'students', 0, '2025', '2025-03-20 00:45:12'),
(1314, 'faculty', 7, '2025', '2025-03-20 00:45:12'),
(1315, 'courses', 0, '03', '2025-03-20 00:45:12'),
(1316, 'events', 2, '03', '2025-03-20 00:45:12'),
(1317, 'students', 0, '2025', '2025-03-20 00:45:12'),
(1318, 'faculty', 7, '2025', '2025-03-20 00:45:12'),
(1319, 'courses', 0, '03', '2025-03-20 00:45:12'),
(1320, 'events', 2, '03', '2025-03-20 00:45:12'),
(1321, 'students', 0, '2025', '2025-03-20 00:45:17'),
(1322, 'faculty', 7, '2025', '2025-03-20 00:45:17'),
(1323, 'courses', 0, '03', '2025-03-20 00:45:17'),
(1324, 'events', 2, '03', '2025-03-20 00:45:17'),
(1325, 'students', 0, '2025', '2025-03-20 00:45:22'),
(1326, 'faculty', 7, '2025', '2025-03-20 00:45:22'),
(1327, 'courses', 0, '03', '2025-03-20 00:45:22'),
(1328, 'events', 2, '03', '2025-03-20 00:45:22'),
(1329, 'students', 0, '2025', '2025-03-20 00:45:23'),
(1330, 'faculty', 7, '2025', '2025-03-20 00:45:23'),
(1331, 'courses', 0, '03', '2025-03-20 00:45:23'),
(1332, 'events', 2, '03', '2025-03-20 00:45:23'),
(1333, 'students', 0, '2025', '2025-03-20 00:45:28'),
(1334, 'faculty', 7, '2025', '2025-03-20 00:45:28'),
(1335, 'courses', 0, '03', '2025-03-20 00:45:28'),
(1336, 'events', 2, '03', '2025-03-20 00:45:28'),
(1337, 'students', 0, '2025', '2025-03-20 00:45:33'),
(1338, 'faculty', 7, '2025', '2025-03-20 00:45:33'),
(1339, 'courses', 0, '03', '2025-03-20 00:45:33'),
(1340, 'events', 2, '03', '2025-03-20 00:45:33'),
(1341, 'students', 0, '2025', '2025-03-20 00:45:38'),
(1342, 'faculty', 7, '2025', '2025-03-20 00:45:38'),
(1343, 'courses', 0, '03', '2025-03-20 00:45:38'),
(1344, 'events', 2, '03', '2025-03-20 00:45:38'),
(1345, 'students', 0, '2025', '2025-03-20 00:45:43'),
(1346, 'faculty', 7, '2025', '2025-03-20 00:45:43'),
(1347, 'courses', 0, '03', '2025-03-20 00:45:43'),
(1348, 'events', 2, '03', '2025-03-20 00:45:43'),
(1349, 'students', 0, '2025', '2025-03-20 00:45:48'),
(1350, 'faculty', 7, '2025', '2025-03-20 00:45:48'),
(1351, 'courses', 0, '03', '2025-03-20 00:45:48'),
(1352, 'events', 2, '03', '2025-03-20 00:45:48'),
(1353, 'students', 0, '2025', '2025-03-20 00:45:53'),
(1354, 'faculty', 7, '2025', '2025-03-20 00:45:53'),
(1355, 'courses', 0, '03', '2025-03-20 00:45:53'),
(1356, 'events', 2, '03', '2025-03-20 00:45:53'),
(1357, 'students', 0, '2025', '2025-03-20 00:45:58'),
(1358, 'faculty', 7, '2025', '2025-03-20 00:45:58'),
(1359, 'courses', 0, '03', '2025-03-20 00:45:58'),
(1360, 'events', 2, '03', '2025-03-20 00:45:58'),
(1361, 'students', 0, '2025', '2025-03-20 00:46:03'),
(1362, 'faculty', 7, '2025', '2025-03-20 00:46:03'),
(1363, 'courses', 0, '03', '2025-03-20 00:46:03'),
(1364, 'events', 2, '03', '2025-03-20 00:46:03'),
(1365, 'students', 0, '2025', '2025-03-20 00:46:08'),
(1366, 'faculty', 7, '2025', '2025-03-20 00:46:08'),
(1367, 'courses', 0, '03', '2025-03-20 00:46:08'),
(1368, 'events', 2, '03', '2025-03-20 00:46:08'),
(1369, 'students', 0, '2025', '2025-03-20 00:46:13'),
(1370, 'faculty', 7, '2025', '2025-03-20 00:46:13'),
(1371, 'courses', 0, '03', '2025-03-20 00:46:13'),
(1372, 'events', 2, '03', '2025-03-20 00:46:13'),
(1373, 'students', 0, '2025', '2025-03-20 00:46:18'),
(1374, 'faculty', 7, '2025', '2025-03-20 00:46:18'),
(1375, 'courses', 0, '03', '2025-03-20 00:46:18'),
(1376, 'events', 2, '03', '2025-03-20 00:46:18'),
(1377, 'students', 0, '2025', '2025-03-20 00:46:23'),
(1378, 'faculty', 7, '2025', '2025-03-20 00:46:23'),
(1379, 'courses', 0, '03', '2025-03-20 00:46:23'),
(1380, 'events', 2, '03', '2025-03-20 00:46:23'),
(1381, 'students', 0, '2025', '2025-03-20 00:46:28'),
(1382, 'faculty', 7, '2025', '2025-03-20 00:46:28'),
(1383, 'courses', 0, '03', '2025-03-20 00:46:28'),
(1384, 'events', 2, '03', '2025-03-20 00:46:28'),
(1385, 'students', 0, '2025', '2025-03-20 00:46:28'),
(1386, 'faculty', 7, '2025', '2025-03-20 00:46:28'),
(1387, 'courses', 0, '03', '2025-03-20 00:46:28'),
(1388, 'events', 2, '03', '2025-03-20 00:46:28'),
(1389, 'students', 0, '2025', '2025-03-20 00:46:28'),
(1390, 'faculty', 7, '2025', '2025-03-20 00:46:28'),
(1391, 'courses', 0, '03', '2025-03-20 00:46:28'),
(1392, 'events', 2, '03', '2025-03-20 00:46:28'),
(1393, 'students', 0, '2025', '2025-03-20 00:46:33'),
(1394, 'faculty', 7, '2025', '2025-03-20 00:46:33'),
(1395, 'courses', 0, '03', '2025-03-20 00:46:33'),
(1396, 'events', 2, '03', '2025-03-20 00:46:33'),
(1397, 'students', 0, '2025', '2025-03-20 00:46:39'),
(1398, 'faculty', 7, '2025', '2025-03-20 00:46:39'),
(1399, 'courses', 0, '03', '2025-03-20 00:46:39'),
(1400, 'events', 2, '03', '2025-03-20 00:46:39'),
(1401, 'students', 0, '2025', '2025-03-20 00:46:43'),
(1402, 'faculty', 7, '2025', '2025-03-20 00:46:43'),
(1403, 'courses', 0, '03', '2025-03-20 00:46:43'),
(1404, 'events', 2, '03', '2025-03-20 00:46:43'),
(1405, 'students', 0, '2025', '2025-03-20 00:46:45'),
(1406, 'faculty', 7, '2025', '2025-03-20 00:46:45'),
(1407, 'courses', 0, '03', '2025-03-20 00:46:45'),
(1408, 'events', 2, '03', '2025-03-20 00:46:45'),
(1409, 'students', 0, '2025', '2025-03-20 00:47:44'),
(1410, 'faculty', 7, '2025', '2025-03-20 00:47:44'),
(1411, 'courses', 0, '03', '2025-03-20 00:47:44'),
(1412, 'events', 2, '03', '2025-03-20 00:47:44'),
(1413, 'students', 0, '2025', '2025-03-20 00:56:32'),
(1414, 'faculty', 7, '2025', '2025-03-20 00:56:32'),
(1415, 'courses', 0, '03', '2025-03-20 00:56:32'),
(1416, 'events', 2, '03', '2025-03-20 00:56:32'),
(1417, 'students', 0, '2025', '2025-03-20 00:58:45'),
(1418, 'faculty', 7, '2025', '2025-03-20 00:58:45'),
(1419, 'courses', 0, '03', '2025-03-20 00:58:45'),
(1420, 'events', 2, '03', '2025-03-20 00:58:45'),
(1421, 'students', 0, '2025', '2025-03-20 01:11:24'),
(1422, 'faculty', 7, '2025', '2025-03-20 01:11:24'),
(1423, 'courses', 0, '03', '2025-03-20 01:11:24'),
(1424, 'events', 2, '03', '2025-03-20 01:11:24'),
(1425, 'students', 0, '2025', '2025-03-20 01:12:34'),
(1426, 'faculty', 7, '2025', '2025-03-20 01:12:34'),
(1427, 'courses', 0, '03', '2025-03-20 01:12:34'),
(1428, 'events', 2, '03', '2025-03-20 01:12:34'),
(1429, 'students', 0, '2025', '2025-03-20 01:12:57'),
(1430, 'faculty', 7, '2025', '2025-03-20 01:12:57'),
(1431, 'courses', 0, '03', '2025-03-20 01:12:57'),
(1432, 'events', 2, '03', '2025-03-20 01:12:57'),
(1433, 'students', 0, '2025', '2025-03-20 01:13:33'),
(1434, 'faculty', 7, '2025', '2025-03-20 01:13:33'),
(1435, 'courses', 0, '03', '2025-03-20 01:13:33'),
(1436, 'events', 2, '03', '2025-03-20 01:13:33'),
(1437, 'students', 0, '2025', '2025-03-20 01:15:01'),
(1438, 'faculty', 7, '2025', '2025-03-20 01:15:01'),
(1439, 'courses', 0, '03', '2025-03-20 01:15:01'),
(1440, 'events', 2, '03', '2025-03-20 01:15:01'),
(1441, 'students', 0, '2025', '2025-03-20 01:17:53'),
(1442, 'faculty', 7, '2025', '2025-03-20 01:17:53'),
(1443, 'courses', 0, '03', '2025-03-20 01:17:53'),
(1444, 'events', 2, '03', '2025-03-20 01:17:53'),
(1445, 'students', 0, '2025', '2025-03-20 01:21:52'),
(1446, 'faculty', 7, '2025', '2025-03-20 01:21:52'),
(1447, 'courses', 0, '03', '2025-03-20 01:21:52'),
(1448, 'events', 2, '03', '2025-03-20 01:21:52'),
(1449, 'students', 0, '2025', '2025-03-20 01:25:23'),
(1450, 'faculty', 7, '2025', '2025-03-20 01:25:23'),
(1451, 'courses', 0, '03', '2025-03-20 01:25:23'),
(1452, 'events', 2, '03', '2025-03-20 01:25:23'),
(1453, 'students', 0, '2025', '2025-03-20 01:27:51'),
(1454, 'faculty', 7, '2025', '2025-03-20 01:27:51'),
(1455, 'courses', 0, '03', '2025-03-20 01:27:51'),
(1456, 'events', 2, '03', '2025-03-20 01:27:51'),
(1457, 'students', 0, '2025', '2025-03-20 01:28:18'),
(1458, 'faculty', 7, '2025', '2025-03-20 01:28:18'),
(1459, 'courses', 0, '03', '2025-03-20 01:28:18'),
(1460, 'events', 2, '03', '2025-03-20 01:28:18'),
(1461, 'students', 0, '2025', '2025-03-20 01:28:41'),
(1462, 'faculty', 7, '2025', '2025-03-20 01:28:41'),
(1463, 'courses', 0, '03', '2025-03-20 01:28:41'),
(1464, 'events', 2, '03', '2025-03-20 01:28:41'),
(1465, 'students', 0, '2025', '2025-03-20 01:30:01'),
(1466, 'faculty', 7, '2025', '2025-03-20 01:30:01'),
(1467, 'courses', 0, '03', '2025-03-20 01:30:01'),
(1468, 'events', 2, '03', '2025-03-20 01:30:01'),
(1469, 'students', 0, '2025', '2025-03-20 01:31:51'),
(1470, 'faculty', 7, '2025', '2025-03-20 01:31:51'),
(1471, 'courses', 0, '03', '2025-03-20 01:31:51'),
(1472, 'events', 2, '03', '2025-03-20 01:31:51'),
(1473, 'students', 0, '2025', '2025-03-20 01:39:44'),
(1474, 'faculty', 7, '2025', '2025-03-20 01:39:44'),
(1475, 'courses', 0, '03', '2025-03-20 01:39:44'),
(1476, 'events', 2, '03', '2025-03-20 01:39:44'),
(1477, 'students', 0, '2025', '2025-03-20 01:42:05'),
(1478, 'faculty', 7, '2025', '2025-03-20 01:42:05'),
(1479, 'courses', 0, '03', '2025-03-20 01:42:05'),
(1480, 'events', 2, '03', '2025-03-20 01:42:05'),
(1481, 'students', 0, '2025', '2025-03-20 01:42:06'),
(1482, 'faculty', 7, '2025', '2025-03-20 01:42:06'),
(1483, 'courses', 0, '03', '2025-03-20 01:42:06'),
(1484, 'events', 2, '03', '2025-03-20 01:42:06'),
(1485, 'students', 0, '2025', '2025-03-20 01:52:35'),
(1486, 'faculty', 7, '2025', '2025-03-20 01:52:35'),
(1487, 'courses', 0, '03', '2025-03-20 01:52:35'),
(1488, 'events', 2, '03', '2025-03-20 01:52:35'),
(1489, 'students', 0, '2025', '2025-03-20 01:52:43'),
(1490, 'faculty', 7, '2025', '2025-03-20 01:52:43'),
(1491, 'courses', 0, '03', '2025-03-20 01:52:43'),
(1492, 'events', 2, '03', '2025-03-20 01:52:43'),
(1493, 'students', 0, '2025', '2025-03-20 01:54:40'),
(1494, 'faculty', 7, '2025', '2025-03-20 01:54:40'),
(1495, 'courses', 0, '03', '2025-03-20 01:54:40'),
(1496, 'events', 2, '03', '2025-03-20 01:54:40'),
(1497, 'students', 0, '2025', '2025-03-20 14:34:34'),
(1498, 'faculty', 7, '2025', '2025-03-20 14:34:34'),
(1499, 'courses', 0, '03', '2025-03-20 14:34:34'),
(1500, 'events', 2, '03', '2025-03-20 14:34:34'),
(1501, 'students', 0, '2025', '2025-03-20 14:37:27'),
(1502, 'faculty', 7, '2025', '2025-03-20 14:37:27'),
(1503, 'courses', 0, '03', '2025-03-20 14:37:27'),
(1504, 'events', 2, '03', '2025-03-20 14:37:27'),
(1505, 'students', 0, '2025', '2025-03-20 14:37:47'),
(1506, 'faculty', 7, '2025', '2025-03-20 14:37:47'),
(1507, 'courses', 0, '03', '2025-03-20 14:37:47'),
(1508, 'events', 2, '03', '2025-03-20 14:37:47'),
(1509, 'students', 0, '2025', '2025-03-20 14:50:31'),
(1510, 'faculty', 7, '2025', '2025-03-20 14:50:31'),
(1511, 'courses', 0, '03', '2025-03-20 14:50:31'),
(1512, 'events', 2, '03', '2025-03-20 14:50:31'),
(1513, 'students', 0, '2025', '2025-03-20 15:18:06'),
(1514, 'faculty', 7, '2025', '2025-03-20 15:18:06'),
(1515, 'courses', 0, '03', '2025-03-20 15:18:06'),
(1516, 'events', 2, '03', '2025-03-20 15:18:06'),
(1517, 'students', 0, '2025', '2025-03-20 16:08:47'),
(1518, 'faculty', 7, '2025', '2025-03-20 16:08:47'),
(1519, 'courses', 0, '03', '2025-03-20 16:08:47'),
(1520, 'events', 2, '03', '2025-03-20 16:08:47'),
(1521, 'students', 0, '2025', '2025-03-20 16:16:01'),
(1522, 'faculty', 7, '2025', '2025-03-20 16:16:01'),
(1523, 'courses', 0, '03', '2025-03-20 16:16:01'),
(1524, 'events', 2, '03', '2025-03-20 16:16:01'),
(1525, 'students', 0, '2025', '2025-03-20 16:16:05'),
(1526, 'faculty', 7, '2025', '2025-03-20 16:16:05'),
(1527, 'courses', 0, '03', '2025-03-20 16:16:05'),
(1528, 'events', 2, '03', '2025-03-20 16:16:05'),
(1529, 'students', 0, '2025', '2025-03-20 16:16:09'),
(1530, 'faculty', 7, '2025', '2025-03-20 16:16:09'),
(1531, 'courses', 0, '03', '2025-03-20 16:16:09'),
(1532, 'events', 2, '03', '2025-03-20 16:16:09'),
(1533, 'students', 0, '2025', '2025-03-20 16:33:13'),
(1534, 'faculty', 7, '2025', '2025-03-20 16:33:13'),
(1535, 'courses', 0, '03', '2025-03-20 16:33:13'),
(1536, 'events', 2, '03', '2025-03-20 16:33:13'),
(1537, 'students', 0, '2025', '2025-03-20 16:40:03'),
(1538, 'faculty', 7, '2025', '2025-03-20 16:40:03'),
(1539, 'courses', 0, '03', '2025-03-20 16:40:03'),
(1540, 'events', 2, '03', '2025-03-20 16:40:03'),
(1541, 'students', 0, '2025', '2025-03-20 16:41:02'),
(1542, 'faculty', 7, '2025', '2025-03-20 16:41:02'),
(1543, 'courses', 0, '03', '2025-03-20 16:41:02'),
(1544, 'events', 2, '03', '2025-03-20 16:41:02'),
(1545, 'students', 0, '2025', '2025-03-20 16:42:13'),
(1546, 'faculty', 7, '2025', '2025-03-20 16:42:13'),
(1547, 'courses', 0, '03', '2025-03-20 16:42:13'),
(1548, 'events', 2, '03', '2025-03-20 16:42:13'),
(1549, 'students', 0, '2025', '2025-03-20 16:53:01'),
(1550, 'faculty', 7, '2025', '2025-03-20 16:53:01'),
(1551, 'courses', 0, '03', '2025-03-20 16:53:01'),
(1552, 'events', 2, '03', '2025-03-20 16:53:01'),
(1553, 'students', 0, '2025', '2025-03-20 16:53:15'),
(1554, 'faculty', 7, '2025', '2025-03-20 16:53:15'),
(1555, 'courses', 0, '03', '2025-03-20 16:53:15'),
(1556, 'events', 2, '03', '2025-03-20 16:53:15'),
(1557, 'students', 0, '2025', '2025-03-20 17:00:20'),
(1558, 'faculty', 7, '2025', '2025-03-20 17:00:20'),
(1559, 'courses', 0, '03', '2025-03-20 17:00:20'),
(1560, 'events', 2, '03', '2025-03-20 17:00:20'),
(1561, 'students', 0, '2025', '2025-03-20 17:00:31'),
(1562, 'faculty', 7, '2025', '2025-03-20 17:00:31'),
(1563, 'courses', 0, '03', '2025-03-20 17:00:31'),
(1564, 'events', 2, '03', '2025-03-20 17:00:31'),
(1565, 'students', 0, '2025', '2025-03-20 17:00:51'),
(1566, 'faculty', 7, '2025', '2025-03-20 17:00:51'),
(1567, 'courses', 0, '03', '2025-03-20 17:00:51'),
(1568, 'events', 2, '03', '2025-03-20 17:00:51'),
(1569, 'students', 0, '2025', '2025-03-20 17:05:26'),
(1570, 'faculty', 7, '2025', '2025-03-20 17:05:26'),
(1571, 'courses', 0, '03', '2025-03-20 17:05:26'),
(1572, 'events', 2, '03', '2025-03-20 17:05:26'),
(1573, 'students', 0, '2025', '2025-03-20 17:10:31'),
(1574, 'faculty', 7, '2025', '2025-03-20 17:10:31'),
(1575, 'courses', 0, '03', '2025-03-20 17:10:31'),
(1576, 'events', 2, '03', '2025-03-20 17:10:31'),
(1577, 'students', 0, '2025', '2025-03-20 17:14:32'),
(1578, 'faculty', 7, '2025', '2025-03-20 17:14:32'),
(1579, 'courses', 0, '03', '2025-03-20 17:14:32'),
(1580, 'events', 2, '03', '2025-03-20 17:14:32'),
(1581, 'students', 0, '2025', '2025-03-20 17:14:33'),
(1582, 'faculty', 7, '2025', '2025-03-20 17:14:33'),
(1583, 'courses', 0, '03', '2025-03-20 17:14:33'),
(1584, 'events', 2, '03', '2025-03-20 17:14:33'),
(1585, 'students', 0, '2025', '2025-03-20 17:15:56'),
(1586, 'faculty', 7, '2025', '2025-03-20 17:15:56'),
(1587, 'courses', 0, '03', '2025-03-20 17:15:56'),
(1588, 'events', 2, '03', '2025-03-20 17:15:56'),
(1589, 'students', 0, '2025', '2025-03-20 17:16:40'),
(1590, 'faculty', 7, '2025', '2025-03-20 17:16:40'),
(1591, 'courses', 0, '03', '2025-03-20 17:16:40'),
(1592, 'events', 2, '03', '2025-03-20 17:16:40'),
(1593, 'students', 0, '2025', '2025-03-20 17:17:21'),
(1594, 'faculty', 7, '2025', '2025-03-20 17:17:21'),
(1595, 'courses', 0, '03', '2025-03-20 17:17:21'),
(1596, 'events', 2, '03', '2025-03-20 17:17:21'),
(1597, 'students', 0, '2025', '2025-03-20 17:17:23'),
(1598, 'faculty', 7, '2025', '2025-03-20 17:17:23'),
(1599, 'courses', 0, '03', '2025-03-20 17:17:23'),
(1600, 'events', 2, '03', '2025-03-20 17:17:23'),
(1601, 'students', 0, '2025', '2025-03-20 17:17:38'),
(1602, 'faculty', 7, '2025', '2025-03-20 17:17:38'),
(1603, 'courses', 0, '03', '2025-03-20 17:17:38'),
(1604, 'events', 2, '03', '2025-03-20 17:17:38'),
(1605, 'students', 0, '2025', '2025-03-20 17:18:15'),
(1606, 'faculty', 7, '2025', '2025-03-20 17:18:15'),
(1607, 'courses', 0, '03', '2025-03-20 17:18:15'),
(1608, 'events', 2, '03', '2025-03-20 17:18:15'),
(1609, 'students', 0, '2025', '2025-03-20 17:18:26'),
(1610, 'faculty', 7, '2025', '2025-03-20 17:18:26'),
(1611, 'courses', 0, '03', '2025-03-20 17:18:26'),
(1612, 'events', 2, '03', '2025-03-20 17:18:26'),
(1613, 'students', 0, '2025', '2025-03-20 17:18:27'),
(1614, 'faculty', 7, '2025', '2025-03-20 17:18:27'),
(1615, 'courses', 0, '03', '2025-03-20 17:18:27'),
(1616, 'events', 2, '03', '2025-03-20 17:18:27'),
(1617, 'students', 0, '2025', '2025-03-20 17:18:35'),
(1618, 'faculty', 7, '2025', '2025-03-20 17:18:35'),
(1619, 'courses', 0, '03', '2025-03-20 17:18:35'),
(1620, 'events', 2, '03', '2025-03-20 17:18:35'),
(1621, 'students', 0, '2025', '2025-03-20 17:19:33'),
(1622, 'faculty', 7, '2025', '2025-03-20 17:19:33'),
(1623, 'courses', 0, '03', '2025-03-20 17:19:33'),
(1624, 'events', 2, '03', '2025-03-20 17:19:33'),
(1625, 'students', 0, '2025', '2025-03-20 17:22:27'),
(1626, 'faculty', 7, '2025', '2025-03-20 17:22:27'),
(1627, 'courses', 0, '03', '2025-03-20 17:22:27'),
(1628, 'events', 2, '03', '2025-03-20 17:22:27'),
(1629, 'students', 0, '2025', '2025-03-20 17:22:28'),
(1630, 'faculty', 7, '2025', '2025-03-20 17:22:28'),
(1631, 'courses', 0, '03', '2025-03-20 17:22:28'),
(1632, 'events', 2, '03', '2025-03-20 17:22:28'),
(1633, 'students', 0, '2025', '2025-03-20 17:22:29'),
(1634, 'faculty', 7, '2025', '2025-03-20 17:22:29'),
(1635, 'courses', 0, '03', '2025-03-20 17:22:29'),
(1636, 'events', 2, '03', '2025-03-20 17:22:29'),
(1637, 'students', 0, '2025', '2025-03-20 17:22:41'),
(1638, 'faculty', 7, '2025', '2025-03-20 17:22:41'),
(1639, 'courses', 0, '03', '2025-03-20 17:22:41'),
(1640, 'events', 2, '03', '2025-03-20 17:22:41'),
(1641, 'students', 0, '2025', '2025-03-20 17:22:42'),
(1642, 'faculty', 7, '2025', '2025-03-20 17:22:42'),
(1643, 'courses', 0, '03', '2025-03-20 17:22:42'),
(1644, 'events', 2, '03', '2025-03-20 17:22:42'),
(1645, 'students', 0, '2025', '2025-03-20 17:22:52'),
(1646, 'faculty', 7, '2025', '2025-03-20 17:22:52'),
(1647, 'courses', 0, '03', '2025-03-20 17:22:52'),
(1648, 'events', 2, '03', '2025-03-20 17:22:52'),
(1649, 'students', 0, '2025', '2025-03-20 17:22:53'),
(1650, 'faculty', 7, '2025', '2025-03-20 17:22:53'),
(1651, 'courses', 0, '03', '2025-03-20 17:22:53'),
(1652, 'events', 2, '03', '2025-03-20 17:22:53'),
(1653, 'students', 0, '2025', '2025-03-20 17:23:07'),
(1654, 'faculty', 7, '2025', '2025-03-20 17:23:07'),
(1655, 'courses', 0, '03', '2025-03-20 17:23:07'),
(1656, 'events', 2, '03', '2025-03-20 17:23:07'),
(1657, 'students', 0, '2025', '2025-03-20 17:23:20'),
(1658, 'faculty', 7, '2025', '2025-03-20 17:23:20'),
(1659, 'courses', 0, '03', '2025-03-20 17:23:20'),
(1660, 'events', 2, '03', '2025-03-20 17:23:20'),
(1661, 'students', 0, '2025', '2025-03-20 17:23:30'),
(1662, 'faculty', 7, '2025', '2025-03-20 17:23:30'),
(1663, 'courses', 0, '03', '2025-03-20 17:23:30'),
(1664, 'events', 2, '03', '2025-03-20 17:23:30'),
(1665, 'students', 0, '2025', '2025-03-20 17:24:10'),
(1666, 'faculty', 7, '2025', '2025-03-20 17:24:10'),
(1667, 'courses', 0, '03', '2025-03-20 17:24:10'),
(1668, 'events', 2, '03', '2025-03-20 17:24:10'),
(1669, 'students', 0, '2025', '2025-03-20 17:24:11'),
(1670, 'faculty', 7, '2025', '2025-03-20 17:24:11'),
(1671, 'courses', 0, '03', '2025-03-20 17:24:11'),
(1672, 'events', 2, '03', '2025-03-20 17:24:11'),
(1673, 'students', 0, '2025', '2025-03-20 17:26:03'),
(1674, 'faculty', 7, '2025', '2025-03-20 17:26:03'),
(1675, 'courses', 0, '03', '2025-03-20 17:26:03'),
(1676, 'events', 2, '03', '2025-03-20 17:26:03'),
(1677, 'students', 0, '2025', '2025-03-20 17:27:16'),
(1678, 'faculty', 7, '2025', '2025-03-20 17:27:16'),
(1679, 'courses', 0, '03', '2025-03-20 17:27:16'),
(1680, 'events', 2, '03', '2025-03-20 17:27:16'),
(1681, 'students', 0, '2025', '2025-03-20 17:27:17'),
(1682, 'faculty', 7, '2025', '2025-03-20 17:27:17'),
(1683, 'courses', 0, '03', '2025-03-20 17:27:17'),
(1684, 'events', 2, '03', '2025-03-20 17:27:17'),
(1685, 'students', 0, '2025', '2025-03-20 17:30:18'),
(1686, 'faculty', 7, '2025', '2025-03-20 17:30:18'),
(1687, 'courses', 0, '03', '2025-03-20 17:30:18'),
(1688, 'events', 2, '03', '2025-03-20 17:30:18'),
(1689, 'students', 0, '2025', '2025-03-20 17:30:23'),
(1690, 'faculty', 7, '2025', '2025-03-20 17:30:23'),
(1691, 'courses', 0, '03', '2025-03-20 17:30:23'),
(1692, 'events', 2, '03', '2025-03-20 17:30:23'),
(1693, 'students', 0, '2025', '2025-03-20 17:30:25'),
(1694, 'faculty', 7, '2025', '2025-03-20 17:30:25'),
(1695, 'courses', 0, '03', '2025-03-20 17:30:25'),
(1696, 'events', 2, '03', '2025-03-20 17:30:25'),
(1697, 'students', 0, '2025', '2025-03-20 17:32:15'),
(1698, 'faculty', 7, '2025', '2025-03-20 17:32:15'),
(1699, 'courses', 0, '03', '2025-03-20 17:32:15'),
(1700, 'events', 2, '03', '2025-03-20 17:32:15'),
(1701, 'students', 0, '2025', '2025-03-20 17:32:16'),
(1702, 'faculty', 7, '2025', '2025-03-20 17:32:16'),
(1703, 'courses', 0, '03', '2025-03-20 17:32:16'),
(1704, 'events', 2, '03', '2025-03-20 17:32:16'),
(1705, 'students', 0, '2025', '2025-03-20 17:32:23'),
(1706, 'faculty', 7, '2025', '2025-03-20 17:32:23'),
(1707, 'courses', 0, '03', '2025-03-20 17:32:23'),
(1708, 'events', 2, '03', '2025-03-20 17:32:23'),
(1709, 'students', 0, '2025', '2025-03-20 17:33:09'),
(1710, 'faculty', 7, '2025', '2025-03-20 17:33:09'),
(1711, 'courses', 0, '03', '2025-03-20 17:33:09'),
(1712, 'events', 2, '03', '2025-03-20 17:33:09'),
(1713, 'students', 0, '2025', '2025-03-20 17:33:55'),
(1714, 'faculty', 7, '2025', '2025-03-20 17:33:55'),
(1715, 'courses', 0, '03', '2025-03-20 17:33:55'),
(1716, 'events', 2, '03', '2025-03-20 17:33:55'),
(1717, 'students', 0, '2025', '2025-03-20 17:34:02'),
(1718, 'faculty', 7, '2025', '2025-03-20 17:34:02'),
(1719, 'courses', 0, '03', '2025-03-20 17:34:02'),
(1720, 'events', 2, '03', '2025-03-20 17:34:02'),
(1721, 'students', 0, '2025', '2025-03-20 17:34:41'),
(1722, 'faculty', 7, '2025', '2025-03-20 17:34:41'),
(1723, 'courses', 0, '03', '2025-03-20 17:34:41'),
(1724, 'events', 2, '03', '2025-03-20 17:34:41'),
(1725, 'students', 0, '2025', '2025-03-20 17:34:42'),
(1726, 'faculty', 7, '2025', '2025-03-20 17:34:42'),
(1727, 'courses', 0, '03', '2025-03-20 17:34:42'),
(1728, 'events', 2, '03', '2025-03-20 17:34:42'),
(1729, 'students', 0, '2025', '2025-03-20 17:34:55'),
(1730, 'faculty', 7, '2025', '2025-03-20 17:34:55'),
(1731, 'courses', 0, '03', '2025-03-20 17:34:55'),
(1732, 'events', 2, '03', '2025-03-20 17:34:55'),
(1733, 'students', 0, '2025', '2025-03-20 17:35:24'),
(1734, 'faculty', 7, '2025', '2025-03-20 17:35:24'),
(1735, 'courses', 0, '03', '2025-03-20 17:35:24'),
(1736, 'events', 2, '03', '2025-03-20 17:35:24'),
(1737, 'students', 0, '2025', '2025-03-20 17:35:44'),
(1738, 'faculty', 7, '2025', '2025-03-20 17:35:44'),
(1739, 'courses', 0, '03', '2025-03-20 17:35:44'),
(1740, 'events', 2, '03', '2025-03-20 17:35:44'),
(1741, 'students', 0, '2025', '2025-03-20 17:35:54'),
(1742, 'faculty', 7, '2025', '2025-03-20 17:35:54'),
(1743, 'courses', 0, '03', '2025-03-20 17:35:54'),
(1744, 'events', 2, '03', '2025-03-20 17:35:54'),
(1745, 'students', 0, '2025', '2025-03-20 17:35:54'),
(1746, 'faculty', 7, '2025', '2025-03-20 17:35:54'),
(1747, 'courses', 0, '03', '2025-03-20 17:35:54'),
(1748, 'events', 2, '03', '2025-03-20 17:35:54'),
(1749, 'students', 0, '2025', '2025-03-20 17:35:56'),
(1750, 'faculty', 7, '2025', '2025-03-20 17:35:56'),
(1751, 'courses', 0, '03', '2025-03-20 17:35:56'),
(1752, 'events', 2, '03', '2025-03-20 17:35:56'),
(1753, 'students', 0, '2025', '2025-03-20 17:36:16'),
(1754, 'faculty', 7, '2025', '2025-03-20 17:36:16'),
(1755, 'courses', 0, '03', '2025-03-20 17:36:16'),
(1756, 'events', 2, '03', '2025-03-20 17:36:16'),
(1757, 'students', 0, '2025', '2025-03-20 17:36:35'),
(1758, 'faculty', 7, '2025', '2025-03-20 17:36:35'),
(1759, 'courses', 0, '03', '2025-03-20 17:36:35'),
(1760, 'events', 2, '03', '2025-03-20 17:36:35'),
(1761, 'students', 0, '2025', '2025-03-20 17:36:36'),
(1762, 'faculty', 7, '2025', '2025-03-20 17:36:36'),
(1763, 'courses', 0, '03', '2025-03-20 17:36:36'),
(1764, 'events', 2, '03', '2025-03-20 17:36:36'),
(1765, 'students', 0, '2025', '2025-03-20 17:36:44'),
(1766, 'faculty', 7, '2025', '2025-03-20 17:36:44'),
(1767, 'courses', 0, '03', '2025-03-20 17:36:44'),
(1768, 'events', 2, '03', '2025-03-20 17:36:44'),
(1769, 'students', 0, '2025', '2025-03-20 17:36:45'),
(1770, 'faculty', 7, '2025', '2025-03-20 17:36:45'),
(1771, 'courses', 0, '03', '2025-03-20 17:36:45'),
(1772, 'events', 2, '03', '2025-03-20 17:36:45'),
(1773, 'students', 0, '2025', '2025-03-20 17:36:56'),
(1774, 'faculty', 7, '2025', '2025-03-20 17:36:56'),
(1775, 'courses', 0, '03', '2025-03-20 17:36:56'),
(1776, 'events', 2, '03', '2025-03-20 17:36:56'),
(1777, 'students', 0, '2025', '2025-03-20 17:36:57'),
(1778, 'faculty', 7, '2025', '2025-03-20 17:36:57'),
(1779, 'courses', 0, '03', '2025-03-20 17:36:57'),
(1780, 'events', 2, '03', '2025-03-20 17:36:57'),
(1781, 'students', 0, '2025', '2025-03-20 17:36:58'),
(1782, 'faculty', 7, '2025', '2025-03-20 17:36:58'),
(1783, 'courses', 0, '03', '2025-03-20 17:36:58'),
(1784, 'events', 2, '03', '2025-03-20 17:36:58'),
(1785, 'students', 0, '2025', '2025-03-20 17:37:18'),
(1786, 'faculty', 7, '2025', '2025-03-20 17:37:18'),
(1787, 'courses', 0, '03', '2025-03-20 17:37:18'),
(1788, 'events', 2, '03', '2025-03-20 17:37:18'),
(1789, 'students', 0, '2025', '2025-03-20 17:41:05'),
(1790, 'faculty', 7, '2025', '2025-03-20 17:41:05'),
(1791, 'courses', 0, '03', '2025-03-20 17:41:05'),
(1792, 'events', 2, '03', '2025-03-20 17:41:05'),
(1793, 'students', 0, '2025', '2025-03-20 17:43:16'),
(1794, 'faculty', 7, '2025', '2025-03-20 17:43:16'),
(1795, 'courses', 0, '03', '2025-03-20 17:43:16'),
(1796, 'events', 2, '03', '2025-03-20 17:43:16'),
(1797, 'students', 0, '2025', '2025-03-20 17:43:39'),
(1798, 'faculty', 7, '2025', '2025-03-20 17:43:39'),
(1799, 'courses', 0, '03', '2025-03-20 17:43:39'),
(1800, 'events', 2, '03', '2025-03-20 17:43:39'),
(1801, 'students', 0, '2025', '2025-03-20 17:43:50'),
(1802, 'faculty', 7, '2025', '2025-03-20 17:43:50'),
(1803, 'courses', 0, '03', '2025-03-20 17:43:50'),
(1804, 'events', 2, '03', '2025-03-20 17:43:50'),
(1805, 'students', 0, '2025', '2025-03-20 17:44:00'),
(1806, 'faculty', 7, '2025', '2025-03-20 17:44:00'),
(1807, 'courses', 0, '03', '2025-03-20 17:44:00'),
(1808, 'events', 2, '03', '2025-03-20 17:44:00'),
(1809, 'students', 0, '2025', '2025-03-20 17:44:03'),
(1810, 'faculty', 7, '2025', '2025-03-20 17:44:03'),
(1811, 'courses', 0, '03', '2025-03-20 17:44:03'),
(1812, 'events', 2, '03', '2025-03-20 17:44:03'),
(1813, 'students', 0, '2025', '2025-03-20 17:45:19'),
(1814, 'faculty', 7, '2025', '2025-03-20 17:45:19'),
(1815, 'courses', 0, '03', '2025-03-20 17:45:19'),
(1816, 'events', 2, '03', '2025-03-20 17:45:19'),
(1817, 'students', 0, '2025', '2025-03-20 21:39:39'),
(1818, 'faculty', 7, '2025', '2025-03-20 21:39:39'),
(1819, 'courses', 0, '03', '2025-03-20 21:39:39'),
(1820, 'events', 2, '03', '2025-03-20 21:39:39'),
(1821, 'students', 0, '2025', '2025-03-20 21:42:07'),
(1822, 'faculty', 7, '2025', '2025-03-20 21:42:07'),
(1823, 'courses', 0, '03', '2025-03-20 21:42:07'),
(1824, 'events', 2, '03', '2025-03-20 21:42:07'),
(1825, 'students', 0, '2025', '2025-03-20 23:12:49'),
(1826, 'faculty', 7, '2025', '2025-03-20 23:12:49'),
(1827, 'courses', 0, '03', '2025-03-20 23:12:49'),
(1828, 'events', 2, '03', '2025-03-20 23:12:49'),
(1829, 'students', 0, '2025', '2025-03-21 00:11:18'),
(1830, 'faculty', 7, '2025', '2025-03-21 00:11:18'),
(1831, 'courses', 0, '03', '2025-03-21 00:11:18'),
(1832, 'events', 2, '03', '2025-03-21 00:11:18'),
(1833, 'students', 0, '2025', '2025-03-21 00:17:00'),
(1834, 'faculty', 7, '2025', '2025-03-21 00:17:00'),
(1835, 'courses', 0, '03', '2025-03-21 00:17:00'),
(1836, 'events', 2, '03', '2025-03-21 00:17:00'),
(1837, 'students', 0, '2025', '2025-03-21 00:17:20'),
(1838, 'faculty', 7, '2025', '2025-03-21 00:17:20'),
(1839, 'courses', 0, '03', '2025-03-21 00:17:20'),
(1840, 'events', 2, '03', '2025-03-21 00:17:20'),
(1841, 'students', 0, '2025', '2025-03-21 00:17:46'),
(1842, 'faculty', 7, '2025', '2025-03-21 00:17:46'),
(1843, 'courses', 0, '03', '2025-03-21 00:17:46'),
(1844, 'events', 2, '03', '2025-03-21 00:17:46'),
(1845, 'students', 0, '2025', '2025-03-21 00:18:31'),
(1846, 'faculty', 7, '2025', '2025-03-21 00:18:31'),
(1847, 'courses', 0, '03', '2025-03-21 00:18:31'),
(1848, 'events', 2, '03', '2025-03-21 00:18:31'),
(1849, 'students', 0, '2025', '2025-03-21 00:19:02'),
(1850, 'faculty', 7, '2025', '2025-03-21 00:19:02'),
(1851, 'courses', 0, '03', '2025-03-21 00:19:02'),
(1852, 'events', 2, '03', '2025-03-21 00:19:02'),
(1853, 'students', 0, '2025', '2025-03-21 00:20:06'),
(1854, 'faculty', 7, '2025', '2025-03-21 00:20:06'),
(1855, 'courses', 0, '03', '2025-03-21 00:20:06'),
(1856, 'events', 2, '03', '2025-03-21 00:20:06'),
(1857, 'students', 0, '2025', '2025-03-21 00:20:12'),
(1858, 'faculty', 7, '2025', '2025-03-21 00:20:12'),
(1859, 'courses', 0, '03', '2025-03-21 00:20:12'),
(1860, 'events', 2, '03', '2025-03-21 00:20:12'),
(1861, 'students', 0, '2025', '2025-03-21 00:34:28'),
(1862, 'faculty', 7, '2025', '2025-03-21 00:34:28'),
(1863, 'courses', 0, '03', '2025-03-21 00:34:28'),
(1864, 'events', 2, '03', '2025-03-21 00:34:28'),
(1865, 'students', 0, '2025', '2025-03-21 00:34:30'),
(1866, 'faculty', 7, '2025', '2025-03-21 00:34:30'),
(1867, 'courses', 0, '03', '2025-03-21 00:34:30'),
(1868, 'events', 2, '03', '2025-03-21 00:34:30'),
(1869, 'students', 0, '2025', '2025-03-21 00:39:46'),
(1870, 'faculty', 7, '2025', '2025-03-21 00:39:46'),
(1871, 'courses', 0, '03', '2025-03-21 00:39:46'),
(1872, 'events', 2, '03', '2025-03-21 00:39:46'),
(1873, 'students', 0, '2025', '2025-03-21 00:39:48'),
(1874, 'faculty', 7, '2025', '2025-03-21 00:39:48'),
(1875, 'courses', 0, '03', '2025-03-21 00:39:48'),
(1876, 'events', 2, '03', '2025-03-21 00:39:48'),
(1877, 'students', 0, '2025', '2025-03-23 15:44:06'),
(1878, 'faculty', 7, '2025', '2025-03-23 15:44:06'),
(1879, 'courses', 0, '03', '2025-03-23 15:44:06'),
(1880, 'events', 2, '03', '2025-03-23 15:44:06'),
(1881, 'students', 0, '2025', '2025-03-23 15:44:23'),
(1882, 'faculty', 7, '2025', '2025-03-23 15:44:23'),
(1883, 'courses', 0, '03', '2025-03-23 15:44:23'),
(1884, 'events', 2, '03', '2025-03-23 15:44:23'),
(1885, 'students', 0, '2025', '2025-03-23 15:48:44'),
(1886, 'faculty', 7, '2025', '2025-03-23 15:48:44'),
(1887, 'courses', 0, '03', '2025-03-23 15:48:44'),
(1888, 'events', 2, '03', '2025-03-23 15:48:44'),
(1889, 'students', 0, '2025', '2025-03-23 15:50:29'),
(1890, 'faculty', 7, '2025', '2025-03-23 15:50:29'),
(1891, 'courses', 0, '03', '2025-03-23 15:50:29'),
(1892, 'events', 2, '03', '2025-03-23 15:50:29'),
(1893, 'students', 0, '2025', '2025-03-23 15:50:30'),
(1894, 'faculty', 7, '2025', '2025-03-23 15:50:30'),
(1895, 'courses', 0, '03', '2025-03-23 15:50:30'),
(1896, 'events', 2, '03', '2025-03-23 15:50:30'),
(1897, 'students', 0, '2025', '2025-03-23 15:50:31'),
(1898, 'faculty', 7, '2025', '2025-03-23 15:50:31'),
(1899, 'courses', 0, '03', '2025-03-23 15:50:31'),
(1900, 'events', 2, '03', '2025-03-23 15:50:31'),
(1901, 'students', 0, '2025', '2025-03-23 15:50:31'),
(1902, 'faculty', 7, '2025', '2025-03-23 15:50:31'),
(1903, 'courses', 0, '03', '2025-03-23 15:50:31'),
(1904, 'events', 2, '03', '2025-03-23 15:50:31'),
(1905, 'students', 0, '2025', '2025-03-23 15:56:45'),
(1906, 'faculty', 7, '2025', '2025-03-23 15:56:45'),
(1907, 'courses', 0, '03', '2025-03-23 15:56:45'),
(1908, 'events', 2, '03', '2025-03-23 15:56:45'),
(1909, 'students', 0, '2025', '2025-03-23 16:01:39'),
(1910, 'faculty', 7, '2025', '2025-03-23 16:01:39'),
(1911, 'courses', 0, '03', '2025-03-23 16:01:39'),
(1912, 'events', 2, '03', '2025-03-23 16:01:39'),
(1913, 'students', 0, '2025', '2025-03-23 16:05:51'),
(1914, 'faculty', 7, '2025', '2025-03-23 16:05:51'),
(1915, 'courses', 0, '03', '2025-03-23 16:05:51'),
(1916, 'events', 2, '03', '2025-03-23 16:05:51'),
(1917, 'students', 0, '2025', '2025-03-23 16:20:50'),
(1918, 'faculty', 7, '2025', '2025-03-23 16:20:50'),
(1919, 'courses', 0, '03', '2025-03-23 16:20:50'),
(1920, 'events', 2, '03', '2025-03-23 16:20:50'),
(1921, 'students', 0, '2025', '2025-03-23 16:21:02'),
(1922, 'faculty', 7, '2025', '2025-03-23 16:21:02'),
(1923, 'courses', 0, '03', '2025-03-23 16:21:02'),
(1924, 'events', 2, '03', '2025-03-23 16:21:02'),
(1925, 'students', 0, '2025', '2025-03-23 16:23:31'),
(1926, 'faculty', 7, '2025', '2025-03-23 16:23:31'),
(1927, 'courses', 0, '03', '2025-03-23 16:23:31'),
(1928, 'events', 2, '03', '2025-03-23 16:23:31'),
(1929, 'students', 0, '2025', '2025-03-23 16:23:36'),
(1930, 'faculty', 7, '2025', '2025-03-23 16:23:36'),
(1931, 'courses', 0, '03', '2025-03-23 16:23:36'),
(1932, 'events', 2, '03', '2025-03-23 16:23:36'),
(1933, 'students', 0, '2025', '2025-03-23 16:23:41'),
(1934, 'faculty', 7, '2025', '2025-03-23 16:23:41'),
(1935, 'courses', 0, '03', '2025-03-23 16:23:41'),
(1936, 'events', 2, '03', '2025-03-23 16:23:41'),
(1937, 'students', 0, '2025', '2025-03-23 16:25:31'),
(1938, 'faculty', 7, '2025', '2025-03-23 16:25:31'),
(1939, 'courses', 0, '03', '2025-03-23 16:25:31'),
(1940, 'events', 2, '03', '2025-03-23 16:25:31'),
(1941, 'students', 0, '2025', '2025-03-23 16:25:32'),
(1942, 'faculty', 7, '2025', '2025-03-23 16:25:32'),
(1943, 'courses', 0, '03', '2025-03-23 16:25:32'),
(1944, 'events', 2, '03', '2025-03-23 16:25:32'),
(1945, 'students', 0, '2025', '2025-03-23 16:26:21'),
(1946, 'faculty', 7, '2025', '2025-03-23 16:26:21'),
(1947, 'courses', 0, '03', '2025-03-23 16:26:21'),
(1948, 'events', 2, '03', '2025-03-23 16:26:21'),
(1949, 'students', 0, '2025', '2025-03-23 16:26:22'),
(1950, 'faculty', 7, '2025', '2025-03-23 16:26:22'),
(1951, 'courses', 0, '03', '2025-03-23 16:26:22'),
(1952, 'events', 2, '03', '2025-03-23 16:26:22'),
(1953, 'students', 0, '2025', '2025-03-23 16:26:23'),
(1954, 'faculty', 7, '2025', '2025-03-23 16:26:23'),
(1955, 'courses', 0, '03', '2025-03-23 16:26:23'),
(1956, 'events', 2, '03', '2025-03-23 16:26:23'),
(1957, 'students', 0, '2025', '2025-03-23 16:28:48'),
(1958, 'faculty', 7, '2025', '2025-03-23 16:28:48'),
(1959, 'courses', 0, '03', '2025-03-23 16:28:48'),
(1960, 'events', 2, '03', '2025-03-23 16:28:48'),
(1961, 'students', 0, '2025', '2025-03-23 16:28:49'),
(1962, 'faculty', 7, '2025', '2025-03-23 16:28:49'),
(1963, 'courses', 0, '03', '2025-03-23 16:28:49'),
(1964, 'events', 2, '03', '2025-03-23 16:28:49'),
(1965, 'students', 0, '2025', '2025-03-23 16:29:16'),
(1966, 'faculty', 7, '2025', '2025-03-23 16:29:16'),
(1967, 'courses', 0, '03', '2025-03-23 16:29:16'),
(1968, 'events', 2, '03', '2025-03-23 16:29:16'),
(1969, 'students', 0, '2025', '2025-03-23 16:30:22'),
(1970, 'faculty', 7, '2025', '2025-03-23 16:30:22'),
(1971, 'courses', 0, '03', '2025-03-23 16:30:22'),
(1972, 'events', 2, '03', '2025-03-23 16:30:22'),
(1973, 'students', 0, '2025', '2025-03-23 16:30:30'),
(1974, 'faculty', 7, '2025', '2025-03-23 16:30:30'),
(1975, 'courses', 0, '03', '2025-03-23 16:30:30'),
(1976, 'events', 2, '03', '2025-03-23 16:30:30'),
(1977, 'students', 0, '2025', '2025-03-23 16:30:49'),
(1978, 'faculty', 7, '2025', '2025-03-23 16:30:49'),
(1979, 'courses', 0, '03', '2025-03-23 16:30:49'),
(1980, 'events', 2, '03', '2025-03-23 16:30:49'),
(1981, 'students', 0, '2025', '2025-03-23 16:31:08'),
(1982, 'faculty', 7, '2025', '2025-03-23 16:31:08'),
(1983, 'courses', 0, '03', '2025-03-23 16:31:08'),
(1984, 'events', 2, '03', '2025-03-23 16:31:08'),
(1985, 'students', 0, '2025', '2025-03-23 16:31:31'),
(1986, 'faculty', 7, '2025', '2025-03-23 16:31:31'),
(1987, 'courses', 0, '03', '2025-03-23 16:31:31'),
(1988, 'events', 2, '03', '2025-03-23 16:31:31'),
(1989, 'students', 0, '2025', '2025-03-23 16:38:40'),
(1990, 'faculty', 7, '2025', '2025-03-23 16:38:40'),
(1991, 'courses', 0, '03', '2025-03-23 16:38:40'),
(1992, 'events', 2, '03', '2025-03-23 16:38:40'),
(1993, 'students', 0, '2025', '2025-03-23 16:38:55'),
(1994, 'faculty', 7, '2025', '2025-03-23 16:38:55'),
(1995, 'courses', 0, '03', '2025-03-23 16:38:55'),
(1996, 'events', 2, '03', '2025-03-23 16:38:55'),
(1997, 'students', 0, '2025', '2025-03-24 14:47:27'),
(1998, 'faculty', 7, '2025', '2025-03-24 14:47:27'),
(1999, 'courses', 0, '03', '2025-03-24 14:47:27'),
(2000, 'events', 2, '03', '2025-03-24 14:47:27'),
(2001, 'students', 7, '2025', '2025-04-22 20:48:49'),
(2002, 'faculty', 10, '2025', '2025-04-22 20:48:49'),
(2003, 'courses', 0, '04', '2025-04-22 20:48:49'),
(2004, 'events', 0, '04', '2025-04-22 20:48:49'),
(2005, 'students', 7, '2025', '2025-04-24 17:00:16'),
(2006, 'faculty', 10, '2025', '2025-04-24 17:00:16'),
(2007, 'courses', 0, '04', '2025-04-24 17:00:16'),
(2008, 'events', 0, '04', '2025-04-24 17:00:16'),
(2009, 'students', 7, '2025', '2025-04-24 17:00:21'),
(2010, 'faculty', 10, '2025', '2025-04-24 17:00:21'),
(2011, 'courses', 0, '04', '2025-04-24 17:00:21'),
(2012, 'events', 0, '04', '2025-04-24 17:00:21'),
(2013, 'students', 7, '2025', '2025-04-24 17:00:25'),
(2014, 'faculty', 10, '2025', '2025-04-24 17:00:25'),
(2015, 'courses', 0, '04', '2025-04-24 17:00:25'),
(2016, 'events', 0, '04', '2025-04-24 17:00:25');

-- --------------------------------------------------------

--
-- Structure de la table `teacher_preferences`
--

CREATE TABLE `teacher_preferences` (
  `id` int(11) NOT NULL,
  `id_enseignant` int(11) NOT NULL,
  `day_of_week` enum('lundi','mardi','mercredi','jeudi','vendredi','samedi') NOT NULL,
  `time_slot` varchar(20) NOT NULL,
  `preference_type` enum('preferred','unavailable') NOT NULL,
  `preference_level` int(11) DEFAULT 5,
  `reason` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `teacher_preferences`
--

INSERT INTO `teacher_preferences` (`id`, `id_enseignant`, `day_of_week`, `time_slot`, `preference_type`, `preference_level`, `reason`, `created_at`, `updated_at`) VALUES
(14, 27, 'mardi', '08h-10h', 'preferred', 5, '', '2025-05-13 21:45:20', '2025-05-13 21:45:20'),
(15, 27, 'lundi', '10h-12h', 'preferred', 5, '', '2025-05-13 22:06:17', '2025-05-13 22:06:17'),
(17, 27, 'lundi', '16h-18h', 'unavailable', 0, '', '2025-05-13 22:19:29', '2025-05-13 22:19:29'),
(18, 1, 'mardi', '08h-10h', 'preferred', 5, '', '2025-05-14 12:46:56', '2025-05-14 12:46:56'),
(19, 1, 'lundi', '16h-18h', 'unavailable', 0, '', '2025-05-14 14:00:29', '2025-05-14 14:00:29'),
(20, 1, 'mardi', '16h-18h', 'preferred', 5, '', '2025-05-14 15:17:04', '2025-05-14 15:17:04');

-- --------------------------------------------------------

--
-- Structure de la table `teacher_workload_limits`
--

CREATE TABLE `teacher_workload_limits` (
  `id` int(11) NOT NULL,
  `id_enseignant` int(11) NOT NULL,
  `max_hours_per_day` int(11) DEFAULT 6,
  `max_hours_per_week` int(11) DEFAULT 24,
  `max_consecutive_hours` int(11) DEFAULT 4,
  `min_break_duration` int(11) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Structure de la table `ue_preferences`
--

CREATE TABLE `ue_preferences` (
  `id` int(11) NOT NULL,
  `id_enseignant` int(11) NOT NULL,
  `id_ue` int(11) NOT NULL,
  `preference_level` int(11) NOT NULL DEFAULT 5,
  `reason` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `ue_preferences`
--

INSERT INTO `ue_preferences` (`id`, `id_enseignant`, `id_ue`, `preference_level`, `reason`, `created_at`, `updated_at`) VALUES
(1, 27, 2, 10, '', '2025-05-13 22:36:29', '2025-05-13 22:36:29'),
(2, 27, 1, 5, '', '2025-05-13 22:36:37', '2025-05-13 22:36:37'),
(3, 1, 2, 10, '', '2025-05-14 12:47:22', '2025-05-14 12:47:22'),
(4, 1, 1, 2, '', '2025-05-14 12:47:33', '2025-05-14 12:47:50'),
(5, 1, 3, 10, '', '2025-05-14 12:50:00', '2025-05-14 12:50:00'),
(6, 1, 4, 10, '', '2025-05-14 12:50:09', '2025-05-14 12:50:27'),
(7, 1, 5, 10, '', '2025-05-14 14:01:07', '2025-05-14 15:17:55'),
(8, 1, 6, 5, '', '2025-05-14 15:18:10', '2025-05-14 15:18:10');

-- --------------------------------------------------------

--
-- Structure de la table `uniteenseignement`
--

CREATE TABLE `uniteenseignement` (
  `id` int(11) NOT NULL,
  `type` enum('Cours','TD','TP') NOT NULL,
  `volume_horaire` int(11) NOT NULL,
  `nb_groupes` int(11) DEFAULT 1,
  `module_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `uniteenseignement`
--

INSERT INTO `uniteenseignement` (`id`, `type`, `volume_horaire`, `nb_groupes`, `module_id`) VALUES
(1, 'TD', 100, 5, 1),
(2, 'Cours', 30, 1, 1),
(3, 'Cours', 36, 1, 2),
(4, 'TD', 72, 2, 2),
(5, 'Cours', 36, 1, 3),
(6, 'TD', 72, 2, 3),
(7, 'Cours', 30, 1, 4),
(8, 'TD', 60, 2, 5),
(9, 'Cours', 30, 1, 5),
(10, 'TD', 56, 2, 4);

-- --------------------------------------------------------

--
-- Structure de la table `users`
--

CREATE TABLE `users` (
  `id_user` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('enseignant','chef de departement','coordinateur','vacataire','etudiant','admin') NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `users`
--

INSERT INTO `users` (`id_user`, `username`, `password`, `role`, `is_active`) VALUES
(1, 'salmaEns', '$2y$10$ecM8dt4f3t79uLPdfTxvoewW8KFXLhOOXwJ7fYYEjXM8H4/xAecvG', 'enseignant', 1),
(2, 'salmaCor', '$2y$10$ecM8dt4f3t79uLPdfTxvoewW8KFXLhOOXwJ7fYYEjXM8H4/xAecvG', 'coordinateur', 1),
(3, 'salmaChef', '$2y$10$ecM8dt4f3t79uLPdfTxvoewW8KFXLhOOXwJ7fYYEjXM8H4/xAecvG', 'chef de departement', 1),
(17, 'CN12345', '$2y$10$kUyr6c1NEyPqknKSNIHUNu/25OPtWgJGpk05uawer2P7iNYAHXmvK', 'admin', 1),
(27, 'douae', '$2y$10$ecM8dt4f3t79uLPdfTxvoewW8KFXLhOOXwJ7fYYEjXM8H4/xAecvG', 'enseignant', 1);

-- --------------------------------------------------------

--
-- Structure de la table `observation`
--

CREATE TABLE `observation` (
  `id_observation` int(11) NOT NULL,
  `id_admin` int(11) NOT NULL,
  `id_enseignant` int(11) NOT NULL,
  `date_observation` date NOT NULL,
  `contenu` text NOT NULL,
  `niveau_gravite` enum('faible','moyen','élevé') NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Structure de la table `visits`
--

CREATE TABLE `visits` (
  `id` int(11) NOT NULL,
  `visit_date` date NOT NULL,
  `visit_count` int(11) DEFAULT 1,
  `user_type` varchar(50) DEFAULT 'anonymous',
  `page_visited` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `visits`
--

INSERT INTO `visits` (`id`, `visit_date`, `visit_count`, `user_type`, `page_visited`, `created_at`) VALUES
(1, '2025-03-26', 0, 'all', 'dashboard', '2025-04-24 20:19:34'),
(2, '2025-03-27', 0, 'all', 'dashboard', '2025-04-24 20:19:34'),
(3, '2025-03-28', 81, 'all', 'dashboard', '2025-04-24 20:19:34'),
(4, '2025-03-29', 79, 'all', 'dashboard', '2025-04-24 20:19:34'),
(5, '2025-03-30', 88, 'all', 'dashboard', '2025-04-24 20:19:34'),
(6, '2025-03-31', 96, 'all', 'dashboard', '2025-04-24 20:19:34'),
(7, '2025-04-01', 81, 'all', 'dashboard', '2025-04-24 20:19:34'),
(8, '2025-04-02', 83, 'all', 'dashboard', '2025-04-24 20:19:34'),
(9, '2025-04-03', 91, 'all', 'dashboard', '2025-04-24 20:19:34'),
(10, '2025-04-04', 72, 'all', 'dashboard', '2025-04-24 20:19:34'),
(11, '2025-04-05', 70, 'all', 'dashboard', '2025-04-24 20:19:34'),
(12, '2025-04-06', 70, 'all', 'dashboard', '2025-04-24 20:19:34'),
(13, '2025-04-07', 67, 'all', 'dashboard', '2025-04-24 20:19:34'),
(14, '2025-04-08', 83, 'all', 'dashboard', '2025-04-24 20:19:34'),
(15, '2025-04-09', 65, 'all', 'dashboard', '2025-04-24 20:19:34'),
(16, '2025-04-10', 61, 'all', 'dashboard', '2025-04-24 20:19:34'),
(17, '2025-04-11', 60, 'all', 'dashboard', '2025-04-24 20:19:34'),
(18, '2025-04-12', 77, 'all', 'dashboard', '2025-04-24 20:19:34'),
(19, '2025-04-13', 64, 'all', 'dashboard', '2025-04-24 20:19:34'),
(20, '2025-04-14', 71, 'all', 'dashboard', '2025-04-24 20:19:34'),
(21, '2025-04-15', 70, 'all', 'dashboard', '2025-04-24 20:19:34'),
(22, '2025-04-16', 60, 'all', 'dashboard', '2025-04-24 20:19:34'),
(23, '2025-04-17', 65, 'all', 'dashboard', '2025-04-24 20:19:34'),
(24, '2025-04-18', 53, 'all', 'dashboard', '2025-04-24 20:19:34'),
(25, '2025-04-19', 54, 'all', 'dashboard', '2025-04-24 20:19:34'),
(26, '2025-04-20', 46, 'all', 'dashboard', '2025-04-24 20:19:34'),
(27, '2025-04-21', 0, 'all', 'dashboard', '2025-04-24 20:19:34'),
(28, '2025-04-22', 0, 'all', 'dashboard', '2025-04-24 20:19:34'),
(29, '2025-04-23', 0, 'all', 'dashboard', '2025-04-24 20:19:34'),
(30, '2025-04-24', 0, 'all', 'dashboard', '2025-04-24 20:19:34'),
(31, '2025-04-24', 0, 'admin', 'dashboard', '2025-04-24 20:22:36'),
(32, '2025-04-25', 25, 'admin', 'dashboard', '2025-04-24 22:00:21'),
(33, '2025-04-25', 2, 'admin', 'login', '2025-04-24 22:18:06'),
(34, '2025-04-26', 62, 'admin', 'dashboard', '2025-04-25 22:05:59'),
(35, '2025-04-26', 2, 'admin', 'login', '2025-04-26 17:58:57'),
(36, '2025-04-30', 3, 'admin', 'login', '2025-04-30 14:03:27'),
(37, '2025-04-30', 4, 'admin', 'dashboard', '2025-04-30 14:03:38'),
(38, '2025-05-03', 5, 'admin', 'login', '2025-05-03 11:43:18'),
(39, '2025-05-03', 18, 'admin', 'dashboard', '2025-05-03 11:43:20'),
(40, '2025-05-04', 5, 'admin', 'login', '2025-05-04 09:54:10'),
(41, '2025-05-04', 79, 'admin', 'dashboard', '2025-05-04 09:54:12'),
(42, '2025-05-04', 1, 'etudiant', 'login', '2025-05-04 21:56:26'),
(43, '2025-05-05', 13, 'admin', 'login', '2025-05-04 23:54:03'),
(44, '2025-05-05', 528, 'admin', 'dashboard', '2025-05-05 03:16:09'),
(45, '2025-05-05', 1, 'etudiant', 'login', '2025-05-05 03:16:57'),
(46, '2025-05-05', 1, NULL, 'dashboard', '2025-05-05 05:08:39'),
(47, '2025-05-05', 2, 'admin', 'profile', '2025-05-05 05:10:32'),
(48, '2025-05-05', 6, 'admin', 'timetable', '2025-05-05 05:15:06'),
(49, '2025-05-05', 2, 'admin', 'exam_schedule', '2025-05-05 05:15:09'),
(50, '2025-05-05', 19, 'admin', 'students', '2025-05-05 05:15:12'),
(51, '2025-05-05', 7, 'admin', 'settings', '2025-05-05 05:16:14'),
(52, '2025-05-05', 5, 'admin', 'user-accounts', '2025-05-05 05:16:35'),
(53, '2025-05-05', 16, 'admin', 'messages', '2025-05-05 05:17:02'),
(54, '2025-05-05', 17, 'admin', 'notifications', '2025-05-05 05:17:03'),
(55, '2025-05-05', 5, 'admin', 'faculty', '2025-05-05 05:23:33'),
(56, '2025-05-05', 4, 'admin', 'staff', '2025-05-05 05:23:36'),
(57, '2025-05-05', 7, 'admin', 'modules', '2025-05-05 05:23:39'),
(58, '2025-05-05', 4, 'admin', 'demandes', '2025-05-05 05:23:42'),
(59, '2025-05-05', 3, 'admin', 'clubs', '2025-05-05 05:24:24'),
(60, '2025-05-05', 13, 'admin', 'events', '2025-05-05 05:24:28'),
(61, '2025-05-05', 1, 'admin', 'personnel', '2025-05-05 05:59:14'),
(62, '2025-05-05', 2, 'admin', 'timetable-display', '2025-05-05 11:39:31'),
(63, '2025-05-09', 3, 'admin', 'login', '2025-05-09 04:45:38'),
(64, '2025-05-09', 21, 'admin', 'dashboard', '2025-05-09 04:45:38'),
(65, '2025-05-09', 5, 'admin', 'profile', '2025-05-09 04:45:48'),
(66, '2025-05-09', 1, 'admin', 'students', '2025-05-09 04:45:57'),
(67, '2025-05-09', 1, 'admin', 'settings', '2025-05-09 04:46:54'),
(68, '2025-05-09', 1, 'admin', 'user-accounts', '2025-05-09 04:46:56'),
(69, '2025-05-09', 1, 'admin', 'messages', '2025-05-09 04:47:05'),
(70, '2025-05-09', 1, 'admin', 'notifications', '2025-05-09 04:47:07'),
(71, '2025-05-09', 1, 'admin', 'demandes', '2025-05-09 04:47:22'),
(72, '2025-05-09', 4, 'admin', 'faculty', '2025-05-09 07:17:24'),
(73, '2025-05-09', 17, 'chef de departement', 'login', '2025-05-09 07:59:34'),
(74, '2025-05-09', 1, 'enseignant', 'login', '2025-05-09 08:55:02'),
(75, '2025-05-09', 3, 'coordinateur', 'login', '2025-05-09 08:55:53'),
(76, '2025-05-09', 1, 'admin', 'modules', '2025-05-09 20:44:28'),
(77, '2025-05-10', 7, 'admin', 'login', '2025-05-10 11:44:49'),
(78, '2025-05-10', 20, 'admin', 'dashboard', '2025-05-10 11:44:54'),
(79, '2025-05-10', 14, 'chef de departement', 'login', '2025-05-10 11:46:15'),
(80, '2025-05-10', 26, 'chef de departement', 'dashboard', '2025-05-10 12:39:34'),
(81, '2025-05-10', 96, 'chef de departement', 'department_professors', '2025-05-10 15:41:22'),
(82, '2025-05-10', 1, 'enseignant', 'login', '2025-05-10 18:44:23'),
(83, '2025-05-10', 1, 'enseignant', 'dashboard', '2025-05-10 18:53:29'),
(84, '2025-05-11', 1, 'coordinateur', 'login', '2025-05-11 20:14:08'),
(85, '2025-05-13', 4, 'admin', 'login', '2025-05-13 19:02:56'),
(86, '2025-05-13', 10, 'admin', 'dashboard', '2025-05-13 19:02:57'),
(87, '2025-05-13', 1, 'coordinateur', 'login', '2025-05-13 19:07:54'),
(88, '2025-05-13', 1, 'chef de departement', 'login', '2025-05-13 19:12:19'),
(89, '2025-05-13', 1, 'chef de departement', 'dashboard', '2025-05-13 19:12:19'),
(90, '2025-05-13', 1, 'chef de departement', 'department_professors', '2025-05-13 19:12:43'),
(91, '2025-05-13', 6, 'enseignant', 'login', '2025-05-13 19:20:13'),
(92, '2025-05-14', 8, 'enseignant', 'login', '2025-05-14 12:46:18'),
(93, '2025-05-14', 6, 'coordinateur', 'login', '2025-05-14 12:52:23'),
(94, '2025-05-14', 4, 'chef de departement', 'login', '2025-05-14 12:57:16'),
(95, '2025-05-14', 4, 'chef de departement', 'dashboard', '2025-05-14 12:57:16'),
(96, '2025-05-14', 2, 'chef de departement', 'department_professors', '2025-05-14 13:23:41'),
(97, '2025-05-14', 6, 'admin', 'login', '2025-05-14 13:54:55'),
(98, '2025-05-14', 28, 'admin', 'dashboard', '2025-05-14 13:54:55'),
(99, '2025-05-14', 31, 'enseignant', 'dashboard', '2025-05-14 18:43:03'),
(100, '2025-05-14', 19, 'coordinateur', 'dashboard', '2025-05-14 19:29:53'),
(101, '2025-05-15', 2, 'coordinateur', 'login', '2025-05-15 10:44:20'),
(102, '2025-05-15', 6, 'coordinateur', 'dashboard', '2025-05-15 10:44:20'),
(103, '2025-05-15', 2, 'enseignant', 'login', '2025-05-15 17:06:36'),
(104, '2025-05-15', 3, 'enseignant', 'dashboard', '2025-05-15 17:06:36'),
(105, '2025-05-16', 12, 'enseignant', 'login', '2025-05-16 11:34:58'),
(106, '2025-05-16', 14, 'enseignant', 'dashboard', '2025-05-16 11:34:58'),
(107, '2025-05-16', 5, 'coordinateur', 'login', '2025-05-16 15:08:02'),
(108, '2025-05-16', 20, 'coordinateur', 'dashboard', '2025-05-16 15:08:02'),
(109, '2025-05-17', 6, 'coordinateur', 'login', '2025-05-17 10:57:30'),
(110, '2025-05-17', 14, 'coordinateur', 'dashboard', '2025-05-17 10:57:30'),
(111, '2025-05-17', 55, 'coordinateur', 'import_grades', '2025-05-17 10:58:50'),
(112, '2025-05-17', 2, 'enseignant', 'login', '2025-05-17 13:52:50'),
(113, '2025-05-17', 2, 'enseignant', 'dashboard', '2025-05-17 13:52:50'),
(114, '2025-05-20', 3, 'coordinateur', 'login', '2025-05-20 11:49:34'),
(115, '2025-05-20', 3, 'coordinateur', 'dashboard', '2025-05-20 11:49:34'),
(116, '2025-05-20', 3, 'coordinateur', 'import_grades', '2025-05-20 11:49:41'),
(117, '2025-05-20', 5, 'enseignant', 'login', '2025-05-20 11:52:37'),
(118, '2025-05-20', 5, 'enseignant', 'dashboard', '2025-05-20 11:52:37'),
(119, '2025-05-21', 2, 'enseignant', 'login', '2025-05-21 17:44:50'),
(120, '2025-05-21', 2, 'enseignant', 'dashboard', '2025-05-21 17:44:50'),
(121, '2025-05-27', 3, 'admin', 'login', '2025-05-27 11:39:30'),
(122, '2025-05-27', 13, 'admin', 'dashboard', '2025-05-27 11:39:30'),
(123, '2025-05-27', 2, 'enseignant', 'login', '2025-05-27 11:45:34'),
(124, '2025-05-27', 4, 'enseignant', 'dashboard', '2025-05-27 11:45:34'),
(125, '2025-05-27', 1, 'chef de departement', 'login', '2025-05-27 11:50:23'),
(126, '2025-05-27', 1, 'chef de departement', 'dashboard', '2025-05-27 11:50:23'),
(127, '2025-05-29', 1, 'admin', 'login', '2025-05-28 23:05:09'),
(128, '2025-05-29', 2, 'admin', 'dashboard', '2025-05-28 23:05:09'),
(129, '2025-05-29', 1, 'enseignant', 'login', '2025-05-28 23:07:56'),
(130, '2025-05-29', 3, 'enseignant', 'dashboard', '2025-05-28 23:07:56');

-- --------------------------------------------------------

--
-- Structure de la table `workload_preferences`
--

CREATE TABLE `workload_preferences` (
  `id` int(11) NOT NULL,
  `id_enseignant` int(11) NOT NULL,
  `max_hours_per_day` int(11) NOT NULL DEFAULT 6,
  `max_hours_per_week` int(11) NOT NULL DEFAULT 24,
  `max_consecutive_hours` int(11) NOT NULL DEFAULT 4,
  `min_break_duration` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `workload_preferences`
--

INSERT INTO `workload_preferences` (`id`, `id_enseignant`, `max_hours_per_day`, `max_hours_per_week`, `max_consecutive_hours`, `min_break_duration`, `created_at`, `updated_at`) VALUES
(1, 27, 6, 24, 4, 1, '2025-05-13 21:55:45', '2025-05-13 21:57:14');

--
-- Index pour les tables déchargées
--

--
-- Index pour la table `activities`
--
ALTER TABLE `activities`
  ADD PRIMARY KEY (`id`),
  ADD KEY `id_user` (`id_user`);

--
-- Index pour la table `admin`
--
ALTER TABLE `admin`
  ADD PRIMARY KEY (`id_admin`),
  ADD UNIQUE KEY `CNI` (`CNI`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Index pour la table `affectation`
--
ALTER TABLE `affectation`
  ADD PRIMARY KEY (`id`),
  ADD KEY `professeur_id` (`professeur_id`),
  ADD KEY `unite_enseignement_id` (`unite_enseignement_id`);

--
-- Index pour la table `ai_generated_schedules`
--
ALTER TABLE `ai_generated_schedules`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_generated_by` (`generated_by`);

--
-- Index pour la table `ai_schedule_items`
--
ALTER TABLE `ai_schedule_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_schedule_id` (`schedule_id`),
  ADD KEY `fk_schedule_module` (`id_module`),
  ADD KEY `fk_schedule_teacher` (`id_enseignant`),
  ADD KEY `fk_schedule_room` (`id_salle`);

--
-- Index pour la table `choixue`
--
ALTER TABLE `choixue`
  ADD PRIMARY KEY (`id`),
  ADD KEY `professeur_id` (`professeur_id`),
  ADD KEY `unite_enseignement_id` (`unite_enseignement_id`);

--
-- Index pour la table `configuration_charge`
--
ALTER TABLE `configuration_charge`
  ADD PRIMARY KEY (`id_config`);

--
-- Index pour la table `cycle`
--
ALTER TABLE `cycle`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `demands`
--
ALTER TABLE `demands`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `departement`
--
ALTER TABLE `departement`
  ADD PRIMARY KEY (`id_departement`),
  ADD KEY `id_chef_departement` (`id_chef_departement`);

--
-- Index pour la table `emploi_temps`
--
ALTER TABLE `emploi_temps`
  ADD PRIMARY KEY (`id`),
  ADD KEY `affectation_id` (`affectation_id`),
  ADD KEY `salle_id` (`salle_id`);

--
-- Index pour la table `enseignant`
--
ALTER TABLE `enseignant`
  ADD PRIMARY KEY (`id_enseignant`),
  ADD UNIQUE KEY `CNI` (`CNI`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `fk_specialite` (`id_specialite`),
  ADD KEY `fk_enseignant_departement` (`id_departement`);

--
-- Index pour la table `enseignant_filiere`
--
ALTER TABLE `enseignant_filiere`
  ADD PRIMARY KEY (`id_enseignant`,`id_filiere`),
  ADD KEY `id_filiere` (`id_filiere`);

--
-- Index pour la table `etudiant`
--
ALTER TABLE `etudiant`
  ADD PRIMARY KEY (`id_etudiant`),
  ADD UNIQUE KEY `CNE` (`CNE`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `id_filiere` (`id_filiere`),
  ADD KEY `fk_etudiant_niveaux` (`id_niveau`) USING BTREE;

--
-- Index pour la table `events`
--
ALTER TABLE `events`
  ADD PRIMARY KEY (`id_event`);

--
-- Index pour la table `events_image`
--
ALTER TABLE `events_image`
  ADD PRIMARY KEY (`id`),
  ADD KEY `id_event` (`id_event`);

--
-- Index pour la table `filiere`
--
ALTER TABLE `filiere`
  ADD PRIMARY KEY (`id_filiere`),
  ADD KEY `chef_filiere` (`id_coordinateur`),
  ADD KEY `id_dep` (`id_dep`),
  ADD KEY `fk_cycle_filiere` (`id_cycle`);

--
-- Index pour la table `messages`
--
ALTER TABLE `messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sender_id` (`sender_id`),
  ADD KEY `fk_receiver` (`receiver_id`);

--
-- Index pour la table `module`
--
ALTER TABLE `module`
  ADD PRIMARY KEY (`id`),
  ADD KEY `specialite_id` (`specialite_id`),
  ADD KEY `fk_modules_semestre` (`id_semestre`),
  ADD KEY `fk_modules_niveaux` (`id_niveau`),
  ADD KEY `filiere_id` (`filiere_id`);

--
-- Index pour la table `module_preferences`
--
ALTER TABLE `module_preferences`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_teacher_module` (`id_enseignant`,`id_module`),
  ADD KEY `fk_module_pref` (`id_module`);

--
-- Index pour la table `niveaux`
--
ALTER TABLE `niveaux`
  ADD PRIMARY KEY (`id`),
  ADD KEY `cycle_id` (`cycle_id`);

--
-- Index pour la table `note`
--
ALTER TABLE `note`
  ADD PRIMARY KEY (`id_note`),
  ADD UNIQUE KEY `unique_note` (`id_etudiant`,`id_module`,`id_niveau`,`session`),
  ADD KEY `note_ibfk_2` (`id_module`),
  ADD KEY `note_ibfk_3` (`id_niveau`),
  ADD KEY `fk_note_filiere` (`id_filiere`);

--
-- Index pour la table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `observation`
--
ALTER TABLE `observation`
  ADD PRIMARY KEY (`id_observation`),
  ADD KEY `id_admin` (`id_admin`),
  ADD KEY `id_enseignant` (`id_enseignant`);

--
-- Index pour la table `password_reset_codes`
--
ALTER TABLE `password_reset_codes`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `pdf_grades`
--
ALTER TABLE `pdf_grades`
  ADD PRIMARY KEY (`id`),
  ADD KEY `id_filiere` (`id_filiere`),
  ADD KEY `id_enseignant` (`id_enseignant`),
  ADD KEY `id_module` (`id_module`),
  ADD KEY `id_niveau` (`id_niveau`),
  ADD KEY `id_semestre` (`id_semestre`);

--
-- Index pour la table `salles`
--
ALTER TABLE `salles`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `schedule_generation_logs`
--
ALTER TABLE `schedule_generation_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_log_schedule` (`schedule_id`);

--
-- Index pour la table `scheduling_constraints`
--
ALTER TABLE `scheduling_constraints`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_constraint_name` (`constraint_name`);

--
-- Index pour la table `semestre`
--
ALTER TABLE `semestre`
  ADD PRIMARY KEY (`id`),
  ADD KEY `niveau_id` (`niveau_id`);

--
-- Index pour la table `service_logs`
--
ALTER TABLE `service_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_service_key` (`service_key`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Index pour la table `service_management`
--
ALTER TABLE `service_management`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `service_name` (`service_name`),
  ADD UNIQUE KEY `service_key` (`service_key`),
  ADD KEY `idx_service_key` (`service_key`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_end_time` (`end_time`);

--
-- Index pour la table `specialite`
--
ALTER TABLE `specialite`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `staff`
--
ALTER TABLE `staff`
  ADD PRIMARY KEY (`CNI`);

--
-- Index pour la table `statistics`
--
ALTER TABLE `statistics`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `teacher_preferences`
--
ALTER TABLE `teacher_preferences`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_teacher_timeslot` (`id_enseignant`,`day_of_week`,`time_slot`);

--
-- Index pour la table `teacher_workload_limits`
--
ALTER TABLE `teacher_workload_limits`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_teacher_workload` (`id_enseignant`);

--
-- Index pour la table `ue_preferences`
--
ALTER TABLE `ue_preferences`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_teacher_ue` (`id_enseignant`,`id_ue`),
  ADD KEY `id_ue` (`id_ue`);

--
-- Index pour la table `uniteenseignement`
--
ALTER TABLE `uniteenseignement`
  ADD PRIMARY KEY (`id`),
  ADD KEY `module_id` (`module_id`);

--
-- Index pour la table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id_user`);

--
-- Index pour la table `visits`
--
ALTER TABLE `visits`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `workload_preferences`
--
ALTER TABLE `workload_preferences`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_teacher` (`id_enseignant`);

--
-- AUTO_INCREMENT pour les tables déchargées
--

--
-- AUTO_INCREMENT pour la table `activities`
--
ALTER TABLE `activities`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `admin`
--
ALTER TABLE `admin`
  MODIFY `id_admin` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT pour la table `affectation`
--
ALTER TABLE `affectation`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT pour la table `ai_generated_schedules`
--
ALTER TABLE `ai_generated_schedules`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `ai_schedule_items`
--
ALTER TABLE `ai_schedule_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `choixue`
--
ALTER TABLE `choixue`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `configuration_charge`
--
ALTER TABLE `configuration_charge`
  MODIFY `id_config` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `cycle`
--
ALTER TABLE `cycle`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT pour la table `departement`
--
ALTER TABLE `departement`
  MODIFY `id_departement` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT pour la table `emploi_temps`
--
ALTER TABLE `emploi_temps`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `enseignant`
--
ALTER TABLE `enseignant`
  MODIFY `id_enseignant` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=44;

--
-- AUTO_INCREMENT pour la table `etudiant`
--
ALTER TABLE `etudiant`
  MODIFY `id_etudiant` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=87;

--
-- AUTO_INCREMENT pour la table `events`
--
ALTER TABLE `events`
  MODIFY `id_event` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT pour la table `events_image`
--
ALTER TABLE `events_image`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT pour la table `filiere`
--
ALTER TABLE `filiere`
  MODIFY `id_filiere` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT pour la table `messages`
--
ALTER TABLE `messages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT pour la table `module`
--
ALTER TABLE `module`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT pour la table `module_preferences`
--
ALTER TABLE `module_preferences`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT pour la table `niveaux`
--
ALTER TABLE `niveaux`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT pour la table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT pour la table `observation`
--
ALTER TABLE `observation`
  MODIFY `id_observation` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `password_reset_codes`
--
ALTER TABLE `password_reset_codes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT pour la table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT pour la table `pdf_grades`
--
ALTER TABLE `pdf_grades`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT pour la table `salles`
--
ALTER TABLE `salles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT pour la table `schedule_generation_logs`
--
ALTER TABLE `schedule_generation_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `scheduling_constraints`
--
ALTER TABLE `scheduling_constraints`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT pour la table `semestre`
--
ALTER TABLE `semestre`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT pour la table `service_logs`
--
ALTER TABLE `service_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT pour la table `service_management`
--
ALTER TABLE `service_management`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT pour la table `specialite`
--
ALTER TABLE `specialite`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT pour la table `statistics`
--
ALTER TABLE `statistics`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2017;

--
-- AUTO_INCREMENT pour la table `teacher_preferences`
--
ALTER TABLE `teacher_preferences`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT pour la table `teacher_workload_limits`
--
ALTER TABLE `teacher_workload_limits`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `ue_preferences`
--
ALTER TABLE `ue_preferences`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT pour la table `uniteenseignement`
--
ALTER TABLE `uniteenseignement`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT pour la table `users`
--
ALTER TABLE `users`
  MODIFY `id_user` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT pour la table `visits`
--
ALTER TABLE `visits`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=131;

--
-- AUTO_INCREMENT pour la table `workload_preferences`
--
ALTER TABLE `workload_preferences`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Contraintes pour les tables déchargées
--

--
-- Contraintes pour la table `activities`
--
ALTER TABLE `activities`
  ADD CONSTRAINT `activities_ibfk_1` FOREIGN KEY (`id_user`) REFERENCES `users` (`id_user`) ON DELETE CASCADE;

--
-- Contraintes pour la table `affectation`
--
ALTER TABLE `affectation`
  ADD CONSTRAINT `affectation_ibfk_1` FOREIGN KEY (`professeur_id`) REFERENCES `enseignant` (`id_enseignant`),
  ADD CONSTRAINT `affectation_ibfk_2` FOREIGN KEY (`unite_enseignement_id`) REFERENCES `uniteenseignement` (`id`);

--
-- Contraintes pour la table `ai_generated_schedules`
--
ALTER TABLE `ai_generated_schedules`
  ADD CONSTRAINT `fk_generated_by` FOREIGN KEY (`generated_by`) REFERENCES `users` (`id_user`) ON DELETE SET NULL;

--
-- Contraintes pour la table `ai_schedule_items`
--
ALTER TABLE `ai_schedule_items`
  ADD CONSTRAINT `fk_schedule_id` FOREIGN KEY (`schedule_id`) REFERENCES `ai_generated_schedules` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_schedule_module` FOREIGN KEY (`id_module`) REFERENCES `module` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_schedule_room` FOREIGN KEY (`id_salle`) REFERENCES `salles` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_schedule_teacher` FOREIGN KEY (`id_enseignant`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE CASCADE;

--
-- Contraintes pour la table `choixue`
--
ALTER TABLE `choixue`
  ADD CONSTRAINT `choixue_ibfk_1` FOREIGN KEY (`professeur_id`) REFERENCES `enseignant` (`id_enseignant`),
  ADD CONSTRAINT `choixue_ibfk_2` FOREIGN KEY (`unite_enseignement_id`) REFERENCES `uniteenseignement` (`id`);

--
-- Contraintes pour la table `departement`
--
ALTER TABLE `departement`
  ADD CONSTRAINT `departement_ibfk_1` FOREIGN KEY (`id_chef_departement`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE SET NULL;

--
-- Contraintes pour la table `emploi_temps`
--
ALTER TABLE `emploi_temps`
  ADD CONSTRAINT `emploi_temps_ibfk_1` FOREIGN KEY (`affectation_id`) REFERENCES `affectation` (`id`),
  ADD CONSTRAINT `emploi_temps_ibfk_2` FOREIGN KEY (`salle_id`) REFERENCES `salles` (`id`);

--
-- Contraintes pour la table `enseignant`
--
ALTER TABLE `enseignant`
  ADD CONSTRAINT `fk_enseignant_departement` FOREIGN KEY (`id_departement`) REFERENCES `departement` (`id_departement`),
  ADD CONSTRAINT `fk_enseignant_filiere` FOREIGN KEY (`id_departement`) REFERENCES `filiere` (`id_filiere`),
  ADD CONSTRAINT `fk_specialite` FOREIGN KEY (`id_specialite`) REFERENCES `specialite` (`id`),
  ADD CONSTRAINT `fk_table_departement` FOREIGN KEY (`id_departement`) REFERENCES `departement` (`id_departement`) ON DELETE SET NULL;

--
-- Contraintes pour la table `enseignant_filiere`
--
ALTER TABLE `enseignant_filiere`
  ADD CONSTRAINT `enseignant_filiere_ibfk_1` FOREIGN KEY (`id_enseignant`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE CASCADE,
  ADD CONSTRAINT `enseignant_filiere_ibfk_2` FOREIGN KEY (`id_filiere`) REFERENCES `filiere` (`id_filiere`) ON DELETE CASCADE;

--
-- Contraintes pour la table `etudiant`
--
ALTER TABLE `etudiant`
  ADD CONSTRAINT `etudiant_ibfk_1` FOREIGN KEY (`id_filiere`) REFERENCES `filiere` (`id_filiere`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_etudiant_niveaux` FOREIGN KEY (`id_niveau`) REFERENCES `niveaux` (`id`),
  ADD CONSTRAINT `fk_module_niveaux` FOREIGN KEY (`id_niveau`) REFERENCES `niveaux` (`id`);

--
-- Contraintes pour la table `events_image`
--
ALTER TABLE `events_image`
  ADD CONSTRAINT `events_image_ibfk_1` FOREIGN KEY (`id_event`) REFERENCES `events` (`id_event`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `filiere`
--
ALTER TABLE `filiere`
  ADD CONSTRAINT `filiere_ibfk_1` FOREIGN KEY (`id_coordinateur`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE SET NULL,
  ADD CONSTRAINT `filiere_ibfk_2` FOREIGN KEY (`id_dep`) REFERENCES `departement` (`id_departement`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_cycle_filiere` FOREIGN KEY (`id_cycle`) REFERENCES `cycle` (`id`);

--
-- Contraintes pour la table `messages`
--
ALTER TABLE `messages`
  ADD CONSTRAINT `fk_receiver` FOREIGN KEY (`receiver_id`) REFERENCES `users` (`id_user`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `messages_ibfk_1` FOREIGN KEY (`sender_id`) REFERENCES `users` (`id_user`) ON DELETE CASCADE;

--
-- Contraintes pour la table `module`
--
ALTER TABLE `module`
  ADD CONSTRAINT `fk_module_niveau` FOREIGN KEY (`id_niveau`) REFERENCES `niveaux` (`id`),
  ADD CONSTRAINT `fk_modules_niveaux` FOREIGN KEY (`id_niveau`) REFERENCES `niveaux` (`id`),
  ADD CONSTRAINT `fk_modules_semestre` FOREIGN KEY (`id_semestre`) REFERENCES `semestre` (`id`),
  ADD CONSTRAINT `module_ibfk_1` FOREIGN KEY (`specialite_id`) REFERENCES `specialite` (`id`),
  ADD CONSTRAINT `module_ibfk_2` FOREIGN KEY (`filiere_id`) REFERENCES `filiere` (`id_filiere`);

--
-- Contraintes pour la table `module_preferences`
--
ALTER TABLE `module_preferences`
  ADD CONSTRAINT `fk_module_pref` FOREIGN KEY (`id_module`) REFERENCES `module` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_teacher_module_pref` FOREIGN KEY (`id_enseignant`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE CASCADE;

--
-- Contraintes pour la table `niveaux`
--
ALTER TABLE `niveaux`
  ADD CONSTRAINT `niveaux_ibfk_1` FOREIGN KEY (`cycle_id`) REFERENCES `cycle` (`id`);

--
-- Contraintes pour la table `note`
--
ALTER TABLE `note`
  ADD CONSTRAINT `fk_note_filiere` FOREIGN KEY (`id_filiere`) REFERENCES `filiere` (`id_filiere`);

--
-- Contraintes pour la table `observation`
--
ALTER TABLE `observation`
  ADD CONSTRAINT `observation_ibfk_1` FOREIGN KEY (`id_admin`) REFERENCES `admin` (`id_admin`) ON DELETE CASCADE,
  ADD CONSTRAINT `observation_ibfk_2` FOREIGN KEY (`id_enseignant`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE CASCADE;

--
-- Contraintes pour la table `pdf_grades`
--
ALTER TABLE `pdf_grades`
  ADD CONSTRAINT `pdf_grades_ibfk_1` FOREIGN KEY (`id_filiere`) REFERENCES `filiere` (`id_filiere`),
  ADD CONSTRAINT `pdf_grades_ibfk_2` FOREIGN KEY (`id_enseignant`) REFERENCES `enseignant` (`id_enseignant`),
  ADD CONSTRAINT `pdf_grades_ibfk_3` FOREIGN KEY (`id_module`) REFERENCES `module` (`id`),
  ADD CONSTRAINT `pdf_grades_ibfk_4` FOREIGN KEY (`id_niveau`) REFERENCES `niveaux` (`id`),
  ADD CONSTRAINT `pdf_grades_ibfk_5` FOREIGN KEY (`id_semestre`) REFERENCES `semestre` (`id`);

--
-- Contraintes pour la table `schedule_generation_logs`
--
ALTER TABLE `schedule_generation_logs`
  ADD CONSTRAINT `fk_log_schedule` FOREIGN KEY (`schedule_id`) REFERENCES `ai_generated_schedules` (`id`) ON DELETE SET NULL;

--
-- Contraintes pour la table `semestre`
--
ALTER TABLE `semestre`
  ADD CONSTRAINT `semestre_ibfk_1` FOREIGN KEY (`niveau_id`) REFERENCES `niveaux` (`id`);

--
-- Contraintes pour la table `teacher_preferences`
--
ALTER TABLE `teacher_preferences`
  ADD CONSTRAINT `fk_teacher_pref` FOREIGN KEY (`id_enseignant`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE CASCADE;

--
-- Contraintes pour la table `teacher_workload_limits`
--
ALTER TABLE `teacher_workload_limits`
  ADD CONSTRAINT `fk_workload_teacher` FOREIGN KEY (`id_enseignant`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE CASCADE;

--
-- Contraintes pour la table `ue_preferences`
--
ALTER TABLE `ue_preferences`
  ADD CONSTRAINT `ue_preferences_ibfk_1` FOREIGN KEY (`id_enseignant`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE CASCADE,
  ADD CONSTRAINT `ue_preferences_ibfk_2` FOREIGN KEY (`id_ue`) REFERENCES `uniteenseignement` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `uniteenseignement`
--
ALTER TABLE `uniteenseignement`
  ADD CONSTRAINT `uniteenseignement_ibfk_1` FOREIGN KEY (`module_id`) REFERENCES `module` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
