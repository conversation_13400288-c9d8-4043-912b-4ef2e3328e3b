// Fonction pour charger toutes les filières
async function loadFilieres() {
    try {
        const response = await fetch('../../route/filiereRoute.php');
        const data = await response.json();
        if (data.data) {
            const filiereSelect = document.getElementById('filiere');
            filiereSelect.innerHTML = '<option value="" disabled selected>Sélectionner filière</option>';
            filiereSelect.innerHTML += '<option value="none">Aucune filière</option>';
            data.data.forEach(filiere => {
                filiereSelect.innerHTML += `<option value="${filiere.id_filiere}">${filiere.nom_filiere}</option>`;
            });
        }
    } catch (error) {
        console.error('Erreur lors du chargement des filières:', error);
    }
}

// Fonction pour charger les niveaux en fonction de la filière sélectionnée
async function loadNiveaux(id_filiere) {
    try {
        let url = '../../route/niveauRoute.php';
        if (id_filiere !== 'none' && id_filiere !== '') {
            url = `../../route/niveauRoute.php?id_filiere=${id_filiere}`;
        }
        
        const response = await fetch(url);
        const data = await response.json();
        
        const niveauSelect = document.getElementById('niveau');
        niveauSelect.innerHTML = '<option value="" disabled selected>Sélectionner niveau</option>';
        
        if (data.data) {
            // Filtrer les niveaux pour n'afficher que ceux avec id_filiere NULL quand aucune filière n'est sélectionnée
            const niveaux = id_filiere === 'none' || id_filiere === ''
                ? data.data.filter(niveau => niveau.id_filiere === null)
                : data.data;
            
            niveaux.forEach(niveau => {
                niveauSelect.innerHTML += `<option value="${niveau.id_niveau}">${niveau.niveau}</option>`;
            });
        }
    } catch (error) {
        console.error('Erreur lors du chargement des niveaux:', error);
    }
}

// Initialisation des événements
document.addEventListener('DOMContentLoaded', () => {
    // Charger les filières au chargement de la page
    loadFilieres();

    // Gérer le changement de filière
    document.getElementById('filiere').addEventListener('change', (e) => {
        const selectedFiliere = e.target.value;
        loadNiveaux(selectedFiliere);
    });
});