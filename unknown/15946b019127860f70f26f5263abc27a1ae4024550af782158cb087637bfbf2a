<?php
/**
 * Vacant Teaching Units Route
 *
 * This file handles API requests for vacant teaching units functionality.
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log the request for debugging if needed
// error_log("vacantUERoute.php accessed with: " . json_encode($_GET));

// Include required files
require_once __DIR__ . '/../controller/vacantUEController.php';
require_once __DIR__ . '/../utils/response.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if the user is authenticated and is a department head
function isDepartmentHead() {
    return (isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'chef de departement') ||
           (isset($_SESSION['role']) && $_SESSION['role'] === 'chef de departement');
}

// Check if the user is an admin
function isAdmin() {
    return (isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'admin') ||
           (isset($_SESSION['role']) && $_SESSION['role'] === 'admin');
}

// Log session info for debugging if needed
// error_log("Session user role: " . ($_SESSION['user']['role'] ?? $_SESSION['role'] ?? 'not set'));

// Verify authentication
if (!isDepartmentHead() && !isAdmin()) {
    error_log("Access denied - not department head or admin");
    http_response_code(403);
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

// Set content type for JSON responses
header('Content-Type: application/json');

// Handle different actions
if (isset($_GET['action'])) {
    $action = $_GET['action'];

    switch ($action) {
        case 'getUnassignedUnits':
            if (!isset($_GET['department_id'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Department ID is required']);
                exit;
            }

            $departmentId = intval($_GET['department_id']);
            $academicYear = isset($_GET['academic_year']) ? $_GET['academic_year'] : null;

            $result = getUnassignedUnitsForDepartment($departmentId, $academicYear);

            if (isset($result['error'])) {
                http_response_code(500);
                echo json_encode(['error' => $result['error']]);
            } else {
                echo json_encode(['data' => $result]);
            }
            break;

        case 'updateVacancyStatus':
            // Get JSON input
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);

            if (!$data) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON data']);
                exit;
            }

            // Validate that the department ID matches the user's department
            if (isDepartmentHead() && isset($_SESSION['user']['department_id'])) {
                if (!isset($data['department_id']) || $data['department_id'] != $_SESSION['user']['department_id']) {
                    http_response_code(403);
                    echo json_encode(['error' => 'You can only manage teaching units in your own department']);
                    exit;
                }
            }

            $result = updateVacancyStatus($data);

            if (isset($result['error'])) {
                http_response_code(500);
                echo json_encode(['error' => $result['error']]);
            } else {
                echo json_encode($result);
            }
            break;

        case 'getCurrentAcademicYear':
            echo json_encode(['academic_year' => getCurrentYear()]);
            break;

        case 'getDepartmentInfo':
            if (!isset($_GET['department_id'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Department ID is required']);
                exit;
            }

            $departmentId = intval($_GET['department_id']);
            $department = getDepartmentInfo($departmentId);

            if ($department) {
                echo json_encode(['data' => $department]);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Department not found']);
            }
            break;

        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
            break;
    }
} else {
    http_response_code(400);
    echo json_encode(['error' => 'No action specified']);
}

exit;
?>
