<?php
require_once "../config/db.php";

/**
 * Get all academic years for a specific teacher
 *
 * @param int $teacherId The teacher's ID
 * @return array Array of academic years
 */
function getTeacherAcademicYears($teacherId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getTeacherAcademicYears");
        return ["error" => "Database connection error"];
    }

    $teacherId = mysqli_real_escape_string($conn, $teacherId);

    // Get academic years from affectation table
    $sql = "SELECT DISTINCT annee_academique
            FROM affectation
            WHERE professeur_id = '$teacherId'
            ORDER BY annee_academique DESC";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getTeacherAcademicYears: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching academic years: " . $error];
    }

    $years = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $years[] = $row['annee_academique'];
    }

    mysqli_close($conn);
    return $years;
}

/**
 * Get teacher's UE assignments for a specific academic year
 *
 * @param int $teacherId The teacher's ID
 * @param string $academicYear The academic year
 * @return array Array of UE assignments
 */
function getTeacherUEAssignments($teacherId, $academicYear) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getTeacherUEAssignments");
        return ["error" => "Database connection error"];
    }

    $teacherId = mysqli_real_escape_string($conn, $teacherId);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);

    $sql = "SELECT a.*, ue.type, ue.volume_horaire, ue.nb_groupes,
                   m.nom as module_name, m.volume_total,
                   f.nom_filiere, n.nom as niveau_name, s.nom as semestre_name
            FROM affectation a
            JOIN uniteenseignement ue ON a.unite_enseignement_id = ue.id
            JOIN module m ON ue.module_id = m.id
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveaux n ON m.id_niveau = n.id
            LEFT JOIN semestre s ON m.id_semestre = s.id
            WHERE a.professeur_id = '$teacherId'
            AND a.annee_academique = '$academicYear'
            ORDER BY m.nom, ue.type";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getTeacherUEAssignments: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching UE assignments: " . $error];
    }

    $assignments = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $assignments[] = $row;
    }

    mysqli_close($conn);
    return $assignments;
}

/**
 * Get teacher's modules for a specific academic year
 *
 * @param int $teacherId The teacher's ID
 * @param string $academicYear The academic year
 * @return array Array of modules with details
 */
function getTeacherModules($teacherId, $academicYear) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getTeacherModules");
        return ["error" => "Database connection error"];
    }

    $teacherId = mysqli_real_escape_string($conn, $teacherId);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);

    $sql = "SELECT DISTINCT m.*, f.nom_filiere, n.nom as niveau_name, s.nom as semestre_name,
                   GROUP_CONCAT(DISTINCT ue.type ORDER BY ue.type) as ue_types,
                   SUM(ue.volume_horaire * ue.nb_groupes) as total_hours
            FROM affectation a
            JOIN uniteenseignement ue ON a.unite_enseignement_id = ue.id
            JOIN module m ON ue.module_id = m.id
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveaux n ON m.id_niveau = n.id
            LEFT JOIN semestre s ON m.id_semestre = s.id
            WHERE a.professeur_id = '$teacherId'
            AND a.annee_academique = '$academicYear'
            GROUP BY m.id
            ORDER BY m.nom";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getTeacherModules: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching modules: " . $error];
    }

    $modules = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $modules[] = $row;
    }

    mysqli_close($conn);
    return $modules;
}

/**
 * Get teacher's annual workload statistics for a specific academic year
 *
 * @param int $teacherId The teacher's ID
 * @param string $academicYear The academic year
 * @return array Array of statistics
 */
function getTeacherAnnualStats($teacherId, $academicYear) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getTeacherAnnualStats");
        return ["error" => "Database connection error"];
    }

    $teacherId = mysqli_real_escape_string($conn, $teacherId);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);

    // Get total hours, modules count, UE count, and hours by teaching type
    $sql = "SELECT
                COUNT(DISTINCT ue.module_id) as total_modules,
                COUNT(DISTINCT a.unite_enseignement_id) as total_ues,
                SUM(ue.volume_horaire * ue.nb_groupes) as total_hours,
                COUNT(DISTINCT CASE WHEN ue.type = 'Cours' THEN a.unite_enseignement_id END) as cours_count,
                COUNT(DISTINCT CASE WHEN ue.type = 'TD' THEN a.unite_enseignement_id END) as td_count,
                COUNT(DISTINCT CASE WHEN ue.type = 'TP' THEN a.unite_enseignement_id END) as tp_count,
                SUM(CASE WHEN ue.type = 'Cours' THEN ue.volume_horaire * ue.nb_groupes ELSE 0 END) as cours_hours,
                SUM(CASE WHEN ue.type = 'TD' THEN ue.volume_horaire * ue.nb_groupes ELSE 0 END) as td_hours,
                SUM(CASE WHEN ue.type = 'TP' THEN ue.volume_horaire * ue.nb_groupes ELSE 0 END) as tp_hours
            FROM affectation a
            JOIN uniteenseignement ue ON a.unite_enseignement_id = ue.id
            WHERE a.professeur_id = '$teacherId'
            AND a.annee_academique = '$academicYear'";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getTeacherAnnualStats: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching statistics: " . $error];
    }

    $stats = mysqli_fetch_assoc($result);

    // Get grade submissions count
    $gradesSql = "SELECT COUNT(DISTINCT n.id_module) as modules_with_grades
                  FROM note n
                  JOIN module m ON n.id_module = m.id
                  JOIN affectation a ON a.unite_enseignement_id IN (
                      SELECT ue.id FROM uniteenseignement ue WHERE ue.module_id = m.id
                  )
                  WHERE a.professeur_id = '$teacherId'
                  AND a.annee_academique = '$academicYear'
                  AND YEAR(n.date_saisie) = YEAR(CURDATE())";

    $gradesResult = mysqli_query($conn, $gradesSql);
    if ($gradesResult) {
        $gradesData = mysqli_fetch_assoc($gradesResult);
        $stats['modules_with_grades'] = $gradesData['modules_with_grades'] ?? 0;
    } else {
        $stats['modules_with_grades'] = 0;
    }

    mysqli_close($conn);
    return $stats;
}

/**
 * Get teacher's grade upload history for a specific academic year
 *
 * @param int $teacherId The teacher's ID
 * @param string $academicYear The academic year
 * @return array Array of grade uploads
 */
function getTeacherGradeHistory($teacherId, $academicYear) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getTeacherGradeHistory");
        return ["error" => "Database connection error"];
    }

    $teacherId = mysqli_real_escape_string($conn, $teacherId);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);

    // Get grade uploads from note table - only the latest version for each module and session
    $sql = "SELECT n.id_module, n.session, n.date_saisie,
                   m.nom as module_name, f.nom_filiere, nv.nom as niveau_name,
                   COUNT(DISTINCT n.id_etudiant) as student_count,
                   AVG(n.valeur) as average_grade
            FROM note n
            JOIN module m ON n.id_module = m.id
            LEFT JOIN filiere f ON n.id_filiere = f.id_filiere
            LEFT JOIN niveaux nv ON n.id_niveau = nv.id
            JOIN affectation a ON a.unite_enseignement_id IN (
                SELECT ue.id FROM uniteenseignement ue WHERE ue.module_id = m.id
            )
            INNER JOIN (
                SELECT id_module, session, MAX(date_saisie) as latest_date
                FROM note n2
                JOIN affectation a2 ON a2.unite_enseignement_id IN (
                    SELECT ue2.id FROM uniteenseignement ue2 WHERE ue2.module_id = n2.id_module
                )
                WHERE a2.professeur_id = '$teacherId'
                AND a2.annee_academique = '$academicYear'
                GROUP BY id_module, session
            ) latest ON n.id_module = latest.id_module
                    AND n.session = latest.session
                    AND n.date_saisie = latest.latest_date
            WHERE a.professeur_id = '$teacherId'
            AND a.annee_academique = '$academicYear'
            GROUP BY n.id_module, n.session, n.date_saisie
            ORDER BY n.date_saisie DESC";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getTeacherGradeHistory: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching grade history: " . $error];
    }

    $gradeHistory = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $gradeHistory[] = $row;
    }

    // Get PDF submissions
    $pdfSql = "SELECT pg.*, m.nom as module_name, f.nom_filiere, nv.nom as niveau_name
               FROM pdf_grades pg
               JOIN module m ON pg.id_module = m.id
               LEFT JOIN filiere f ON pg.id_filiere = f.id_filiere
               LEFT JOIN niveaux nv ON pg.id_niveau = nv.id
               WHERE pg.id_enseignant = '$teacherId'
               ORDER BY pg.id DESC";

    $pdfResult = mysqli_query($conn, $pdfSql);
    $pdfSubmissions = [];

    if ($pdfResult) {
        while ($row = mysqli_fetch_assoc($pdfResult)) {
            $pdfSubmissions[] = $row;
        }
    }

    mysqli_close($conn);

    return [
        'grade_uploads' => $gradeHistory,
        'pdf_submissions' => $pdfSubmissions
    ];
}

/**
 * Get teacher's administrative observations for a specific academic year
 *
 * @param int $teacherId The teacher's ID
 * @param string $academicYear The academic year
 * @return array Array of administrative observations
 */
function getTeacherObservations($teacherId, $academicYear) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getTeacherObservations");
        return ["error" => "Database connection error"];
    }

    $teacherId = mysqli_real_escape_string($conn, $teacherId);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);

    // Extract years from academic year (e.g., "2024/2025" -> ["2024", "2025"])
    $yearParts = explode('/', $academicYear);
    $startYear = $yearParts[0];
    $endYear = isset($yearParts[1]) ? $yearParts[1] : $startYear;

    $sql = "SELECT o.*, a.nom as admin_nom, a.prenom as admin_prenom
            FROM observation o
            JOIN admin a ON o.id_admin = a.id_admin
            WHERE o.id_enseignant = '$teacherId'
            AND (YEAR(o.date_observation) = '$startYear' OR YEAR(o.date_observation) = '$endYear')
            ORDER BY o.date_observation DESC, o.niveau_gravite DESC";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getTeacherObservations: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching observations: " . $error];
    }

    $observations = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $observations[] = $row;
    }

    mysqli_close($conn);
    return $observations;
}

/**
 * Generate annual report data for a teacher
 *
 * @param int $teacherId The teacher's ID
 * @param string $academicYear The academic year
 * @return array Complete annual report data
 */
function generateAnnualReport($teacherId, $academicYear) {
    $report = [
        'academic_year' => $academicYear,
        'teacher_id' => $teacherId,
        'ue_assignments' => getTeacherUEAssignments($teacherId, $academicYear),
        'modules' => getTeacherModules($teacherId, $academicYear),
        'statistics' => getTeacherAnnualStats($teacherId, $academicYear),
        'grade_history' => getTeacherGradeHistory($teacherId, $academicYear),
        'observations' => getTeacherObservations($teacherId, $academicYear),
        'generated_at' => date('Y-m-d H:i:s')
    ];

    return $report;
}
?>
