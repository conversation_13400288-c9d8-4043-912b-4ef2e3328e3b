<?php
/**
 * Controller for managing teaching unit assignments to part-time lecturers (vacataires)
 * Handles API requests for coordinator assignment functionality
 */

require_once __DIR__ . "/../model/affecterUEVacataireModel.php";
require_once __DIR__ . "/../utils/response.php";

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// For testing purposes, set a default coordinator session if not set
if (!isset($_SESSION['user'])) {
    // Check if we have session data in different format
    if (isset($_SESSION['id_filiere'])) {
        $_SESSION['user'] = [
            'username' => $_SESSION['username'] ?? 'coordinator',
            'role' => 'coordinateur',
            'filiere_id' => $_SESSION['id_filiere'],
            'filiere_name' => $_SESSION['filiere_name'] ?? 'Unknown'
        ];
    } else {
        $_SESSION['user'] = [
            'username' => 'test_coordinator',
            'role' => 'coordinateur',
            'filiere_id' => 1, // Default to filiere ID 1 (informatique)
            'filiere_name' => 'Informatique'
        ];
    }
    error_log("[DEBUG] Created test coordinator session in affecterUEVacataireController");
}

/**
 * Get vacant teaching units for the coordinator's filière
 */
function getVacantUEsForCoordinatorAPI() {
    try {
        // Check if user is logged in and is a coordinator
        if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'coordinateur') {
            error_log("[DEBUG] Unauthorized access attempt in getVacantUEsForCoordinatorAPI");
            jsonResponse(['error' => 'Unauthorized access'], 401);
            return;
        }

        // Get coordinator's filière ID
        $filiereId = $_SESSION['user']['filiere_id'] ?? $_SESSION['id_filiere'] ?? null;
        error_log("[DEBUG] Coordinator filière ID: " . ($filiereId ?? 'null'));

        if (!$filiereId) {
            error_log("[DEBUG] Filière ID not found in session");
            jsonResponse(['error' => 'Filière ID not found in session'], 400);
            return;
        }

        // Get vacant teaching units
        error_log("[DEBUG] Fetching vacant UEs for filière: $filiereId");
        $vacantUEs = getVacantUEsByFiliere($filiereId);

        if (isset($vacantUEs['error'])) {
            error_log("[DEBUG] Error fetching vacant UEs: " . $vacantUEs['error']);
            jsonResponse(['error' => $vacantUEs['error']], 500);
            return;
        }

        error_log("[DEBUG] Found " . count($vacantUEs) . " vacant UEs");
        jsonResponse(['data' => $vacantUEs], 200);
    } catch (Exception $e) {
        error_log("[ERROR] Exception in getVacantUEsForCoordinatorAPI: " . $e->getMessage());
        jsonResponse(['error' => 'Internal server error: ' . $e->getMessage()], 500);
    }
}

/**
 * Get available vacataires for the coordinator's filière
 */
function getAvailableVacatairesForCoordinatorAPI() {
    try {
        // Check if user is logged in and is a coordinator
        if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'coordinateur') {
            error_log("[DEBUG] Unauthorized access attempt in getAvailableVacatairesForCoordinatorAPI");
            jsonResponse(['error' => 'Unauthorized access'], 401);
            return;
        }

        // Get coordinator's filière ID
        $filiereId = $_SESSION['user']['filiere_id'] ?? $_SESSION['id_filiere'] ?? null;
        error_log("[DEBUG] Coordinator filière ID for vacataires: " . ($filiereId ?? 'null'));

        if (!$filiereId) {
            error_log("[DEBUG] Filière ID not found in session for vacataires");
            jsonResponse(['error' => 'Filière ID not found in session'], 400);
            return;
        }

        // Get available vacataires
        error_log("[DEBUG] Fetching vacataires for filière: $filiereId");
        $vacataires = getAvailableVacatairesByFiliere($filiereId);

        if (isset($vacataires['error'])) {
            error_log("[DEBUG] Error fetching vacataires: " . $vacataires['error']);
            jsonResponse(['error' => $vacataires['error']], 500);
            return;
        }

        error_log("[DEBUG] Found " . count($vacataires) . " vacataires");
        jsonResponse(['data' => $vacataires], 200);
    } catch (Exception $e) {
        error_log("[ERROR] Exception in getAvailableVacatairesForCoordinatorAPI: " . $e->getMessage());
        jsonResponse(['error' => 'Internal server error: ' . $e->getMessage()], 500);
    }
}

/**
 * Assign teaching units to a vacataire
 */
function assignUEsToVacataireAPI() {
    // Check if user is logged in and is a coordinator
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'coordinateur') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        return;
    }

    // Get POST data
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON data'], 400);
        return;
    }

    // Validate required fields
    $vacataireId = $input['vacataire_id'] ?? null;
    $ueIds = $input['ue_ids'] ?? [];
    $comments = $input['comments'] ?? '';

    if (!$vacataireId) {
        jsonResponse(['error' => 'Vacataire ID is required'], 400);
        return;
    }

    if (empty($ueIds) || !is_array($ueIds)) {
        jsonResponse(['error' => 'At least one teaching unit must be selected'], 400);
        return;
    }

    // Validate that all UE IDs are numeric
    foreach ($ueIds as $ueId) {
        if (!is_numeric($ueId)) {
            jsonResponse(['error' => 'Invalid teaching unit ID: ' . $ueId], 400);
            return;
        }
    }

    // Perform the assignment
    $result = assignUEsToVacataire($vacataireId, $ueIds, null, $comments);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 500);
        return;
    }

    jsonResponse(['success' => true, 'message' => $result['message'], 'assigned_count' => $result['assigned_count']], 200);
}

/**
 * Get assignment statistics for the coordinator's filière
 */
function getAssignmentStatisticsForCoordinatorAPI() {
    try {
        // Check if user is logged in and is a coordinator
        if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'coordinateur') {
            error_log("[DEBUG] Unauthorized access attempt in getAssignmentStatisticsForCoordinatorAPI");
            jsonResponse(['error' => 'Unauthorized access'], 401);
            return;
        }

        // Get coordinator's filière ID
        $filiereId = $_SESSION['user']['filiere_id'] ?? $_SESSION['id_filiere'] ?? null;
        error_log("[DEBUG] Coordinator filière ID for statistics: " . ($filiereId ?? 'null'));

        if (!$filiereId) {
            error_log("[DEBUG] Filière ID not found in session for statistics");
            jsonResponse(['error' => 'Filière ID not found in session'], 400);
            return;
        }

        // Get statistics
        error_log("[DEBUG] Fetching statistics for filière: $filiereId");
        $stats = getAssignmentStatistics($filiereId);

        if (isset($stats['error'])) {
            error_log("[DEBUG] Error fetching statistics: " . $stats['error']);
            jsonResponse(['error' => $stats['error']], 500);
            return;
        }

        error_log("[DEBUG] Statistics: " . json_encode($stats));
        jsonResponse(['data' => $stats], 200);
    } catch (Exception $e) {
        error_log("[ERROR] Exception in getAssignmentStatisticsForCoordinatorAPI: " . $e->getMessage());
        jsonResponse(['error' => 'Internal server error: ' . $e->getMessage()], 500);
    }
}

/**
 * Handle API requests based on action parameter
 */
function handleAffecterUEVacataireRequest() {
    $method = $_SERVER['REQUEST_METHOD'];

    if ($method === 'GET') {
        $action = $_GET['action'] ?? '';

        switch ($action) {
            case 'getVacantUEs':
                getVacantUEsForCoordinatorAPI();
                break;

            case 'getVacataires':
                getAvailableVacatairesForCoordinatorAPI();
                break;

            case 'getStatistics':
                getAssignmentStatisticsForCoordinatorAPI();
                break;

            default:
                jsonResponse(['error' => 'Invalid action'], 400);
                break;
        }
    } elseif ($method === 'POST') {
        $action = $_GET['action'] ?? '';

        switch ($action) {
            case 'assignUEs':
                assignUEsToVacataireAPI();
                break;

            default:
                jsonResponse(['error' => 'Invalid action'], 400);
                break;
        }
    } else {
        jsonResponse(['error' => 'Method not allowed'], 405);
    }
}

// Handle the request if this file is called directly
if (basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
    handleAffecterUEVacataireRequest();
}
?>
