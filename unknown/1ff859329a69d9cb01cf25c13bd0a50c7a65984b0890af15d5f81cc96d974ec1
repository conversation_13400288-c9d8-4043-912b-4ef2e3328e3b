<?php
/**
 * Script de test pour insérer des observations administratives
 * Ce fichier est uniquement pour tester la fonctionnalité
 */

require_once 'config/db.php';

// Fonction pour insérer des observations de test
function insertTestObservations() {
    $conn = getConnection();
    
    if (!$conn) {
        echo "Erreur de connexion à la base de données\n";
        return;
    }
    
    // Données de test pour les observations
    $observations = [
        [
            'id_admin' => 1, // Assurez-vous que cet admin existe
            'id_enseignant' => 1, // Assurez-vous que cet enseignant existe
            'date_observation' => '2024-01-15',
            'contenu' => 'L\'enseignant a montré une excellente performance dans ses cours. Les étudiants sont très satisfaits de la qualité de l\'enseignement.',
            'niveau_gravite' => 'faible'
        ],
        [
            'id_admin' => 1,
            'id_enseignant' => 1,
            'date_observation' => '2024-02-20',
            'contenu' => 'Quelques retards ont été observés lors des séances de TP. Il est recommandé d\'améliorer la ponctualité.',
            'niveau_gravite' => 'moyen'
        ],
        [
            'id_admin' => 1,
            'id_enseignant' => 1,
            'date_observation' => '2024-03-10',
            'contenu' => 'Absence non justifiée lors d\'une séance importante. Cette situation nécessite une attention particulière.',
            'niveau_gravite' => 'élevé'
        ],
        [
            'id_admin' => 1,
            'id_enseignant' => 1,
            'date_observation' => '2023-11-05',
            'contenu' => 'Participation active aux réunions pédagogiques et contribution positive aux discussions.',
            'niveau_gravite' => 'faible'
        ]
    ];
    
    $sql = "INSERT INTO observation (id_admin, id_enseignant, date_observation, contenu, niveau_gravite) 
            VALUES (?, ?, ?, ?, ?)";
    
    $stmt = mysqli_prepare($conn, $sql);
    
    if (!$stmt) {
        echo "Erreur de préparation de la requête: " . mysqli_error($conn) . "\n";
        return;
    }
    
    $insertedCount = 0;
    
    foreach ($observations as $obs) {
        mysqli_stmt_bind_param($stmt, "iisss", 
            $obs['id_admin'], 
            $obs['id_enseignant'], 
            $obs['date_observation'], 
            $obs['contenu'], 
            $obs['niveau_gravite']
        );
        
        if (mysqli_stmt_execute($stmt)) {
            $insertedCount++;
            echo "Observation insérée: " . $obs['date_observation'] . " - " . $obs['niveau_gravite'] . "\n";
        } else {
            echo "Erreur lors de l'insertion: " . mysqli_stmt_error($stmt) . "\n";
        }
    }
    
    mysqli_stmt_close($stmt);
    mysqli_close($conn);
    
    echo "\nTotal des observations insérées: $insertedCount\n";
}

// Fonction pour vérifier les observations existantes
function checkExistingObservations() {
    $conn = getConnection();
    
    if (!$conn) {
        echo "Erreur de connexion à la base de données\n";
        return;
    }
    
    $sql = "SELECT o.*, a.nom as admin_nom, a.prenom as admin_prenom, 
                   e.nom as enseignant_nom, e.prenom as enseignant_prenom
            FROM observation o
            JOIN admin a ON o.id_admin = a.id_admin
            JOIN enseignant e ON o.id_enseignant = e.id_enseignant
            ORDER BY o.date_observation DESC";
    
    $result = mysqli_query($conn, $sql);
    
    if (!$result) {
        echo "Erreur lors de la récupération: " . mysqli_error($conn) . "\n";
        return;
    }
    
    echo "\n=== Observations existantes ===\n";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "ID: " . $row['id_observation'] . "\n";
        echo "Enseignant: " . $row['enseignant_prenom'] . " " . $row['enseignant_nom'] . "\n";
        echo "Admin: " . $row['admin_prenom'] . " " . $row['admin_nom'] . "\n";
        echo "Date: " . $row['date_observation'] . "\n";
        echo "Gravité: " . $row['niveau_gravite'] . "\n";
        echo "Contenu: " . substr($row['contenu'], 0, 100) . "...\n";
        echo "---\n";
    }
    
    mysqli_close($conn);
}

// Exécution du script
echo "=== Script de test pour les observations ===\n\n";

// Vérifier les observations existantes
checkExistingObservations();

// Demander confirmation pour insérer de nouvelles observations
echo "\nVoulez-vous insérer des observations de test ? (y/n): ";
$handle = fopen("php://stdin", "r");
$line = fgets($handle);
fclose($handle);

if (trim($line) === 'y' || trim($line) === 'Y') {
    insertTestObservations();
    echo "\nObservations de test insérées avec succès!\n";
    
    // Afficher les observations après insertion
    checkExistingObservations();
} else {
    echo "Insertion annulée.\n";
}

echo "\nScript terminé.\n";
?>
