<?php
/**
 * Controller for Chef de Département History
 *
 * This controller handles API requests for department head history functionality,
 * including professor management, module assignments, workload tracking, and observations.
 */

require_once __DIR__ . '/../model/chefHistoryModel.php';
require_once __DIR__ . '/../utils/response.php';

/**
 * API endpoint to get academic years for a department head
 *
 * @param int $chefId The chef de département ID
 */
function getChefAcademicYearsAPI($chefId) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a department head
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'chef de departement') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    if (!$chefId) {
        jsonResponse(['error' => 'Chef ID is required'], 400);
        exit;
    }

    $years = getChefAcademicYears($chefId);

    if (isset($years['error'])) {
        jsonResponse(['error' => $years['error']], 500);
        exit;
    }

    jsonResponse(['success' => true, 'data' => $years], 200);
}

/**
 * API endpoint to get department statistics
 *
 * @param int $chefId The chef de département ID
 * @param string $academicYear The academic year
 */
function getDepartmentStatisticsAPI($chefId, $academicYear) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a department head
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'chef de departement') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    if (!$chefId || !$academicYear) {
        jsonResponse(['error' => 'Chef ID and academic year are required'], 400);
        exit;
    }

    $statistics = getDepartmentStatistics($chefId, $academicYear);

    if (isset($statistics['error'])) {
        jsonResponse(['error' => $statistics['error']], 500);
        exit;
    }

    jsonResponse(['success' => true, 'data' => $statistics], 200);
}

/**
 * API endpoint to get department assignments
 *
 * @param int $chefId The chef de département ID
 * @param string $academicYear The academic year
 */
function getDepartmentAssignmentsAPI($chefId, $academicYear) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a department head
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'chef de departement') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    if (!$chefId || !$academicYear) {
        jsonResponse(['error' => 'Chef ID and academic year are required'], 400);
        exit;
    }

    $assignments = getDepartmentAssignments($chefId, $academicYear);

    if (isset($assignments['error'])) {
        jsonResponse(['error' => $assignments['error']], 500);
        exit;
    }

    jsonResponse(['success' => true, 'data' => $assignments], 200);
}

/**
 * API endpoint to get professor choices
 *
 * @param int $chefId The chef de département ID
 * @param string $academicYear The academic year
 */
function getDepartmentProfessorChoicesAPI($chefId, $academicYear) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a department head
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'chef de departement') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    if (!$chefId || !$academicYear) {
        jsonResponse(['error' => 'Chef ID and academic year are required'], 400);
        exit;
    }

    $choices = getDepartmentProfessorChoices($chefId, $academicYear);

    if (isset($choices['error'])) {
        jsonResponse(['error' => $choices['error']], 500);
        exit;
    }

    jsonResponse(['success' => true, 'data' => $choices], 200);
}

/**
 * API endpoint to get department workload
 *
 * @param int $chefId The chef de département ID
 * @param string $academicYear The academic year
 */
function getDepartmentWorkloadAPI($chefId, $academicYear) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a department head
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'chef de departement') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    if (!$chefId || !$academicYear) {
        jsonResponse(['error' => 'Chef ID and academic year are required'], 400);
        exit;
    }

    $workload = getDepartmentWorkload($chefId, $academicYear);

    if (isset($workload['error'])) {
        jsonResponse(['error' => $workload['error']], 500);
        exit;
    }

    jsonResponse(['success' => true, 'data' => $workload], 200);
}

/**
 * API endpoint to get department observations
 *
 * @param int $chefId The chef de département ID
 * @param string $academicYear The academic year
 */
function getDepartmentObservationsAPI($chefId, $academicYear) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a department head
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'chef de departement') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    if (!$chefId || !$academicYear) {
        jsonResponse(['error' => 'Chef ID and academic year are required'], 400);
        exit;
    }

    $observations = getDepartmentObservations($chefId, $academicYear);

    if (isset($observations['error'])) {
        jsonResponse(['error' => $observations['error']], 500);
        exit;
    }

    jsonResponse(['success' => true, 'data' => $observations], 200);
}

/**
 * API endpoint to get department modules
 *
 * @param int $chefId The chef de département ID
 * @param string $academicYear The academic year
 */
function getDepartmentModulesAPI($chefId, $academicYear) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a department head
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'chef de departement') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    if (!$chefId || !$academicYear) {
        jsonResponse(['error' => 'Chef ID and academic year are required'], 400);
        exit;
    }

    $modules = getDepartmentModules($chefId, $academicYear);

    if (isset($modules['error'])) {
        jsonResponse(['error' => $modules['error']], 500);
        exit;
    }

    jsonResponse(['success' => true, 'data' => $modules], 200);
}

/**
 * API endpoint to get complete history data for a department head
 *
 * @param int $chefId The chef de département ID
 * @param string $academicYear The academic year
 */
function getCompleteChefHistoryAPI($chefId, $academicYear) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a department head
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'chef de departement') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    if (!$chefId || !$academicYear) {
        jsonResponse(['error' => 'Chef ID and academic year are required'], 400);
        exit;
    }

    $history = getCompleteChefHistory($chefId, $academicYear);

    if (isset($history['error'])) {
        jsonResponse(['error' => $history['error']], 500);
        exit;
    }

    jsonResponse(['success' => true, 'data' => $history], 200);
}

/**
 * API endpoint to get department information
 *
 * @param int $chefId The chef de département ID
 */
function getDepartmentInfoAPI($chefId) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a department head
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'chef de departement') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    if (!$chefId) {
        jsonResponse(['error' => 'Chef ID is required'], 400);
        exit;
    }

    $department = getDepartmentInfoByChef($chefId);

    if (isset($department['error'])) {
        jsonResponse(['error' => $department['error']], 500);
        exit;
    }

    jsonResponse(['success' => true, 'data' => $department], 200);
}

?>
