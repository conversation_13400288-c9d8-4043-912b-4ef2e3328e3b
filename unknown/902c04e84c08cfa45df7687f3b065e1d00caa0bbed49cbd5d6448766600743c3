/* Teacher-specific styles */

/* Additional styles for teacher pages */
.teacher-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.teacher-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 30px;
    overflow: hidden;
}

.teacher-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 25px;
    border-radius: 20px 20px 0 0;
}

/* Module and UE cards */
.module-item {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 15px;
    padding: 20px;
    margin: 15px 0;
    border-left: 5px solid #ff6b6b;
    transition: all 0.3s ease;
}

.module-item:hover {
    transform: translateX(10px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.ue-item {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    border-left: 4px solid #6c5ce7;
}

/* Grade and history items */
.grade-item {
    background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    border-left: 4px solid #6c5ce7;
}

/* Statistics cards */
.teacher-stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin: 10px;
    text-align: center;
    transition: transform 0.3s ease;
}

.teacher-stats-card:hover {
    transform: translateY(-5px);
}

/* Badges and labels */
.type-badge {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.85em;
    margin: 2px;
    display: inline-block;
}

.session-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8em;
    font-weight: 500;
}

.session-normale {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
}

.session-rattrapage {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #333;
}

/* Timeline styles */
.timeline-container {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    padding-left: 30px;
    margin-bottom: 20px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 10px;
    top: 5px;
    width: 10px;
    height: 10px;
    background: #667eea;
    border-radius: 50%;
}

.timeline-item::after {
    content: '';
    position: absolute;
    left: 14px;
    top: 15px;
    width: 2px;
    height: calc(100% + 10px);
    background: #e0e0e0;
}

.timeline-item:last-child::after {
    display: none;
}

/* Loading and empty states */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.empty-state {
    text-align: center;
    padding: 50px;
    color: #666;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #ccc;
}

/* Buttons */
.teacher-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 25px;
    padding: 10px 25px;
    color: white;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.teacher-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    color: white;
    text-decoration: none;
}

.teacher-btn-secondary {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
}

.teacher-btn-secondary:hover {
    color: #333;
}

/* Responsive design */
@media (max-width: 768px) {
    .teacher-container {
        padding: 10px 0;
    }
    
    .teacher-card {
        margin: 15px;
        border-radius: 15px;
    }
    
    .teacher-header {
        padding: 20px;
        border-radius: 15px 15px 0 0;
    }
    
    .module-item,
    .ue-item,
    .grade-item {
        margin: 10px 0;
        padding: 15px;
    }
    
    .teacher-stats-card {
        margin: 5px;
        padding: 15px;
    }
    
    .timeline-item {
        padding-left: 20px;
    }
    
    .timeline-item::before {
        left: 5px;
    }
    
    .timeline-item::after {
        left: 9px;
    }
}

/* Print styles */
@media print {
    .teacher-container {
        background: white !important;
        padding: 0 !important;
    }
    
    .teacher-card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        margin: 10px 0 !important;
    }
    
    .teacher-header {
        background: #f8f9fa !important;
        color: #333 !important;
    }
    
    .teacher-btn {
        display: none !important;
    }
}
