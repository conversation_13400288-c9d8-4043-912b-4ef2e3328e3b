<?php
/**
 * Test file for debugging the affectation functionality
 */

// Start session
session_start();

// Set test session data
$_SESSION['user'] = [
    'username' => 'test_coordinator',
    'role' => 'coordinateur',
    'filiere_id' => 1,
    'filiere_name' => 'Informatique'
];

echo "<h1>Testing Affectation Functionality</h1>";

// Test database connection
require_once 'config/db.php';
$conn = getConnection();

if (!$conn) {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
    exit;
} else {
    echo "<p style='color: green;'>✅ Database connection successful</p>";
}

// Test if required tables exist
$tables = ['filiere', 'module', 'uniteenseignement', 'enseignant', 'affectation', 'ue_vacantes'];
foreach ($tables as $table) {
    $result = mysqli_query($conn, "SHOW TABLES LIKE '$table'");
    if (mysqli_num_rows($result) > 0) {
        echo "<p style='color: green;'>✅ Table '$table' exists</p>";
    } else {
        echo "<p style='color: red;'>❌ Table '$table' does not exist</p>";
    }
}

// Test model functions
require_once 'model/affecterUEVacataireModel.php';

echo "<h2>Testing Model Functions</h2>";

// Test getCurrentAcademicYear
$academicYear = getCurrentAcademicYear();
echo "<p>Current academic year: <strong>$academicYear</strong></p>";

// Test ensureUeVacantesTableExists
$tableCreated = ensureUeVacantesTableExists();
if ($tableCreated) {
    echo "<p style='color: green;'>✅ ue_vacantes table ensured</p>";
} else {
    echo "<p style='color: red;'>❌ Failed to ensure ue_vacantes table</p>";
}

// Test getVacantUEsByFiliere
echo "<h3>Testing getVacantUEsByFiliere(1)</h3>";
$vacantUEs = getVacantUEsByFiliere(1);
if (isset($vacantUEs['error'])) {
    echo "<p style='color: red;'>❌ Error: " . $vacantUEs['error'] . "</p>";
} else {
    echo "<p style='color: green;'>✅ Found " . count($vacantUEs) . " vacant UEs</p>";
    if (count($vacantUEs) > 0) {
        echo "<pre>" . print_r($vacantUEs[0], true) . "</pre>";
    }
}

// Test getAvailableVacatairesByFiliere
echo "<h3>Testing getAvailableVacatairesByFiliere(1)</h3>";
$vacataires = getAvailableVacatairesByFiliere(1);
if (isset($vacataires['error'])) {
    echo "<p style='color: red;'>❌ Error: " . $vacataires['error'] . "</p>";
} else {
    echo "<p style='color: green;'>✅ Found " . count($vacataires) . " vacataires</p>";
    if (count($vacataires) > 0) {
        echo "<pre>" . print_r($vacataires[0], true) . "</pre>";
    }
}

// Test getAssignmentStatistics
echo "<h3>Testing getAssignmentStatistics(1)</h3>";
$stats = getAssignmentStatistics(1);
if (isset($stats['error'])) {
    echo "<p style='color: red;'>❌ Error: " . $stats['error'] . "</p>";
} else {
    echo "<p style='color: green;'>✅ Statistics retrieved</p>";
    echo "<pre>" . print_r($stats, true) . "</pre>";
}

// Test API endpoints
echo "<h2>Testing API Endpoints</h2>";

$baseUrl = 'http://localhost/Web-Projet-API/route/affecterUEVacataireRoute.php';

$endpoints = [
    'getStatistics' => $baseUrl . '?action=getStatistics',
    'getVacantUEs' => $baseUrl . '?action=getVacantUEs',
    'getVacataires' => $baseUrl . '?action=getVacataires'
];

foreach ($endpoints as $name => $url) {
    echo "<h4>Testing $name</h4>";
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => "Cookie: " . session_name() . "=" . session_id()
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    if ($response === false) {
        echo "<p style='color: red;'>❌ Failed to fetch $name</p>";
    } else {
        $data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            if (isset($data['error'])) {
                echo "<p style='color: red;'>❌ API Error: " . $data['error'] . "</p>";
            } else {
                echo "<p style='color: green;'>✅ $name successful</p>";
                if (isset($data['data'])) {
                    echo "<p>Data count: " . (is_array($data['data']) ? count($data['data']) : 'N/A') . "</p>";
                }
            }
        } else {
            echo "<p style='color: red;'>❌ Invalid JSON response for $name</p>";
            echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
        }
    }
}

mysqli_close($conn);
echo "<h2>Test Complete</h2>";
?>
