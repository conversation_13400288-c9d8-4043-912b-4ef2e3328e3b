<?php
/**
 * Route file for teacher history functionality
 * 
 * This file handles all API routes related to teacher history,
 * including academic years, UE assignments, modules, statistics,
 * and grade history.
 */

// Include the controller
require_once '../controller/teacherHistoryController.php';

// Set content type to JSON
header('Content-Type: application/json');

// Enable CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// The controller handles the routing logic
// This file just includes the controller which processes the request
?>
