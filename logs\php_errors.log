[26-Apr-2025 18:13:02 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-04-26 18:13:02
[26-Apr-2025 18:13:02 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL>
[26-Apr-2025 18:13:02 Europe/Berlin] Code de réinitialisation: 3F0XYS
[26-Apr-2025 18:13:02 Europe/Berlin] Email simulé (MAIL_SIMULATE=true)
[26-Apr-2025 18:13:02 Europe/Berlin] À: <EMAIL>
[26-Apr-2025 18:13:02 Europe/Berlin] Sujet: Réinitialisation de votre mot de passe ENSAH
[26-Apr-2025 18:13:02 Europe/Berlin] Message: 
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-heigh...
[26-Apr-2025 18:13:02 Europe/Berlin] Code de débogage inclus dans la réponse: 3F0XYS
[26-Apr-2025 18:16:00 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-04-26 18:16:00
[26-Apr-2025 18:16:00 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL>
[26-Apr-2025 18:16:00 Europe/Berlin] Code de réinitialisation: M3TJEW
[26-Apr-2025 18:16:21 Europe/Berlin] ERREUR SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 18:16:21 Europe/Berlin] Trace: #0 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(2118): PHPMailer\PHPMailer\PHPMailer->smtpConnect(Array)
#1 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1747): PHPMailer\PHPMailer\PHPMailer->smtpSend('Date: Sat, 26 A...', '\r\n    <html>\r\n ...')
#2 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1564): PHPMailer\PHPMailer\PHPMailer->postSend()
#3 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(113): PHPMailer\PHPMailer\PHPMailer->send()
#4 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(167): sendEmail('salmachiboub08@...', 'R\xC3\xA9initialisati...', '\n    <html>\n   ...')
#5 C:\xampp\htdocs\Projet-Web\controller\passwordResetController.php(36): sendPasswordResetEmail('salmachiboub08@...', 'M3TJEW')
#6 C:\xampp\htdocs\Projet-Web\route\passwordResetRoute.php(30): requestPasswordReset('CN12345')
#7 C:\xampp\htdocs\Projet-Web\route\reset-password-index.php(7): require_once('C:\\xampp\\htdocs...')
#8 {main}
[26-Apr-2025 18:21:08 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-04-26 18:21:08
[26-Apr-2025 18:21:08 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL>
[26-Apr-2025 18:21:08 Europe/Berlin] Code de réinitialisation: 6MVE0R
[26-Apr-2025 18:21:29 Europe/Berlin] DEBUG SMTP: SMTP ERROR: Failed to connect to server: Une tentative de connexion a échoué car le parti connecté n’a pas répondu convenablement au-delà d’une certaine durée ou une connexion établie a échoué car l’hôte de connexion n’a pas répondu (10060)
[26-Apr-2025 18:21:29 Europe/Berlin] DEBUG SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 18:21:29 Europe/Berlin] ERREUR SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 18:21:29 Europe/Berlin] Trace: #0 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(2118): PHPMailer\PHPMailer\PHPMailer->smtpConnect(Array)
#1 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1747): PHPMailer\PHPMailer\PHPMailer->smtpSend('Date: Sat, 26 A...', '\r\n    <html>\r\n ...')
#2 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1564): PHPMailer\PHPMailer\PHPMailer->postSend()
#3 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(141): PHPMailer\PHPMailer\PHPMailer->send()
#4 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(195): sendEmail('salmachiboub08@...', 'R\xC3\xA9initialisati...', '\n    <html>\n   ...')
#5 C:\xampp\htdocs\Projet-Web\controller\passwordResetController.php(36): sendPasswordResetEmail('salmachiboub08@...', '6MVE0R')
#6 C:\xampp\htdocs\Projet-Web\route\passwordResetRoute.php(30): requestPasswordReset('CN12345')
#7 C:\xampp\htdocs\Projet-Web\route\reset-password-index.php(7): require_once('C:\\xampp\\htdocs...')
#8 {main}
[26-Apr-2025 18:23:41 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-04-26 18:23:41
[26-Apr-2025 18:23:41 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL>
[26-Apr-2025 18:23:41 Europe/Berlin] Code de réinitialisation: E7OQM4
[26-Apr-2025 18:24:02 Europe/Berlin] DEBUG SMTP: SMTP ERROR: Failed to connect to server: Une tentative de connexion a échoué car le parti connecté n’a pas répondu convenablement au-delà d’une certaine durée ou une connexion établie a échoué car l’hôte de connexion n’a pas répondu (10060)
[26-Apr-2025 18:24:02 Europe/Berlin] DEBUG SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 18:24:02 Europe/Berlin] ERREUR SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 18:24:02 Europe/Berlin] Trace: #0 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(2118): PHPMailer\PHPMailer\PHPMailer->smtpConnect(Array)
#1 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1747): PHPMailer\PHPMailer\PHPMailer->smtpSend('Date: Sat, 26 A...', '\r\n    <html>\r\n ...')
#2 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1564): PHPMailer\PHPMailer\PHPMailer->postSend()
#3 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(141): PHPMailer\PHPMailer\PHPMailer->send()
#4 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(195): sendEmail('salmachiboub08@...', 'R\xC3\xA9initialisati...', '\n    <html>\n   ...')
#5 C:\xampp\htdocs\Projet-Web\controller\passwordResetController.php(36): sendPasswordResetEmail('salmachiboub08@...', 'E7OQM4')
#6 C:\xampp\htdocs\Projet-Web\route\passwordResetRoute.php(30): requestPasswordReset('CN12345')
#7 C:\xampp\htdocs\Projet-Web\route\reset-password-index.php(7): require_once('C:\\xampp\\htdocs...')
#8 {main}
[26-Apr-2025 18:27:35 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-04-26 18:27:35
[26-Apr-2025 18:27:35 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL>
[26-Apr-2025 18:27:35 Europe/Berlin] Code de réinitialisation: 9W2SRE
[26-Apr-2025 18:27:35 Europe/Berlin] Email simulé (mode simulation)
[26-Apr-2025 18:27:35 Europe/Berlin] À: <EMAIL>
[26-Apr-2025 18:27:35 Europe/Berlin] Sujet: Réinitialisation de votre mot de passe ENSAH
[26-Apr-2025 18:27:35 Europe/Berlin] Message: 
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-heigh...
[26-Apr-2025 18:27:35 Europe/Berlin] Code de débogage inclus dans la réponse: 9W2SRE
[26-Apr-2025 18:38:55 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-04-26 18:38:55
[26-Apr-2025 18:38:55 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL>
[26-Apr-2025 18:38:55 Europe/Berlin] Code de réinitialisation: 24HYHN
[26-Apr-2025 18:38:55 Europe/Berlin] Tentative d'envoi d'email via Mailtrap à: <EMAIL>
[26-Apr-2025 18:38:55 Europe/Berlin] Code de réinitialisation: 24HYHN
[26-Apr-2025 18:39:37 Europe/Berlin] DEBUG SMTP: SMTP ERROR: Failed to connect to server: Une tentative de connexion a échoué car le parti connecté n’a pas répondu convenablement au-delà d’une certaine durée ou une connexion établie a échoué car l’hôte de connexion n’a pas répondu (10060)
[26-Apr-2025 18:39:37 Europe/Berlin] DEBUG SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 18:39:37 Europe/Berlin] ERREUR MAILTRAP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 18:39:37 Europe/Berlin] Trace: #0 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(2118): PHPMailer\PHPMailer\PHPMailer->smtpConnect(Array)
#1 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1747): PHPMailer\PHPMailer\PHPMailer->smtpSend('Date: Sat, 26 A...', '\r\n    <html>\r\n ...')
#2 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1564): PHPMailer\PHPMailer\PHPMailer->postSend()
#3 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(246): PHPMailer\PHPMailer\PHPMailer->send()
#4 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(83): sendEmailViaMailtrap('salmachiboub004...', 'R\xC3\xA9initialisati...', '\n    <html>\n   ...', Array)
#5 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(292): sendEmail('salmachiboub004...', 'R\xC3\xA9initialisati...', '\n    <html>\n   ...')
#6 C:\xampp\htdocs\Projet-Web\test_mailtrap.php(96): sendPasswordResetEmail('salmachiboub004...', '24HYHN')
#7 {main}
[26-Apr-2025 19:02:17 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-04-26 19:02:17
[26-Apr-2025 19:02:17 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL>
[26-Apr-2025 19:02:17 Europe/Berlin] Code de réinitialisation: 1PYPXZ
[26-Apr-2025 19:02:38 Europe/Berlin] DEBUG SMTP: SMTP ERROR: Failed to connect to server: Une tentative de connexion a échoué car le parti connecté n’a pas répondu convenablement au-delà d’une certaine durée ou une connexion établie a échoué car l’hôte de connexion n’a pas répondu (10060)
[26-Apr-2025 19:02:38 Europe/Berlin] DEBUG SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 19:02:38 Europe/Berlin] ERREUR SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 19:02:38 Europe/Berlin] Trace: #0 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(2118): PHPMailer\PHPMailer\PHPMailer->smtpConnect(Array)
#1 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1747): PHPMailer\PHPMailer\PHPMailer->smtpSend('Date: Sat, 26 A...', '\r\n    <html>\r\n ...')
#2 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1564): PHPMailer\PHPMailer\PHPMailer->postSend()
#3 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(141): PHPMailer\PHPMailer\PHPMailer->send()
#4 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(195): sendEmail('salmachiboub08@...', 'R\xC3\xA9initialisati...', '\n    <html>\n   ...')
#5 C:\xampp\htdocs\Projet-Web\controller\passwordResetController.php(36): sendPasswordResetEmail('salmachiboub08@...', '1PYPXZ')
#6 C:\xampp\htdocs\Projet-Web\route\passwordResetRoute.php(30): requestPasswordReset('CN12345')
#7 C:\xampp\htdocs\Projet-Web\route\reset-password-index.php(7): require_once('C:\\xampp\\htdocs...')
#8 {main}
[26-Apr-2025 19:06:53 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-04-26 19:06:53
[26-Apr-2025 19:06:53 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL>
[26-Apr-2025 19:06:53 Europe/Berlin] Code de réinitialisation: KILTLY
[26-Apr-2025 19:07:09 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-04-26 19:07:09
[26-Apr-2025 19:07:09 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL>
[26-Apr-2025 19:07:09 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-04-26 19:07:09
[26-Apr-2025 19:07:09 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL>
[26-Apr-2025 19:07:11 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-04-26 19:07:11
[26-Apr-2025 19:07:11 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL>
[26-Apr-2025 19:07:14 Europe/Berlin] DEBUG SMTP: SMTP ERROR: Failed to connect to server: Une tentative de connexion a échoué car le parti connecté n’a pas répondu convenablement au-delà d’une certaine durée ou une connexion établie a échoué car l’hôte de connexion n’a pas répondu (10060)
[26-Apr-2025 19:07:14 Europe/Berlin] DEBUG SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 19:07:14 Europe/Berlin] ERREUR SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 19:07:14 Europe/Berlin] Trace: #0 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(2118): PHPMailer\PHPMailer\PHPMailer->smtpConnect(Array)
#1 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1747): PHPMailer\PHPMailer\PHPMailer->smtpSend('Date: Sat, 26 A...', '\r\n    <html>\r\n ...')
#2 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1564): PHPMailer\PHPMailer\PHPMailer->postSend()
#3 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(141): PHPMailer\PHPMailer\PHPMailer->send()
#4 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(195): sendEmail('salmachiboub08@...', 'R\xC3\xA9initialisati...', '\n    <html>\n   ...')
#5 C:\xampp\htdocs\Projet-Web\test_smtp.php(99): sendPasswordResetEmail('salmachiboub08@...', 'KILTLY')
#6 {main}
[26-Apr-2025 19:07:14 Europe/Berlin] Code de réinitialisation: 7GVSND
[26-Apr-2025 19:07:35 Europe/Berlin] DEBUG SMTP: SMTP ERROR: Failed to connect to server: Une tentative de connexion a échoué car le parti connecté n’a pas répondu convenablement au-delà d’une certaine durée ou une connexion établie a échoué car l’hôte de connexion n’a pas répondu (10060)
[26-Apr-2025 19:07:35 Europe/Berlin] DEBUG SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 19:07:35 Europe/Berlin] ERREUR SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 19:07:35 Europe/Berlin] Trace: #0 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(2118): PHPMailer\PHPMailer\PHPMailer->smtpConnect(Array)
#1 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1747): PHPMailer\PHPMailer\PHPMailer->smtpSend('Date: Sat, 26 A...', '\r\n    <html>\r\n ...')
#2 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1564): PHPMailer\PHPMailer\PHPMailer->postSend()
#3 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(141): PHPMailer\PHPMailer\PHPMailer->send()
#4 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(195): sendEmail('salmachiboub08@...', 'R\xC3\xA9initialisati...', '\n    <html>\n   ...')
#5 C:\xampp\htdocs\Projet-Web\test_smtp.php(99): sendPasswordResetEmail('salmachiboub08@...', '7GVSND')
#6 {main}
[26-Apr-2025 19:07:35 Europe/Berlin] Code de réinitialisation: SLQC57
[26-Apr-2025 19:07:45 Europe/Berlin] DEBUG SMTP: SMTP ERROR: Failed to connect to server: Une connexion établie a été abandonnée par un logiciel de votre ordinateur hôte (10053)
[26-Apr-2025 19:07:45 Europe/Berlin] DEBUG SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 19:07:45 Europe/Berlin] ERREUR SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 19:07:45 Europe/Berlin] Trace: #0 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(2118): PHPMailer\PHPMailer\PHPMailer->smtpConnect(Array)
#1 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1747): PHPMailer\PHPMailer\PHPMailer->smtpSend('Date: Sat, 26 A...', '\r\n    <html>\r\n ...')
#2 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1564): PHPMailer\PHPMailer\PHPMailer->postSend()
#3 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(141): PHPMailer\PHPMailer\PHPMailer->send()
#4 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(195): sendEmail('salmachiboub08@...', 'R\xC3\xA9initialisati...', '\n    <html>\n   ...')
#5 C:\xampp\htdocs\Projet-Web\test_smtp.php(99): sendPasswordResetEmail('salmachiboub08@...', 'SLQC57')
#6 {main}
[26-Apr-2025 19:07:45 Europe/Berlin] Code de réinitialisation: UX2Z0H
[26-Apr-2025 19:07:45 Europe/Berlin] DEBUG SMTP: SMTP ERROR: Failed to connect to server: php_network_getaddresses: getaddrinfo for smtp.gmail.com failed: H�te inconnu.  (0)
[26-Apr-2025 19:07:45 Europe/Berlin] DEBUG SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 19:07:45 Europe/Berlin] ERREUR SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 19:07:45 Europe/Berlin] Trace: #0 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(2118): PHPMailer\PHPMailer\PHPMailer->smtpConnect(Array)
#1 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1747): PHPMailer\PHPMailer\PHPMailer->smtpSend('Date: Sat, 26 A...', '\r\n    <html>\r\n ...')
#2 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1564): PHPMailer\PHPMailer\PHPMailer->postSend()
#3 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(141): PHPMailer\PHPMailer\PHPMailer->send()
#4 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(195): sendEmail('salmachiboub08@...', 'R\xC3\xA9initialisati...', '\n    <html>\n   ...')
#5 C:\xampp\htdocs\Projet-Web\test_smtp.php(99): sendPasswordResetEmail('salmachiboub08@...', 'UX2Z0H')
#6 {main}
[26-Apr-2025 19:10:22 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-04-26 19:10:22
[26-Apr-2025 19:10:22 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL>
[26-Apr-2025 19:10:22 Europe/Berlin] Code de réinitialisation: 8R0ZCV
[26-Apr-2025 19:10:22 Europe/Berlin] Tentative de connexion au serveur SMTP: smtp.gmail.com:465
[26-Apr-2025 19:10:22 Europe/Berlin] Utilisateur SMTP: <EMAIL>
[26-Apr-2025 19:10:22 Europe/Berlin] Encryption: ssl
[26-Apr-2025 19:10:45 Europe/Berlin] DEBUG SMTP: SMTP ERROR: Failed to connect to server: Une tentative de connexion a échoué car le parti connecté n’a pas répondu convenablement au-delà d’une certaine durée ou une connexion établie a échoué car l’hôte de connexion n’a pas répondu (10060)
[26-Apr-2025 19:10:45 Europe/Berlin] DEBUG SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 19:10:45 Europe/Berlin] ERREUR SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 19:10:45 Europe/Berlin] Trace: #0 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(2118): PHPMailer\PHPMailer\PHPMailer->smtpConnect(Array)
#1 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1747): PHPMailer\PHPMailer\PHPMailer->smtpSend('Date: Sat, 26 A...', '\r\n    <html>\r\n ...')
#2 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1564): PHPMailer\PHPMailer\PHPMailer->postSend()
#3 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(155): PHPMailer\PHPMailer\PHPMailer->send()
#4 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(223): sendEmail('salmachiboub08@...', 'R\xC3\xA9initialisati...', '\n    <html>\n   ...')
#5 C:\xampp\htdocs\Projet-Web\test_smtp.php(99): sendPasswordResetEmail('salmachiboub08@...', '8R0ZCV')
#6 {main}
[26-Apr-2025 19:10:45 Europe/Berlin] CONSEIL: Vérifiez que le serveur SMTP est accessible et que le port n'est pas bloqué par un pare-feu.
[26-Apr-2025 19:14:28 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-04-26 19:14:28
[26-Apr-2025 19:14:28 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL>
[26-Apr-2025 19:14:28 Europe/Berlin] Code de réinitialisation: S28WLW
[26-Apr-2025 19:14:28 Europe/Berlin] Tentative de connexion au serveur SMTP: smtp.gmail.com:465
[26-Apr-2025 19:14:28 Europe/Berlin] Utilisateur SMTP: <EMAIL>
[26-Apr-2025 19:14:28 Europe/Berlin] Encryption: ssl
[26-Apr-2025 19:14:38 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-04-26 19:14:38
[26-Apr-2025 19:14:38 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL>
[26-Apr-2025 19:14:49 Europe/Berlin] DEBUG SMTP: SMTP ERROR: Failed to connect to server: Une tentative de connexion a échoué car le parti connecté n’a pas répondu convenablement au-delà d’une certaine durée ou une connexion établie a échoué car l’hôte de connexion n’a pas répondu (10060)
[26-Apr-2025 19:14:49 Europe/Berlin] DEBUG SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 19:14:49 Europe/Berlin] ERREUR SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 19:14:49 Europe/Berlin] Trace: #0 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(2118): PHPMailer\PHPMailer\PHPMailer->smtpConnect(Array)
#1 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1747): PHPMailer\PHPMailer\PHPMailer->smtpSend('Date: Sat, 26 A...', '\r\n    <html>\r\n ...')
#2 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1564): PHPMailer\PHPMailer\PHPMailer->postSend()
#3 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(155): PHPMailer\PHPMailer\PHPMailer->send()
#4 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(223): sendEmail('salmachiboub004...', 'R\xC3\xA9initialisati...', '\n    <html>\n   ...')
#5 C:\xampp\htdocs\Projet-Web\test_smtp.php(99): sendPasswordResetEmail('salmachiboub004...', 'S28WLW')
#6 {main}
[26-Apr-2025 19:14:49 Europe/Berlin] CONSEIL: Vérifiez que le serveur SMTP est accessible et que le port n'est pas bloqué par un pare-feu.
[26-Apr-2025 19:14:49 Europe/Berlin] Code de réinitialisation: XQDDG5
[26-Apr-2025 19:14:49 Europe/Berlin] Tentative de connexion au serveur SMTP: smtp.gmail.com:465
[26-Apr-2025 19:14:49 Europe/Berlin] Utilisateur SMTP: <EMAIL>
[26-Apr-2025 19:14:49 Europe/Berlin] Encryption: ssl
[26-Apr-2025 19:15:10 Europe/Berlin] DEBUG SMTP: SMTP ERROR: Failed to connect to server: Une tentative de connexion a échoué car le parti connecté n’a pas répondu convenablement au-delà d’une certaine durée ou une connexion établie a échoué car l’hôte de connexion n’a pas répondu (10060)
[26-Apr-2025 19:15:10 Europe/Berlin] DEBUG SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 19:15:10 Europe/Berlin] ERREUR SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 19:15:10 Europe/Berlin] Trace: #0 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(2118): PHPMailer\PHPMailer\PHPMailer->smtpConnect(Array)
#1 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1747): PHPMailer\PHPMailer\PHPMailer->smtpSend('Date: Sat, 26 A...', '\r\n    <html>\r\n ...')
#2 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1564): PHPMailer\PHPMailer\PHPMailer->postSend()
#3 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(155): PHPMailer\PHPMailer\PHPMailer->send()
#4 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(223): sendEmail('salmachiboub004...', 'R\xC3\xA9initialisati...', '\n    <html>\n   ...')
#5 C:\xampp\htdocs\Projet-Web\test_smtp.php(99): sendPasswordResetEmail('salmachiboub004...', 'XQDDG5')
#6 {main}
[26-Apr-2025 19:15:10 Europe/Berlin] CONSEIL: Vérifiez que le serveur SMTP est accessible et que le port n'est pas bloqué par un pare-feu.
[26-Apr-2025 19:30:59 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-04-26 19:30:59
[26-Apr-2025 19:30:59 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL>
[26-Apr-2025 19:30:59 Europe/Berlin] Code de réinitialisation: 8K8XPS
[26-Apr-2025 19:30:59 Europe/Berlin] Tentative de connexion au serveur SMTP: smtp.gmail.com:587
[26-Apr-2025 19:30:59 Europe/Berlin] Utilisateur SMTP: <EMAIL>
[26-Apr-2025 19:30:59 Europe/Berlin] Encryption: tls
[26-Apr-2025 19:31:20 Europe/Berlin] DEBUG SMTP: SMTP ERROR: Failed to connect to server: Une tentative de connexion a échoué car le parti connecté n’a pas répondu convenablement au-delà d’une certaine durée ou une connexion établie a échoué car l’hôte de connexion n’a pas répondu (10060)
[26-Apr-2025 19:31:20 Europe/Berlin] DEBUG SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 19:31:20 Europe/Berlin] ERREUR SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 19:31:20 Europe/Berlin] Trace: #0 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(2118): PHPMailer\PHPMailer\PHPMailer->smtpConnect(Array)
#1 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1747): PHPMailer\PHPMailer\PHPMailer->smtpSend('Date: Sat, 26 A...', '\r\n    <html>\r\n ...')
#2 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1564): PHPMailer\PHPMailer\PHPMailer->postSend()
#3 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(155): PHPMailer\PHPMailer\PHPMailer->send()
#4 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(223): sendEmail('salmachiboub004...', 'R\xC3\xA9initialisati...', '\n    <html>\n   ...')
#5 C:\xampp\htdocs\Projet-Web\test_smtp.php(99): sendPasswordResetEmail('salmachiboub004...', '8K8XPS')
#6 {main}
[26-Apr-2025 19:31:20 Europe/Berlin] CONSEIL: Vérifiez que le serveur SMTP est accessible et que le port n'est pas bloqué par un pare-feu.
[26-Apr-2025 19:31:23 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-04-26 19:31:23
[26-Apr-2025 19:31:23 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL>
[26-Apr-2025 19:31:23 Europe/Berlin] Code de réinitialisation: LS056K
[26-Apr-2025 19:31:23 Europe/Berlin] Tentative de connexion au serveur SMTP: smtp.gmail.com:587
[26-Apr-2025 19:31:23 Europe/Berlin] Utilisateur SMTP: <EMAIL>
[26-Apr-2025 19:31:23 Europe/Berlin] Encryption: tls
[26-Apr-2025 19:31:44 Europe/Berlin] DEBUG SMTP: SMTP ERROR: Failed to connect to server: Une tentative de connexion a échoué car le parti connecté n’a pas répondu convenablement au-delà d’une certaine durée ou une connexion établie a échoué car l’hôte de connexion n’a pas répondu (10060)
[26-Apr-2025 19:31:44 Europe/Berlin] DEBUG SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 19:31:44 Europe/Berlin] ERREUR SMTP: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[26-Apr-2025 19:31:44 Europe/Berlin] Trace: #0 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(2118): PHPMailer\PHPMailer\PHPMailer->smtpConnect(Array)
#1 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1747): PHPMailer\PHPMailer\PHPMailer->smtpSend('Date: Sat, 26 A...', '\r\n    <html>\r\n ...')
#2 C:\xampp\htdocs\Projet-Web\vendor\phpmailer\PHPMailer.php(1564): PHPMailer\PHPMailer\PHPMailer->postSend()
#3 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(155): PHPMailer\PHPMailer\PHPMailer->send()
#4 C:\xampp\htdocs\Projet-Web\utils\emailSender.php(223): sendEmail('salmachiboub08@...', 'R\xC3\xA9initialisati...', '\n    <html>\n   ...')
#5 C:\xampp\htdocs\Projet-Web\test_email.php(21): sendPasswordResetEmail('salmachiboub08@...', 'LS056K')
#6 {main}
[26-Apr-2025 19:31:44 Europe/Berlin] CONSEIL: Vérifiez que le serveur SMTP est accessible et que le port n'est pas bloqué par un pare-feu.
[26-Apr-2025 19:56:45 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-04-26 19:56:45
[26-Apr-2025 19:56:45 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL>
[26-Apr-2025 19:56:45 Europe/Berlin] Code de réinitialisation: RQ3SD7
[26-Apr-2025 19:56:45 Europe/Berlin] Email simulé (mode simulation)
[26-Apr-2025 19:56:45 Europe/Berlin] À: <EMAIL>
[26-Apr-2025 19:56:45 Europe/Berlin] Sujet: Réinitialisation de votre mot de passe ENSAH
[26-Apr-2025 19:56:45 Europe/Berlin] Message: 
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-heigh...
[26-Apr-2025 19:56:45 Europe/Berlin] Code de débogage inclus dans la réponse: RQ3SD7
[05-May-2025 01:16:13 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-05 01:16:13
[05-May-2025 01:16:13 Europe/Berlin] Email simulé (MAIL_SIMULATE=true)
[05-May-2025 01:16:13 Europe/Berlin] À: <EMAIL>
[05-May-2025 01:16:13 Europe/Berlin] Sujet: Initialisation de votre mot de passe - UniAdmin
[05-May-2025 01:16:13 Europe/Berlin] Message: 
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-h...
[05-May-2025 01:16:13 Europe/Berlin] Email d'initialisation de mot de passe envoyé à: <EMAIL>
[05-May-2025 01:16:13 Europe/Berlin] Lien de réinitialisation: http://localhost/view/initialize-password.php?token=0fb115649de9f827f830fa57cd1ad3b36e112517bc992f951216c5b53f83123d
[05-May-2025 01:18:18 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-05 01:18:18
[05-May-2025 01:18:18 Europe/Berlin] Email simulé (MAIL_SIMULATE=true)
[05-May-2025 01:18:18 Europe/Berlin] À: <EMAIL>
[05-May-2025 01:18:18 Europe/Berlin] Sujet: Initialisation de votre mot de passe - UniAdmin
[05-May-2025 01:18:18 Europe/Berlin] Message: 
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-h...
[05-May-2025 01:18:18 Europe/Berlin] Email d'initialisation de mot de passe envoyé à: <EMAIL>
[05-May-2025 01:18:19 Europe/Berlin] Lien de réinitialisation: http://localhost/view/initialize-password.php?token=d46c02fbc6837a0a465485bd4a72360a7e0d9cf901da2397e3644efb9c583e7b
[05-May-2025 02:00:48 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-05 02:00:48
[05-May-2025 02:00:48 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[05-May-2025 02:00:48 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-05 02:00:48
[05-May-2025 02:00:48 Europe/Berlin] Configuration SendGrid:
[05-May-2025 02:00:48 Europe/Berlin] API Key: SG.62YP2tv...
[05-May-2025 02:00:48 Europe/Berlin] From Email: <EMAIL>
[05-May-2025 02:00:48 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[05-May-2025 02:00:48 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[05-May-2025 02:00:48 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"Test Email from UniAdmin","content":[{"type":"text\/html","value":"\n        <html>\n        <head>\n            <style>\n                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n                .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n    ...
[05-May-2025 02:00:49 Europe/Berlin] Réponse SendGrid: Code 202 - 
[05-May-2025 02:00:49 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
[05-May-2025 02:19:58 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-05 02:19:58
[05-May-2025 02:19:58 Europe/Berlin] Code de réinitialisation: 
[05-May-2025 02:19:58 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[05-May-2025 02:19:58 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-05 02:19:58
[05-May-2025 02:19:58 Europe/Berlin] Code de réinitialisation: 
[05-May-2025 02:19:58 Europe/Berlin] Configuration SendGrid:
[05-May-2025 02:19:58 Europe/Berlin] API Key: SG.62YP2tv...
[05-May-2025 02:19:58 Europe/Berlin] From Email: <EMAIL>
[05-May-2025 02:19:58 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[05-May-2025 02:19:58 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[05-May-2025 02:19:58 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"R\u00e9initialisation de votre mot de passe ENSAH","content":[{"type":"text\/html","value":"\n    <html>\n    <head>\n        <style>\n            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n            .container { max-width: 600px; margin: 0 auto; padding: 20px; }\...
[05-May-2025 02:19:59 Europe/Berlin] Réponse SendGrid: Code 202 - 
[05-May-2025 02:19:59 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
[05-May-2025 02:19:59 Europe/Berlin] Code de débogage inclus dans la réponse: 
[05-May-2025 02:22:40 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-05 02:22:40
[05-May-2025 02:22:40 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[05-May-2025 02:22:40 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-05 02:22:40
[05-May-2025 02:22:40 Europe/Berlin] Configuration SendGrid:
[05-May-2025 02:22:40 Europe/Berlin] API Key: SG.62YP2tv...
[05-May-2025 02:22:40 Europe/Berlin] From Email: <EMAIL>
[05-May-2025 02:22:40 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[05-May-2025 02:22:40 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[05-May-2025 02:22:40 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"Initialisation de votre mot de passe - UniAdmin","content":[{"type":"text\/html","value":"\r\n    <html>\r\n    <head>\r\n        <style>\r\n            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\r\n            .container { max-width: 600px; margin: 0 auto; padding: ...
[05-May-2025 02:22:41 Europe/Berlin] Réponse SendGrid: Code 202 - 
[05-May-2025 02:22:41 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
[05-May-2025 02:22:41 Europe/Berlin] Email d'initialisation de mot de passe envoyé à: <EMAIL>
[05-May-2025 02:22:41 Europe/Berlin] Lien de réinitialisation: http://localhost/view/initialize-password.php?token=fdb2a423e4498a951b653e13168d440838c05a4a170519f8e01d61b9389386b9
[05-May-2025 02:25:39 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-05 02:25:39
[05-May-2025 02:25:39 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[05-May-2025 02:25:39 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-05 02:25:39
[05-May-2025 02:25:39 Europe/Berlin] Configuration SendGrid:
[05-May-2025 02:25:39 Europe/Berlin] API Key: SG.62YP2tv...
[05-May-2025 02:25:39 Europe/Berlin] From Email: <EMAIL>
[05-May-2025 02:25:39 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[05-May-2025 02:25:39 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[05-May-2025 02:25:39 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"Initialisation de votre mot de passe - UniAdmin","content":[{"type":"text\/html","value":"\r\n    <html>\r\n    <head>\r\n        <style>\r\n            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\r\n            .container { max-width: 600px; margin: 0 auto; padding: ...
[05-May-2025 02:25:40 Europe/Berlin] Réponse SendGrid: Code 202 - 
[05-May-2025 02:25:40 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
[05-May-2025 02:25:40 Europe/Berlin] Email d'initialisation de mot de passe envoyé à: <EMAIL>
[05-May-2025 02:25:40 Europe/Berlin] Lien de réinitialisation: http://localhost/view/initialize-password.php?token=8aef3110719dbc9e8c33dd595c5220327ff6e1eb494a15820a81bbdfc467a898
[05-May-2025 03:09:55 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-05 03:09:55
[05-May-2025 03:09:55 Europe/Berlin] Code de réinitialisation: 
[05-May-2025 03:09:55 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[05-May-2025 03:09:55 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-05 03:09:55
[05-May-2025 03:09:55 Europe/Berlin] Code de réinitialisation: 
[05-May-2025 03:09:55 Europe/Berlin] Configuration SendGrid:
[05-May-2025 03:09:55 Europe/Berlin] API Key: SG.62YP2tv...
[05-May-2025 03:09:55 Europe/Berlin] From Email: <EMAIL>
[05-May-2025 03:09:55 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[05-May-2025 03:09:55 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[05-May-2025 03:09:55 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"R\u00e9initialisation de votre mot de passe ENSAH","content":[{"type":"text\/html","value":"\n    <html>\n    <head>\n        <style>\n            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n            .container { max-width: 600px; margin: 0 auto; padding: 20px;...
[05-May-2025 03:09:55 Europe/Berlin] Réponse SendGrid: Code 202 - 
[05-May-2025 03:09:55 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
[05-May-2025 03:09:55 Europe/Berlin] Code de débogage inclus dans la réponse: 
[05-May-2025 04:38:30 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-05 04:38:30
[05-May-2025 04:38:30 Europe/Berlin] Code de réinitialisation: 
[05-May-2025 04:38:30 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[05-May-2025 04:38:30 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-05 04:38:30
[05-May-2025 04:38:30 Europe/Berlin] Code de réinitialisation: 
[05-May-2025 04:38:30 Europe/Berlin] Configuration SendGrid:
[05-May-2025 04:38:30 Europe/Berlin] API Key: SG.62YP2tv...
[05-May-2025 04:38:30 Europe/Berlin] From Email: <EMAIL>
[05-May-2025 04:38:30 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[05-May-2025 04:38:30 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[05-May-2025 04:38:30 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"R\u00e9initialisation de votre mot de passe ENSAH","content":[{"type":"text\/html","value":"\n    <html>\n    <head>\n        <style>\n            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n            .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n...
[05-May-2025 04:38:31 Europe/Berlin] Réponse SendGrid: Code 202 - 
[05-May-2025 04:38:31 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
[05-May-2025 04:38:31 Europe/Berlin] Code de débogage inclus dans la réponse: 
[05-May-2025 05:04:55 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-05 05:04:55
[05-May-2025 05:04:55 Europe/Berlin] Code de réinitialisation: 
[05-May-2025 05:04:55 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[05-May-2025 05:04:55 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-05 05:04:55
[05-May-2025 05:04:55 Europe/Berlin] Code de réinitialisation: 
[05-May-2025 05:04:55 Europe/Berlin] Configuration SendGrid:
[05-May-2025 05:04:55 Europe/Berlin] API Key: SG.62YP2tv...
[05-May-2025 05:04:55 Europe/Berlin] From Email: <EMAIL>
[05-May-2025 05:04:55 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[05-May-2025 05:04:55 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[05-May-2025 05:04:55 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"R\u00e9initialisation de votre mot de passe ENSAH","content":[{"type":"text\/html","value":"\n    <html>\n    <head>\n        <style>\n            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n            .container { max-width: 600px; margin: 0 auto; padding: 20px; }\...
[05-May-2025 05:04:56 Europe/Berlin] Réponse SendGrid: Code 202 - 
[05-May-2025 05:04:56 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
[05-May-2025 05:04:56 Europe/Berlin] Code de débogage inclus dans la réponse: 
[05-May-2025 05:14:27 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-05 05:14:27
[05-May-2025 05:14:27 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[05-May-2025 05:14:27 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-05 05:14:27
[05-May-2025 05:14:27 Europe/Berlin] Configuration SendGrid:
[05-May-2025 05:14:27 Europe/Berlin] API Key: SG.62YP2tv...
[05-May-2025 05:14:27 Europe/Berlin] From Email: <EMAIL>
[05-May-2025 05:14:27 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[05-May-2025 05:14:27 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[05-May-2025 05:14:27 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"Initialisation de votre mot de passe - UniAdmin","content":[{"type":"text\/html","value":"\r\n    <html>\r\n    <head>\r\n        <style>\r\n            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\r\n            .container { max-width: 600px; margin: 0 auto; padding: ...
[05-May-2025 05:14:27 Europe/Berlin] Réponse SendGrid: Code 202 - 
[05-May-2025 05:14:27 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
[05-May-2025 05:14:27 Europe/Berlin] Email d'initialisation de mot de passe envoyé à: <EMAIL>
[05-May-2025 05:14:27 Europe/Berlin] Lien de réinitialisation: http://localhost/Projet-Web/view/initialize-password.php?token=c60e38a0c1182a7b70b585677a4203b20281caa85cd290ca0a4d81c1ceec37b0
[05-May-2025 05:20:13 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-05 05:20:13
[05-May-2025 05:20:13 Europe/Berlin] Code de réinitialisation: 
[05-May-2025 05:20:13 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[05-May-2025 05:20:13 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-05 05:20:13
[05-May-2025 05:20:13 Europe/Berlin] Code de réinitialisation: 
[05-May-2025 05:20:13 Europe/Berlin] Configuration SendGrid:
[05-May-2025 05:20:13 Europe/Berlin] API Key: SG.62YP2tv...
[05-May-2025 05:20:13 Europe/Berlin] From Email: <EMAIL>
[05-May-2025 05:20:13 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[05-May-2025 05:20:13 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[05-May-2025 05:20:13 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"R\u00e9initialisation de votre mot de passe ENSAH","content":[{"type":"text\/html","value":"\n    <html>\n    <head>\n        <style>\n            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n            .container { max-width: 600px; margin: 0 auto; padding: 20px; }\...
[05-May-2025 05:20:14 Europe/Berlin] Réponse SendGrid: Code 202 - 
[05-May-2025 05:20:14 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
[05-May-2025 05:20:14 Europe/Berlin] Code de débogage inclus dans la réponse: 
[05-May-2025 13:26:33 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-05 13:26:33
[05-May-2025 13:26:33 Europe/Berlin] Code de réinitialisation: 
[05-May-2025 13:26:33 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[05-May-2025 13:26:33 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-05 13:26:33
[05-May-2025 13:26:33 Europe/Berlin] Code de réinitialisation: 
[05-May-2025 13:26:33 Europe/Berlin] Configuration SendGrid:
[05-May-2025 13:26:33 Europe/Berlin] API Key: SG.62YP2tv...
[05-May-2025 13:26:33 Europe/Berlin] From Email: <EMAIL>
[05-May-2025 13:26:33 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[05-May-2025 13:26:33 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[05-May-2025 13:26:33 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"R\u00e9initialisation de votre mot de passe ENSAH","content":[{"type":"text\/html","value":"\n    <html>\n    <head>\n        <style>\n            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n            .container { max-width: 600px; margin: 0 auto; padding: 20px; ...
[05-May-2025 13:26:34 Europe/Berlin] Réponse SendGrid: Code 202 - 
[05-May-2025 13:26:34 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
[05-May-2025 13:26:34 Europe/Berlin] Code de débogage inclus dans la réponse: 
[10-May-2025 17:50:05 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-10 17:50:05
[10-May-2025 17:50:05 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[10-May-2025 17:50:05 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-10 17:50:05
[10-May-2025 17:50:05 Europe/Berlin] Configuration SendGrid:
[10-May-2025 17:50:05 Europe/Berlin] API Key: SG.62YP2tv...
[10-May-2025 17:50:05 Europe/Berlin] From Email: <EMAIL>
[10-May-2025 17:50:05 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[10-May-2025 17:50:05 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[10-May-2025 17:50:05 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"Initialisation de votre mot de passe - UniAdmin","content":[{"type":"text\/html","value":"\r\n    <html>\r\n    <head>\r\n        <style>\r\n            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\r\n            .container { max-width: 600px; margin: 0 auto; padding: ...
[10-May-2025 17:50:06 Europe/Berlin] Réponse SendGrid: Code 202 - 
[10-May-2025 17:50:06 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
[10-May-2025 17:50:06 Europe/Berlin] Email d'initialisation de mot de passe envoyé à: <EMAIL>
[10-May-2025 17:50:06 Europe/Berlin] Lien de réinitialisation: http://localhost/Projet-Web/view/initialize-password.php?token=f51676fa168b951bb0732aa591fde326092838e14823a1024c664073c3670fe3
[22-May-2025 00:30:02 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-22 00:30:02
[22-May-2025 00:30:02 Europe/Berlin] Code de réinitialisation: 
[22-May-2025 00:30:02 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[22-May-2025 00:30:02 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-22 00:30:02
[22-May-2025 00:30:02 Europe/Berlin] Code de réinitialisation: 
[22-May-2025 00:30:02 Europe/Berlin] Configuration SendGrid:
[22-May-2025 00:30:02 Europe/Berlin] API Key: SG.62YP2tv...
[22-May-2025 00:30:02 Europe/Berlin] From Email: <EMAIL>
[22-May-2025 00:30:02 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[22-May-2025 00:30:02 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[22-May-2025 00:30:02 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"R\u00e9initialisation de votre mot de passe ENSAH","content":[{"type":"text\/html","value":"\n    <html>\n    <head>\n        <style>\n            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n            .container { max-width: 600px; margin: 0 auto; padding: 20px; ...
[22-May-2025 00:30:03 Europe/Berlin] Réponse SendGrid: Code 202 - 
[22-May-2025 00:30:03 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
[22-May-2025 00:30:03 Europe/Berlin] Code de débogage inclus dans la réponse: 
[27-May-2025 05:19:56 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-27 05:19:56
[27-May-2025 05:19:56 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[27-May-2025 05:19:56 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-27 05:19:56
[27-May-2025 05:19:56 Europe/Berlin] Configuration SendGrid:
[27-May-2025 05:19:56 Europe/Berlin] API Key: SG.62YP2tv...
[27-May-2025 05:19:56 Europe/Berlin] From Email: <EMAIL>
[27-May-2025 05:19:56 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[27-May-2025 05:19:56 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[27-May-2025 05:19:56 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"Cr\u00e9ation de votre compte vacataire - ENSAH","content":[{"type":"text\/html","value":"\n    <!DOCTYPE html>\n    <html>\n    <head>\n        <meta charset='UTF-8'>\n        <style>\n            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n            .container ...
[27-May-2025 05:19:57 Europe/Berlin] Réponse SendGrid: Code 202 - 
[27-May-2025 05:19:57 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
[27-May-2025 05:30:32 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-27 05:30:32
[27-May-2025 05:30:32 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[27-May-2025 05:30:32 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-27 05:30:32
[27-May-2025 05:30:32 Europe/Berlin] Configuration SendGrid:
[27-May-2025 05:30:32 Europe/Berlin] API Key: SG.62YP2tv...
[27-May-2025 05:30:32 Europe/Berlin] From Email: <EMAIL>
[27-May-2025 05:30:32 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[27-May-2025 05:30:32 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[27-May-2025 05:30:32 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"Cr\u00e9ation de votre compte vacataire - ENSAH","content":[{"type":"text\/html","value":"\n    <!DOCTYPE html>\n    <html>\n    <head>\n        <meta charset='UTF-8'>\n        <style>\n            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n            .container ...
[27-May-2025 05:30:33 Europe/Berlin] Réponse SendGrid: Code 202 - 
[27-May-2025 05:30:33 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
[27-May-2025 06:34:53 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-27 06:34:53
[27-May-2025 06:34:53 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[27-May-2025 06:34:53 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-27 06:34:53
[27-May-2025 06:34:53 Europe/Berlin] Configuration SendGrid:
[27-May-2025 06:34:53 Europe/Berlin] API Key: SG.62YP2tv...
[27-May-2025 06:34:53 Europe/Berlin] From Email: <EMAIL>
[27-May-2025 06:34:53 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[27-May-2025 06:34:53 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[27-May-2025 06:34:53 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"Cr\u00e9ation de votre compte vacataire - ENSAH","content":[{"type":"text\/html","value":"\n    <!DOCTYPE html>\n    <html>\n    <head>\n        <meta charset='UTF-8'>\n        <style>\n            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n            .container ...
[27-May-2025 06:34:55 Europe/Berlin] Réponse SendGrid: Code 202 - 
[27-May-2025 06:34:55 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
[28-May-2025 03:46:26 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-28 03:46:26
[28-May-2025 03:46:26 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[28-May-2025 03:46:26 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-28 03:46:26
[28-May-2025 03:46:26 Europe/Berlin] Configuration SendGrid:
[28-May-2025 03:46:26 Europe/Berlin] API Key: SG.62YP2tv...
[28-May-2025 03:46:26 Europe/Berlin] From Email: <EMAIL>
[28-May-2025 03:46:26 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[28-May-2025 03:46:26 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[28-May-2025 03:46:26 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"Cr\u00e9ation de votre compte vacataire - ENSAH","content":[{"type":"text\/html","value":"\n    <!DOCTYPE html>\n    <html>\n    <head>\n        <meta charset='UTF-8'>\n        <style>\n            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n            .container { ma...
[28-May-2025 03:46:27 Europe/Berlin] Réponse SendGrid: Code 202 - 
[28-May-2025 03:46:27 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
[28-May-2025 04:35:34 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-28 04:35:34
[28-May-2025 04:35:34 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[28-May-2025 04:35:34 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-28 04:35:34
[28-May-2025 04:35:34 Europe/Berlin] Configuration SendGrid:
[28-May-2025 04:35:34 Europe/Berlin] API Key: SG.62YP2tv...
[28-May-2025 04:35:34 Europe/Berlin] From Email: <EMAIL>
[28-May-2025 04:35:34 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[28-May-2025 04:35:34 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[28-May-2025 04:35:34 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"Cr\u00e9ation de votre compte vacataire - ENSAH","content":[{"type":"text\/html","value":"\n    <!DOCTYPE html>\n    <html>\n    <head>\n        <meta charset='UTF-8'>\n        <style>\n            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n            .container { ma...
[28-May-2025 04:35:35 Europe/Berlin] Réponse SendGrid: Code 202 - 
[28-May-2025 04:35:35 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
[28-May-2025 04:39:17 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-28 04:39:17
[28-May-2025 04:39:17 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[28-May-2025 04:39:17 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-28 04:39:17
[28-May-2025 04:39:17 Europe/Berlin] Configuration SendGrid:
[28-May-2025 04:39:17 Europe/Berlin] API Key: SG.62YP2tv...
[28-May-2025 04:39:17 Europe/Berlin] From Email: <EMAIL>
[28-May-2025 04:39:17 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[28-May-2025 04:39:17 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[28-May-2025 04:39:17 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"Cr\u00e9ation de votre compte vacataire - ENSAH","content":[{"type":"text\/html","value":"\n    <!DOCTYPE html>\n    <html>\n    <head>\n        <meta charset='UTF-8'>\n        <style>\n            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n            .container { ma...
[28-May-2025 04:39:18 Europe/Berlin] Réponse SendGrid: Code 202 - 
[28-May-2025 04:39:18 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
[28-May-2025 04:46:19 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-28 04:46:19
[28-May-2025 04:46:19 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[28-May-2025 04:46:19 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-28 04:46:19
[28-May-2025 04:46:19 Europe/Berlin] Configuration SendGrid:
[28-May-2025 04:46:19 Europe/Berlin] API Key: SG.62YP2tv...
[28-May-2025 04:46:19 Europe/Berlin] From Email: <EMAIL>
[28-May-2025 04:46:19 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[28-May-2025 04:46:19 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[28-May-2025 04:46:19 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"Cr\u00e9ation de votre compte vacataire - ENSAH","content":[{"type":"text\/html","value":"\n    <!DOCTYPE html>\n    <html>\n    <head>\n        <meta charset='UTF-8'>\n        <style>\n            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n            .container { ma...
[28-May-2025 04:46:20 Europe/Berlin] Réponse SendGrid: Code 202 - 
[28-May-2025 04:46:20 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
[28-May-2025 04:53:05 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-28 04:53:05
[28-May-2025 04:53:05 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[28-May-2025 04:53:05 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-28 04:53:05
[28-May-2025 04:53:05 Europe/Berlin] Configuration SendGrid:
[28-May-2025 04:53:05 Europe/Berlin] API Key: SG.62YP2tv...
[28-May-2025 04:53:05 Europe/Berlin] From Email: <EMAIL>
[28-May-2025 04:53:05 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[28-May-2025 04:53:05 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[28-May-2025 04:53:05 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"Cr\u00e9ation de votre compte vacataire - ENSAH","content":[{"type":"text\/html","value":"\n    <!DOCTYPE html>\n    <html>\n    <head>\n        <meta charset='UTF-8'>\n        <style>\n            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n            .container { ma...
[28-May-2025 04:53:06 Europe/Berlin] Réponse SendGrid: Code 202 - 
[28-May-2025 04:53:06 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
[28-May-2025 05:06:16 Europe/Berlin] Tentative d'envoi d'email à: <EMAIL> - 2025-05-28 05:06:16
[28-May-2025 05:06:16 Europe/Berlin] Mode réel forcé - Tentative d'envoi d'email via SendGrid
[28-May-2025 05:06:16 Europe/Berlin] Tentative d'envoi d'email via SendGrid à: <EMAIL> - 2025-05-28 05:06:16
[28-May-2025 05:06:16 Europe/Berlin] Configuration SendGrid:
[28-May-2025 05:06:16 Europe/Berlin] API Key: SG.62YP2tv...
[28-May-2025 05:06:16 Europe/Berlin] From Email: <EMAIL>
[28-May-2025 05:06:16 Europe/Berlin] From Name: ENSAH - École Nationale des Sciences Appliquées Al Hoceima
[28-May-2025 05:06:16 Europe/Berlin] Utilisation de l'adresse d'expéditeur: <EMAIL>
[28-May-2025 05:06:16 Europe/Berlin] Données envoyées à SendGrid: {"personalizations":[{"to":[{"email":"<EMAIL>"}]}],"from":{"email":"<EMAIL>","name":"ENSAH - \u00c9cole Nationale des Sciences Appliqu\u00e9es Al Hoceima"},"subject":"Cr\u00e9ation de votre compte vacataire - ENSAH","content":[{"type":"text\/html","value":"\r\n    <!DOCTYPE html>\r\n    <html>\r\n    <head>\r\n        <meta charset='UTF-8'>\r\n        <style>\r\n            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\r\n            .cont...
[28-May-2025 05:06:17 Europe/Berlin] Réponse SendGrid: Code 202 - 
[28-May-2025 05:06:17 Europe/Berlin] Email envoyé avec succès via SendGrid à: <EMAIL>
