<?php
require_once __DIR__ . "/../config/db.php";

/**
 * Get all clubs
 *
 * @return array Array of clubs with member information
 */
function getAllClubs() {
    $conn = getConnection();

    $query = "SELECT c.*,
              e1.nom as president_nom, e1.prenom as president_prenom,
              e2.nom as vice_president_nom, e2.prenom as vice_president_prenom,
              e3.nom as secretary_nom, e3.prenom as secretary_prenom,
              e4.nom as treasurer_nom, e4.prenom as treasurer_prenom
              FROM club c
              LEFT JOIN etudiant e1 ON c.president_id = e1.CNE
              LEFT JOIN etudiant e2 ON c.vice_president_id = e2.CNE
              LEFT JOIN etudiant e3 ON c.secretary_id = e3.CNE
              LEFT JOIN etudiant e4 ON c.treasurer_id = e4.CNE
              ORDER BY c.nom";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        return ['error' => 'Error fetching clubs: ' . mysqli_error($conn)];
    }

    $clubs = mysqli_fetch_all($result, MYSQLI_ASSOC);
    mysqli_close($conn);

    return $clubs;
}

/**
 * Get all clubs with pagination
 *
 * @param int $page Page number (starting from 1)
 * @param int $perPage Number of clubs per page
 * @return array Array of clubs with pagination info
 */
function getAllClubsPaginated($page = 1, $perPage = 3) {
    $conn = getConnection();

    // Calculate offset
    $offset = ($page - 1) * $perPage;

    // Get total count for pagination
    $countQuery = "SELECT COUNT(*) as total FROM club";
    $countResult = mysqli_query($conn, $countQuery);
    $totalCount = mysqli_fetch_assoc($countResult)['total'];
    $totalPages = ceil($totalCount / $perPage);

    // Get clubs for current page
    $query = "SELECT c.*,
              e1.nom as president_nom, e1.prenom as president_prenom,
              e2.nom as vice_president_nom, e2.prenom as vice_president_prenom,
              e3.nom as secretary_nom, e3.prenom as secretary_prenom,
              e4.nom as treasurer_nom, e4.prenom as treasurer_prenom
              FROM club c
              LEFT JOIN etudiant e1 ON c.president_id = e1.CNE
              LEFT JOIN etudiant e2 ON c.vice_president_id = e2.CNE
              LEFT JOIN etudiant e3 ON c.secretary_id = e3.CNE
              LEFT JOIN etudiant e4 ON c.treasurer_id = e4.CNE
              ORDER BY c.nom
              LIMIT $perPage OFFSET $offset";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        return ['error' => 'Error fetching clubs: ' . mysqli_error($conn)];
    }

    $clubs = mysqli_fetch_all($result, MYSQLI_ASSOC);
    mysqli_close($conn);

    return [
        'clubs' => $clubs,
        'pagination' => [
            'currentPage' => $page,
            'perPage' => $perPage,
            'totalItems' => $totalCount,
            'totalPages' => $totalPages
        ]
    ];
}

/**
 * Get a club by ID
 *
 * @param int $id_club Club ID
 * @return array Club data with member information
 */
function getClubById($id_club) {
    $conn = getConnection();

    $id_club = mysqli_real_escape_string($conn, $id_club);

    $query = "SELECT c.*,
              e1.nom as president_nom, e1.prenom as president_prenom,
              e2.nom as vice_president_nom, e2.prenom as vice_president_prenom,
              e3.nom as secretary_nom, e3.prenom as secretary_prenom,
              e4.nom as treasurer_nom, e4.prenom as treasurer_prenom
              FROM club c
              LEFT JOIN etudiant e1 ON c.president_id = e1.CNE
              LEFT JOIN etudiant e2 ON c.vice_president_id = e2.CNE
              LEFT JOIN etudiant e3 ON c.secretary_id = e3.CNE
              LEFT JOIN etudiant e4 ON c.treasurer_id = e4.CNE
              WHERE c.id_club = '$id_club'";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        return ['error' => 'Error fetching club: ' . mysqli_error($conn)];
    }

    $club = mysqli_fetch_assoc($result);
    mysqli_close($conn);

    return $club;
}

/**
 * Create a new club
 *
 * @param array $data Club data
 * @return bool|array True on success, error array on failure
 */
function createClub($data) {
    $conn = getConnection();

    // Prepare the query
    $query = "INSERT INTO club (nom, description, date_creation, logo, president_id, vice_president_id, secretary_id, treasurer_id, statut)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

    $stmt = mysqli_prepare($conn, $query);

    if (!$stmt) {
        return ['error' => 'Error preparing statement: ' . mysqli_error($conn)];
    }

    // Set default values for optional fields
    $logo = isset($data['logo']) ? $data['logo'] : null;
    $president_id = isset($data['president_id']) ? $data['president_id'] : null;
    $vice_president_id = isset($data['vice_president_id']) ? $data['vice_president_id'] : null;
    $secretary_id = isset($data['secretary_id']) ? $data['secretary_id'] : null;
    $treasurer_id = isset($data['treasurer_id']) ? $data['treasurer_id'] : null;
    $statut = isset($data['statut']) ? $data['statut'] : 'actif';

    // Bind parameters
    mysqli_stmt_bind_param($stmt, "sssssssss",
        $data['nom'],
        $data['description'],
        $data['date_creation'],
        $logo,
        $president_id,
        $vice_president_id,
        $secretary_id,
        $treasurer_id,
        $statut
    );

    // Execute the query
    $result = mysqli_stmt_execute($stmt);

    if (!$result) {
        return ['error' => 'Error creating club: ' . mysqli_stmt_error($stmt)];
    }

    $club_id = mysqli_insert_id($conn);
    mysqli_stmt_close($stmt);
    mysqli_close($conn);

    return ['success' => true, 'id_club' => $club_id];
}

/**
 * Update a club
 *
 * @param int $id_club Club ID
 * @param array $data Club data
 * @return bool|array True on success, error array on failure
 */
function updateClub($id_club, $data) {
    $conn = getConnection();

    // Prepare the query
    $query = "UPDATE club SET
              nom = ?,
              description = ?,
              date_creation = ?,
              logo = ?,
              president_id = ?,
              vice_president_id = ?,
              secretary_id = ?,
              treasurer_id = ?,
              statut = ?
              WHERE id_club = ?";

    $stmt = mysqli_prepare($conn, $query);

    if (!$stmt) {
        return ['error' => 'Error preparing statement: ' . mysqli_error($conn)];
    }

    // Set default values for optional fields
    $logo = isset($data['logo']) ? $data['logo'] : null;
    $president_id = isset($data['president_id']) ? $data['president_id'] : null;
    $vice_president_id = isset($data['vice_president_id']) ? $data['vice_president_id'] : null;
    $secretary_id = isset($data['secretary_id']) ? $data['secretary_id'] : null;
    $treasurer_id = isset($data['treasurer_id']) ? $data['treasurer_id'] : null;
    $statut = isset($data['statut']) ? $data['statut'] : 'actif';

    // Bind parameters
    mysqli_stmt_bind_param($stmt, "sssssssssi",
        $data['nom'],
        $data['description'],
        $data['date_creation'],
        $logo,
        $president_id,
        $vice_president_id,
        $secretary_id,
        $treasurer_id,
        $statut,
        $id_club
    );

    // Execute the query
    $result = mysqli_stmt_execute($stmt);

    if (!$result) {
        return ['error' => 'Error updating club: ' . mysqli_stmt_error($stmt)];
    }

    mysqli_stmt_close($stmt);
    mysqli_close($conn);

    return ['success' => true];
}

/**
 * Delete a club
 *
 * @param int $id_club Club ID
 * @return bool|array True on success, error array on failure
 */
function deleteClub($id_club) {
    $conn = getConnection();

    $id_club = mysqli_real_escape_string($conn, $id_club);

    $query = "DELETE FROM club WHERE id_club = '$id_club'";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        return ['error' => 'Error deleting club: ' . mysqli_error($conn)];
    }

    mysqli_close($conn);

    return ['success' => true];
}

/**
 * Get all students for dropdown selection
 *
 * @return array Array of students
 */
function getAllStudentsForDropdown() {
    $conn = getConnection();

    $query = "SELECT CNE, nom, prenom FROM etudiant ORDER BY nom, prenom";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        return ['error' => 'Error fetching students: ' . mysqli_error($conn)];
    }

    $students = mysqli_fetch_all($result, MYSQLI_ASSOC);
    mysqli_close($conn);

    return $students;
}



/**
 * Get all programme items for a specific club
 *
 * @param int $id_club Club ID
 * @return array Array of programme items
 */
function getClubProgramme($id_club) {
    $conn = getConnection();

    $id_club = mysqli_real_escape_string($conn, $id_club);

    $query = "SELECT * FROM club_programme
              WHERE id_club = '$id_club'
              ORDER BY date_debut ASC";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        return ['error' => 'Error fetching club programme: ' . mysqli_error($conn)];
    }

    $programmes = mysqli_fetch_all($result, MYSQLI_ASSOC);
    mysqli_close($conn);

    return $programmes;
}

/**
 * Add a new programme item to a club
 *
 * @param array $data Programme data
 * @return bool|array True on success, error array on failure
 */
function addClubProgramme($data) {
    $conn = getConnection();

    // Prepare the query
    $query = "INSERT INTO club_programme (id_club, titre, description, date_debut, date_fin, lieu, type, statut)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

    $stmt = mysqli_prepare($conn, $query);

    if (!$stmt) {
        return ['error' => 'Error preparing statement: ' . mysqli_error($conn)];
    }

    // Bind parameters
    mysqli_stmt_bind_param($stmt, "isssssss",
        $data['id_club'],
        $data['titre'],
        $data['description'],
        $data['date_debut'],
        $data['date_fin'],
        $data['lieu'],
        $data['type'],
        $data['statut']
    );

    // Execute the query
    $result = mysqli_stmt_execute($stmt);

    if (!$result) {
        return ['error' => 'Error adding programme: ' . mysqli_stmt_error($stmt)];
    }

    $programme_id = mysqli_insert_id($conn);
    mysqli_stmt_close($stmt);
    mysqli_close($conn);

    return ['success' => true, 'id_programme' => $programme_id];
}

/**
 * Delete a club programme item
 *
 * @param int $id_programme Programme ID
 * @return bool|array True on success, error array on failure
 */
function deleteClubProgramme($id_programme) {
    $conn = getConnection();

    $id_programme = mysqli_real_escape_string($conn, $id_programme);

    $query = "DELETE FROM club_programme WHERE id_programme = '$id_programme'";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        return ['error' => 'Error deleting programme: ' . mysqli_error($conn)];
    }

    mysqli_close($conn);

    return ['success' => true];
}