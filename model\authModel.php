<?php
require_once __DIR__ . '/../config/db.php';

function authenticateUser($username, $password) {
    $conn = getConnection();

    // Sanitize input
    $username = mysqli_real_escape_string($conn, $username);

    // Get user from database
    $sql = "SELECT * FROM users WHERE username = '$username'";
    $result = mysqli_query($conn, $sql);

    if ($result && mysqli_num_rows($result) > 0) {
        $user = mysqli_fetch_assoc($result);

        // Vérifier si le compte est actif
        if (isset($user['is_active']) && $user['is_active'] == 0) {
            error_log("Login failed for $username: Account is inactive (is_active = " . $user['is_active'] . ")");
            mysqli_close($conn);
            return ['success' => false, 'error' => 'Ce compte a été désactivé. Veuillez contacter l\'administrateur.'];
        }

        // Verify password
        if (password_verify($password, $user['password'])) {
            // Remove password from user array before returning
            unset($user['password']);

            // Si l'utilisateur est un enseignant, vacataire, chef de département ou coordinateur
            if ($user['role'] === 'enseignant' || $user['role'] === 'vacataire' ||
                $user['role'] === 'chef de departement' || $user['role'] === 'coordinateur') {

                // Récupérer les informations de l'enseignant
                $teacherQuery = "SELECT nom, prenom, id_enseignant, id_specialite, id_departement FROM enseignant WHERE CNI = '$username'";
                $teacherResult = mysqli_query($conn, $teacherQuery);

                if ($teacherResult && mysqli_num_rows($teacherResult) > 0) {
                    $teacherData = mysqli_fetch_assoc($teacherResult);

                    // Stocker les informations personnelles
                    $user['nom'] = $teacherData['nom'];
                    $user['prenom'] = $teacherData['prenom'];
                    $user['teacher_id'] = $teacherData['id_enseignant'];
                    $user['specialty_id'] = $teacherData['id_specialite'];
                    $user['department_id'] = $teacherData['id_departement'];

                    // Log successful teacher data retrieval
                    error_log("Teacher data retrieved for $username: " . json_encode($teacherData));

                    // Récupérer les noms des spécialités et départements
                    if ($teacherData['id_specialite']) {
                        $specialtyQuery = "SELECT nom FROM specialite WHERE id = " . $teacherData['id_specialite'];
                        $specialtyResult = mysqli_query($conn, $specialtyQuery);
                        if ($specialtyResult && mysqli_num_rows($specialtyResult) > 0) {
                            $specialtyData = mysqli_fetch_assoc($specialtyResult);
                            $user['specialty_name'] = $specialtyData['nom'];
                        }
                    }

                    if ($teacherData['id_departement']) {
                        $departmentQuery = "SELECT nom_dep FROM departement WHERE id_departement = " . $teacherData['id_departement'];
                        $departmentResult = mysqli_query($conn, $departmentQuery);
                        if ($departmentResult && mysqli_num_rows($departmentResult) > 0) {
                            $departmentData = mysqli_fetch_assoc($departmentResult);
                            $user['department_name'] = $departmentData['nom_dep'];
                        }
                    }

                    // Si l'utilisateur est un coordinateur, récupérer l'ID de la filière
                    if ($user['role'] === 'coordinateur') {
                        $filiereId = getCoordinatorFiliereId($conn, $username);
                        if ($filiereId) {
                            $user['filiere_id'] = $filiereId;

                            // Récupérer le nom de la filière
                            $filiereQuery = "SELECT nom_filiere FROM filiere WHERE id_filiere = $filiereId";
                            $filiereResult = mysqli_query($conn, $filiereQuery);
                            if ($filiereResult && mysqli_num_rows($filiereResult) > 0) {
                                $filiereData = mysqli_fetch_assoc($filiereResult);
                                $user['filiere_name'] = $filiereData['nom_filiere'];
                            }
                        }
                    }
                } else {
                    // Log when teacher data is not found
                    error_log("No teacher data found for $username (role: " . $user['role'] . ")");
                }
            }

            // Si l'utilisateur est un étudiant
            if ($user['role'] === 'etudiant') {
                $studentQuery = "SELECT nom, prenom, id_filiere, niveau FROM student WHERE CNE = '$username'";
                $studentResult = mysqli_query($conn, $studentQuery);

                if ($studentResult && mysqli_num_rows($studentResult) > 0) {
                    $studentData = mysqli_fetch_assoc($studentResult);

                    // Stocker les informations personnelles
                    $user['nom'] = $studentData['nom'];
                    $user['prenom'] = $studentData['prenom'];
                    $user['student_id'] = $studentData['id_filiere'];
                    $user['niveau'] = $studentData['niveau'];

                    // Récupérer le nom de la filière
                    if ($studentData['id_filiere']) {
                        $filiereQuery = "SELECT nom_filiere FROM filiere WHERE id_filiere = " . $studentData['id_filiere'];
                        $filiereResult = mysqli_query($conn, $filiereQuery);
                        if ($filiereResult && mysqli_num_rows($filiereResult) > 0) {
                            $filiereData = mysqli_fetch_assoc($filiereResult);
                            $user['filiere_name'] = $filiereData['nom_filiere'];
                        }
                    }
                }
            }

            mysqli_close($conn);
            return ['success' => true, 'user' => $user];
        }
    }

    mysqli_close($conn);
    return ['success' => false, 'error' => 'Nom d\'utilisateur ou mot de passe incorrect'];
}



function getUserInfo($username) {
    $conn = getConnection();

    // Sanitize input
    $username = mysqli_real_escape_string($conn, $username);

    // Get user from database with admin information
    $sql = "SELECT u.id, u.username, u.role, a.CNI, a.telephone, a.nom, a.prenom, a.email, a.date_naissance, a.lieu_naissance, a.sexe, a.ville, a.pays, a.date_debut_travail
            FROM users u
            LEFT JOIN admin a ON u.username = a.CNI
            WHERE u.username = '$username'";
    $result = mysqli_query($conn, $sql);

    if ($result && mysqli_num_rows($result) > 0) {
        $user = mysqli_fetch_assoc($result);
        mysqli_close($conn);
        return ['success' => true, 'user' => $user];
    }

    mysqli_close($conn);
    return ['success' => false, 'error' => 'Utilisateur non trouvé'];
}



/**
 * Get the filiere ID for a coordinator based on their CNI (username)
 *
 * @param mysqli $conn Database connection
 * @param string $username The username (CNI) of the coordinator
 * @return int|null The filiere ID or null if not found
 */
function getCoordinatorFiliereId($conn, $username) {
    try {
        // If connection is not provided, create a new one
        $closeConn = false;
        if (!$conn) {
            $conn = getConnection();
            $closeConn = true;

            // Vérifier si la connexion a réussi
            if (!$conn) {
                error_log("Failed to establish database connection in getCoordinatorFiliereId for user: $username");
                return null;
            }
        }

        // Sanitize input
        $username = mysqli_real_escape_string($conn, $username);

        // Journaliser l'appel à la fonction
        error_log("getCoordinatorFiliereId called for username: $username");

        // First, get the enseignant ID based on the CNI (username)
        $sql = "SELECT id_enseignant FROM enseignant WHERE CNI = '$username'";
        error_log("Executing query: $sql");

        $result = mysqli_query($conn, $sql);

        if (!$result) {
            error_log("Error in getCoordinatorFiliereId: " . mysqli_error($conn) . " for user: $username");
            if ($closeConn) mysqli_close($conn);
            return null;
        }

        if (mysqli_num_rows($result) > 0) {
            $row = mysqli_fetch_assoc($result);
            $enseignantId = $row['id_enseignant'];

            error_log("Found enseignant ID: $enseignantId for user: $username");

            // Vérifier les différentes possibilités de noms de colonnes pour le chef de filière
            $possibleColumns = [
                'id_chef_filiere',
                'chef_filiere',
                'id_coordinateur',
                'coordinateur_id'
            ];

            // Vérifier quelle colonne existe dans la table filiere
            $columnToUse = null;
            foreach ($possibleColumns as $column) {
                $checkColumn = mysqli_query($conn, "SHOW COLUMNS FROM filiere LIKE '$column'");
                if ($checkColumn && mysqli_num_rows($checkColumn) > 0) {
                    $columnToUse = $column;
                    error_log("Found column for coordinator in filiere table: $columnToUse");
                    break;
                }
            }

            if (!$columnToUse) {
                error_log("No suitable column found in filiere table for coordinator association");
                if ($closeConn) mysqli_close($conn);
                return null;
            }

            // Now, check if this teacher is a coordinator in the filiere table
            $sql = "SELECT id_filiere FROM filiere WHERE $columnToUse = '$enseignantId'";
            error_log("Executing query: $sql");

            $result = mysqli_query($conn, $sql);

            if (!$result) {
                error_log("Error in getCoordinatorFiliereId (filiere check): " . mysqli_error($conn) . " for user: $username");
                if ($closeConn) mysqli_close($conn);
                return null;
            }

            if (mysqli_num_rows($result) > 0) {
                $row = mysqli_fetch_assoc($result);
                $filiereId = $row['id_filiere'];
                error_log("Found filiere ID: $filiereId for user: $username");

                if ($closeConn) mysqli_close($conn);
                return $filiereId;
            } else {
                error_log("No filiere found for enseignant ID: $enseignantId (user: $username) using column: $columnToUse");

                // Essayer de trouver une filière par défaut pour ce coordinateur
                // Cette partie est une solution de secours si aucune association n'est trouvée
                $sql = "SELECT id_filiere FROM filiere LIMIT 1";
                error_log("Trying fallback query: $sql");

                $result = mysqli_query($conn, $sql);
                if ($result && mysqli_num_rows($result) > 0) {
                    $row = mysqli_fetch_assoc($result);
                    $filiereId = $row['id_filiere'];
                    error_log("Found fallback filiere ID: $filiereId for user: $username");

                    // Optionnel: Mettre à jour la table filiere pour associer ce coordinateur à cette filière
                    // Décommentez les lignes suivantes si vous souhaitez effectuer cette mise à jour automatique
                    /*
                    $updateSql = "UPDATE filiere SET $columnToUse = '$enseignantId' WHERE id_filiere = '$filiereId'";
                    error_log("Executing update query: $updateSql");
                    mysqli_query($conn, $updateSql);
                    */

                    if ($closeConn) mysqli_close($conn);
                    return $filiereId;
                }
            }
        } else {
            error_log("No enseignant found with CNI: $username");
        }

        // If no filiere found, return null
        if ($closeConn) mysqli_close($conn);
        return null;
    } catch (Exception $e) {
        error_log("Exception in getCoordinatorFiliereId: " . $e->getMessage() . " for user: $username");
        if (isset($closeConn) && $closeConn && isset($conn)) mysqli_close($conn);
        return null;
    }
}

/**
 * Get the filiere ID for a coordinator (standalone function for external use)
 *
 * @param string $username The username (CNI) of the coordinator
 * @return int|null The filiere ID or null if not found
 */
function getCoordinatorFiliere($username) {
    try {
        error_log("getCoordinatorFiliere called for username: $username");

        $conn = getConnection();
        if (!$conn) {
            error_log("Failed to establish database connection in getCoordinatorFiliere for user: $username");
            return null;
        }

        // Vérifier si l'utilisateur existe dans la table enseignant
        $checkUser = mysqli_query($conn, "SELECT id_enseignant FROM enseignant WHERE CNI = '" . mysqli_real_escape_string($conn, $username) . "'");
        if (!$checkUser || mysqli_num_rows($checkUser) == 0) {
            error_log("User $username not found in enseignant table");
            mysqli_close($conn);
            return null;
        }

        $filiereId = getCoordinatorFiliereId($conn, $username);

        // Si aucun ID de filière n'est trouvé, essayer de trouver une filière par défaut
        if (!$filiereId) {
            error_log("No filiere ID found for coordinator: $username. Trying to find a default filiere.");

            // Récupérer la première filière disponible
            $defaultFiliereQuery = mysqli_query($conn, "SELECT id_filiere FROM filiere LIMIT 1");
            if ($defaultFiliereQuery && mysqli_num_rows($defaultFiliereQuery) > 0) {
                $defaultFiliere = mysqli_fetch_assoc($defaultFiliereQuery);
                $filiereId = $defaultFiliere['id_filiere'];
                error_log("Using default filiere ID: $filiereId for coordinator: $username");

                // Optionnel: Associer automatiquement le coordinateur à cette filière
                // Cette partie est commentée car elle modifie la base de données
                /*
                $enseignantRow = mysqli_fetch_assoc($checkUser);
                $enseignantId = $enseignantRow['id_enseignant'];

                // Trouver la colonne appropriée dans la table filiere
                $possibleColumns = ['id_chef_filiere', 'chef_filiere', 'id_coordinateur', 'coordinateur_id'];
                $columnToUse = null;

                foreach ($possibleColumns as $column) {
                    $checkColumn = mysqli_query($conn, "SHOW COLUMNS FROM filiere LIKE '$column'");
                    if ($checkColumn && mysqli_num_rows($checkColumn) > 0) {
                        $columnToUse = $column;
                        break;
                    }
                }

                if ($columnToUse) {
                    $updateQuery = "UPDATE filiere SET $columnToUse = '$enseignantId' WHERE id_filiere = '$filiereId'";
                    mysqli_query($conn, $updateQuery);
                    error_log("Updated filiere $filiereId to associate with coordinator $username (enseignant ID: $enseignantId)");
                }
                */
            }
        }

        mysqli_close($conn);

        error_log("getCoordinatorFiliere returning filiere ID: " . ($filiereId ?? 'null') . " for user: $username");
        return $filiereId;
    } catch (Exception $e) {
        error_log("Exception in getCoordinatorFiliere: " . $e->getMessage() . " for user: $username");
        if (isset($conn)) mysqli_close($conn);
        return null;
    }
}

/**
 * Get the department ID for a department head based on their CNI (username)
 *
 * @param mysqli $conn Database connection
 * @param string $username The username (CNI) of the department head
 * @return int|null The department ID or null if not found
 */
function getDepartmentHeadDepartmentId($conn, $username) {
    // If connection is not provided, create a new one
    $closeConn = false;
    if (!$conn) {
        $conn = getConnection();
        $closeConn = true;
    }

    // Sanitize input
    $username = mysqli_real_escape_string($conn, $username);

    // First, get the enseignant ID based on the CNI (username)
    $sql = "SELECT id_enseignant FROM enseignant WHERE CNI = '$username'";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        error_log("Error in getDepartmentHeadDepartmentId: " . mysqli_error($conn));
        if ($closeConn) mysqli_close($conn);
        return null;
    }

    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $enseignantId = $row['id_enseignant'];

        // Now, check if this teacher is a department head in the departement table
        $sql = "SELECT id_departement FROM departement WHERE id_chef_departement = '$enseignantId'";
        $result = mysqli_query($conn, $sql);

        if (!$result) {
            error_log("Error in getDepartmentHeadDepartmentId (departement check): " . mysqli_error($conn));
            if ($closeConn) mysqli_close($conn);
            return null;
        }

        if (mysqli_num_rows($result) > 0) {
            $row = mysqli_fetch_assoc($result);
            $departmentId = $row['id_departement'];
            if ($closeConn) mysqli_close($conn);
            return $departmentId;
        }
    }

    // If no department found, return null
    if ($closeConn) mysqli_close($conn);
    return null;
}

/**
 * Get the department ID for a department head (standalone function for external use)
 *
 * @param string $username The username (CNI) of the department head
 * @return int|null The department ID or null if not found
 */
function getDepartmentHeadDepartment($username) {
    $conn = getConnection();
    $departmentId = getDepartmentHeadDepartmentId($conn, $username);
    mysqli_close($conn);
    return $departmentId;
}

/**
 * Get teacher information based on their CNI (username)
 *
 * @param mysqli $conn Database connection
 * @param string $username The username (CNI) of the teacher
 * @return array|null The teacher information or null if not found
 */
function getTeacherInfo($conn, $username) {
    // If connection is not provided, create a new one
    $closeConn = false;
    if (!$conn) {
        $conn = getConnection();
        $closeConn = true;
    }

    // Sanitize input
    $username = mysqli_real_escape_string($conn, $username);

    // Get teacher information including specialty and department names
    $sql = "SELECT e.*, s.nom as specialty_name, d.nom_dep as department_name
            FROM enseignant e
            LEFT JOIN specialite s ON e.id_specialite = s.id
            LEFT JOIN departement d ON e.id_departement = d.id_departement
            WHERE e.CNI = '$username'";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        error_log("Error in getTeacherInfo: " . mysqli_error($conn));
        if ($closeConn) mysqli_close($conn);
        return null;
    }

    if (mysqli_num_rows($result) > 0) {
        $teacherInfo = mysqli_fetch_assoc($result);
        if ($closeConn) mysqli_close($conn);
        return $teacherInfo;
    }

    // If no teacher found, return null
    if ($closeConn) mysqli_close($conn);
    return null;
}

/**
 * Get teacher information (standalone function for external use)
 *
 * @param string $username The username (CNI) of the teacher
 * @return array|null The teacher information or null if not found
 */
function getTeacher($username) {
    $conn = getConnection();
    $teacherInfo = getTeacherInfo($conn, $username);
    mysqli_close($conn);
    return $teacherInfo;
}
?>