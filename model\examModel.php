<?php
require_once "../config/db.php";

// Get all exams
function getAllExams() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAllExams");
        return ["error" => "Database connection error"];
    }

    $sql = "SELECT e.*,
            m.nom_module, en.nom as prof_nom, en.prenom as prof_prenom, s.nom_salle,
            DATE_FORMAT(e.date_examen, '%b %d, %Y') as formatted_date
            FROM exams e
            LEFT JOIN module m ON e.id_module = m.id_module
            LEFT JOIN enseignant en ON e.id_enseignant = en.id_enseignant
            LEFT JOIN salle s ON e.id_salle = s.id_salle
            ORDER BY e.date_examen ASC, e.heure_debut ASC";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error executing query in getAllExams: $error");
        mysqli_close($conn);
        return ["error" => "Error fetching exams: " . $error];
    }

    $exams = [];
    while ($row = mysqli_fetch_assoc($result)) {
        // Format the professor name
        if (isset($row['prof_nom']) && isset($row['prof_prenom'])) {
            $row['nom_enseignant'] = $row['prof_nom'] . ' ' . $row['prof_prenom'];
        }
        $exams[] = $row;
    }

    mysqli_close($conn);
    return $exams;
}

// Get exams by filters
function getExamsByFilters($filiere = null, $niveau = null, $groupe = null, $semestre = null) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    // Set charset to UTF-8
    mysqli_set_charset($conn, "utf8mb4");

    // Debug log
    error_log("getExamsByFilters called with: filiere=$filiere, niveau=$niveau, groupe=$groupe, semestre=$semestre");

    // First check if the exams table exists
    $tableExists = false;
    $checkTable = mysqli_query($conn, "SHOW TABLES LIKE 'exams'");
    if ($checkTable) {
        if (mysqli_num_rows($checkTable) > 0) {
            $tableExists = true;
        }
        mysqli_free_result($checkTable);
    }

    if (!$tableExists) {
        error_log("Exams table does not exist in the database");
        mysqli_close($conn);
        return ["error" => "Exams table does not exist in the database"];
    }

    // Check the structure of the exams table
    $checkColumns = mysqli_query($conn, "SHOW COLUMNS FROM exams");
    if (!$checkColumns) {
        $error = mysqli_error($conn);
        error_log("Error checking exams table structure: $error");
        mysqli_close($conn);
        return ["error" => "Error checking exams table structure: " . $error];
    }

    $columns = [];
    while ($column = mysqli_fetch_assoc($checkColumns)) {
        $columns[] = $column['Field'];
    }
    mysqli_free_result($checkColumns);

    error_log("Exams table columns: " . implode(", ", $columns));

    // Build the SQL query
    $sql = "SELECT e.id_examen, e.id_module, e.id_enseignant, e.id_salle, e.type,
                   e.date_examen, e.heure_debut, e.heure_fin, e.id_niveau, e.semestre,
                   e.id_filiere, e.id_groupe, e.day,
                   m.nom_module, en.nom as prof_nom, en.prenom as prof_prenom, s.nom_salle,
                   DATE_FORMAT(e.date_examen, '%b %d, %Y') as formatted_date,
                   DAYOFWEEK(e.date_examen) as calculated_day
            FROM exams e
            LEFT JOIN module m ON e.id_module = m.id_module
            LEFT JOIN enseignant en ON e.id_enseignant = en.id_enseignant
            LEFT JOIN salle s ON e.id_salle = s.id_salle
            WHERE 1=1";

    // Add filters
    if ($niveau) {
        $niveau = mysqli_real_escape_string($conn, $niveau);
        $sql .= " AND e.id_niveau = '$niveau'";
    }

    if ($semestre) {
        $semestre = mysqli_real_escape_string($conn, $semestre);
        $sql .= " AND e.semestre = '$semestre'";
    }

    if ($filiere && $filiere !== 'none') {
        $filiere = mysqli_real_escape_string($conn, $filiere);
        $sql .= " AND e.id_filiere = '$filiere'";
    }

    if ($groupe && $groupe !== 'all') {
        $groupe = mysqli_real_escape_string($conn, $groupe);
        $sql .= " AND e.id_groupe = '$groupe'";
    }

    $sql .= " ORDER BY e.date_examen ASC, e.heure_debut ASC";

    error_log("SQL query for getExamsByFilters: $sql");

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error executing query in getExamsByFilters: $error");
        mysqli_close($conn);
        return ["error" => "Error fetching exams: " . $error];
    }

    $exams = [];
    while ($row = mysqli_fetch_assoc($result)) {
        // Format the professor name
        if (isset($row['prof_nom']) && isset($row['prof_prenom'])) {
            $row['nom_enseignant'] = $row['prof_nom'] . ' ' . $row['prof_prenom'];
        }

        // Process the row to ensure it has all the necessary fields

        // First, check if we need to convert a string day to a number
        if (isset($row['day']) && !empty($row['day']) && !is_numeric($row['day'])) {
            $dayMap = [
                'lundi' => 1, 'monday' => 1,
                'mardi' => 2, 'tuesday' => 2,
                'mercredi' => 3, 'wednesday' => 3,
                'jeudi' => 4, 'thursday' => 4,
                'vendredi' => 5, 'friday' => 5,
                'samedi' => 6, 'saturday' => 6,
                'dimanche' => 7, 'sunday' => 7
            ];
            $dayLower = strtolower($row['day']);
            if (isset($dayMap[$dayLower])) {
                $row['day'] = $dayMap[$dayLower];
                error_log("Converted string day '{$row['day']}' to number: {$dayMap[$dayLower]}");
            }
        }

        // If day is still not set, try using the calculated_day from SQL
        if ((!isset($row['day']) || empty($row['day'])) && isset($row['calculated_day']) && !empty($row['calculated_day'])) {
            // DAYOFWEEK() returns 1 for Sunday, 2 for Monday, etc.
            // We need to convert to 1 for Monday, 2 for Tuesday, etc.
            $calculatedDay = $row['calculated_day'];
            $adjustedDay = ($calculatedDay == 1) ? 7 : $calculatedDay - 1;
            $row['day'] = $adjustedDay;
            error_log("Using calculated_day from SQL: {$row['calculated_day']} -> adjusted to: $adjustedDay");
        }
        // If still not set but date_examen is available, calculate day from date
        else if ((!isset($row['day']) || empty($row['day'])) && isset($row['date_examen']) && !empty($row['date_examen'])) {
            try {
                $date = new DateTime($row['date_examen']);
                $dayOfWeek = $date->format('N'); // 1 (Monday) to 7 (Sunday)
                $row['day'] = $dayOfWeek;
                error_log("Calculated day from date_examen ({$row['date_examen']}): $dayOfWeek");
            } catch (Exception $e) {
                error_log("Error parsing date: " . $e->getMessage());
                // If we can't parse the date, try to extract the day from the date string
                if (preg_match('/(\d{4})-(\d{2})-(\d{2})/', $row['date_examen'], $matches)) {
                    $year = $matches[1];
                    $month = $matches[2];
                    $day = $matches[3];
                    $dateStr = "$year-$month-$day";
                    try {
                        $manualDate = new DateTime($dateStr);
                        $manualDayOfWeek = $manualDate->format('N');
                        $row['day'] = $manualDayOfWeek;
                        error_log("Manually calculated day from date_examen ($dateStr): $manualDayOfWeek");
                    } catch (Exception $e2) {
                        error_log("Error in manual date parsing: " . $e2->getMessage());
                    }
                }
            }
        }

        // Add the row to the results array
        $exams[] = $row;
    }

    mysqli_close($conn);
    return $exams;
}

// Get exam by ID
function getExamById($id) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getExamById");
        return ["error" => "Database connection error"];
    }

    $id = mysqli_real_escape_string($conn, $id);
    error_log("Getting exam with ID: $id");

    $sql = "SELECT e.*,
            m.nom_module, en.nom as prof_nom, en.prenom as prof_prenom, s.nom_salle,
            DATE_FORMAT(e.date_examen, '%b %d, %Y') as formatted_date
            FROM exams e
            LEFT JOIN module m ON e.id_module = m.id_module
            LEFT JOIN enseignant en ON e.id_enseignant = en.id_enseignant
            LEFT JOIN salle s ON e.id_salle = s.id_salle
            WHERE e.id_examen = '$id'";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error executing query in getExamById: $error");
        mysqli_close($conn);
        return ["error" => "Error fetching exam: " . $error];
    }

    $exam = mysqli_fetch_assoc($result);

    if (!$exam) {
        error_log("No exam found with ID: $id");
        mysqli_close($conn);
        return ["error" => "Exam not found"];
    }

    // Format the professor name
    if (isset($exam['prof_nom']) && isset($exam['prof_prenom'])) {
        $exam['nom_enseignant'] = $exam['prof_nom'] . ' ' . $exam['prof_prenom'];
    }

    mysqli_close($conn);
    return $exam;
}

// Add a new exam
function addExam($data) {
    error_log("addExam function called with data: " . print_r($data, true));

    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in addExam");
        return ["error" => "Database connection error"];
    }

    // Validate required fields
    $requiredFields = ['id_module', 'id_enseignant', 'id_salle', 'type', 'date_examen', 'heure_debut', 'heure_fin', 'id_niveau', 'semestre'];

    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            error_log("Missing required field: $field");
            mysqli_close($conn);
            return ["error" => "Missing required field: $field"];
        }
    }

    // Escape data
    $id_module = mysqli_real_escape_string($conn, $data['id_module']);
    $id_enseignant = mysqli_real_escape_string($conn, $data['id_enseignant']);
    $id_salle = mysqli_real_escape_string($conn, $data['id_salle']);
    $type = mysqli_real_escape_string($conn, $data['type']);
    $date_examen = mysqli_real_escape_string($conn, $data['date_examen']);
    $heure_debut = mysqli_real_escape_string($conn, $data['heure_debut']);
    $heure_fin = mysqli_real_escape_string($conn, $data['heure_fin']);
    $id_niveau = mysqli_real_escape_string($conn, $data['id_niveau']);
    $semestre = mysqli_real_escape_string($conn, $data['semestre']);

    // Optional fields
    $id_filiere = isset($data['id_filiere']) && $data['id_filiere'] !== '' ? mysqli_real_escape_string($conn, $data['id_filiere']) : 'NULL';
    $id_groupe = isset($data['id_groupe']) && $data['id_groupe'] !== '' ? mysqli_real_escape_string($conn, $data['id_groupe']) : 'NULL';
    $day = isset($data['day']) && $data['day'] !== '' ? mysqli_real_escape_string($conn, $data['day']) : 'NULL';

    // If day is not set but date_examen is, calculate day from date
    if ($day === 'NULL' && $date_examen) {
        try {
            $date = new DateTime($date_examen);
            $dayOfWeek = $date->format('N'); // 1 (Monday) to 7 (Sunday)
            $day = $dayOfWeek;
            error_log("Calculated day from date_examen ($date_examen): $dayOfWeek");
        } catch (Exception $e) {
            error_log("Error calculating day from date: " . $e->getMessage());
        }
    }

    // Build the SQL query
    $sql = "INSERT INTO exams (id_module, id_enseignant, id_salle, type, date_examen, heure_debut, heure_fin, id_niveau, semestre";

    if ($id_filiere !== 'NULL') {
        $sql .= ", id_filiere";
    }

    if ($id_groupe !== 'NULL') {
        $sql .= ", id_groupe";
    }

    if ($day !== 'NULL') {
        $sql .= ", day";
    }

    $sql .= ") VALUES ('$id_module', '$id_enseignant', '$id_salle', '$type', '$date_examen', '$heure_debut', '$heure_fin', '$id_niveau', '$semestre'";

    if ($id_filiere !== 'NULL') {
        $sql .= ", '$id_filiere'";
    }

    if ($id_groupe !== 'NULL') {
        $sql .= ", '$id_groupe'";
    }

    if ($day !== 'NULL') {
        $sql .= ", '$day'";
    }

    $sql .= ")";

    error_log("SQL query for addExam: $sql");

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error executing query in addExam: $error");
        mysqli_close($conn);
        return ["error" => "Error adding exam: " . $error];
    }

    $id = mysqli_insert_id($conn);
    error_log("Exam added successfully with ID: $id");
    mysqli_close($conn);

    return ["id" => $id, "message" => "Exam added successfully"];
}

// Update an exam
function updateExam($data) {
    error_log("updateExam function called with data: " . print_r($data, true));

    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in updateExam");
        return ["error" => "Database connection error"];
    }

    // Validate required fields
    $requiredFields = ['id_examen', 'id_module', 'id_enseignant', 'id_salle', 'type', 'date_examen', 'heure_debut', 'heure_fin'];

    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            error_log("Missing required field: $field");
            mysqli_close($conn);
            return ["error" => "Missing required field: $field"];
        }
    }

    // Escape data
    $id_examen = mysqli_real_escape_string($conn, $data['id_examen']);
    $id_module = mysqli_real_escape_string($conn, $data['id_module']);
    $id_enseignant = mysqli_real_escape_string($conn, $data['id_enseignant']);
    $id_salle = mysqli_real_escape_string($conn, $data['id_salle']);
    $type = mysqli_real_escape_string($conn, $data['type']);
    $date_examen = mysqli_real_escape_string($conn, $data['date_examen']);
    $heure_debut = mysqli_real_escape_string($conn, $data['heure_debut']);
    $heure_fin = mysqli_real_escape_string($conn, $data['heure_fin']);

    // Build the SQL query
    $sql = "UPDATE exams SET
            id_module = '$id_module',
            id_enseignant = '$id_enseignant',
            id_salle = '$id_salle',
            type = '$type',
            date_examen = '$date_examen',
            heure_debut = '$heure_debut',
            heure_fin = '$heure_fin'";

    // Optional fields
    if (isset($data['id_niveau']) && !empty($data['id_niveau'])) {
        $id_niveau = mysqli_real_escape_string($conn, $data['id_niveau']);
        $sql .= ", id_niveau = '$id_niveau'";
    }

    if (isset($data['semestre']) && !empty($data['semestre'])) {
        $semestre = mysqli_real_escape_string($conn, $data['semestre']);
        $sql .= ", semestre = '$semestre'";
    }

    if (isset($data['id_filiere']) && !empty($data['id_filiere'])) {
        $id_filiere = mysqli_real_escape_string($conn, $data['id_filiere']);
        $sql .= ", id_filiere = '$id_filiere'";
    } else {
        $sql .= ", id_filiere = NULL";
    }

    if (isset($data['id_groupe']) && !empty($data['id_groupe'])) {
        $id_groupe = mysqli_real_escape_string($conn, $data['id_groupe']);
        $sql .= ", id_groupe = '$id_groupe'";
    } else {
        $sql .= ", id_groupe = NULL";
    }

    // Update day field
    if (isset($data['day']) && !empty($data['day'])) {
        $day = mysqli_real_escape_string($conn, $data['day']);
        $sql .= ", day = '$day'";
    } else if ($date_examen) {
        // If day is not provided but date_examen is, calculate day from date
        try {
            $date = new DateTime($date_examen);
            $dayOfWeek = $date->format('N'); // 1 (Monday) to 7 (Sunday)
            $sql .= ", day = '$dayOfWeek'";
            error_log("Calculated day from date_examen ($date_examen): $dayOfWeek");
        } catch (Exception $e) {
            error_log("Error calculating day from date: " . $e->getMessage());
        }
    }

    $sql .= " WHERE id_examen = '$id_examen'";

    error_log("SQL query for updateExam: $sql");

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error executing query in updateExam: $error");
        mysqli_close($conn);
        return ["error" => "Error updating exam: " . $error];
    }

    error_log("Exam updated successfully with ID: $id_examen");
    mysqli_close($conn);

    return ["id" => $id_examen, "message" => "Exam updated successfully"];
}

// Delete an exam
function deleteExam($id) {
    error_log("deleteExam function called with ID: $id");

    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in deleteExam");
        return ["error" => "Database connection error"];
    }

    $id = mysqli_real_escape_string($conn, $id);

    $sql = "DELETE FROM exams WHERE id_examen = '$id'";

    error_log("SQL query for deleteExam: $sql");

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error executing query in deleteExam: $error");
        mysqli_close($conn);
        return ["error" => "Error deleting exam: " . $error];
    }

    error_log("Exam deleted successfully with ID: $id");
    mysqli_close($conn);

    return ["message" => "Exam deleted successfully"];
}
?>