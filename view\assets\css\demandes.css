:root {
    --primary-color: #60A5FA;
    --primary-light: #EFF6FF;
    --success-color: #34D399;
    --success-light: #ECFDF5;
    --danger-color: #FB7185;
    --danger-light: #FEE2E2;
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-600: #4B5563;
    --light-blue: #e8f0fe;
    --royal-blue: #1a73e8;
}

body {
    background-color: #F9FAFB;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}


/* Header Styles */
.main-content {
    flex: 1;
}

.app-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    background-color: white;
    border-bottom: 1px solid var(--gray-200);
}


/* Content Area Styles */
.content-area {
    padding: 2rem;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1F2937;
}

.demands-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.25rem;
    margin-top: 1.5rem;
}

.demand-card {
    background-color: var(--light-blue);
    border-radius: 0.75rem;
    padding: 1.25rem;
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
    font-size: 0.95rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.demand-card:hover {
    box-shadow: 0 10px 15px -3px rgba(26, 115, 232, 0.1), 0 4px 6px -2px rgba(26, 115, 232, 0.05);
    transform: translateY(-3px);
    border-color: var(--royal-blue);
}

.demand-header {
    margin-bottom: 1rem;
    flex: 0 0 auto;
}

.demand-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--royal-blue);
    margin: 0;
    display: flex;
    align-items: center;
}

.bg-soft-primary {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.bg-soft-success {
    background-color: var(--success-light);
    color: var(--success-color);
}

.badge {
    font-weight: 500;
    padding: 0.35rem 0.75rem;
    border-radius: 0.5rem;
    display: inline-flex;
    align-items: center;
    gap: 0.35rem;
    font-size: 0.8rem;
}

.demand-title {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-top: 0.75rem;
    background-color: rgba(255, 255, 255, 0.7);
    padding: 0.75rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    flex: 1 0 auto;
}

.demand-title i {
    margin-top: 0.25rem;
    font-size: 1.25rem;
    color: var(--royal-blue);
}

.demand-title p {
    margin: 0;
    color: var(--gray-700);
    line-height: 1.5;
    font-size: 0.95rem;
}

.demand-author {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 1rem 0;
    flex: 0 0 auto;
}

.author-avatar {
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    background-color: var(--gray-200);
    background-size: cover;
    background-position: center;
}

.author-name {
    color: var(--gray-600);
}

.separator {
    color: var(--gray-300);
}

.date {
    color: var(--gray-300);
}

.demand-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    flex: 0 0 auto;
    justify-content: flex-end;
}

.btn-success-soft {
    background-color: var(--success-light);
    color: var(--success-color);
    border: none;
    transition: all 0.3s ease;
    font-weight: 500;
    padding: 0.35rem 0.6rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.35rem;
    font-size: 0.85rem;
}

.btn-success-soft:hover {
    background-color: var(--success-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(52, 211, 153, 0.2), 0 2px 4px -1px rgba(52, 211, 153, 0.1);
}

.btn-danger-soft {
    background-color: var(--danger-light);
    color: var(--danger-color);
    border: none;
    transition: all 0.3s ease;
    font-weight: 500;
    padding: 0.35rem 0.6rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.35rem;
    font-size: 0.85rem;
}

.btn-danger-soft:hover {
    background-color: var(--danger-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(251, 113, 133, 0.2), 0 2px 4px -1px rgba(251, 113, 133, 0.1);
}

.btn-light {
    color: var(--gray-600);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.35rem;
    padding: 0.35rem 0.6rem;
    border-radius: 0.5rem;
    font-size: 0.85rem;
    background-color: var(--gray-100);
}

.btn-light:hover {
    background-color: var(--gray-100);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
}

/* Modal Styles */
.modal-content {
    border-radius: 0.75rem;
    border: none;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.modal-header {
    border-bottom: 1px solid var(--gray-200);
    padding: 1.25rem 1.5rem;
}

.modal-title {
    font-weight: 600;
    color: #1F2937;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid var(--gray-200);
    padding: 1.25rem 1.5rem;
}

#messageText {
    border: 1px solid var(--gray-300);
    border-radius: 0.5rem;
    padding: 0.75rem;
    resize: none;
    transition: border-color 0.2s;
}

#messageText:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2);
    outline: none;
}

.btn-primary {
    background-color: var(--primary-color);
    border: none;
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    transition: background-color 0.2s;
}

.btn-primary:hover {
    background-color: #3B82F6;
}

.btn-secondary {
    background-color: var(--gray-200);
    color: var(--gray-600);
    border: none;
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    transition: background-color 0.2s;
}

.btn-secondary:hover {
    background-color: var(--gray-300);
    color: var(--gray-600);
}

/* Toast Notification Styles */
.toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 9999;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s, transform 0.3s;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.toast.success {
    background-color: var(--success-color);
}

.toast.error {
    background-color: var(--danger-color);
}

.toast.show {
    opacity: 1;
    transform: translateY(0);
}

/* Demand Emoji Styles */
.demand-emoji {
    font-size: 1.25rem;
    margin-right: 0.5rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background-color: rgba(26, 115, 232, 0.1);
    border-radius: 50%;
    color: var(--royal-blue);
}

/* Demand Status Badge */
.demand-status-badge {
    padding: 0.35rem 0.6rem;
    border-radius: 0.5rem;
    font-weight: 500;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.35rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    white-space: nowrap;
}

.demand-status-badge i {
    font-size: 1.1rem;
}

.btn-success-soft i, .btn-danger-soft i, .btn-light i {
    font-size: 1.1rem;
}

.demand-status-badge.pending {
    background-color: var(--gray-100);
    color: var(--gray-600);
}

.demand-status-badge.accepted {
    background-color: var(--success-light);
    color: var(--success-color);
}

.demand-status-badge.rejected {
    background-color: var(--danger-light);
    color: var(--danger-color);
}

/* Profile Hover Card Styles */
.author-avatar {
    position: relative;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.author-avatar:hover {
    transform: scale(1.1);
}

.profile-hover-card {
    position: absolute;
    bottom: calc(100% + 10px);
    left: 50%;
    transform: translateX(-50%) translateY(10px);
    width: 280px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    padding: 1rem;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    pointer-events: none;
    border: 1px solid var(--gray-200);
}

/* Fix for hover cards near the sidebar */
.demand-card:first-child .profile-hover-card,
.demand-card:nth-child(odd) .profile-hover-card {
    left: 0;
    transform: translateX(0) translateY(10px);
}

.demand-card:first-child .author-avatar:hover .profile-hover-card,
.demand-card:nth-child(odd) .author-avatar:hover .profile-hover-card {
    transform: translateX(0) translateY(0);
}

/* Adjust the arrow position for left-aligned cards */
.demand-card:first-child .profile-hover-card::after,
.demand-card:nth-child(odd) .profile-hover-card::after {
    left: 20px;
}

.profile-hover-card::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
    width: 16px;
    height: 16px;
    background-color: white;
    border-right: 1px solid var(--gray-200);
    border-bottom: 1px solid var(--gray-200);
}

.author-avatar:hover .profile-hover-card {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
    pointer-events: auto;
}

.profile-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.profile-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    margin-right: 1rem;
    border: 3px solid var(--light-blue);
}

.profile-info {
    flex: 1;
}

.profile-name {
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--royal-blue);
    margin: 0;
}

.profile-role {
    color: var(--gray-600);
    font-size: 0.9rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.profile-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.profile-detail {
    display: flex;
    align-items: center;
    font-size: 0.85rem;
}

.profile-detail i {
    color: var(--royal-blue);
    margin-right: 0.5rem;
    font-size: 1rem;
}

.profile-detail-label {
    color: var(--gray-600);
    margin-right: 0.25rem;
}

.profile-detail-value {
    color: var(--gray-800);
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100px;
}

.view-profile-btn {
    display: block;
    text-align: center;
    padding: 0.5rem;
    background-color: var(--light-blue);
    color: var(--royal-blue);
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.view-profile-btn:hover {
    background-color: var(--royal-blue);
    color: white;
}

/* Pagination Styles */
.pagination-info {
    color: var(--gray-600);
    font-size: 0.9rem;
    text-align: center;
}

.pagination {
    display: flex;
    justify-content: center;
    gap: 0.25rem;
}

.page-item.active .page-link {
    background-color: var(--royal-blue);
    border-color: var(--royal-blue);
}

.page-link {
    color: var(--royal-blue);
    border-radius: 0.35rem;
    padding: 0.35rem 0.65rem;
    font-size: 0.85rem;
    border: 1px solid var(--gray-200);
}

.page-link:hover {
    background-color: var(--light-blue);
    color: var(--royal-blue);
    border-color: var(--royal-blue);
}

.page-item.disabled .page-link {
    color: var(--gray-400);
    border-color: var(--gray-200);
    background-color: var(--gray-100);
}

/* Confirmation Icons */
.confirmation-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    font-size: 2.5rem;
}

.confirmation-icon.success {
    background-color: var(--success-light);
    color: var(--success-color);
}

.confirmation-icon.danger {
    background-color: var(--danger-light);
    color: var(--danger-color);
}

/* Modal Header Backgrounds */
.modal-header.bg-success-soft {
    background-color: var(--success-light);
}

.modal-header.bg-danger-soft {
    background-color: var(--danger-light);
}

/* Button Styles */
.btn-success {
    background-color: var(--success-color);
    border: none;
    color: white;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-success:hover {
    background-color: #2ebb89;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(52, 211, 153, 0.2), 0 2px 4px -1px rgba(52, 211, 153, 0.1);
}

.btn-danger {
    background-color: var(--danger-color);
    border: none;
    color: white;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-danger:hover {
    background-color: #f9546c;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(251, 113, 133, 0.2), 0 2px 4px -1px rgba(251, 113, 133, 0.1);
}

.btn-outline-secondary {
    border: 1px solid var(--gray-300);
    background-color: transparent;
    color: var(--gray-600);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-secondary:hover {
    background-color: var(--gray-100);
    color: var(--gray-600);
    border-color: var(--gray-400);
}