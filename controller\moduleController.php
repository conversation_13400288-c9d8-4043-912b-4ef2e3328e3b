<?php
require_once "../model/moduleModel.php";
require_once "../utils/response.php";

/**
 * API to get all modules
 */
function getAllModulesAPI() {
    $modules = getAllModules();
    
    if (isset($modules['error'])) {
        jsonResponse(['error' => $modules['error'], 'success' => false], 404);
    }
    
    jsonResponse(['data' => $modules, 'success' => true], 200);
}

/**
 * API to get modules for a specific teacher
 *
 * @param int $teacherId The teacher ID
 * @param int|null $filiereId The field ID (optional)
 * @param int|null $niveauId The level ID (optional)
 */
function getTeacherModulesAPI($teacherId, $filiereId = null, $niveauId = null) {
    $modules = getTeacherModules($teacherId, $filiereId, $niveauId);
    
    if (isset($modules['error'])) {
        jsonResponse(['error' => $modules['error'], 'success' => false], 404);
    }
    
    jsonResponse(['data' => $modules, 'success' => true], 200);
}

/**
 * API to create a new module
 *
 * @param array $data Module data
 */
function createModuleAPI($data) {
    // Validate required fields
    $requiredFields = ['nom', 'volume_total', 'filiere_id', 'id_niveau', 'semestre'];
    
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            jsonResponse(['error' => "Field '$field' is required", 'success' => false], 400);
            return;
        }
    }
    
    $result = createModule($data);
    
    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error'], 'success' => false], 500);
        return;
    }
    
    jsonResponse(['message' => 'Module created successfully', 'id' => $result['id'], 'success' => true], 201);
}

/**
 * API to update a module
 *
 * @param int $id Module ID
 * @param array $data Module data
 */
function updateModuleAPI($id, $data) {
    $result = updateModule($id, $data);
    
    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error'], 'success' => false], 500);
        return;
    }
    
    jsonResponse(['message' => 'Module updated successfully', 'success' => true], 200);
}

/**
 * API to delete a module
 *
 * @param int $id Module ID
 */
function deleteModuleAPI($id) {
    $result = deleteModule($id);
    
    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error'], 'success' => false], 500);
        return;
    }
    
    jsonResponse(['message' => 'Module deleted successfully', 'success' => true], 200);
}
?>