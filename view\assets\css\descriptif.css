/* Descriptif Page Styles */

:root {
    /* Pastel Colors */
    --primary-color: #a0c4ff;
    --primary-light: #e2eafc;
    --primary-dark: #7b9ff2;
    --success-color: #9bf6b0;
    --success-light: #e3fcef;
    --success-dark: #6bdb8f;
    --warning-color: #ffd166;
    --warning-light: #fff3d9;
    --warning-dark: #ffba3d;
    --danger-color: #ffadad;
    --danger-light: #ffe5e5;
    --danger-dark: #ff8080;
    --info-color: #bdb2ff;
    --info-light: #e7e2ff;
    --info-dark: #9d8cff;

    /* Gray Scale */
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-400: #9CA3AF;
    --gray-500: #6B7280;
    --gray-600: #4B5563;
    --gray-700: #374151;
    --gray-800: #1F2937;
    --gray-900: #111827;
}

/* Header styles */
.descriptif-header {
    margin-bottom: 30px;
    padding: 15px 0;
    text-align: left;
    animation: fadeIn 0.8s ease-in-out;
    position: relative;
}

.descriptif-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100px;
    height: 3px;
    background-color: var(--primary-color);
    background-image: linear-gradient(to right, var(--primary-color), var(--primary-light));
    border-radius: 3px;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.descriptif-header h1 {
    font-weight: 700;
    margin-bottom: 5px;
    font-size: 2.2rem;
    color: var(--gray-900);
    display: flex;
    align-items: center;
}

.descriptif-header h1 i {
    color: var(--primary-dark);
}

/* Filter section */
.filter-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.filter-title {
    color: var(--gray-700);
    font-weight: 600;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

/* Teaching units table */
.teaching-units-table {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
}

.teaching-units-table .table {
    margin-bottom: 0;
}

.teaching-units-table th {
    background-color: var(--gray-100);
    color: var(--gray-700);
    font-weight: 600;
    border-bottom: 2px solid var(--gray-200);
    padding: 12px 15px;
}

.teaching-units-table td {
    padding: 12px 15px;
    vertical-align: middle;
    border-bottom: 1px solid var(--gray-200);
}

.teaching-units-table tbody tr:hover {
    background-color: var(--primary-light);
}

/* Teaching unit cells */
.teaching-unit-cell {
    display: flex;
    flex-direction: column;
    gap: 5px;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.teaching-unit-cell:hover {
    background-color: var(--gray-100);
    transform: translateY(-2px);
}

.teaching-unit-cell .hours {
    margin-bottom: 5px;
}

.teaching-unit-cell .teacher {
    font-size: 0.9rem;
    color: var(--gray-700);
}

/* Type badges */
.badge-cours {
    background-color: var(--primary-light);
    color: var(--primary-dark);
    font-weight: 600;
    padding: 5px 10px;
    border-radius: 6px;
    display: inline-block;
}

.badge-td {
    background-color: var(--warning-light);
    color: var(--warning-dark);
    font-weight: 600;
    padding: 5px 10px;
    border-radius: 6px;
    display: inline-block;
}

.badge-tp {
    background-color: var(--success-light);
    color: var(--success-dark);
    font-weight: 600;
    padding: 5px 10px;
    border-radius: 6px;
    display: inline-block;
}

/* Action buttons */
.action-buttons {
    display: flex;
    gap: 8px;
}

.btn-edit {
    background-color: var(--warning-light);
    color: var(--warning-dark);
    border: 1px solid var(--warning-light);
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 6px 12px;
}

.btn-edit:hover {
    background-color: var(--warning-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 209, 102, 0.3);
}

.btn-delete {
    background-color: var(--danger-light);
    color: var(--danger-dark);
    border: 1px solid var(--danger-light);
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 6px 12px;
}

.btn-delete:hover {
    background-color: var(--danger-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 173, 173, 0.3);
}

/* Action buttons container */
.action-buttons-container {
    margin: 40px 0;
}

/* Add teaching unit button */
.btn-add-unit {
    background-color: var(--primary-color);
    color: var(--gray-800);
    border: none;
    padding: 15px 25px;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
    z-index: 1;
    font-size: 1.1rem;
}

.btn-add-unit::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background-color: var(--primary-dark);
    transition: all 0.3s ease;
    z-index: -1;
    border-radius: 12px;
}

.btn-add-unit:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(160, 196, 255, 0.4);
}

/* Import modules button */
.btn-import-module {
    background-color: var(--info-color);
    color: var(--gray-800);
    border: none;
    padding: 15px 25px;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
    z-index: 1;
    font-size: 1.1rem;
}

.btn-import-module::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background-color: var(--info-dark);
    transition: all 0.3s ease;
    z-index: -1;
    border-radius: 12px;
}

.btn-import-module:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(189, 178, 255, 0.4);
}

/* Direct module form container */
.direct-module-form-container {
    max-width: 900px;
    margin: 0 auto 30px;
}

.direct-module-form-container .card {
    border: none;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.direct-module-form-container .card:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1) !important;
}

.direct-module-form-container .card-header {
    border-bottom: 1px solid var(--gray-200);
    padding: 15px 20px;
}

.direct-module-form-container .card-header h5 {
    font-weight: 600;
    color: var(--gray-800);
}

.direct-module-form-container .card-body {
    padding: 20px;
}

.direct-module-form-container .form-label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 8px;
}

.direct-module-form-container .form-control,
.direct-module-form-container .form-select {
    border-radius: 8px;
    padding: 10px 12px;
    border-color: var(--gray-300);
    transition: all 0.2s ease;
}

.direct-module-form-container .form-control:focus,
.direct-module-form-container .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(160, 196, 255, 0.25);
}

.direct-module-form-container .btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--gray-800);
    border-radius: 8px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.direct-module-form-container .btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(160, 196, 255, 0.4);
}

/* View all modules button container */
.view-button-container {
    margin-top: 20px;
}

/* View all modules button - visually distinct */
.btn-view-all {
    background-color: transparent;
    color: var(--gray-700);
    border: 2px solid var(--gray-300);
    padding: 12px 30px;
    border-radius: 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
    font-size: 1.05rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.btn-view-all i {
    color: var(--primary-dark);
    transition: all 0.3s ease;
}

.btn-view-all:hover {
    color: var(--primary-dark);
    border-color: var(--primary-color);
    background-color: var(--primary-light);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(160, 196, 255, 0.3);
}

.btn-view-all:hover i {
    transform: scale(1.1);
}

.btn-add-unit:hover::before,
.btn-import-module:hover::before {
    width: 100%;
}

.btn-add-unit i {
    margin-right: 8px;
}

/* Modal styles */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    color: var(--gray-800);
    border-bottom: none;
    padding: 20px 25px;
}

.modal-header .btn-close {
    color: var(--gray-800);
    opacity: 0.8;
    transition: all 0.3s ease;
}

.modal-header .btn-close:hover {
    opacity: 1;
    transform: rotate(90deg);
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    border-top: none;
    padding: 15px 25px 25px;
}

.modal-footer .btn-primary {
    background-color: var(--primary-color);
    border: none;
    color: var(--gray-800);
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.modal-footer .btn-primary:hover {
    background-color: var(--primary-dark);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(160, 196, 255, 0.4);
}

.modal-footer .btn-secondary {
    background-color: var(--gray-200);
    border: none;
    color: var(--gray-700);
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.modal-footer .btn-secondary:hover {
    background-color: var(--gray-300);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Form styles */
.form-label {
    color: var(--gray-700);
    font-weight: 500;
    margin-bottom: 8px;
}

.form-control, .form-select {
    border: 1px solid var(--gray-300);
    border-radius: 10px;
    padding: 12px 15px;
    transition: all 0.3s ease;
    background-color: var(--gray-100);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(160, 196, 255, 0.3);
    background-color: white;
}

/* Section headings in forms */
.modal-body h5 {
    color: var(--gray-700);
    font-weight: 600;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--primary-light);
    margin-top: 20px;
    margin-bottom: 20px;
    position: relative;
}

.modal-body h5:after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background-color: var(--primary-color);
}

/* Import section */
.import-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.import-title {
    color: var(--gray-700);
    font-weight: 600;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.import-form {
    display: flex;
    align-items: center;
    gap: 15px;
}

.file-input-wrapper {
    flex-grow: 1;
}

.btn-import {
    background-color: var(--success-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-import:hover {
    background-color: #10b981;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-import i {
    margin-right: 8px;
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--gray-500);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: var(--gray-400);
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: var(--gray-700);
}

.empty-state p {
    margin-bottom: 20px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* Module cards */
.module-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    overflow: hidden;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.module-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.module-header {
    background-color: var(--gray-100);
    padding: 20px;
    border-bottom: 1px solid var(--gray-200);
}

.module-header h4 {
    margin-bottom: 5px;
    color: var(--gray-800);
    font-weight: 600;
}

.module-body {
    padding: 20px;
}

.unit-badge {
    background-color: var(--primary-light);
    color: var(--primary-dark);
    border-radius: 20px;
    padding: 5px 12px;
    font-size: 0.9rem;
    font-weight: 500;
    display: inline-block;
    margin-right: 10px;
}

.unit-badge.cours {
    background-color: var(--primary-light);
    color: var(--primary-dark);
}

.unit-badge.td {
    background-color: var(--warning-light);
    color: var(--warning-dark);
}

.unit-badge.tp {
    background-color: var(--success-light);
    color: var(--success-dark);
}

.unit-item {
    padding: 10px;
    border-bottom: 1px solid var(--gray-200);
    transition: all 0.3s ease;
}

.unit-item:hover {
    background-color: var(--gray-100);
}

.unit-item:last-child {
    border-bottom: none;
}

/* Checkbox styles */
.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-dark);
}

/* Unit type forms */
.unit-type-form {
    background-color: var(--gray-100);
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
    border-left: 4px solid var(--primary-color);
}

.unit-type-form h5 {
    color: var(--gray-700);
    margin-bottom: 15px;
    font-weight: 600;
    border-bottom: none;
}

.unit-type-form h5:after {
    display: none;
}

/* Add module button */
.btn-add-module {
    background-color: var(--primary-color);
    color: var(--gray-800);
    border: none;
    padding: 10px 20px;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-add-module::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background-color: var(--primary-dark);
    transition: all 0.3s ease;
    z-index: -1;
    border-radius: 12px;
}

.btn-add-module:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(160, 196, 255, 0.4);
}

.btn-add-module:hover::before {
    width: 100%;
}

.btn-add-module i {
    margin-right: 8px;
}

/* Import modules button */
.btn-import-module {
    background-color: var(--info-color);
    color: var(--gray-800);
    border: none;
    padding: 10px 20px;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-import-module::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background-color: var(--info-dark);
    transition: all 0.3s ease;
    z-index: -1;
    border-radius: 12px;
}

.btn-import-module:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(160, 196, 255, 0.4);
}

.btn-import-module:hover::before {
    width: 100%;
}

.btn-import-module i {
    margin-right: 8px;
}

/* Action buttons in module cards */
.action-btn {
    border-radius: 8px;
    padding: 6px 12px;
    margin-right: 5px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-add-unit.action-btn {
    background-color: var(--primary-light);
    color: var(--primary-dark);
    border: 1px solid var(--primary-light);
}

.btn-add-unit.action-btn:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(160, 196, 255, 0.3);
}

.btn-edit.action-btn {
    background-color: var(--warning-light);
    color: var(--warning-dark);
    border: 1px solid var(--warning-light);
}

.btn-edit.action-btn:hover {
    background-color: var(--warning-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 209, 102, 0.3);
}

.btn-delete.action-btn {
    background-color: var(--danger-light);
    color: var(--danger-dark);
    border: 1px solid var(--danger-light);
}

.btn-delete.action-btn:hover {
    background-color: var(--danger-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 173, 173, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .descriptif-header {
        padding: 20px;
    }

    .descriptif-header h1 {
        font-size: 1.8rem;
    }

    .filter-section, .import-section {
        padding: 15px;
    }

    .import-form {
        flex-direction: column;
        align-items: stretch;
    }

    .teaching-units-table {
        overflow-x: auto;
    }

    .teaching-units-table th,
    .teaching-units-table td {
        padding: 10px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 5px;
    }

    .module-header .row {
        flex-direction: column;
    }

    .module-header .col-md-4 {
        text-align: left !important;
        margin-top: 10px;
    }

    /* Responsive styles for action buttons */
    .btn-add-module, .btn-import-module {
        padding: 6px 12px;
        font-size: 0.9rem;
    }

    /* Responsive styles for direct module form */
    .direct-module-form-container {
        padding: 0 10px;
    }

    .direct-module-form-container .card-body {
        padding: 15px;
    }

    .direct-module-form-container .form-label {
        font-size: 0.9rem;
    }

    .direct-module-form-container .d-flex {
        flex-wrap: wrap;
    }

    /* Responsive styles for view all button */
    .view-button-container {
        text-align: center;
    }

    .btn-view-all {
        width: 100%;
        max-width: 250px;
        margin: 15px auto 5px;
    }
}