/* Pastel Color Variables */
:root {
  --pastel-blue: #A8D8F0;
  --pastel-purple: #D6C6E1;
  --pastel-pink: #F7D1D1;
  --pastel-green: #B8E0D2;
  --pastel-yellow: #F9EBC8;
  --pastel-orange: #F9D5BA;
  --pastel-light-blue: #E6F3FF;
  --pastel-light-purple: #F0EBFF;
  --pastel-light-pink: #FFF0F0;
  --pastel-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  --pastel-text: #4a5568;
  --pastel-border: #D1E3F8;
}

/* Main Container Styles */
.student-management-container {
  background-color: #f8f9fa;
  border-radius: 12px;
  padding: 15px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Page Header Styles */
.student-management-container h2 {
  color: var(--pastel-text);
  font-weight: 600;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.5rem;
}

.student-management-container h2:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: var(--pastel-blue);
  border-radius: 3px;
}

/* Button Styles */
.btn-primary {
  background-color: var(--pastel-blue);
  border: none;
  color: #4a5568;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  background-color: #8EC8E0;
}

.btn-secondary {
  background-color: #f8f9fa;
  border: 1px solid var(--pastel-border);
  color: var(--pastel-text);
}

.btn-secondary:hover {
  background-color: #e9ecef;
  color: var(--pastel-text);
}

/* Search and Filters Card */
.search-filters {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: var(--pastel-shadow);
  border: 1px solid var(--pastel-border);
  margin-bottom: 1.5rem;
  padding: 15px;
}

.search-box {
  position: relative;
}

.search-box .bi-search {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #a0aec0;
}

.search-box input {
  padding-left: 40px;
  border-radius: 8px;
  border: 1px solid var(--pastel-border);
  height: 45px;
  transition: all 0.3s ease;
}

.search-box input:focus {
  border-color: var(--pastel-purple);
  box-shadow: 0 0 0 3px rgba(186, 169, 255, 0.2);
}

.form-select {
  border-radius: 8px;
  border: 1px solid var(--pastel-border);
  height: 45px;
  color: var(--pastel-text);
  transition: all 0.3s ease;
}

.form-select:focus {
  border-color: var(--pastel-purple);
  box-shadow: 0 0 0 3px rgba(186, 169, 255, 0.2);
}

/* Student Table Styles */
.student-table {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: var(--pastel-shadow);
  border: 1px solid var(--pastel-border);
  overflow: hidden;
  max-width: 100%;
}

.student-table .card-body {
  padding: 5px;
  overflow-x: hidden;
}

.table {
  margin-bottom: 0;
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 12px; /* Increased space between rows */
  table-layout: fixed;
  overflow-x: hidden;
}

/* Column widths for better alignment */
.table th:nth-child(1),
.table td:nth-child(1) {
  width: 12%; /* CNE column */
}

.table th:nth-child(2),
.table td:nth-child(2) {
  width: 12%; /* First name column */
}

.table th:nth-child(3),
.table td:nth-child(3) {
  width: 12%; /* Last name column */
}

.table th:nth-child(4),
.table td:nth-child(4) {
  width: 18%; /* Email column */
}

.table th:nth-child(5),
.table td:nth-child(5) {
  width: 10%; /* Gender column */
}

.table th:nth-child(6),
.table td:nth-child(6) {
  width: 14%; /* Field column */
}

.table th:nth-child(7),
.table td:nth-child(7) {
  width: 8%; /* N° column */
  min-width: 40px;
  text-align: center;
  padding-left: 2px;
  padding-right: 2px;
}

.table th:nth-child(8),
.table td:nth-child(8) {
  width: 10%; /* Actions column */
  min-width: 80px;
}

.student-table th {
  background-color: var(--pastel-light-blue);
  color: var(--pastel-text);
  font-weight: 600;
  padding: 15px;
  border: none;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
  position: sticky;
  top: 0;
  z-index: 1;
  text-align: center;
}

.student-table td {
  vertical-align: middle;
  padding: 12px 8px;
  border: none;
  color: #4a5568;
  font-size: 0.85rem;
  background-color: #fff;
  transition: all 0.2s ease;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* First cell in row */
.student-table td:first-child {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

/* Last cell in row */
.student-table td:last-child {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}

.student-table tr:hover td {
  background-color: var(--pastel-light-blue);
}

/* Alternating row colors */
.student-table tbody tr:nth-child(even) td {
  background-color: #f8f9fa;
}

.student-table tbody tr:nth-child(even):hover td {
  background-color: var(--pastel-light-purple);
}

/* Add a subtle line between rows */
.student-table tbody tr {
  position: relative;
}

.student-table tbody tr::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--pastel-border);
  opacity: 0.5;
}

/* Mobile Student Cards */
.student-cards {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.student-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.student-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.student-card-header {
  background-color: var(--pastel-light-blue);
  padding: 12px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--pastel-border);
}

.student-card-header h5 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--pastel-text);
}

.student-card-body {
  padding: 15px;
}

.student-card-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.student-card-info-item {
  display: flex;
  justify-content: space-between;
  padding-bottom: 8px;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.05);
}

.student-card-info-item:last-child {
  border-bottom: none;
}

.student-card-label {
  font-weight: 500;
  color: #718096;
  font-size: 0.85rem;
}

.student-card-value {
  color: var(--pastel-text);
  font-size: 0.9rem;
  text-align: right;
}

.student-card-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid var(--pastel-border);
}

/* Action Buttons */
.student-actions {
  display: flex;
  justify-content: center;
  gap: 2px;
  padding: 5px 0;
}

.student-actions .btn {
  width: 32px;
  height: 32px;
  padding: 0;
  border-radius: 6px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  margin: 0 1px;
}

.student-actions .btn i {
  font-size: 0.8rem;
}

.student-actions .btn-info {
  background-color: var(--pastel-blue);
  border: none;
  color: #2c5282;
}

.student-actions .btn-warning {
  background-color: var(--pastel-orange);
  border: none;
  color: #744210;
}

.student-actions .btn-danger {
  background-color: var(--pastel-pink);
  border: none;
  color: #742a2a;
}

.student-actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Modal Styles */
.modal-content {
  border-radius: 12px;
  border: none;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.modal-header {
  background-color: var(--pastel-blue);
  color: var(--pastel-text);
  border-bottom: none;
  padding: 20px 25px;
}

.modal-title {
  font-weight: 600;
}

.modal-body {
  padding: 25px;
}

.modal-footer {
  background-color: #f8f9fa;
  border-top: 1px solid var(--pastel-border);
  padding: 15px 25px;
}

.form-label {
  color: var(--pastel-text);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-control {
  border-radius: 8px;
  border: 1px solid var(--pastel-border);
  padding: 10px 15px;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--pastel-purple);
  box-shadow: 0 0 0 3px rgba(186, 169, 255, 0.2);
}

/* Form Section Styles */
.form-section {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  border: 1px solid var(--pastel-border);
  transition: all 0.3s ease;
}

.form-section:hover {
  box-shadow: var(--pastel-shadow);
}

.form-section-title {
  color: var(--pastel-text);
  font-weight: 600;
  position: relative;
  padding-bottom: 8px;
  margin-bottom: 15px;
}

.form-section-title:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background-color: var(--pastel-blue);
  border-radius: 2px;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .student-management-container {
    padding: 15px;
  }

  /* Ajustement des largeurs de colonnes pour les écrans plus petits */
  .table th:nth-child(1),
  .table td:nth-child(1) {
    width: 8%; /* N° column */
  }

  .table th:nth-child(2),
  .table td:nth-child(2),
  .table th:nth-child(3),
  .table td:nth-child(3) {
    width: 14%; /* First name, Last name columns */
  }
}

@media (max-width: 992px) {
  /* Ajuster la table pour qu'elle soit responsive sans défilement horizontal */
  .table-responsive {
    overflow-x: hidden !important;
    margin-bottom: 1rem;
    max-width: 100%;
  }

  .student-table {
    width: 100%;
  }

  .student-table th,
  .student-table td {
    padding: 10px 8px;
  }

  .student-management-container h2 {
    font-size: 1.5rem;
  }

  .search-filters .row {
    flex-direction: column;
  }

  .search-filters .col-md-4,
  .search-filters .col-md-3 {
    width: 100%;
    margin-bottom: 10px;
  }

  /* Ajuster les boutons d'action pour les écrans moyens */
  .student-actions .btn {
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 768px) {
  .d-flex.justify-content-between.align-items-center {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 15px;
  }

  .d-flex.justify-content-between.align-items-center button {
    align-self: stretch;
  }

  /* Améliorer la pagination sur mobile */
  .pagination-controls {
    flex-direction: column;
    gap: 15px;
    width: 100%;
    padding-top: 20px;
  }

  .pagination-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
    margin-top: 10px;
  }

  .pagination-buttons .btn {
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 10px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .pagination-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
  }

  .pagination-buttons .btn-primary {
    background-color: var(--pastel-blue);
    border-color: var(--pastel-blue);
    color: #333;
  }

  /* Approche complètement différente pour mobile - Masquer le tableau et afficher des cartes */
  .table-responsive {
    overflow-x: hidden !important;
    max-width: 100%;
  }

  .student-table .table {
    display: none; /* Masquer complètement le tableau sur mobile */
  }

  /* Créer des cartes d'étudiant pour mobile */
  .student-cards {
    display: block !important; /* Forcer l'affichage des cartes sur mobile */
    margin-top: 20px;
  }

  .student-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    margin-bottom: 20px;
    overflow: hidden;
    border: 1px solid var(--pastel-border);
  }

  .student-card-header {
    background-color: var(--pastel-light-blue);
    padding: 15px;
    border-bottom: 1px solid var(--pastel-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .student-card-header h5 {
    margin: 0;
    font-weight: 600;
    color: var(--pastel-text);
    font-size: 1.1rem;
  }

  .student-card-header .badge {
    background-color: var(--pastel-blue);
    color: #333;
    font-weight: 600;
    padding: 5px 10px;
    border-radius: 6px;
  }

  .student-card-body {
    padding: 0;
  }

  .student-card-info {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .student-card-info li {
    display: flex;
    padding: 12px 15px;
    border-bottom: 1px solid var(--pastel-border);
    align-items: center;
  }

  .student-card-info li:nth-child(even) {
    background-color: rgba(248, 249, 250, 0.5);
  }

  .student-card-label {
    font-weight: 600;
    width: 40%;
    min-width: 100px;
    color: var(--pastel-text);
    font-size: 0.9rem;
    opacity: 0.8;
  }

  .student-card-value {
    flex: 1;
    text-align: right;
    font-weight: 500;
    color: #333;
    word-break: break-word;
  }

  .student-card-actions {
    display: flex;
    justify-content: center;
    padding: 15px;
    background-color: #f8f9fa;
    border-top: 1px solid var(--pastel-border);
    gap: 10px;
  }

  .student-card-actions .btn {
    width: 45px;
    height: 45px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
  }

  .student-card-actions .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  .student-card-actions .btn i {
    font-size: 1.1rem;
  }

  .modal-dialog {
    margin: 10px;
  }

  .modal-body {
    padding: 15px;
  }

  .form-control,
  .form-select {
    font-size: 0.9rem;
  }
}

@media (max-width: 576px) {
  .student-management-container {
    padding: 10px;
  }

  .card-body {
    padding: 15px 10px;
  }

  /* Simplifier l'affichage des boutons de pagination sur très petits écrans */
  .pagination-buttons .btn {
    padding: 4px 8px;
    font-size: 0.8rem;
  }

  .pagination-buttons .btn i {
    font-size: 0.8rem;
  }

  /* Cacher certains numéros de page sur très petits écrans */
  .pagination-buttons .btn:not(:first-child):not(:last-child):not(.active) {
    display: none;
  }

  /* Ajustements supplémentaires pour la table sur très petits écrans */
  .table td {
    padding: 8px 10px;
    font-size: 0.85rem;
  }

  .table td:before {
    font-size: 0.8rem;
  }

  /* Ajuster les boutons d'action pour les très petits écrans */
  .student-actions .btn {
    width: 30px;
    height: 30px;
  }

  .student-actions .btn i {
    font-size: 0.8rem;
  }

  .modal-header,
  .modal-footer {
    padding: 15px;
  }

  .modal-body {
    padding: 10px;
  }

  .btn {
    font-size: 0.85rem;
    padding: 6px 12px;
  }

  /* Améliorer l'affichage du formulaire sur mobile */
  .form-section {
    padding: 15px 10px;
  }

  .form-section-title {
    font-size: 1rem;
  }

  .row.g-3 {
    gap: 0.5rem !important;
  }
}