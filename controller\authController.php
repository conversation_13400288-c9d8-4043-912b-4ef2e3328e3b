<?php
require_once "../model/authModel.php";
require_once "../model/visitsModel.php";
require_once "../config/constants.php";

/**
 * Enregistre une visite lors de l'authentification d'un utilisateur
 *
 * @param string $userType Type d'utilisateur (admin, enseignant, etudiant, etc.)
 * @return bool Succès de l'opération
 */
function recordUserVisit($userType) {
    try {
        // Appeler la fonction du modèle pour enregistrer la visite
        return recordVisit($userType, 'login');
    } catch (Exception $e) {
        error_log("Erreur lors de l'enregistrement de la visite: " . $e->getMessage());
        return false;
    }
}

// Traitement des requêtes POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents('php://input'), true);

    if (isset($data['action'])) {
        switch ($data['action']) {
            case 'login':
                $response = handleLogin($data['username'], $data['password']);
                header('Content-Type: application/json');
                echo json_encode($response);
                exit;
            case 'logout':
                $response = logout();
                header('Content-Type: application/json');
                echo json_encode($response);
                exit;
            case 'changePassword':
                $response = changePassword($data['username'], $data['oldPassword'], $data['newPassword']);
                header('Content-Type: application/json');
                echo json_encode($response);
                exit;
        }
    }
}

function handleLogin($username, $password) {
    try {
        error_log("Login attempt for username: $username");

        if (empty($username) || empty($password)) {
            return ['success' => false, 'error' => 'Veuillez remplir tous les champs'];
        }

        // Validation des données
        $username = filter_var($username, FILTER_SANITIZE_STRING);
        if (strlen($password) < 6) {
            return ['success' => false, 'error' => 'Le mot de passe doit contenir au moins 6 caractères'];
        }

        $result = authenticateUser($username, $password);

        if ($result['success']) {
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }
            session_regenerate_id(true);
            $_SESSION['user'] = $result['user'];
            $_SESSION['last_activity'] = time();
            $_SESSION['ip'] = $_SERVER['REMOTE_ADDR'];

            // Enregistrer la visite
            recordUserVisit($result['user']['role']);

            // Journaliser le rôle de l'utilisateur
            error_log("User authenticated successfully. Username: $username, Role: " . $result['user']['role']);

            // Traitement spécial pour les coordinateurs
            if ($result['user']['role'] === 'coordinateur') {
                error_log("Coordinator login detected. Username: $username");

                // Inclure le modèle d'authentification pour accéder à la fonction getCoordinatorFiliere
                require_once __DIR__ . '/../model/authModel.php';

                // Vérifier si le filiere_id est déjà dans le résultat
                if (!isset($result['user']['filiere_id'])) {
                    error_log("filiere_id not found in result for coordinator: $username");

                    // Récupérer le filiere_id
                    $filiereId = getCoordinatorFiliere($username);

                    // Si aucun filiere_id n'est trouvé, définir une valeur par défaut
                    if (!$filiereId) {
                        error_log("No filiere_id found for coordinator: $username. Setting default value.");
                        $_SESSION['user']['filiere_id'] = 0;
                        $_SESSION['user']['filiere_name'] = 'Non spécifié';
                    } else {
                        error_log("filiere_id found for coordinator: $username. ID: $filiereId");
                        $_SESSION['user']['filiere_id'] = $filiereId;

                        // Récupérer le nom de la filière
                        $conn = getConnection();
                        if ($conn) {
                            $sql = "SELECT nom_filiere FROM filiere WHERE id_filiere = $filiereId";
                            $queryResult = mysqli_query($conn, $sql);
                            if ($queryResult && mysqli_num_rows($queryResult) > 0) {
                                $row = mysqli_fetch_assoc($queryResult);
                                $_SESSION['user']['filiere_name'] = $row['nom_filiere'];
                                error_log("filiere_name found: " . $_SESSION['user']['filiere_name']);
                            } else {
                                $_SESSION['user']['filiere_name'] = 'Non spécifié';
                                error_log("No filiere_name found for ID: $filiereId");
                            }
                            mysqli_close($conn);
                        }
                    }
                } else {
                    error_log("filiere_id already in result for coordinator: $username. ID: " . $result['user']['filiere_id']);
                }
            }

            // Définir l'URL de redirection en fonction du rôle
            switch ($result['user']['role']) {
                case 'admin':
                    $redirectUrl = BASE_URL . '/view/admin/dashboard.php';
                    break;
                case 'enseignant':
                    $redirectUrl = BASE_URL . '/view/enseignant/dashboard.php';
                    break;
                case 'chef de departement':
                    $redirectUrl = BASE_URL . '/view/chef/dashboard.php';
                    break;
                case 'coordinateur':
                    $redirectUrl = BASE_URL . '/view/coordinator/dashboard.php';
                    break;
                case 'vacataire':
                    $redirectUrl = BASE_URL . '/view/vacataire/dashboard.php';
                    break;
                case 'etudiant':
                    $redirectUrl = BASE_URL . '/view/etudiant/dashboard.php';
                    break;
                default:
                    $redirectUrl = BASE_URL . '/view/index.php';
            }

            error_log("Redirecting user to: $redirectUrl");
            return ['success' => true, 'redirect' => $redirectUrl];
        } else {
            error_log("Authentication failed for username: $username. Error: " . ($result['error'] ?? 'Unknown error'));
        }

        return $result;
    } catch (Exception $e) {
        error_log("Exception in handleLogin: " . $e->getMessage() . " for username: $username");
        return ['success' => false, 'error' => 'Une erreur est survenue lors de la connexion. Veuillez réessayer.'];
    }
}

function isAuthenticated() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Vérifier si l'utilisateur est connecté
    if (!isset($_SESSION['user'])) {
        return false;
    }

    // Vérifier l'expiration de la session (30 minutes)
    if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > 1800)) {
        session_destroy();
        return false;
    }

    // Vérifier si l'IP a changé
    if (!isset($_SESSION['ip']) || $_SESSION['ip'] !== $_SERVER['REMOTE_ADDR']) {
        // Pour les coordinateurs, mettre à jour l'IP au lieu de détruire la session
        if (isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'coordinateur') {
            // Journaliser l'incident
            error_log("IP mismatch in isAuthenticated for coordinator: " .
                      "Username: " . ($_SESSION['user']['username'] ?? 'unknown') .
                      ", Session IP: " . ($_SESSION['ip'] ?? 'not set') .
                      ", Current IP: " . $_SERVER['REMOTE_ADDR']);

            // Mettre à jour l'IP
            $_SESSION['ip'] = $_SERVER['REMOTE_ADDR'];

            // Enregistrer le changement d'IP
            if (!isset($_SESSION['ip_changes'])) {
                $_SESSION['ip_changes'] = [];
            }
            $_SESSION['ip_changes'][] = [
                'old_ip' => $_SESSION['ip'] ?? 'not set',
                'new_ip' => $_SERVER['REMOTE_ADDR'],
                'timestamp' => time(),
                'context' => 'isAuthenticated'
            ];
        } else {
            // Pour les autres utilisateurs, comportement standard
            session_destroy();
            return false;
        }
    }

    // Mettre à jour le timestamp de dernière activité
    $_SESSION['last_activity'] = time();
    return true;
}

function logout() {
    session_start();
    session_destroy();
    return ['success' => true, 'redirect' => BASE_URL . '/view/index.php'];
}

function getUserProfile($username) {
    if (empty($username)) {
        return ['success' => false, 'error' => 'Nom d\'utilisateur requis'];
    }

    $result = getUserInfo($username);
    return $result;
}

function changePassword($username, $oldPassword, $newPassword) {
    // Validation des données
    if (empty($username) || empty($oldPassword) || empty($newPassword)) {
        return ['success' => false, 'error' => 'Tous les champs sont requis'];
    }

    // Vérifier la longueur du nouveau mot de passe
    if (strlen($newPassword) < 6) {
        return ['success' => false, 'error' => 'Le nouveau mot de passe doit contenir au moins 6 caractères'];
    }

    // Mettre à jour le mot de passe
    $result = updateUserPassword($username, $oldPassword, $newPassword);
    return $result;
}
?>