// Global variables and functions
// Fonction pour récupérer dynamiquement le chemin de base
function getBasePath(routeFile = 'messagesRoute.php') {
    // Obtenir le chemin de base à partir de l'URL actuelle
    const currentPath = window.location.pathname;
    // Trouver l'index du dossier "view" ou "admin"
    const viewIndex = currentPath.indexOf('/view/');
    const adminIndex = currentPath.indexOf('/admin/');

    // Déterminer le chemin de base en fonction de la structure de l'URL
    let basePath = '';
    if (viewIndex !== -1) {
        // Si nous sommes dans un sous-dossier de "view"
        basePath = currentPath.substring(0, viewIndex);
    } else if (adminIndex !== -1) {
        // Si nous sommes dans un sous-dossier de "admin"
        basePath = currentPath.substring(0, adminIndex);
    }

    return `${basePath}/route/${routeFile}`;
}

const basePath = getBasePath('messagesRoute.php');
// Get admin ID from session (you may need to adjust this based on your authentication system)
// We'll set this to null and let the server find a valid user ID
const adminId = null;

// Make createNewMessage function globally accessible
function createNewMessage() {
    console.log('createNewMessage function called');

    // Get the send button
    const sendButton = document.getElementById('sendMessageBtn');

    // Check if the button is already disabled (to prevent duplicate submissions)
    if (sendButton && sendButton.disabled) {
        console.log('Send button is already disabled, preventing duplicate submission');
        return;
    }

    // Get form values
    const title = document.getElementById('messageTitle').value.trim();
    const receiverId = document.getElementById('receiverId').value.trim();
    const receiverUsername = document.getElementById('receiverUsername').value.trim();
    const content = document.getElementById('messageContent').value.trim();
    const mediaUrl = document.getElementById('mediaUrl').value.trim();

    console.log('Form values:', { title, receiverId, receiverUsername, content, mediaUrl });

    // Validate form
    if (!title || !content) {
        showErrorModal("Veuillez remplir tous les champs obligatoires");
        return;
    }

    if (!receiverId || !receiverUsername) {
        showErrorModal("Veuillez sélectionner un destinataire valide");
        return;
    }

    // Disable the send button and show loading state
    if (sendButton) {
        sendButton.disabled = true;
        sendButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...';
    }

    // Check if we have a file to upload
    if (window.selectedFile) {
        // Upload the file first
        const formData = new FormData();
        formData.append('file', window.selectedFile);

        console.log('Uploading file:', window.selectedFile.name);

        fetch('../../route/fileUploadRoute.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Upload response status:', response.status);

            // Même si la réponse n'est pas OK, on essaie de récupérer le texte
            return response.text().then(text => {
                console.log('Upload response text:', text);

                // Essayer de parser le texte en JSON
                try {
                    const data = JSON.parse(text);
                    // Si la réponse n'est pas OK, on lance une erreur avec le message d'erreur
                    if (!response.ok) {
                        throw new Error(data.error || 'Erreur serveur: ' + response.status);
                    }
                    return data;
                } catch (e) {
                    console.error('Error parsing JSON:', e);
                    // Si on ne peut pas parser le JSON, on affiche le texte brut
                    if (!response.ok) {
                        throw new Error('Erreur serveur: ' + response.status + ' - ' + text.substring(0, 100));
                    } else {
                        throw new Error('Erreur de parsing JSON: ' + text.substring(0, 100));
                    }
                }
            });
        })
        .then(data => {
            if (data.success) {
                console.log('File uploaded successfully:', data.file_path);
                // Now send the message with the file path
                sendMessageWithFilePath(title, receiverId, content, mediaUrl, data.file_path);
            } else {
                throw new Error('Failed to upload file: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error uploading file:', error);
            showErrorModal("Erreur lors du téléchargement du fichier: " + error.message);

            // Re-enable the send button
            if (sendButton) {
                sendButton.disabled = false;
                sendButton.textContent = "Send Message";
            }
        });
    } else {
        // No file to upload, send the message directly
        sendMessageWithFilePath(title, receiverId, content, mediaUrl, null);
    }
}

function sendMessageWithFilePath(title, receiverId, content, mediaUrl, filePath) {
    const sendButton = document.getElementById('sendMessageBtn');

    // Log pour le débogage
    console.log("Sending message with file path:", {
        title,
        receiverId,
        content,
        mediaUrl,
        filePath
    });

    // Send the request to create a new message
    fetch(`${basePath}?action=createAdminMessage`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
            sender_id: null, // Let the server find a valid user ID
            title: title,
            content: content,
            receiver_id: receiverId,
            media_url: mediaUrl || null,
            file_path: filePath || null
        })
    })
    .then(response => response.text())
    .then(text => {
        console.log('Response text:', text);
        try {
            const data = JSON.parse(text);
            if (data.success) {
                // Hide the create message modal
                const createModalElement = document.getElementById('createMessageModal');
                const createModal = bootstrap.Modal.getInstance(createModalElement);
                if (createModal) {
                    createModal.hide();

                    // Supprimez tous les backdrops après la fermeture
                    setTimeout(() => {
                        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                            backdrop.remove();
                        });

                        // Réinitialisez le style du body
                        document.body.classList.remove('modal-open');
                        document.body.style.overflow = '';
                        document.body.style.paddingRight = '';
                    }, 300);
                }

                // Reset form
                document.getElementById('createMessageForm').reset();

                // Reset hidden fields
                document.getElementById('mediaUrl').value = '';
                document.getElementById('filePath').value = '';

                // Reset selected file
                window.selectedFile = null;

                // Remove attachment indicators
                const indicators = document.querySelectorAll('.message-attachment-indicator');
                indicators.forEach(indicator => indicator.remove());

                // Set success message
                document.getElementById('successMessage').textContent = "Message envoyé avec succès!";

                // Show success modal
                const successModalElement = document.getElementById('successModal');
                if (successModalElement) {
                    // Utiliser notre fonction utilitaire pour afficher le modal proprement
                    showCleanModal(successModalElement, 3000);
                }

                // Reload messages
                loadMessages(currentPage);
            } else {
                // Show error in modal
                showErrorModal("Échec de l'envoi du message: " + (data.error || "Erreur inconnue"));
            }
        } catch (e) {
            // Show parsing error in modal
            showErrorModal("Erreur de traitement de la réponse: " + e.message);
        }
    })
    .catch(error => {
        // Show network error in modal
        showErrorModal("Erreur d'envoi du message: " + error.message);
    })
    .finally(() => {
        // Re-enable the send button
        if (sendButton) {
            sendButton.disabled = false;
            sendButton.textContent = "Send Message";
        }
    });
}

// Fonction utilitaire pour afficher un modal de manière propre
function showCleanModal(modalElement, autoHideDelay = 0) {
    if (!modalElement) return;

    // Assurez-vous qu'aucun modal n'est déjà ouvert
    const existingModal = bootstrap.Modal.getInstance(modalElement);
    if (existingModal) {
        existingModal.dispose();
    }

    // Supprimez tous les backdrops existants
    document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
        backdrop.remove();
    });

    // Réinitialisez le style du body
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';

    // Créez et affichez le nouveau modal
    const modal = new bootstrap.Modal(modalElement, {
        backdrop: 'static',
        keyboard: false
    });

    // Ajoutez un événement pour nettoyer après la fermeture
    modalElement.addEventListener('hidden.bs.modal', function () {
        // Supprimez tous les backdrops
        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
            backdrop.remove();
        });

        // Réinitialisez le style du body
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
    }, { once: true });

    modal.show();

    // Auto-hide si un délai est spécifié
    if (autoHideDelay > 0) {
        setTimeout(() => {
            modal.hide();

            // Supprimez tous les backdrops après la fermeture
            document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                backdrop.remove();
            });

            // Réinitialisez le style du body
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }, autoHideDelay);
    }

    return modal;
}

// Fonction pour afficher un message d'erreur dans le modal
function showErrorModal(errorMessage) {
    // Configurer le modal pour afficher une erreur
    document.getElementById('successModalLabel').innerHTML = '<i class="fas fa-exclamation-circle me-2"></i>Erreur';
    document.getElementById('successModalLabel').parentNode.classList.remove('bg-success');
    document.getElementById('successModalLabel').parentNode.classList.add('bg-danger');
    document.getElementById('successMessage').textContent = errorMessage;
    document.querySelector('#successModal .modal-body i').classList.remove('fa-check-circle', 'text-success');
    document.querySelector('#successModal .modal-body i').classList.add('fa-exclamation-circle', 'text-danger');
    document.querySelector('#successModal .modal-footer button').classList.remove('btn-success');
    document.querySelector('#successModal .modal-footer button').classList.add('btn-danger');

    // Afficher le modal
    showCleanModal(document.getElementById('successModal'));
}

// Fonction pour afficher un modal de confirmation
function showConfirmationModal(message, callback) {
    // Configurer le message de confirmation
    document.getElementById('confirmationMessage').textContent = message;

    // Configurer le bouton de confirmation
    const confirmBtn = document.getElementById('confirmActionBtn');

    // Supprimer les anciens écouteurs d'événements
    const newConfirmBtn = confirmBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

    // Ajouter un nouvel écouteur d'événement
    newConfirmBtn.addEventListener('click', function() {
        // Fermer le modal
        const confirmationModalElement = document.getElementById('confirmationModal');
        const confirmationModal = bootstrap.Modal.getInstance(confirmationModalElement);
        if (confirmationModal) {
            confirmationModal.hide();
        }

        // Exécuter le callback
        if (typeof callback === 'function') {
            callback();
        }
    });

    // Afficher le modal proprement
    showCleanModal(document.getElementById('confirmationModal'));
}

// Variable globale pour la page courante
let currentPage = 1;

// Fonction globale pour charger les messages
function loadMessages(page = 1) {
    currentPage = page;
    console.log(`Fetching messages from: ${basePath}?action=getAll&page=${page}`);
    fetch(`${basePath}?action=getAll&page=${page}`)
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                return response.text().then(text => {
                    console.error('Error response text:', text);
                    throw new Error(`Server responded with status ${response.status}: ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            const container = document.getElementById("notificationsList");
            if (!container) return;

            if (!data.messages || data.messages.length === 0) {
                container.innerHTML = "<p class='empty-state'>No messages found.</p>";
                return;
            }

            let messagesHTML = '';
            data.messages.forEach(message => {
                // Carte compacte
                messagesHTML += `
                    <div class="me-card compact ${message.is_read ? 'read' : ''}" data-message-id="${message.id}">
                        <div class="notification-icon">
                            <i class="fas fa-envelope${message.is_read ? '-open' : ''}"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-title-container">
                                <h3 class="notification-title">${message.title}</h3>
                            </div>
                            <span class="notification-time">
                                ${new Date(message.created_at).toLocaleString()}
                            </span>
                        </div>
                    </div>`;
            });

            // Ajouter le HTML pour le modal de détails
            messagesHTML += `
                <div class="modal fade" id="messageDetailModal" tabindex="-1" aria-labelledby="messageDetailModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="messageDetailModalLabel">Message Details</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body" id="messageDetailContent">
                                <!-- Le contenu sera injecté dynamiquement -->
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>`;


            const paginationHTML = `
                <div class="pagination-controls">
                    ${page > 1 ?
                        `<button class="pagination-btn" data-page="${page-1}">Previous</button>` : ''}
                    <span class="page-info">Page ${page} of ${data.pages}</span>
                    ${page < data.pages ?
                        `<button class="pagination-btn" data-page="${page+1}">Next</button>` : ''}
                </div>
            `;

            container.innerHTML = messagesHTML + paginationHTML;
            addEventListeners();
            addPaginationListeners();
        })
        .catch(err => {
            console.error("Error loading messages:", err);
            const container = document.getElementById("notificationsList");
            if (container) {
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Error loading messages</h4>
                        <p>${err.message}</p>
                        <p>Please check the console for more details or try again later.</p>
                    </div>
                `;
            }
        });
}

// Fonction globale pour ajouter des écouteurs d'événements aux boutons de pagination
function addPaginationListeners() {
    document.querySelectorAll('.pagination-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            currentPage = parseInt(e.target.dataset.page);
            loadMessages(currentPage);
        });
    });
}

// Fonction pour afficher les détails d'un message dans le modal
function showMessageDetails(messageId) {
    console.log('Showing details for message:', messageId);

    // Trouver le message dans les données actuelles
    fetch(`${basePath}?action=getAll&page=${currentPage}`)
        .then(response => response.json())
        .then(data => {
            const message = data.messages.find(m => m.id == messageId);
            if (!message) {
                console.error('Message not found:', messageId);
                return;
            }

            // Construire le contenu HTML pour le modal
            let detailsHTML = `
                <div class="message-detail-header">
                    <h3>${message.title}</h3>
                    <div class="message-meta">
                        <span class="message-date">${new Date(message.created_at).toLocaleString()}</span>
                        ${message.receiver_id ? `<span class="message-receiver">Sent to: Receiver #${message.receiver_id}</span>` : ''}
                    </div>
                </div>
                <div class="message-detail-content">
                    <p>${message.content}</p>
                </div>`;

            // Ajouter les liens pour les pièces jointes
            if (message.media_url || message.file_path) {
                detailsHTML += `<div class="message-attachments">`;

                if (message.media_url) {
                    detailsHTML += `
                        <a href="${message.media_url}" target="_blank" rel="noopener noreferrer"
                            class="action-link visit-link-btn" onclick="window.open('${message.media_url}', '_blank')">
                            Visit Link</a>`;
                }

                if (message.file_path) {
                    detailsHTML += `
                        <a href="../../route/downloadFileRoute.php?file=${encodeURIComponent(message.file_path)}"
                            class="action-link download-file-btn">
                            Download File</a>`;
                }

                detailsHTML += `</div>`;
            }

            // Ajouter les boutons d'action
            detailsHTML += `<div class="message-actions mt-4">`;

            // Le bouton "Mark as read" ne sera pas affiché car le message sera automatiquement marqué comme lu
            // Nous gardons cette logique au cas où nous voudrions changer le comportement plus tard

            detailsHTML += `
                <button class="delete-btn" data-id="${message.id}">Delete</button>
            </div>`;

            // Injecter le contenu dans le modal
            document.getElementById('messageDetailContent').innerHTML = detailsHTML;
            document.getElementById('messageDetailModalLabel').textContent = message.is_read ? 'Message' : 'New Message';

            // Ajouter les écouteurs d'événements aux boutons dans le modal
            const modal = document.getElementById('messageDetailModal');

            // Bouton "Delete"
            const deleteBtn = modal.querySelector('.delete-btn');
            if (deleteBtn) {
                deleteBtn.addEventListener('click', function() {
                    // Fermer le modal de détails
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }

                    // Afficher le modal de confirmation
                    showConfirmationModal("Êtes-vous sûr de vouloir supprimer ce message?", async function() {
                        try {
                            const response = await fetch(`${basePath}?action=deleteMessage`, {
                                method: "POST",
                                headers: { "Content-Type": "application/json" },
                                body: JSON.stringify({ id: messageId })
                            });
                            const data = await response.json();
                            if (data.success) {
                                // Afficher le message de succès
                                document.getElementById('successModalLabel').innerHTML = '<i class="fas fa-check-circle me-2"></i>Succès';
                                document.getElementById('successModalLabel').parentNode.classList.remove('bg-danger');
                                document.getElementById('successModalLabel').parentNode.classList.add('bg-success');
                                document.getElementById('successMessage').textContent = "Message supprimé avec succès!";
                                document.querySelector('#successModal .modal-body i').classList.remove('fa-exclamation-circle', 'text-danger');
                                document.querySelector('#successModal .modal-body i').classList.add('fa-check-circle', 'text-success');
                                document.querySelector('#successModal .modal-footer button').classList.remove('btn-danger');
                                document.querySelector('#successModal .modal-footer button').classList.add('btn-success');

                                const successModalElement = document.getElementById('successModal');
                                if (successModalElement) {
                                    showCleanModal(successModalElement, 2000);
                                }

                                // Recharger les messages
                                loadMessages(currentPage);
                            }
                        } catch (error) {
                            console.error("Error deleting message:", error);
                            showErrorModal("Erreur lors de la suppression du message");
                        }
                    });
                });
            }

            // Si le message n'est pas encore lu, le marquer comme lu
            if (!message.is_read) {
                console.log('Marking message as read automatically:', messageId);
                fetch(`${basePath}?action=markAsRead`, {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ id: messageId })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Server responded with status ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        console.log('Message marked as read successfully');

                        // Mettre à jour l'apparence de la carte dans la liste
                        const messageCard = document.querySelector(`.me-card[data-message-id="${messageId}"]`);
                        if (messageCard) {
                            messageCard.classList.add('read');
                            const icon = messageCard.querySelector('.notification-icon i');
                            if (icon) {
                                icon.className = 'fas fa-envelope-open';
                            }
                        }

                        // Cacher le bouton "Mark as read" dans le modal
                        const markReadBtn = modal.querySelector('.mark-read-btn');
                        if (markReadBtn) {
                            markReadBtn.style.display = 'none';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error marking message as read:', error);
                    // Ne pas afficher d'erreur à l'utilisateur pour ne pas perturber l'expérience
                });
            }

            // Afficher le modal en utilisant notre fonction showCleanModal
            showCleanModal(modal);
        })
        .catch(error => {
            console.error("Error fetching message details:", error);
            showToast("Error fetching message details", true);
        });
}

// Fonction globale pour ajouter des écouteurs d'événements
function addEventListeners() {
    console.log('Adding event listeners...');

    // Create Message button event listener
    const createMessageBtn = document.getElementById('createMessageBtn');
    console.log('Create Message button found:', !!createMessageBtn);
    if (createMessageBtn) {
        createMessageBtn.addEventListener('click', function() {
            console.log('Create Message button clicked');
            // Show the create message modal using our clean modal function
            const createMessageModalElement = document.getElementById('createMessageModal');

            // Assurez-vous qu'aucun modal n'est déjà ouvert
            const existingModal = bootstrap.Modal.getInstance(createMessageModalElement);
            if (existingModal) {
                existingModal.dispose();
            }

            // Supprimez tous les backdrops existants
            document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                backdrop.remove();
            });

            // Réinitialisez le style du body
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';

            // Créez et affichez le nouveau modal
            const createMessageModal = new bootstrap.Modal(createMessageModalElement);

            // Ajoutez un événement pour nettoyer après la fermeture
            createMessageModalElement.addEventListener('hidden.bs.modal', function () {
                // Supprimez tous les backdrops
                document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                    backdrop.remove();
                });

                // Réinitialisez le style du body
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
            }, { once: true });

            createMessageModal.show();

            // Make sure the Send Message button has an event listener
            // We'll add this listener only once when the modal is shown
            const sendMessageBtn = document.getElementById('sendMessageBtn');
            if (sendMessageBtn && !sendMessageBtn.hasAttribute('data-listener-attached')) {
                console.log('Adding event listener to Send Message button inside modal');
                sendMessageBtn.addEventListener('click', createNewMessage);
                // Mark the button as having a listener attached
                sendMessageBtn.setAttribute('data-listener-attached', 'true');
            }
        });
    }

    // Ajouter des écouteurs d'événements aux cartes de messages
    document.querySelectorAll('.me-card.compact').forEach(card => {
        card.addEventListener('click', function() {
            const messageId = this.getAttribute('data-message-id');
            if (messageId) {
                showMessageDetails(messageId);
            }
        });
    });

    // Add event listeners to download file buttons
    document.querySelectorAll(".download-file-btn").forEach(button => {
        button.addEventListener("click", function() {
            console.log('Download file button clicked');
            // Nous ne faisons rien de spécial ici, car le lien va directement au script de téléchargement
            // Mais nous pouvons ajouter des fonctionnalités supplémentaires si nécessaire
        });
    });

    // Add event listeners to mark-read buttons
    document.querySelectorAll(".mark-read-btn[data-id]").forEach(button => {
        button.addEventListener("click", async function(e) {
            e.preventDefault();
            const id = this.getAttribute("data-id");
            if (!id) {
                console.error("No message ID found for mark-read button");
                return;
            }

            console.log("Marking message as read:", id);

            try {
                const response = await fetch(`${basePath}?action=markAsRead`, {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ id: id })
                });

                if (!response.ok) {
                    throw new Error(`Server responded with status ${response.status}`);
                }

                const data = await response.json();
                if (data.success) {
                    showToast("Message marked as read");
                    loadMessages(currentPage);
                } else if (data.error) {
                    throw new Error(data.error);
                }
            } catch (error) {
                console.error("Error marking message as read:", error);
                showToast("Error marking message as read: " + error.message, true);
            }
        });
    });

    // Add event listeners to delete buttons
    document.querySelectorAll(".delete-btn").forEach(button => {
        button.addEventListener("click", function(e) {
            e.preventDefault();
            const id = this.getAttribute("data-id");

            // Utiliser le modal de confirmation au lieu de confirm()
            showConfirmationModal("Êtes-vous sûr de vouloir supprimer ce message?", async function() {
                try {
                    const response = await fetch(`${basePath}?action=deleteMessage`, {
                        method: "POST",
                        headers: { "Content-Type": "application/json" },
                        body: JSON.stringify({ id: id })
                    });
                    const data = await response.json();
                    if (data.success) {
                        // Utiliser le modal de succès au lieu du toast
                        document.getElementById('successModalLabel').innerHTML = '<i class="fas fa-check-circle me-2"></i>Succès';
                        document.getElementById('successModalLabel').parentNode.classList.remove('bg-danger');
                        document.getElementById('successModalLabel').parentNode.classList.add('bg-success');
                        document.getElementById('successMessage').textContent = "Message supprimé avec succès!";
                        document.querySelector('#successModal .modal-body i').classList.remove('fa-exclamation-circle', 'text-danger');
                        document.querySelector('#successModal .modal-body i').classList.add('fa-check-circle', 'text-success');
                        document.querySelector('#successModal .modal-footer button').classList.remove('btn-danger');
                        document.querySelector('#successModal .modal-footer button').classList.add('btn-success');

                        const successModalElement = document.getElementById('successModal');
                        if (successModalElement) {
                            // Utiliser notre fonction utilitaire pour afficher le modal proprement
                            showCleanModal(successModalElement, 2000);
                        }

                        loadMessages(currentPage);
                    }
                } catch (error) {
                    console.error("Error deleting message:", error);
                    showErrorModal("Erreur lors de la suppression du message");
                }
            });
        });
    });

    // Add event listener to mark all as read button
    const markAllBtn = document.getElementById('markAllBtn');
    if (markAllBtn) {
        markAllBtn.addEventListener("click", async function(e) {
            e.preventDefault();
            try {
                const response = await fetch(`${basePath}?action=markAllAsRead`, {
                    method: "POST",
                    headers: { "Content-Type": "application/json" }
                });
                const data = await response.json();
                if (data.success) {
                    showToast("All messages marked as read");
                    loadMessages(currentPage);
                }
            } catch (error) {
                console.error("Error marking all messages as read:", error);
                showToast("Error marking all messages as read", true);
            }
        });
    }
}

// Les fonctions saveMediaUrl, saveFilePath, showAttachmentIndicator et removeAttachment
// sont maintenant définies directement dans la page HTML

document.addEventListener("DOMContentLoaded", function() {
    // Check if the database has been updated
    checkDatabaseColumns();

    // Initialize the receiver select dropdown
    initReceiverSelect();

    // Function to initialize the receiver select dropdown
    function initReceiverSelect() {
        const receiverSelect = document.getElementById('receiverSelect');
        const receiverId = document.getElementById('receiverId');

        if (receiverSelect && receiverId) {
            receiverSelect.addEventListener('change', function() {
                if (this.value === 'custom') {
                    receiverId.style.display = 'block';
                    receiverId.required = true;
                } else {
                    receiverId.style.display = 'none';
                    receiverId.required = false;

                    // If "All Users" is selected, set receiverId to 0
                    if (this.value === 'all') {
                        receiverId.value = '0';
                    } else {
                        receiverId.value = '';
                    }
                }
            });
        }

        // Les boutons d'ajout d'URL et de fichier utilisent maintenant les attributs data-bs-toggle et data-bs-target

        // Initialize save buttons for media URL and file path
        const saveMediaUrlBtn = document.getElementById('saveMediaUrlBtn');
        const saveFilePathBtn = document.getElementById('saveFilePathBtn');

        if (saveMediaUrlBtn) {
            saveMediaUrlBtn.addEventListener('click', function() {
                const mediaUrlInput = document.getElementById('mediaUrlInput');
                const mediaUrl = document.getElementById('mediaUrl');

                if (mediaUrlInput && mediaUrl) {
                    mediaUrl.value = mediaUrlInput.value;

                    // Show indicator in the message content area
                    showAttachmentIndicator('mediaUrl', mediaUrlInput.value);

                    // Hide the modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('mediaUrlModal'));
                    if (modal) {
                        modal.hide();
                    }
                }
            });
        }

        if (saveFilePathBtn) {
            saveFilePathBtn.addEventListener('click', function() {
                const filePathInput = document.getElementById('filePathInput');
                const filePath = document.getElementById('filePath');

                if (filePathInput && filePath) {
                    filePath.value = filePathInput.value;

                    // Show indicator in the message content area
                    showAttachmentIndicator('filePath', filePathInput.value);

                    // Hide the modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('filePathModal'));
                    if (modal) {
                        modal.hide();
                    }
                }
            });
        }
    }

    // Les fonctions showAttachmentIndicator et removeAttachment sont maintenant définies directement dans la page HTML

    // Function to show the receivers modal and load the data
    function showReceiversModal() {
        // Show the modal
        const checkReceiversModal = new bootstrap.Modal(document.getElementById('checkReceiversModal'));
        checkReceiversModal.show();

        // Load the data
        loadReceivers();
    }

    // Function to load receivers data
    function loadReceivers() {
        fetch('messages.php?action=get_receivers&type=all')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the students list
                    updateReceiversList('studentsList', data.data.students, 'student');

                    // Update the teachers list
                    updateReceiversList('teachersList', data.data.teachers, 'teacher');

                    // Update the users list
                    updateUsersList('usersList', data.data.users);
                } else {
                    console.error('Error loading receivers:', data.error);
                }
            })
            .catch(error => {
                console.error('Error fetching receivers:', error);
            });
    }

    // Function to update a receivers list
    function updateReceiversList(containerId, receivers, type) {
        const container = document.getElementById(containerId);
        if (!container) return;

        if (!receivers || receivers.length === 0) {
            container.innerHTML = '<p>No ' + type + 's found.</p>';
            return;
        }

        let html = '<table class="table table-sm table-hover">';
        html += '<thead><tr><th>ID</th><th>Name</th><th>Action</th></tr></thead>';
        html += '<tbody>';

        receivers.forEach(receiver => {
            html += `<tr>
                <td>${receiver.id}</td>
                <td>${receiver.name}</td>
                <td>
                    <button class="btn btn-sm btn-primary select-receiver-btn"
                            data-id="${receiver.id}"
                            data-type="${type}">
                        Select
                    </button>
                </td>
            </tr>`;
        });

        html += '</tbody></table>';
        container.innerHTML = html;

        // Add event listeners to the select buttons
        container.querySelectorAll('.select-receiver-btn').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const type = this.getAttribute('data-type');
                selectReceiver(id, type);
            });
        });
    }

    // Function to update the users list
    function updateUsersList(containerId, users) {
        const container = document.getElementById(containerId);
        if (!container) return;

        if (!users || users.length === 0) {
            container.innerHTML = '<p>No users found.</p>';
            return;
        }

        let html = '<table class="table table-sm">';
        html += '<thead><tr><th>ID</th><th>Username</th><th>Role</th></tr></thead>';
        html += '<tbody>';

        users.forEach(user => {
            html += `<tr>
                <td>${user.id}</td>
                <td>${user.username}</td>
                <td>${user.role}</td>
            </tr>`;
        });

        html += '</tbody></table>';
        container.innerHTML = html;
    }

    // Function to select a receiver
    function selectReceiver(id, type) {
        // Update the receiver select and ID in the form
        const receiverSelect = document.getElementById('receiverSelect');
        const receiverIdInput = document.getElementById('receiverId');

        if (receiverSelect && receiverIdInput) {
            // Set the receiver select to "custom"
            receiverSelect.value = 'custom';

            // Show the receiver ID input
            receiverIdInput.style.display = 'block';

            // Set the receiver ID
            receiverIdInput.value = id;

            // Hide the modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('checkReceiversModal'));
            if (modal) {
                modal.hide();
            }

            // Show a toast message
            showToast(`Selected ${type} with ID ${id}`);
        }
    }

    // Function to use the selected ID
    function useSelectedId() {
        // Get all selected receivers
        const selectedButtons = document.querySelectorAll('.select-receiver-btn.selected');
        if (selectedButtons.length === 0) {
            showToast('Please select a receiver first', true);
            return;
        }

        // Use the first selected receiver
        const button = selectedButtons[0];
        const id = button.getAttribute('data-id');
        const type = button.getAttribute('data-type');

        selectReceiver(id, type);
    }

    // La fonction createNewMessage est maintenant définie globalement

    function showToast(message, isError = false) {
        const toastContainer = document.querySelector('.toast-container') || createToastContainer();
        const toast = document.createElement('div');
        toast.className = `toast ${isError ? 'error' : 'success'}`;
        toast.textContent = message;

        toastContainer.appendChild(toast);

        setTimeout(() => {
            toast.classList.add('fade-out');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    function createToastContainer() {
        const container = document.createElement('div');
        container.className = 'toast-container';
        document.body.appendChild(container);
        return container;
    }

    // Function to check if the database has the required columns
    function checkDatabaseColumns() {
        // We'll make a test request to the createAdminMessage endpoint
        // If it fails with a specific error about missing columns, we'll show the alert
        fetch(`${basePath}?action=checkColumns`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ test: true })
        })
        .then(response => response.json())
        .then(data => {
            if (data.needsUpdate) {
                // Show the database update alert
                const dbUpdateAlert = document.getElementById('dbUpdateAlert');
                if (dbUpdateAlert) {
                    dbUpdateAlert.style.display = 'block';
                }
            } else {
                // Hide the alert if the database is up to date
                const dbUpdateAlert = document.getElementById('dbUpdateAlert');
                if (dbUpdateAlert) {
                    dbUpdateAlert.style.display = 'none';
                }
            }
        })
        .catch(error => {
            console.error('Error checking database columns:', error);
            // Show the alert by default if there's an error
            const dbUpdateAlert = document.getElementById('dbUpdateAlert');
            if (dbUpdateAlert) {
                dbUpdateAlert.style.display = 'block';
            }
        });
    }

    // Initial load of messages
    loadMessages(currentPage);

    // Add event listeners to the page
    addEventListeners();
});
