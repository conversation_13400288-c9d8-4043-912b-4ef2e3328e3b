<?php
// Vérifier l'authentification
require_once '../includes/auth_check_coordinateur.php';

// Récupérer les informations du coordinateur depuis la session
$userName = $_SESSION['user']['username'] ?? 'Coordinateur';
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$filiereName = $_SESSION['user']['filiere_name'] ?? 'Non spécifié';
$filiereId = $_SESSION['user']['filiere_id'] ?? 1;

// Construire le nom complet
$fullName = $prenom . ' ' . $nom;
if (trim($fullName) === '') {
    $fullName = $userName;
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import des Notes - Coordinateur</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard-style.css">
    <!-- CSS personnalisé pour la page import_grades - n'affecte que le contenu principal -->
    <link rel="stylesheet" href="../assets/css/import_grades_custom.css">
</head>
<body>
    <div class="dashboard-container">
        <?php
        include '../includes/sidebar.php';

        // Inclure le modèle des visites et enregistrer la visite
        require_once '../../model/visitsModel.php';
        recordVisit('coordinateur', 'import_grades');
        ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>
            <div class="container-fluid p-4">
                <h1 class="page-title">Import des Notes</h1>
                <p class="text-muted">Importez les notes des étudiants pour les modules de votre filière.</p>

                <!-- Filtres -->
                <div class="filter-card">
                    <h2 class="filter-title">Filtres</h2>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="teacher-filter" class="form-label">Enseignant</label>
                            <select id="teacher-filter" class="form-select">
                                <option value="">Sélectionner un enseignant</option>
                                <!-- Options will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="level-filter" class="form-label">Niveau</label>
                            <select id="level-filter" class="form-select" disabled>
                                <option value="">Sélectionner un niveau</option>
                                <!-- Options will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="semester-filter" class="form-label">Semestre</label>
                            <select id="semester-filter" class="form-select" disabled>
                                <option value="">Sélectionner un semestre</option>
                                <!-- Options will be loaded dynamically -->
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="module-filter" class="form-label">Module</label>
                            <select id="module-filter" class="form-select" disabled>
                                <option value="">Sélectionner un module</option>
                                <!-- Options will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="session-filter" class="form-label">Session</label>
                            <select id="session-filter" class="form-select" disabled>
                                <option value="">Sélectionner une session</option>
                                <option value="normale">Normale</option>
                                <option value="rattrapage">Rattrapage</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12 text-center">
                            <button id="display-pdfs-btn" class="btn btn-primary" disabled>
                                <i class="fas fa-search me-2"></i>Afficher les PDFs
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Zone d'upload -->
                <!-- <div class="upload-area" id="upload-area">
                    <i class="fas fa-file-upload upload-icon"></i>
                    <h3>Déposer le fichier PDF des notes ici</h3>
                    <p>ou</p>
                    <input type="file" id="file-upload" class="d-none" accept=".pdf">
                    <button class="btn btn-primary" id="browse-button">Parcourir les fichiers</button>
                    <p class="mt-2 text-muted">Formats acceptés: PDF</p>
                </div> -->

                <!-- Indicateur de chargement -->
                <div class="loading-spinner" id="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2">Traitement en cours...</p>
                </div>

                <!-- Alertes -->
                <div class="alert alert-success alert-upload" id="success-alert" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle me-3" style="font-size: 1.5rem;"></i>
                        <div>
                            <strong>Succès!</strong>
                            <p class="mb-0">Le fichier a été téléchargé avec succès.</p>
                        </div>
                    </div>
                </div>
                <div class="alert alert-danger alert-upload" id="error-alert" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-circle me-3" style="font-size: 1.5rem;"></i>
                        <div>
                            <strong>Erreur!</strong>
                            <p class="mb-0" id="error-message">Une erreur s'est produite lors de l'importation du fichier.</p>
                        </div>
                    </div>
                </div>

                <!-- Informations du module sélectionné -->
                <div class="module-info mt-4" id="module-info" style="display: none;">
                    <div class="module-card">
                        <h4 id="module-name">Nom du module</h4>
                        <div class="teacher-card">
                            <strong>Enseignant:</strong> <span id="teacher-name">Nom de l'enseignant</span>
                        </div>
                        <div class="coordinator-card">
                            <strong>Coordinateur:</strong> <span id="coordinator-name"><?php echo htmlspecialchars($fullName); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Section d'affichage des PDFs -->
                <div class="pdf-display-section mt-4" id="pdf-display-section" style="display: none;">
                    <h3 class="section-title mb-3">PDFs des notes disponibles</h3>
                    <div class="pdf-loading" id="pdf-loading">
                        <div class="d-flex justify-content-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                        <p class="text-center mt-2">Chargement des PDFs...</p>
                    </div>
                    <div class="pdf-list" id="pdf-list">
                        <!-- Les PDFs seront chargés ici dynamiquement -->
                    </div>
                    <div class="no-pdfs-message" id="no-pdfs-message" style="display: none;">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> Aucun PDF trouvé pour les filtres sélectionnés.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/sidebar.js"></script>
    <script src="../assets/js/importGradesController.js"></script>
</body>
</html>
