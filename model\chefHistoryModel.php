<?php
/**
 * Model for Chef de Département History
 *
 * This model handles all database operations related to department head history,
 * including professor management, module assignments, workload tracking, and observations.
 */

require_once __DIR__ . '/../config/db.php';

/**
 * Get academic years for a department head
 *
 * @param int $chefId The chef de département ID
 * @return array Academic years or error
 */
function getChefAcademicYears($chefId) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    $chefId = mysqli_real_escape_string($conn, $chefId);

    // Get academic years from affectation table for the department
    $sql = "SELECT DISTINCT
                CASE
                    WHEN a.annee_academique LIKE '%-%' THEN REPLACE(a.annee_academique, '-', '/')
                    ELSE a.annee_academique
                END as raw_year
            FROM affectation a
            JOIN enseignant e ON a.professeur_id = e.id_enseignant
            JOIN departement d ON e.id_departement = d.id_departement
            WHERE d.id_chef_departement = '$chefId'
            AND a.annee_academique IS NOT NULL
            AND a.annee_academique != ''
            ORDER BY raw_year DESC";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getChefAcademicYears: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching academic years: " . $error];
    }

    $years = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $rawYear = $row['raw_year'];
        // Convert to standard format if needed
        if (strpos($rawYear, '/') !== false) {
            $years[] = $rawYear;
        } elseif (strpos($rawYear, '-') !== false) {
            // Convert format like "2024-2025" to "2024/2025"
            $years[] = str_replace('-', '/', $rawYear);
        } else {
            $nextYear = intval($rawYear) + 1;
            $years[] = "$rawYear/$nextYear";
        }
    }

    // Remove duplicates and sort
    $years = array_unique($years);
    rsort($years);

    // If no years found, add current year
    if (empty($years)) {
        $currentYear = date('Y');
        $nextYear = $currentYear + 1;
        $years[] = "$currentYear/$nextYear";
    }

    mysqli_close($conn);
    return $years;
}

/**
 * Get department statistics for a specific academic year
 *
 * @param int $chefId The chef de département ID
 * @param string $academicYear The academic year (e.g., "2023/2024")
 * @return array Statistics or error
 */
function getDepartmentStatistics($chefId, $academicYear) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    $chefId = mysqli_real_escape_string($conn, $chefId);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);

    // Extract year from academic year (e.g., "2023/2024" -> "2023")
    $year = explode('/', $academicYear)[0];

    // Get department ID
    $deptSql = "SELECT id_departement FROM departement WHERE id_chef_departement = '$chefId'";
    $deptResult = mysqli_query($conn, $deptSql);

    if (!$deptResult || mysqli_num_rows($deptResult) == 0) {
        mysqli_close($conn);
        return ["error" => "Department not found"];
    }

    $deptRow = mysqli_fetch_assoc($deptResult);
    $departmentId = $deptRow['id_departement'];

    $stats = [];

    // Total professors in department
    $profSql = "SELECT COUNT(*) as total_professors
                FROM enseignant
                WHERE id_departement = '$departmentId'";
    $profResult = mysqli_query($conn, $profSql);
    $stats['total_professors'] = mysqli_fetch_assoc($profResult)['total_professors'];

    // Total modules managed (simplified - count UE in department)
    $moduleSql = "SELECT COUNT(DISTINCT ue.id) as total_modules
                  FROM uniteenseignement ue
                  JOIN module m ON ue.module_id = m.id
                  JOIN filiere f ON m.filiere_id = f.id_filiere
                  WHERE f.id_dep = '$departmentId'";
    $moduleResult = mysqli_query($conn, $moduleSql);
    if ($moduleResult) {
        $stats['total_modules'] = mysqli_fetch_assoc($moduleResult)['total_modules'];
    } else {
        $stats['total_modules'] = 0;
    }

    // Assignments made in the year
    $assignSql = "SELECT COUNT(*) as assignments_made
                  FROM affectation a
                  JOIN enseignant e ON a.professeur_id = e.id_enseignant
                  WHERE e.id_departement = '$departmentId'
                  AND (a.annee_academique = '$academicYear' OR a.annee_academique = '$year')";
    $assignResult = mysqli_query($conn, $assignSql);
    if ($assignResult) {
        $stats['assignments_made'] = mysqli_fetch_assoc($assignResult)['assignments_made'];
    } else {
        $stats['assignments_made'] = 0;
    }

    // Vacant modules (UE explicitly marked as vacant in ue_vacantes table, regardless of assignment to vacataire)
    $academicYearWithDash = str_replace('/', '-', $academicYear);
    $vacantSql = "SELECT COUNT(DISTINCT ue.id) as vacant_modules
                  FROM uniteenseignement ue
                  JOIN module m ON ue.module_id = m.id
                  JOIN filiere f ON m.filiere_id = f.id_filiere
                  JOIN ue_vacantes uv ON ue.id = uv.ue_id
                  WHERE f.id_dep = '$departmentId'
                  AND uv.department_id = '$departmentId'
                  AND uv.is_vacant = 1
                  AND (uv.academic_year = '$academicYear'
                       OR uv.academic_year = '$academicYearWithDash'
                       OR uv.academic_year = '$year')";
    $vacantResult = mysqli_query($conn, $vacantSql);
    if ($vacantResult) {
        $stats['vacant_modules'] = mysqli_fetch_assoc($vacantResult)['vacant_modules'];
    } else {
        $stats['vacant_modules'] = 0;
    }

    // Assigned modules (UE assigned to permanent teachers, not vacataires, and not marked as vacant)
    $assignedSql = "SELECT COUNT(DISTINCT ue.id) as assigned_modules
                    FROM uniteenseignement ue
                    JOIN module m ON ue.module_id = m.id
                    JOIN filiere f ON m.filiere_id = f.id_filiere
                    JOIN affectation a ON ue.id = a.unite_enseignement_id
                    JOIN enseignant e ON a.professeur_id = e.id_enseignant
                    LEFT JOIN ue_vacantes uv ON ue.id = uv.ue_id
                    AND uv.department_id = '$departmentId'
                    AND (uv.academic_year = '$academicYear'
                         OR uv.academic_year = '$academicYearWithDash'
                         OR uv.academic_year = '$year')
                    WHERE f.id_dep = '$departmentId'
                    AND (a.annee_academique = '$academicYear'
                         OR a.annee_academique = '$academicYearWithDash'
                         OR a.annee_academique = '$year')
                    AND a.valide = 1
                    AND e.role != 'vacataire'
                    AND (uv.is_vacant IS NULL OR uv.is_vacant = 0)";
    $assignedResult = mysqli_query($conn, $assignedSql);
    if ($assignedResult) {
        $stats['assigned_modules'] = mysqli_fetch_assoc($assignedResult)['assigned_modules'];
    } else {
        $stats['assigned_modules'] = 0;
    }

    // Update total modules calculation to be more accurate
    $stats['unassigned_modules'] = $stats['total_modules'] - $stats['vacant_modules'] - $stats['assigned_modules'];

    mysqli_close($conn);
    return $stats;
}

/**
 * Get professor assignments history for the department
 *
 * @param int $chefId The chef de département ID
 * @param string $academicYear The academic year
 * @return array Assignments or error
 */
function getDepartmentAssignments($chefId, $academicYear) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    $chefId = mysqli_real_escape_string($conn, $chefId);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);

    // Extract year from academic year and handle different formats
    $year = explode('/', $academicYear)[0];
    $academicYearWithDash = str_replace('/', '-', $academicYear);

    $sql = "SELECT a.*, e.nom, e.prenom, e.email,
                   CONCAT(m.nom, ' - ', ue.type) as nom_ue,
                   m.nom as nom_module,
                   a.annee_academique,
                   a.annee_academique as date_affectation,
                   '' as commentaire
            FROM affectation a
            JOIN enseignant e ON a.professeur_id = e.id_enseignant
            JOIN uniteenseignement ue ON a.unite_enseignement_id = ue.id
            JOIN module m ON ue.module_id = m.id
            JOIN departement d ON e.id_departement = d.id_departement
            WHERE d.id_chef_departement = '$chefId'
            AND (a.annee_academique = '$academicYear'
                 OR a.annee_academique = '$academicYearWithDash'
                 OR a.annee_academique = '$year')
            AND a.valide = 1
            ORDER BY a.id DESC";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getDepartmentAssignments: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching assignments: " . $error];
    }

    $assignments = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $assignments[] = $row;
    }

    mysqli_close($conn);
    return $assignments;
}

/**
 * Get professor choices/requests for the department
 *
 * @param int $chefId The chef de département ID
 * @param string $academicYear The academic year
 * @return array Choices or error
 */
function getDepartmentProfessorChoices($chefId, $academicYear) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    $chefId = mysqli_real_escape_string($conn, $chefId);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);

    // Extract year from academic year
    $year = explode('/', $academicYear)[0];

    $sql = "SELECT c.*, e.nom, e.prenom, e.email,
                   CONCAT(m.nom, ' - ', ue.type) as nom_ue,
                   m.nom as nom_module,
                   c.ordre_de_choix as priorite,
                   'En attente' as statut,
                   NOW() as date_choix
            FROM choixue c
            JOIN enseignant e ON c.professeur_id = e.id_enseignant
            JOIN uniteenseignement ue ON c.unite_enseignement_id = ue.id
            JOIN module m ON ue.module_id = m.id
            WHERE e.id_departement = (
                SELECT id_departement FROM departement WHERE id_chef_departement = '$chefId'
            )
            ORDER BY c.ordre_de_choix ASC";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getDepartmentProfessorChoices: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching professor choices: " . $error];
    }

    $choices = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $choices[] = $row;
    }

    mysqli_close($conn);
    return $choices;
}

/**
 * Get workload distribution for department professors using dynamic calculation
 *
 * @param int $chefId The chef de département ID
 * @param string $academicYear The academic year
 * @return array Workload data or error
 */
function getDepartmentWorkload($chefId, $academicYear) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    $chefId = mysqli_real_escape_string($conn, $chefId);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);

    // Get department ID for the chef
    $deptSql = "SELECT id_departement FROM departement WHERE id_chef_departement = '$chefId'";
    $deptResult = mysqli_query($conn, $deptSql);
    if (!$deptResult || mysqli_num_rows($deptResult) == 0) {
        mysqli_close($conn);
        return ["error" => "Department not found for chef"];
    }
    $departmentId = mysqli_fetch_assoc($deptResult)['id_departement'];

    // Use the new dynamic workload calculation function
    require_once __DIR__ . '/affectationModel.php';
    $workload = getDepartmentWorkloadByYear($departmentId, $academicYear);

    mysqli_close($conn);

    if (isset($workload['error'])) {
        return $workload;
    }

    // Convert to format expected by existing code
    $formattedWorkload = [];
    foreach ($workload as $teacher) {
        $formattedWorkload[] = [
            'id_enseignant' => $teacher['id_enseignant'],
            'nom' => $teacher['nom'],
            'prenom' => $teacher['prenom'],
            'role' => $teacher['role'],
            'total_assignments' => $teacher['total_assignments'],
            'total_hours' => $teacher['total_hours'],
            'cours_hours' => $teacher['cours_hours'],
            'td_hours' => $teacher['td_hours'],
            'tp_hours' => $teacher['tp_hours']
        ];
    }

    return $formattedWorkload;
}

/**
 * Get administrative observations for department professors
 *
 * @param int $chefId The chef de département ID
 * @param string $academicYear The academic year
 * @return array Observations or error
 */
function getDepartmentObservations($chefId, $academicYear) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    $chefId = mysqli_real_escape_string($conn, $chefId);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);

    // Extract year from academic year
    $year = explode('/', $academicYear)[0];

    $sql = "SELECT o.*, a.nom as admin_nom, a.prenom as admin_prenom,
                   e.nom as enseignant_nom, e.prenom as enseignant_prenom
            FROM observation o
            JOIN admin a ON o.id_admin = a.id_admin
            JOIN enseignant e ON o.id_enseignant = e.id_enseignant
            JOIN departement d ON e.id_departement = d.id_departement
            WHERE d.id_chef_departement = '$chefId'
            AND YEAR(o.date_observation) = '$year'
            ORDER BY o.date_observation DESC, o.niveau_gravite DESC";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getDepartmentObservations: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching observations: " . $error];
    }

    $observations = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $observations[] = $row;
    }

    mysqli_close($conn);
    return $observations;
}

/**
 * Get department modules and their assignment status
 *
 * @param int $chefId The chef de département ID
 * @param string $academicYear The academic year
 * @return array Modules or error
 */
function getDepartmentModules($chefId, $academicYear) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    $chefId = mysqli_real_escape_string($conn, $chefId);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);

    // Extract year from academic year and handle different formats
    $year = explode('/', $academicYear)[0];
    $academicYearWithDash = str_replace('/', '-', $academicYear);

    // Get department ID for the chef
    $deptSql = "SELECT id_departement FROM departement WHERE id_chef_departement = '$chefId'";
    $deptResult = mysqli_query($conn, $deptSql);
    if (!$deptResult || mysqli_num_rows($deptResult) == 0) {
        mysqli_close($conn);
        return ["error" => "Department not found for chef"];
    }
    $departmentId = mysqli_fetch_assoc($deptResult)['id_departement'];

    $sql = "SELECT ue.*,
                   CONCAT(m.nom, ' - ', ue.type) as nom_ue,
                   m.nom as nom_module,
                   f.nom_filiere,
                   n.nom as niveau_nom,
                   ue.volume_horaire as volume_horaire_cours,
                   0 as volume_horaire_td,
                   0 as volume_horaire_tp,
                   a.professeur_id,
                   e.nom as prof_nom,
                   e.prenom as prof_prenom,
                   e.role as prof_role,
                   a.annee_academique as date_affectation,
                   uv.is_vacant,
                   CASE
                       WHEN uv.is_vacant = 1 THEN 'Vacant'
                       WHEN a.id IS NOT NULL AND a.valide = 1 AND e.role != 'vacataire' THEN 'Assigné'
                       ELSE 'Non assigné'
                   END as statut
            FROM uniteenseignement ue
            JOIN module m ON ue.module_id = m.id
            JOIN filiere f ON m.filiere_id = f.id_filiere
            JOIN niveaux n ON m.id_niveau = n.id
            LEFT JOIN affectation a ON ue.id = a.unite_enseignement_id
            AND (a.annee_academique = '$academicYear'
                 OR a.annee_academique = '$academicYearWithDash'
                 OR a.annee_academique = '$year')
            AND a.valide = 1
            LEFT JOIN enseignant e ON a.professeur_id = e.id_enseignant
            LEFT JOIN ue_vacantes uv ON ue.id = uv.ue_id
            AND uv.department_id = '$departmentId'
            AND (uv.academic_year = '$academicYear'
                 OR uv.academic_year = '$academicYearWithDash'
                 OR uv.academic_year = '$year')
            WHERE f.id_dep = '$departmentId'
            ORDER BY f.nom_filiere, n.nom, m.nom, ue.type";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getDepartmentModules: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching modules: " . $error];
    }

    $modules = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $modules[] = $row;
    }

    mysqli_close($conn);
    return $modules;
}

/**
 * Get module choices history for department teachers
 *
 * @param int $chefId The chef de département ID
 * @param string $academicYear The academic year
 * @return array Module choices history or error
 */
function getDepartmentModuleChoicesHistory($chefId, $academicYear) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    $chefId = mysqli_real_escape_string($conn, $chefId);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);

    // Extract years from academic year (e.g., "2024/2025" -> ["2024", "2025"])
    $yearParts = explode('/', $academicYear);
    $startYear = $yearParts[0];
    $endYear = isset($yearParts[1]) ? $yearParts[1] : $startYear;

    $sql = "SELECT p.*,
                   e.nom as enseignant_nom,
                   e.prenom as enseignant_prenom,
                   m.nom as module_name,
                   ue.type as ue_type,
                   p.created_at as date_demande,
                   p.updated_at as date_traitement,
                   p.statut,
                   p.reason as commentaire_chef,
                   chef.nom as chef_nom,
                   chef.prenom as chef_prenom
            FROM ue_preferences p
            JOIN enseignant e ON p.id_enseignant = e.id_enseignant
            JOIN uniteenseignement ue ON p.id_ue = ue.id
            JOIN module m ON ue.module_id = m.id
            JOIN departement d ON e.id_departement = d.id_departement
            LEFT JOIN enseignant chef ON d.id_chef_departement = chef.id_enseignant
            WHERE d.id_chef_departement = '$chefId'
            AND (YEAR(p.created_at) = '$startYear' OR YEAR(p.created_at) = '$endYear')
            ORDER BY p.created_at DESC, p.statut ASC";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getDepartmentModuleChoicesHistory: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching module choices history: " . $error];
    }

    $moduleChoices = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $moduleChoices[] = $row;
    }

    mysqli_close($conn);
    return $moduleChoices;
}

/**
 * Get complete history data for a department head
 *
 * @param int $chefId The chef de département ID
 * @param string $academicYear The academic year
 * @return array Complete history data or error
 */
function getCompleteChefHistory($chefId, $academicYear) {
    $statistics = getDepartmentStatistics($chefId, $academicYear);
    $assignments = getDepartmentAssignments($chefId, $academicYear);
    $choices = getDepartmentProfessorChoices($chefId, $academicYear);
    $moduleChoicesHistory = getDepartmentModuleChoicesHistory($chefId, $academicYear);
    $workload = getDepartmentWorkload($chefId, $academicYear);
    $observations = getDepartmentObservations($chefId, $academicYear);
    $modules = getDepartmentModules($chefId, $academicYear);

    return [
        'statistics' => $statistics,
        'assignments' => $assignments,
        'professor_choices' => $choices,
        'module_choices_history' => $moduleChoicesHistory,
        'workload' => $workload,
        'observations' => $observations,
        'modules' => $modules,
        'academic_year' => $academicYear
    ];
}

/**
 * Get department information by chef ID
 *
 * @param int $chefId The chef de département ID
 * @return array Department info or error
 */
function getDepartmentInfoByChef($chefId) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    $chefId = mysqli_real_escape_string($conn, $chefId);

    $sql = "SELECT d.*, d.nom_dep as nom_departement, e.nom as chef_nom, e.prenom as chef_prenom
            FROM departement d
            JOIN enseignant e ON d.id_chef_departement = e.id_enseignant
            WHERE d.id_chef_departement = '$chefId'";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getDepartmentInfoByChef: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching department info: " . $error];
    }

    if (mysqli_num_rows($result) == 0) {
        mysqli_close($conn);
        return ["error" => "Department not found"];
    }

    $department = mysqli_fetch_assoc($result);
    mysqli_close($conn);
    return $department;
}
?>
