<?php
// Vérifier l'authentification
require_once '../includes/auth_check_chef.php';

// Get the department head's ID and department ID from the session
$chefId = $_SESSION['user']['teacher_id'] ?? null;
$departmentId = $_SESSION['user']['department_id'] ?? null;

// Get department head's name from session
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$chefName = trim($prenom . ' ' . $nom);
if (empty($chefName)) {
    $chefName = $_SESSION['user']['username'] ?? 'Chef de département';
}

// Get the current academic year
require_once '../../model/affectationModel.php';
$academicYear = getCurrentAcademicYear();
// Debug log
error_log("Academic year: " . $academicYear);

// Page title
$pageTitle = "Demandes d'Unités d'Enseignement";
$currentPage = "ue_requests.php";
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> | Système de Gestion ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        :root {
            /* Palette de couleurs pastel */
            --primary-color: #6d8bd3;       /* Bleu pastel */
            --primary-light: rgba(109, 139, 211, 0.1);
            --secondary-color: #7bc5ae;     /* Vert pastel */
            --secondary-light: rgba(123, 197, 174, 0.1);
            --warning-color: #f8d486;       /* Jaune pastel */
            --danger-color: #e6a4a4;        /* Rouge pastel */
            --dark-color: #5a5c69;          /* Gris foncé */
            --light-color: #f8f9fc;         /* Gris très clair */
            --card-border-radius: 0.35rem;  /* Rayon de bordure réduit */
            --transition-speed: 0.2s;       /* Transition plus rapide */
            --spacing-sm: 0.5rem;           /* Petit espacement */
            --spacing-md: 0.75rem;          /* Espacement moyen */
            --spacing-lg: 1rem;             /* Grand espacement */
        }

        /* Page Layout */
        .main-content {
            background-color: #f8f9fc;
        }

        .page-title {
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.2rem;
            font-size: 1.5rem;
        }

        .page-subtitle {
            font-size: 0.9rem;
            margin-bottom: 0;
        }

        /* Container avec espacement réduit */
        .container-fluid {
            padding: var(--spacing-lg) !important;
        }

        /* Card Styles */
        .card {
            border: none;
            border-radius: var(--card-border-radius);
            box-shadow: 0 0.1rem 0.75rem 0 rgba(58, 59, 69, 0.08);
            margin-bottom: var(--spacing-lg);
            transition: box-shadow var(--transition-speed);
        }

        .card:hover {
            box-shadow: 0 0.15rem 1rem 0 rgba(58, 59, 69, 0.12);
        }

        .card-header {
            padding: var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid #e3e6f0;
            background-color: white;
            border-top-left-radius: var(--card-border-radius) !important;
            border-top-right-radius: var(--card-border-radius) !important;
        }

        /* Filter Styles */
        .filter-section {
            background-color: white;
            border-radius: var(--card-border-radius);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            box-shadow: 0 0.1rem 0.75rem 0 rgba(58, 59, 69, 0.08);
        }

        .filter-label {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: var(--spacing-sm);
            font-size: 0.85rem;
        }

        .filter-select {
            transition: border-color var(--transition-speed), box-shadow var(--transition-speed);
            cursor: pointer;
            font-size: 0.9rem;
            padding: 0.4rem 0.75rem;
            height: auto;
        }

        .filter-select:hover {
            border-color: var(--primary-color);
        }

        .filter-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.15rem rgba(109, 139, 211, 0.2);
        }

        .filter-active {
            border-color: var(--primary-color);
            background-color: var(--primary-light);
        }

        /* Table Styles */
        .table-responsive {
            border-radius: var(--card-border-radius);
            overflow: hidden;
        }

        .table {
            margin-bottom: 0;
            font-size: 0.9rem;
        }

        .table th {
            background-color: var(--primary-light);
            color: var(--primary-color);
            font-weight: 600;
            border-bottom: none;
            padding: var(--spacing-md) var(--spacing-md);
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.03em;
        }

        .table td {
            padding: var(--spacing-sm) var(--spacing-md);
            vertical-align: middle;
            border-bottom-width: 1px;
            border-bottom-color: rgba(0,0,0,0.05);
        }

        .table tbody tr {
            transition: background-color var(--transition-speed);
        }

        .table tbody tr:hover {
            background-color: var(--primary-light);
        }

        /* Status Badges */
        .badge {
            font-weight: 500;
            font-size: 0.75rem;
            padding: 0.35em 0.65em;
            border-radius: 30px;
        }

        .badge-en-attente {
            background-color: var(--warning-color);
            color: #856404;
        }

        .badge-acceptee {
            background-color: var(--secondary-color);
            color: #155724;
        }

        .badge-rejetee {
            background-color: var(--danger-color);
            color: #721c24;
        }

        /* Action Buttons */
        .btn {
            font-size: 0.85rem;
            border-radius: 0.25rem;
            transition: all var(--transition-speed);
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        .btn-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-accept {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: white;
        }

        .btn-accept:hover {
            background-color: #6ab09b;
            border-color: #6ab09b;
            color: white;
            box-shadow: 0 0 0 0.15rem rgba(123, 197, 174, 0.25);
        }

        .btn-reject {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
            color: white;
        }

        .btn-reject:hover {
            background-color: #d99393;
            border-color: #d99393;
            color: white;
            box-shadow: 0 0 0 0.15rem rgba(230, 164, 164, 0.25);
        }

        /* Alert Styles */
        .alert-container {
            position: fixed;
            top: 15px;
            right: 15px;
            z-index: 1050;
            max-width: 320px;
        }

        .alert {
            box-shadow: 0 0.1rem 0.5rem 0 rgba(58, 59, 69, 0.1);
            border: none;
            border-left: 3px solid;
            border-radius: var(--card-border-radius);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            animation: slideIn 0.25s ease-out forwards;
            font-size: 0.85rem;
        }

        .alert-success {
            background-color: rgba(123, 197, 174, 0.15);
            border-left-color: var(--secondary-color);
            color: #155724;
        }

        .alert-danger {
            background-color: rgba(230, 164, 164, 0.15);
            border-left-color: var(--danger-color);
            color: #721c24;
        }

        @keyframes slideIn {
            0% {
                transform: translateX(100%);
                opacity: 0;
            }
            100% {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .alert.fade {
            animation: slideOut 0.25s ease-in forwards;
        }

        @keyframes slideOut {
            0% {
                transform: translateX(0);
                opacity: 1;
            }
            100% {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* Modal Styles */
        .modal-content {
            border: none;
            border-radius: var(--card-border-radius);
            box-shadow: 0 0.1rem 1rem 0 rgba(58, 59, 69, 0.1);
        }

        .modal-header {
            background-color: var(--primary-color);
            color: white;
            border-top-left-radius: var(--card-border-radius);
            border-top-right-radius: var(--card-border-radius);
            padding: var(--spacing-md) var(--spacing-lg);
        }

        .modal-body {
            padding: var(--spacing-lg);
            font-size: 0.9rem;
        }

        .modal-footer {
            padding: var(--spacing-md) var(--spacing-lg);
            border-top: 1px solid rgba(0,0,0,0.05);
        }

        .modal-header .btn-close {
            color: white;
            filter: brightness(0) invert(1);
            opacity: 0.8;
            transition: opacity var(--transition-speed);
        }

        .modal-header .btn-close:hover {
            opacity: 1;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 2rem 1rem;
        }

        .empty-state i {
            font-size: 2.5rem;
            color: #d1d1d1;
            margin-bottom: 0.75rem;
        }

        .empty-state h5 {
            font-size: 1rem;
            margin-bottom: 0.5rem;
            color: var(--dark-color);
        }

        .empty-state p {
            font-size: 0.85rem;
            color: #6c757d;
        }

        /* Compteur de résultats */
        .request-count {
            background-color: var(--primary-light);
            color: var(--primary-color);
            font-weight: 600;
            font-size: 0.75rem;
            padding: 0.25em 0.6em;
            border-radius: 30px;
        }

        /* Statut des filtres */
        .filter-status {
            font-size: 0.8rem;
            color: #6c757d;
        }

        /* Espacement réduit */
        .g-3 {
            --bs-gutter-y: 0.75rem;
        }

        /* Boutons d'action compacts */
        .action-buttons {
            white-space: nowrap;
        }

        /* Badge pour l'année académique */
        .bg-primary-light {
            background-color: var(--primary-light);
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <h1 class="page-title"><?php echo $pageTitle; ?></h1>
                        <p class="page-subtitle text-muted">Gérez les demandes d'UE soumises par les enseignants</p>
                    </div>
                    <div>
                        <span class="badge bg-primary-light text-primary py-1 px-2">
                            <i class="bi bi-calendar-check me-1"></i> <?php echo htmlspecialchars($academicYear); ?>
                        </span>
                    </div>
                </div>

                <!-- Alert Container -->
                <div class="alert-container" id="alertContainer"></div>

                <!-- Filters Section -->
                <div class="filter-section mb-3">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="moduleFilter" class="filter-label">Module</label>
                            <select class="form-select form-select-sm filter-select" id="moduleFilter">
                                <option value="">Tous les modules</option>
                                <!-- Options will be populated dynamically -->
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="statusFilter" class="filter-label">Statut</label>
                            <select class="form-select form-select-sm filter-select" id="statusFilter">
                                <option value="">Tous les statuts</option>
                                <option value="en_attente">En attente</option>
                                <option value="acceptee">Acceptée</option>
                                <option value="rejetee">Rejetée</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="enseignantFilter" class="filter-label">Enseignant</label>
                            <select class="form-select form-select-sm filter-select" id="enseignantFilter">
                                <option value="">Tous les enseignants</option>
                                <!-- Options will be populated dynamically -->
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Requests Table -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <h5 class="mb-0 me-2">Demandes d'UE</h5>
                            <span class="request-count" id="requestCount">0</span>
                            <span class="ms-2 filter-status" id="filterStatus"></span>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover" id="requestsTable">
                                <thead>
                                    <tr>
                                        <th>Enseignant</th>
                                        <th>Module</th>
                                        <th>UE</th>
                                        <th>Date demande</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="requestsTableBody">
                                    <!-- Table content will be loaded dynamically -->
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Chargement...</span>
                                            </div>
                                            <p class="mt-2">Chargement des demandes...</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Accept Request Modal -->
    <div class="modal fade" id="acceptModal" tabindex="-1" aria-labelledby="acceptModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-sm">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="acceptModalLabel">Accepter la demande</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p><strong id="acceptTeacherName"></strong><br>
                    UE: <strong id="acceptUeName"></strong><br>
                    Module: <strong id="acceptModuleName"></strong></p>
                    <div class="mb-2">
                        <label for="acceptComment" class="form-label">Commentaire</label>
                        <textarea class="form-control form-control-sm" id="acceptComment" rows="2" placeholder="Optionnel"></textarea>
                    </div>
                    <input type="hidden" id="acceptPreferenceId">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-sm btn-accept" id="confirmAcceptBtn">Confirmer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Reject Request Modal -->
    <div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-sm">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="rejectModalLabel">Rejeter la demande</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p><strong id="rejectTeacherName"></strong><br>
                    UE: <strong id="rejectUeName"></strong><br>
                    Module: <strong id="rejectModuleName"></strong></p>
                    <div class="mb-2">
                        <label for="rejectComment" class="form-label">Commentaire</label>
                        <textarea class="form-control form-control-sm" id="rejectComment" rows="2" placeholder="Optionnel"></textarea>
                    </div>
                    <input type="hidden" id="rejectPreferenceId">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-sm btn-reject" id="confirmRejectBtn">Confirmer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Global variables
        const departmentId = <?php echo json_encode($departmentId); ?>;
        const academicYear = <?php echo json_encode($academicYear); ?> || '<?php echo date('Y') . '-' . (date('Y') + 1); ?>';
        let requests = [];
        let filteredRequests = [];

        // DOM elements
        const requestsTableBody = document.getElementById('requestsTableBody');
        const moduleFilter = document.getElementById('moduleFilter');
        const statusFilter = document.getElementById('statusFilter');
        const enseignantFilter = document.getElementById('enseignantFilter');
        const filterSelects = document.querySelectorAll('.filter-select');

        // Accept modal elements
        const acceptModal = new bootstrap.Modal(document.getElementById('acceptModal'));
        const acceptTeacherName = document.getElementById('acceptTeacherName');
        const acceptUeName = document.getElementById('acceptUeName');
        const acceptModuleName = document.getElementById('acceptModuleName');
        const acceptComment = document.getElementById('acceptComment');
        const acceptPreferenceId = document.getElementById('acceptPreferenceId');
        const confirmAcceptBtn = document.getElementById('confirmAcceptBtn');

        // Reject modal elements
        const rejectModal = new bootstrap.Modal(document.getElementById('rejectModal'));
        const rejectTeacherName = document.getElementById('rejectTeacherName');
        const rejectUeName = document.getElementById('rejectUeName');
        const rejectModuleName = document.getElementById('rejectModuleName');
        const rejectComment = document.getElementById('rejectComment');
        const rejectPreferenceId = document.getElementById('rejectPreferenceId');
        const confirmRejectBtn = document.getElementById('confirmRejectBtn');

        // Load requests when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            loadRequests();

            // Add event listeners
            confirmAcceptBtn.addEventListener('click', acceptRequest);
            confirmRejectBtn.addEventListener('click', rejectRequest);

            // Add change event listeners to all filter selects
            filterSelects.forEach(select => {
                select.addEventListener('change', function() {
                    // Add visual feedback for active filters
                    if (this.value) {
                        this.classList.add('filter-active');
                    } else {
                        this.classList.remove('filter-active');
                    }

                    // Apply filters immediately
                    applyFilters();
                });
            });
        });

        // Load requests from the server
        async function loadRequests() {
            if (!departmentId) {
                showAlert('ID de département non disponible. Veuillez vous reconnecter.', 'danger');
                return;
            }

            try {
                const response = await fetch(`../../route/affectationRoute.php?action=getUePreferencesByDepartment&department_id=${departmentId}`);

                // Check if response is OK
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Server error:', errorText);
                    showAlert(`Erreur serveur: ${response.status} ${response.statusText}`, 'danger');
                    return;
                }

                // Check content type
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const errorText = await response.text();
                    console.error('Invalid content type:', contentType, 'Response:', errorText);
                    showAlert('Erreur: La réponse du serveur n\'est pas au format JSON', 'danger');
                    return;
                }

                const data = await response.json();

                if (data.error) {
                    showAlert(`Erreur: ${data.error}`, 'danger');
                    return;
                }

                requests = data.data || [];
                filteredRequests = [...requests];

                // Populate filters (this will preserve current filter selections)
                populateFilters();

                // Apply filters (this will update the filtered requests and render them)
                applyFilters();
            } catch (error) {
                console.error('Error loading requests:', error);
                showAlert('Erreur lors du chargement des demandes. Vérifiez la console pour plus de détails.', 'danger');
            }
        }

        // Populate filter dropdowns
        function populateFilters() {
            // Save current selections
            const currentModuleValue = moduleFilter.value;
            const currentEnseignantValue = enseignantFilter.value;
            const currentStatusValue = statusFilter.value;

            // Clear existing options
            moduleFilter.innerHTML = '<option value="">Tous les modules</option>';
            enseignantFilter.innerHTML = '<option value="">Tous les enseignants</option>';

            // Create sets to track unique values
            const modules = new Set();
            const enseignants = new Map();

            // Populate sets with unique values
            requests.forEach(request => {
                modules.add(request.module_name);
                enseignants.set(request.id_enseignant, `${request.enseignant_nom} ${request.enseignant_prenom}`);
            });

            // Add module options
            [...modules].sort().forEach(module => {
                const option = document.createElement('option');
                option.value = module;
                option.textContent = module;
                option.selected = (module === currentModuleValue);
                moduleFilter.appendChild(option);
            });

            // Add enseignant options
            [...enseignants.entries()].sort((a, b) => a[1].localeCompare(b[1])).forEach(([id, name]) => {
                const option = document.createElement('option');
                option.value = id;
                option.textContent = name;
                option.selected = (id === currentEnseignantValue);
                enseignantFilter.appendChild(option);
            });

            // Restore status filter value
            if (currentStatusValue) {
                statusFilter.value = currentStatusValue;
            }

            // Update visual feedback for active filters
            filterSelects.forEach(select => {
                if (select.value) {
                    select.classList.add('filter-active');
                } else {
                    select.classList.remove('filter-active');
                }
            });
        }

        // Apply filters to the requests
        function applyFilters() {
            const moduleValue = moduleFilter.value;
            const statusValue = statusFilter.value;
            const enseignantValue = enseignantFilter.value;

            // Check if any filter is active
            const isFiltering = moduleValue || statusValue || enseignantValue;

            // Apply filters
            filteredRequests = requests.filter(request => {
                const moduleMatch = !moduleValue || request.module_name === moduleValue;
                const statusMatch = !statusValue || request.statut === statusValue;
                const enseignantMatch = !enseignantValue || request.id_enseignant === enseignantValue;
                return moduleMatch && statusMatch && enseignantMatch;
            });

            // Update filter status text
            const filterStatus = document.getElementById('filterStatus');
            const requestCount = document.getElementById('requestCount');

            if (isFiltering) {
                let filterText = [];
                if (moduleValue) filterText.push(`Module: ${moduleValue}`);
                if (statusValue) {
                    const statusText = statusValue === 'en_attente' ? 'En attente' :
                                      (statusValue === 'acceptee' ? 'Acceptée' : 'Rejetée');
                    filterText.push(`Statut: ${statusText}`);
                }
                if (enseignantValue) {
                    const enseignantOption = enseignantFilter.querySelector(`option[value="${enseignantValue}"]`);
                    if (enseignantOption) {
                        filterText.push(`Enseignant: ${enseignantOption.textContent}`);
                    }
                }

                filterStatus.textContent = `Filtres actifs: ${filterText.join(', ')}`;
                filterStatus.style.display = 'inline';
            } else {
                filterStatus.textContent = '';
                filterStatus.style.display = 'none';
            }

            // Update count
            requestCount.textContent = filteredRequests.length;

            renderRequests();
        }

        // Render requests in the table
        function renderRequests() {
            if (filteredRequests.length === 0) {
                requestsTableBody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-4">
                        <div class="empty-state">
                            <i class="bi bi-inbox"></i>
                            <h5>Aucune demande trouvée</h5>
                            <p class="text-muted">Il n'y a pas de demandes correspondant à vos critères de recherche.</p>
                        </div>
                    </td>
                </tr>`;
                return;
            }

            let html = '';

            filteredRequests.forEach(request => {
                const statusClass = `badge-${request.statut || 'en_attente'}`;
                const statusText = request.statut === 'acceptee' ? 'Acceptée' :
                                  (request.statut === 'rejetee' ? 'Rejetée' : 'En attente');

                const actionsHtml = request.statut === 'en_attente' ? `
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-accept me-1 accept-btn" data-preference-id="${request.preference_id}"
                            data-teacher-name="${request.enseignant_nom} ${request.enseignant_prenom}"
                            data-ue-name="${request.ue_type}"
                            data-module-name="${request.module_name}" title="Accepter">
                            <i class="bi bi-check-circle me-1"></i> Accepter
                        </button>
                        <button class="btn btn-sm btn-reject reject-btn" data-preference-id="${request.preference_id}"
                            data-teacher-name="${request.enseignant_nom} ${request.enseignant_prenom}"
                            data-ue-name="${request.ue_type}"
                            data-module-name="${request.module_name}" title="Rejeter">
                            <i class="bi bi-x-circle me-1"></i> Rejeter
                        </button>
                    </div>
                ` : `<span class="text-muted small">-</span>`;

                html += `
                <tr>
                    <td>${request.enseignant_nom} ${request.enseignant_prenom}</td>
                    <td>${request.module_name}</td>
                    <td>${request.ue_type} (${request.volume_horaire}h)</td>
                    <td>${request.date_demande}</td>
                    <td><span class="badge ${statusClass}">${statusText}</span></td>
                    <td>${actionsHtml}</td>
                </tr>`;
            });

            requestsTableBody.innerHTML = html;

            // Add event listeners to action buttons
            document.querySelectorAll('.accept-btn').forEach(button => {
                button.addEventListener('click', () => {
                    const preferenceId = button.dataset.preferenceId;
                    const teacherName = button.dataset.teacherName;
                    const ueName = button.dataset.ueName;
                    const moduleName = button.dataset.moduleName;

                    acceptTeacherName.textContent = teacherName;
                    acceptUeName.textContent = ueName;
                    acceptModuleName.textContent = moduleName;
                    acceptPreferenceId.value = preferenceId;
                    acceptComment.value = '';

                    acceptModal.show();
                });
            });

            document.querySelectorAll('.reject-btn').forEach(button => {
                button.addEventListener('click', () => {
                    const preferenceId = button.dataset.preferenceId;
                    const teacherName = button.dataset.teacherName;
                    const ueName = button.dataset.ueName;
                    const moduleName = button.dataset.moduleName;

                    rejectTeacherName.textContent = teacherName;
                    rejectUeName.textContent = ueName;
                    rejectModuleName.textContent = moduleName;
                    rejectPreferenceId.value = preferenceId;
                    rejectComment.value = '';

                    rejectModal.show();
                });
            });
        }

        // Accept a request
        async function acceptRequest() {
            const preferenceId = acceptPreferenceId.value;
            const commentaire = acceptComment.value;

            // Debug logs
            console.log('Accepting preference with ID:', preferenceId);
            console.log('Academic year type:', typeof academicYear);
            console.log('Academic year value:', academicYear);
            console.log('Comment:', commentaire);

            // Validate required fields
            if (!preferenceId) {
                showAlert('Erreur: ID de préférence manquant', 'danger');
                return;
            }

            // Ensure academicYear is defined and valid
            let yearToUse = academicYear;
            if (!yearToUse || yearToUse === "null" || yearToUse === "undefined") {
                // Fallback to current academic year
                const currentYear = new Date().getFullYear();
                const nextYear = currentYear + 1;
                yearToUse = `${currentYear}-${nextYear}`;
                console.log(`academicYear was invalid, using fallback: ${yearToUse}`);
            }

            // Force string values for all fields
            const requestData = {
                preference_id: String(preferenceId),
                academic_year: String(yearToUse),
                commentaire: commentaire ? String(commentaire) : ''
            };

            console.log('Request data:', requestData);
            console.log('JSON.stringify result:', JSON.stringify(requestData));

            try {
                const response = await fetch('../../route/affectationRoute.php?action=acceptUePreference', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                // Check if response is OK
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Server error:', errorText);
                    showAlert(`Erreur serveur: ${response.status} ${response.statusText}`, 'danger');
                    return;
                }

                // Check content type
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const errorText = await response.text();
                    console.error('Invalid content type:', contentType, 'Response:', errorText);
                    showAlert('Erreur: La réponse du serveur n\'est pas au format JSON', 'danger');
                    return;
                }

                const data = await response.json();

                if (data.error) {
                    // Check for specific error messages
                    if (data.error.includes("Unauthorized") || data.error.includes("another department")) {
                        showAlert(`Erreur d'autorisation: Vous n'avez pas les droits pour gérer cette demande car elle appartient à un autre département.`, 'danger');
                    } else {
                        showAlert(`Erreur: ${data.error}`, 'danger');
                    }
                    return;
                }

                showAlert('Demande acceptée avec succès', 'success');
                acceptModal.hide();
                loadRequests();
            } catch (error) {
                console.error('Error accepting request:', error);
                showAlert('Erreur lors de l\'acceptation de la demande. Vérifiez la console pour plus de détails.', 'danger');
            }
        }

        // Reject a request
        async function rejectRequest() {
            const preferenceId = rejectPreferenceId.value;
            const commentaire = rejectComment.value;

            try {
                const response = await fetch('../../route/affectationRoute.php?action=rejectUePreference', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        preference_id: preferenceId,
                        commentaire: commentaire
                    })
                });

                // Check if response is OK
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Server error:', errorText);
                    showAlert(`Erreur serveur: ${response.status} ${response.statusText}`, 'danger');
                    return;
                }

                // Check content type
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const errorText = await response.text();
                    console.error('Invalid content type:', contentType, 'Response:', errorText);
                    showAlert('Erreur: La réponse du serveur n\'est pas au format JSON', 'danger');
                    return;
                }

                const data = await response.json();

                if (data.error) {
                    // Check for specific error messages
                    if (data.error.includes("Unauthorized") || data.error.includes("another department")) {
                        showAlert(`Erreur d'autorisation: Vous n'avez pas les droits pour gérer cette demande car elle appartient à un autre département.`, 'danger');
                    } else {
                        showAlert(`Erreur: ${data.error}`, 'danger');
                    }
                    return;
                }

                showAlert('Demande rejetée avec succès', 'success');
                rejectModal.hide();
                loadRequests();
            } catch (error) {
                console.error('Error rejecting request:', error);
                showAlert('Erreur lors du rejet de la demande. Vérifiez la console pour plus de détails.', 'danger');
            }
        }

        // Show an alert message
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertId = `alert-${Date.now()}`;

            const alertHtml = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>`;

            alertContainer.insertAdjacentHTML('beforeend', alertHtml);

            // Auto-dismiss after 4 seconds
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    alertElement.classList.remove('show');
                    setTimeout(() => alertElement.remove(), 250);
                }
            }, 4000);
        }
    </script>
</body>
</html>
