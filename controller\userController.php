<?php
require_once "../model/userModel.php";
require_once "../utils/response.php";

/**
 * Réinitialise le mot de passe d'un utilisateur
 *
 * @param string $token Le token de réinitialisation
 * @param string $username Le nom d'utilisateur
 * @param string $password Le nouveau mot de passe
 * @param string $confirmPassword La confirmation du mot de passe
 * @return array Résultat de l'opération
 */
function resetPasswordAPI($token, $username, $password, $confirmPassword) {
    try {
        // Vérifier que les mots de passe correspondent
        if ($password !== $confirmPassword) {
            return ['success' => false, 'error' => 'Les mots de passe ne correspondent pas.'];
        }

        // Vérifier la complexité du mot de passe
        if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/', $password)) {
            return ['success' => false, 'error' => 'Le mot de passe doit contenir au moins 8 caractères, une majuscule, un chiffre et un caractère spécial.'];
        }

        // Vérifier le token
        $tokenResult = verifyPasswordResetToken($token);

        if (isset($tokenResult['error'])) {
            return ['success' => false, 'error' => 'Le token est invalide ou a expiré.'];
        }

        // Vérifier que le username correspond
        if ($tokenResult['username'] !== $username) {
            return ['success' => false, 'error' => 'Le nom d\'utilisateur ne correspond pas au token.'];
        }

        // Hasher le mot de passe
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

        // Mettre à jour le mot de passe
        $result = updateUserPassword($username, $hashedPassword);

        if (isset($result['error'])) {
            return ['success' => false, 'error' => 'Erreur lors de la mise à jour du mot de passe: ' . $result['error']];
        }

        // Activer le compte utilisateur après l'initialisation du mot de passe
        $activationResult = activateUserAccount($username);
        if (isset($activationResult['error'])) {
            error_log("Warning: Failed to activate user account for $username: " . $activationResult['error']);
            // Continue anyway since password was updated successfully
        } else {
            error_log("User account activated successfully for username: $username");
        }

        // Retourner le succès
        return ['success' => true, 'message' => 'Mot de passe initialisé avec succès.'];
    } catch (Exception $e) {
        error_log("Exception dans resetPasswordAPI: " . $e->getMessage());
        return ['success' => false, 'error' => 'Une erreur est survenue lors de la réinitialisation du mot de passe.'];
    }
}

/**
 * Récupère le nombre de messages non lus pour l'utilisateur connecté
 */
function getUserUnreadMessagesCountAPI() {
    // Vérifier si l'utilisateur est connecté
    session_start();
    if (!isset($_SESSION['user']) || !isset($_SESSION['user']['username'])) {
        jsonResponse(['error' => 'Utilisateur non connecté'], 401);
        return;
    }

    $username = $_SESSION['user']['username'];

    // Récupérer le nombre de messages non lus
    $count = getUserUnreadMessagesCount($username);

    jsonResponse(['count' => $count], 200);
}

/**
 * Récupère le nombre de messages non lus pour un utilisateur
 *
 * @param string $username Le nom d'utilisateur
 * @return int Le nombre de messages non lus
 */
function getUserUnreadMessagesCount($username) {
    // Cette fonction devrait être implémentée dans le modèle de messages
    // Pour l'instant, nous retournons un nombre aléatoire pour la démonstration
    return rand(0, 5);
}
?>
