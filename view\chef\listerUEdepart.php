<?php
// Vérifier l'authentification
require_once '../includes/auth_check_chef.php';

// Inclure le contrôleur
require_once __DIR__ . '/../../controller/listerUEdepartController.php';

// Récupérer l'ID du département du chef connecté
$departementId = null;

// Vérifier si le department_id est déjà dans la session
if (isset($_SESSION['user']['department_id'])) {
    $departementId = $_SESSION['user']['department_id'];
}

// Enregistrer la visite
recordDepartmentModulesVisit('chef de departement');

// Récupérer les informations du département
$departement = getDepartmentInfo($departementId);
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modules du Département - Chef de Département</title>
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🎓</text></svg>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard-style.css">
    <link rel="stylesheet" href="../assets/css/listerUEdepart.css">

    <!-- Styles moved to listerUEdepart.css -->
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>
            <div class="container-fluid p-4">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="dashboard-title">Modules du Département</h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Tableau de bord</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Modules du département</li>
                            </ol>
                        </nav>
                    </div>
                </div>

                <?php if (!$departementId): ?>
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Vous n'êtes pas associé à un département en tant que chef. Veuillez contacter l'administrateur.
                </div>
                <?php else: ?>
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="department-card">
                            <div class="department-header">
                                <div class="department-icon">
                                    <i class="fas fa-university"></i>
                                </div>
                                <div class="department-title">
                                    <h5>
                                        Département: <span class="department-name"><?php echo isset($departement['nom_dep']) ? htmlspecialchars($departement['nom_dep']) : 'Non spécifié'; ?></span>
                                    </h5>
                                    <p class="department-description">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Cette page affiche tous les modules des filières appartenant à votre département.
                                    </p>
                                </div>
                            </div>
                            <div class="department-filter">
                                <div class="filter-container">
                                    <label for="filiere-filter" class="form-label">
                                        <i class="fas fa-filter me-2"></i>Filtrer par filière:
                                    </label>
                                    <select id="filiere-filter" class="form-select">
                                        <option value="all" selected>Toutes les filières</option>
                                        <!-- Options will be populated dynamically -->
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row" id="filieres-container" data-department-id="<?php echo htmlspecialchars($departementId); ?>">
                    <!-- Les filières et leurs modules seront chargés dynamiquement ici -->
                    <div class="col-12">
                        <div class="spinner-container">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                            <p class="mt-3">Chargement des modules...</p>
                        </div>
                    </div>
                </div>

                <!-- Debug section removed -->
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Modal pour afficher les unités d'enseignement -->
    <div class="modal fade" id="unitsModal" tabindex="-1" aria-labelledby="unitsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="unitsModalLabel">
                        <i class="fas fa-book me-2"></i>
                        Unités d'Enseignement
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="units-container">
                    <!-- Les unités d'enseignement seront chargées dynamiquement ici -->
                    <div class="spinner-container">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                        <p class="mt-3">Chargement des unités d'enseignement...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>
                        Fermer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/listerUEdepart.js"></script>

    <script>
    // Add a console log to verify the script is loaded
    console.log('listerUEdepart.js script loaded');

    // Add a fallback in case the script fails to load
    window.addEventListener('error', function(e) {
        if (e.target.tagName === 'SCRIPT' && e.target.src.includes('listerUEdepart.js')) {
            console.error('Failed to load listerUEdepart.js, trying alternative path');
            var script = document.createElement('script');
            script.src = '../../view/assets/js/listerUEdepart.js';
            document.body.appendChild(script);
        }
    }, true);
    </script>
</body>
</html>
