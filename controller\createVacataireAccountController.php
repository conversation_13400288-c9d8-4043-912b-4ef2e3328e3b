<?php
/**
 * Controller for creating vacataire user accounts by coordinators
 */

// Include required files
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../model/userModel.php';
require_once __DIR__ . '/../model/authModel.php';
require_once __DIR__ . '/../model/filiereModel.php';
require_once __DIR__ . '/../model/specialiteModel.php';
require_once __DIR__ . '/../model/enseignantModel.php';
require_once __DIR__ . '/../model/departementModel.php';
require_once __DIR__ . '/../utils/emailSender.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * JSON response helper function
 */
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * Create a vacataire user account
 */
function createVacataireAccountAPI() {
    // Check if user is authenticated and is a coordinator
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'coordinateur') {
        jsonResponse(['error' => 'Accès non autorisé. Seuls les coordinateurs peuvent créer des comptes vacataires.'], 403);
    }

    // Get JSON data
    $data = json_decode(file_get_contents("php://input"), true);
    if (!$data) {
        jsonResponse(['error' => 'Données JSON invalides ou manquantes'], 400);
    }

    // Validate required fields
    $required_fields = ['CNI', 'email', 'nom', 'prenom', 'sexe', 'departement', 'specialite'];
    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            jsonResponse(['error' => "Le champ '$field' est obligatoire"], 400);
        }
    }

    // Sanitize and validate input data
    $cni = strtoupper(trim($data['CNI']));
    $email = strtolower(trim($data['email']));
    $nom = trim($data['nom']);
    $prenom = trim($data['prenom']);
    $sexe = trim($data['sexe']);
    $departementId = intval($data['departement']);
    $specialiteId = intval($data['specialite']);
    $role = 'vacataire'; // Fixed role

    // Validate CNI format (alphanumeric, 5-20 characters)
    if (!preg_match('/^[A-Z0-9]{5,20}$/', $cni)) {
        jsonResponse(['error' => 'Le CNI doit contenir entre 5 et 20 caractères alphanumériques'], 400);
    }

    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        jsonResponse(['error' => 'Format d\'email invalide'], 400);
    }

    // Validate name lengths
    if (strlen($nom) < 2 || strlen($nom) > 50) {
        jsonResponse(['error' => 'Le nom doit contenir entre 2 et 50 caractères'], 400);
    }

    if (strlen($prenom) < 2 || strlen($prenom) > 50) {
        jsonResponse(['error' => 'Le prénom doit contenir entre 2 et 50 caractères'], 400);
    }

    // Validate sexe
    if (!in_array($sexe, ['masculin', 'feminin'])) {
        jsonResponse(['error' => 'Le sexe doit être "masculin" ou "feminin"'], 400);
    }

    // Validate departement and specialité IDs
    if ($departementId <= 0) {
        jsonResponse(['error' => 'ID de département invalide'], 400);
    }

    if ($specialiteId <= 0) {
        jsonResponse(['error' => 'ID de spécialité invalide'], 400);
    }

    // Check if user with this CNI already exists
    $existingUser = getUserByUsername($cni);
    if ($existingUser && !isset($existingUser['error'])) {
        jsonResponse(['error' => 'Un utilisateur avec ce CNI existe déjà'], 409);
    }

    // Check if enseignant with this CNI already exists
    $existingEnseignant = getEnseignantByCNI($cni);
    if ($existingEnseignant && !isset($existingEnseignant['error'])) {
        jsonResponse(['error' => 'Un enseignant avec ce CNI existe déjà'], 409);
    }

    // Log the department ID that was selected
    error_log("Using departement ID: $departementId");

    // Generate a temporary password and hash it
    $tempPassword = bin2hex(random_bytes(8));
    $hashedPassword = password_hash($tempPassword, PASSWORD_DEFAULT);

    // Create the user account first
    $result = createUser($cni, $hashedPassword, $role);

    if (isset($result['error'])) {
        jsonResponse(['error' => 'Erreur lors de la création du compte: ' . $result['error']], 500);
    }

    // Log successful user creation
    error_log("User account created successfully for CNI: $cni with role: $role");

    // Validate that departement and specialite exist
    require_once __DIR__ . '/../model/departementModel.php';
    require_once __DIR__ . '/../model/specialiteModel.php';

    // Check if departement exists
    $departements = getAllDepartements();
    if (isset($departements['error'])) {
        error_log("Error fetching departements: " . $departements['error']);
        // Clean up user account
        require_once __DIR__ . '/../model/userModel.php';
        deleteUserByUsername($cni);
        jsonResponse(['error' => 'Erreur lors de la validation du département. Veuillez réessayer.'], 500);
    }

    $departementExists = false;
    foreach ($departements as $dept) {
        if ($dept['id_departement'] == $departementId) {
            $departementExists = true;
            break;
        }
    }

    if (!$departementExists) {
        error_log("Invalid departement ID: $departementId. Available departments: " . json_encode($departements));
        // Clean up user account
        require_once __DIR__ . '/../model/userModel.php';
        deleteUserByUsername($cni);
        jsonResponse(['error' => 'Département invalide. Veuillez réessayer.'], 400);
    }

    // Check if specialite exists
    $specialites = getAllSpecialites();
    if (isset($specialites['error'])) {
        error_log("Error fetching specialites: " . $specialites['error']);
        // Clean up user account
        require_once __DIR__ . '/../model/userModel.php';
        deleteUserByUsername($cni);
        jsonResponse(['error' => 'Erreur lors de la validation de la spécialité. Veuillez réessayer.'], 500);
    }

    $specialiteExists = false;
    foreach ($specialites as $spec) {
        if ($spec['id'] == $specialiteId) {
            $specialiteExists = true;
            break;
        }
    }

    if (!$specialiteExists) {
        error_log("Invalid specialite ID: $specialiteId. Available specialites: " . json_encode($specialites));
        // Clean up user account
        require_once __DIR__ . '/../model/userModel.php';
        deleteUserByUsername($cni);
        jsonResponse(['error' => 'Spécialité invalide. Veuillez réessayer.'], 400);
    }

    // Check if enseignant with this CNI already exists
    require_once __DIR__ . '/../model/enseignantModel.php';
    $existingEnseignant = getEnseignantByCNI($cni);
    if ($existingEnseignant) {
        error_log("Enseignant with CNI $cni already exists");
        // Clean up user account
        require_once __DIR__ . '/../model/userModel.php';
        deleteUserByUsername($cni);
        jsonResponse(['error' => 'Un enseignant avec ce CNI existe déjà. Veuillez vérifier le CNI.'], 400);
    }

    // Ensure departement and specialite IDs are integers
    $departementId = intval($departementId);
    $specialiteId = intval($specialiteId);

    // Validate that the IDs are positive integers
    if ($departementId <= 0) {
        error_log("Invalid departement ID after conversion: $departementId");
        require_once __DIR__ . '/../model/userModel.php';
        deleteUserByUsername($cni);
        jsonResponse(['error' => 'ID de département invalide'], 400);
    }

    if ($specialiteId <= 0) {
        error_log("Invalid specialite ID after conversion: $specialiteId");
        require_once __DIR__ . '/../model/userModel.php';
        deleteUserByUsername($cni);
        jsonResponse(['error' => 'ID de spécialité invalide'], 400);
    }

    // Create the enseignant record
    $enseignantData = [
        'CNI' => $cni,
        'nom' => $nom,
        'prenom' => $prenom,
        'email' => $email,
        'tele' => '', // Empty phone for now
        'date_naissance' => '1990-01-01', // Default date
        'lieu_naissance' => 'Non spécifié', // Default value instead of empty
        'sexe' => $sexe, // Use the selected gender
        'ville' => 'Non spécifié', // Default value instead of empty
        'pays' => 'Maroc', // Default country
        'role' => 'vacataire',
        'id_departement' => $departementId,
        'id_specialite' => $specialiteId,
        'date_debut_travail' => date('Y-m-d')
    ];

    // Final validation: Check that the foreign key values actually exist
    require_once __DIR__ . '/../model/departementModel.php';
    $departementInfo = getDepartementById($departementId);
    if (!$departementInfo || isset($departementInfo['error'])) {
        error_log("Departement ID $departementId does not exist in database");
        require_once __DIR__ . '/../model/userModel.php';
        deleteUserByUsername($cni);
        jsonResponse(['error' => 'Le département sélectionné n\'existe pas'], 400);
    }

    $specialiteInfo = getSpecialiteById($specialiteId);
    if (!$specialiteInfo || isset($specialiteInfo['error'])) {
        error_log("Specialite ID $specialiteId does not exist in database");
        require_once __DIR__ . '/../model/userModel.php';
        deleteUserByUsername($cni);
        jsonResponse(['error' => 'La spécialité sélectionnée n\'existe pas'], 400);
    }

    // Log the enseignant data for debugging
    error_log("Creating enseignant record with data: " . json_encode($enseignantData));
    error_log("Validated departement: " . json_encode($departementInfo));
    error_log("Validated specialite: " . json_encode($specialiteInfo));

    // Double-check that CNI doesn't exist in enseignant table before insertion
    $conn = getConnection();
    if ($conn) {
        $checkCNI = mysqli_prepare($conn, "SELECT CNI FROM enseignant WHERE CNI = ?");
        mysqli_stmt_bind_param($checkCNI, "s", $cni);
        mysqli_stmt_execute($checkCNI);
        $result = mysqli_stmt_get_result($checkCNI);

        if (mysqli_num_rows($result) > 0) {
            mysqli_stmt_close($checkCNI);
            mysqli_close($conn);
            // Clean up user account
            require_once __DIR__ . '/../model/userModel.php';
            deleteUserByUsername($cni);
            jsonResponse(['error' => 'Un enseignant avec ce CNI existe déjà dans la base de données'], 409);
        }
        mysqli_stmt_close($checkCNI);
        mysqli_close($conn);
    }

    $enseignantResult = createEnseignant($enseignantData);

    if (!$enseignantResult) {
        // If enseignant creation fails, we should clean up the user account
        error_log("Failed to create enseignant record for CNI: $cni");
        error_log("Enseignant data that failed: " . json_encode($enseignantData));

        // Get more specific error information
        $conn = getConnection();
        if ($conn) {
            $lastError = mysqli_error($conn);
            error_log("MySQL error during enseignant creation: " . $lastError);
            mysqli_close($conn);
        }

        // Try to delete the user account that was just created
        require_once __DIR__ . '/../model/userModel.php';
        $deleteResult = deleteUserByUsername($cni);
        if (isset($deleteResult['error'])) {
            error_log("Failed to cleanup user account for CNI: $cni after enseignant creation failure");
        } else {
            error_log("Successfully cleaned up user account for CNI: $cni");
        }

        jsonResponse(['error' => 'Erreur lors de la création du profil enseignant. Détails: Vérifiez que le CNI n\'est pas déjà utilisé, que les IDs de département et spécialité sont valides, ou contactez l\'administrateur.'], 500);
    }

    // Log successful enseignant creation
    error_log("Enseignant record created successfully for CNI: $cni");

    // Generate password reset token
    $token = bin2hex(random_bytes(32));
    $expiry = date('Y-m-d H:i:s', strtotime('+24 hours'));

    // Store the password reset token
    $tokenResult = storePasswordResetToken($cni, $token, $expiry);

    if (isset($tokenResult['error'])) {
        // Account was created but token storage failed
        jsonResponse(['error' => 'Compte créé mais erreur lors de la génération du lien de réinitialisation: ' . $tokenResult['error']], 500);
    }

    // Get department name for email
    $departementInfo = getDepartementById($departementId);
    $departementName = $departementInfo ? $departementInfo['nom_dep'] : 'Département non spécifié';

    // Send password initialization email
    $emailResult = sendVacataireAccountEmail($email, $nom, $prenom, $cni, $token, $departementName);

    if (!$emailResult['success']) {
        // Account and token were created but email failed
        jsonResponse([
            'success' => true,
            'message' => 'Compte vacataire créé avec succès, mais l\'email n\'a pas pu être envoyé. Veuillez contacter l\'administrateur pour obtenir le lien de réinitialisation.',
            'warning' => 'Email non envoyé: ' . $emailResult['message']
        ], 201);
    }

    // Everything successful
    jsonResponse([
        'success' => true,
        'message' => 'Compte vacataire créé avec succès. Un email avec les instructions de connexion a été envoyé à ' . $email,
        'username' => $cni
    ], 201);
}

/**
 * Send account creation email to vacataire
 */
function sendVacataireAccountEmail($email, $nom, $prenom, $username, $token, $departementName) {
    // Get coordinator info for email context
    $coordinatorName = $_SESSION['user']['prenom'] . ' ' . $_SESSION['user']['nom'];

    // Build password reset link
    $host = $_SERVER['HTTP_HOST'];
    require_once __DIR__ . '/../config/constants.php';
    $resetLink = "http://$host" . BASE_URL . "/view/initialize-password.php?token=$token";

    $subject = "Création de votre compte vacataire - ENSAH";

    $message = "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
            .important { color: #e74c3c; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>Bienvenue sur la plateforme ENSAH</h1>
                <p>Votre compte vacataire a été créé</p>
            </div>
            <div class='content'>
                <p>Bonjour <strong>$prenom $nom</strong>,</p>

                <p>Votre compte vacataire a été créé par le coordinateur <strong>$coordinatorName</strong> pour le département <strong>$departementName</strong>.</p>

                <p><strong>Informations de votre compte :</strong></p>
                <ul>
                    <li><strong>Nom d'utilisateur :</strong> $username</li>
                    <li><strong>Rôle :</strong> Vacataire</li>
                    <li><strong>Département :</strong> $departementName</li>
                </ul>

                <p>Pour activer votre compte et définir votre mot de passe, veuillez cliquer sur le lien ci-dessous :</p>

                <p style='text-align: center;'>
                    <a href='$resetLink' class='button'>Initialiser mon mot de passe</a>
                </p>

                <p><span class='important'>Important :</span> Ce lien est valable pendant <strong>24 heures</strong> seulement.</p>

                <p>Après avoir initialisé votre mot de passe, vous pourrez vous connecter à la plateforme et accéder à toutes les fonctionnalités réservées aux vacataires.</p>

                <p>Si vous avez des questions, n'hésitez pas à contacter le coordinateur de votre filière.</p>

                <p>Cordialement,<br><strong>L'équipe ENSAH</strong></p>
            </div>
            <div class='footer'>
                <p>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</p>
                <p>© " . date('Y') . " École Nationale des Sciences Appliquées Al Hoceima. Tous droits réservés.</p>
            </div>
        </div>
    </body>
    </html>";

    // Send the email
    return sendEmail($email, $subject, $message);
}

/**
 * Get all departments
 */
function getDepartementsAPI() {
    // Check if user is authenticated and is a coordinator
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'coordinateur') {
        jsonResponse(['error' => 'Accès non autorisé'], 403);
    }

    $departements = getAllDepartements();

    if (isset($departements['error'])) {
        jsonResponse(['error' => 'Erreur lors de la récupération des départements: ' . $departements['error']], 500);
    }

    jsonResponse(['success' => true, 'departements' => $departements]);
}

/**
 * Get all specialties
 */
function getAllSpecialitesAPI() {
    // Check if user is authenticated and is a coordinator
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'coordinateur') {
        jsonResponse(['error' => 'Accès non autorisé'], 403);
    }

    $specialites = getAllSpecialites();

    if (isset($specialites['error'])) {
        jsonResponse(['error' => 'Erreur lors de la récupération des spécialités: ' . $specialites['error']], 500);
    }

    jsonResponse(['success' => true, 'specialites' => $specialites]);
}



/**
 * Handle different HTTP methods and actions
 */
function handleRequest() {
    $method = $_SERVER['REQUEST_METHOD'];
    $action = isset($_GET['action']) ? $_GET['action'] : '';

    switch ($method) {
        case 'GET':
            switch ($action) {
                case 'getDepartements':
                    getDepartementsAPI();
                    break;
                case 'getAllSpecialites':
                    getAllSpecialitesAPI();
                    break;
                default:
                    jsonResponse(['error' => 'Action non reconnue'], 400);
                    break;
            }
            break;

        case 'POST':
            createVacataireAccountAPI();
            break;

        default:
            jsonResponse(['error' => 'Méthode non autorisée'], 405);
            break;
    }
}

// Handle the request if this file is called directly
if (basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
    handleRequest();
}
?>
