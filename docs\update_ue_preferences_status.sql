-- Script pour modifier la table ue_preferences en ajoutant la colonne statut

-- Vérifier si la table existe
SET @table_exists = 0;
SELECT COUNT(*) INTO @table_exists FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'ue_preferences';

-- Si la table existe, ajouter la colonne statut si elle n'existe pas déjà
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'ue_preferences' AND column_name = 'statut';

SET @alter_statement = IF(@table_exists > 0 AND @column_exists = 0, 
    'ALTER TABLE ue_preferences ADD COLUMN statut ENUM("en_attente", "acceptee", "rejetee") NOT NULL DEFAULT "en_attente"',
    'SELECT "Column statut already exists or table ue_preferences does not exist"');

PREPARE stmt FROM @alter_statement;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
