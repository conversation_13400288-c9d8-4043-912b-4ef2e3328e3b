<?php
require_once "../model/etudiantModel.php";
require_once "../model/userModel.php";
require_once "../utils/response.php";
require_once "../utils/emailSender.php";



// Afficher tous les étudiants
function getAllEtudiantsAPI() {
    $etudiants = getAllEtudiants();

    if (isset($etudiants['error'])) {
        jsonResponse(['error' => $etudiants['error']], 404);
    }

    jsonResponse(['data' => $etudiants], 200);
}

// Afficher un étudiant par CNE
function getEtudiantByCNEAPI($cne) {
    $etudiant = getEtudiantByCNE($cne);

    if (isset($etudiant['error'])) {
        jsonResponse(['error' => $etudiant['error']], 404);
    }

    jsonResponse(['data' => $etudiant], 200);
}



// Créer un nouvel étudiant
function createEtudiantAPI() {
    // Récupérer les données POST au format JSON
    $json = file_get_contents("php://input");
    error_log("JSON reçu : " . $json);
    $data = json_decode($json, true);

    // Validation des données
    if (!$data) {
        error_log("Erreur de décodage JSON : " . json_last_error_msg());
        jsonResponse(['error' => 'Donnees JSON invalides'], 400);
    }

    $requiredFields = ['CNE', 'nom', 'prenom', 'email', 'tele', 'sexe', 'pays', 'ville',
                     'date_naissance', 'lieu_naissance', 'coordonne_parental', 'id_filiere', 'date_inscription'];

    foreach ($requiredFields as $field) {
        if (empty($data[$field])) {
            jsonResponse(['error' => "Le champ $field est obligatoire"], 400);
        }
    }

    // Appel au modèle
    $result = insertEtudiant(
        $data['CNE'],
        $data['nom'],
        $data['prenom'],
        $data['email'],
        $data['tele'],
        $data['sexe'],
        $data['pays'],
        $data['ville'],
        $data['date_naissance'],
        $data['lieu_naissance'],
        $data['coordonne_parental'],
        $data['id_filiere'],
        $data['date_inscription'],
        $data['id_niveau']
    );

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 400);
    } else if (isset($result['success'])) {
        // Vérifier si l'option de création de compte est activée
        if (isset($data['createAccount']) && $data['createAccount'] === true) {
            // Créer un compte utilisateur pour l'étudiant
            $accountResult = createStudentAccount($data['CNE'], $data['email'], $data['nom'], $data['prenom']);

            if (isset($accountResult['error'])) {
                // L'étudiant a été créé mais pas le compte
                jsonResponse([
                    'message' => 'Étudiant créé avec succès, mais erreur lors de la création du compte: ' . $accountResult['error'],
                    'CNE' => $data['CNE']
                ], 201);
            } else {
                // Tout s'est bien passé
                jsonResponse([
                    'message' => 'Étudiant créé avec succès et compte utilisateur initialisé',
                    'CNE' => $data['CNE']
                ], 201);
            }
        } else {
            // Pas de création de compte demandée
            jsonResponse(['message' => 'Étudiant créé avec succès', 'CNE' => $data['CNE']], 201);
        }
    } else {
        jsonResponse(['error' => 'Erreur inattendue lors de la création de l\'étudiant'], 500);
    }
}

/**
 * Crée un compte utilisateur pour un étudiant et envoie un email d'initialisation de mot de passe
 *
 * @param string $cne Le CNE de l'étudiant (sera utilisé comme nom d'utilisateur)
 * @param string $email L'email de l'étudiant
 * @param string $nom Le nom de l'étudiant
 * @param string $prenom Le prénom de l'étudiant
 * @return array Résultat de l'opération
 */
function createStudentAccount($cne, $email, $nom, $prenom) {
    // Vérifier si un utilisateur avec ce nom d'utilisateur existe déjà
    $existingUser = getUserByUsername($cne);
    if ($existingUser && !isset($existingUser['error'])) {
        return ['error' => 'Un utilisateur avec ce CNE existe déjà'];
    }

    // Générer un token unique pour la réinitialisation du mot de passe
    $token = bin2hex(random_bytes(32));
    $expiry = date('Y-m-d H:i:s', strtotime('+24 hours'));

    // Créer l'utilisateur avec un mot de passe temporaire (qui ne sera pas utilisé)
    $tempPassword = bin2hex(random_bytes(8));
    $hashedPassword = password_hash($tempPassword, PASSWORD_DEFAULT);

    // Insérer l'utilisateur dans la base de données
    $result = createUser($cne, $hashedPassword, 'etudiant');

    if (isset($result['error'])) {
        return ['error' => $result['error']];
    }

    // Stocker le token de réinitialisation
    $tokenResult = storePasswordResetToken($cne, $token, $expiry);

    if (isset($tokenResult['error'])) {
        return ['error' => $tokenResult['error']];
    }

    // Envoyer l'email d'initialisation de mot de passe
    $fullName = $prenom . ' ' . $nom;

    // Récupérer le chemin de base dynamiquement
    require_once "../config/constants.php";
    $resetLink = "http://" . $_SERVER['HTTP_HOST'] . BASE_URL . "/view/initialize-password.php?token=" . $token;

    $subject = "Initialisation de votre mot de passe - UniAdmin";
    $message = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #4285f4; color: white; padding: 15px 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { padding: 30px; background-color: #f9f9f9; border: 1px solid #ddd; border-radius: 0 0 8px 8px; }
            .button { display: inline-block; background-color: #4285f4; color: white; padding: 12px 30px; text-decoration: none; border-radius: 4px; font-weight: bold; font-size: 16px; }
            .button:hover { background-color: #2a75f3; }
            .credentials { background-color: #e8f0fe; border-left: 4px solid #4285f4; padding: 15px; margin: 20px 0; }
            .link-container { background-color: #f5f5f5; padding: 15px; border-radius: 4px; margin: 20px 0; border: 1px solid #ddd; }
            .important { font-weight: bold; color: #ea4335; }
            .footer { font-size: 12px; text-align: center; margin-top: 30px; color: #777; }
            .highlight { font-weight: bold; color: #4285f4; }
            .student-info { background-color: #fef7e0; padding: 15px; border-radius: 4px; margin: 15px 0; border: 1px solid #fbbc04; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2 style='margin: 0; font-size: 24px;'>Bienvenue à l'ENSAH</h2>
            </div>
            <div class='content'>
                <p style='font-size: 18px;'><strong>Bonjour $fullName,</strong></p>

                <p>Félicitations ! Un compte étudiant a été créé pour vous sur la plateforme UniAdmin de l'École Nationale des Sciences Appliquées Al Hoceima.</p>

                <div class='student-info'>
                    <p style='margin: 5px 0; font-size: 16px;'><strong>Informations importantes :</strong></p>
                    <p style='margin: 5px 0;'>Ce compte vous permettra d'accéder à tous les services numériques de l'école, y compris les emplois du temps, les notes, et les ressources pédagogiques.</p>
                </div>

                <div class='credentials'>
                    <p style='margin: 5px 0;'><strong>Vos informations de connexion :</strong></p>
                    <p style='margin: 5px 0;'><span class='highlight'>Nom d'utilisateur :</span> <strong>$cne</strong></p>
                    <p style='margin: 5px 0;'><span class='highlight'>Mot de passe :</span> À définir en cliquant sur le bouton ci-dessous</p>
                </div>

                <p>Pour activer votre compte, vous devez initialiser votre mot de passe en cliquant sur le bouton ci-dessous :</p>

                <p style='text-align: center; margin: 30px 0;'>
                    <a href='$resetLink' class='button'>Initialiser mon mot de passe</a>
                </p>

                <p>Si le bouton ne fonctionne pas, vous pouvez copier et coller ce lien dans votre navigateur :</p>

                <div class='link-container'>
                    <p style='word-break: break-all; margin: 0;'>$resetLink</p>
                </div>

                <p><span class='important'>Important :</span> Ce lien est valable pendant <strong>24 heures</strong> seulement.</p>

                <p>Après avoir initialisé votre mot de passe, vous pourrez vous connecter à la plateforme et accéder à toutes les fonctionnalités réservées aux étudiants.</p>

                <p>Cordialement,<br><strong>L'équipe UniAdmin</strong></p>
            </div>
            <div class='footer'>
                <p>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</p>
                <p>© " . date('Y') . " École Nationale des Sciences Appliquées Al Hoceima. Tous droits réservés.</p>
            </div>
        </div>
    </body>
    </html>";

    // Inclure le fichier d'envoi d'email
    require_once "../utils/emailSender.php";

    // Envoyer l'email
    $emailResult = sendEmail($email, $subject, $message);

    // Vérifier si l'email a été envoyé avec succès
    if (!$emailResult['success']) {
        error_log("Erreur lors de l'envoi de l'email: " . $emailResult['message']);
        return ['error' => 'Compte créé mais erreur lors de l\'envoi de l\'email: ' . $emailResult['message']];
    }

    // Journaliser l'envoi de l'email
    error_log("Email d'initialisation de mot de passe envoyé à: " . $email);

    // Stocker le token dans la session pour faciliter les tests
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    $_SESSION['last_password_reset_token'] = $token;
    $_SESSION['last_password_reset_link'] = $resetLink;

    // Afficher le lien dans les logs pour faciliter les tests
    error_log("Lien de réinitialisation: " . $resetLink);

    return ['success' => true, 'message' => 'Compte créé et email d\'initialisation envoyé'];
}


// Mettre à jour un étudiant
function updateEtudiantAPI($cne) {
    // Récupérer les données POST au format JSON
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);

    // Validation des données
    if (!$data) {
        jsonResponse(['error' => 'Données JSON invalides'], 400);
    }

    // Les champs requis pour la mise à jour
    $requiredFields = ['nom', 'prenom', 'email', 'tele', 'sexe', 'pays', 'ville',
                       'date_naissance', 'lieu_naissance', 'coordonne_parental', 'id_filiere', 'date_inscription'];

    foreach ($requiredFields as $field) {
        if (empty($data[$field])) {
            jsonResponse(['error' => "Le champ $field est obligatoire"], 400);
        }
    }

    // Appel au modèle pour mettre à jour l'étudiant
    $result = updateEtudiant(
        $cne,
        $data['nom'],
        $data['prenom'],
        $data['email'],
        $data['tele'],
        $data['sexe'],
        $data['pays'],
        $data['ville'],
        $data['date_naissance'],
        $data['lieu_naissance'],
        $data['coordonne_parental'],
        $data['id_filiere'],
        $data['date_inscription'],
        $data['id_niveau']
    );

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 400);
    } else if (isset($result['success'])) {
        jsonResponse(['message' => 'Les informations de l\'étudiant ont été mises à jour avec succès'], 200);
    } else {
        jsonResponse(['error' => 'Erreur inattendue lors de la mise à jour des informations de l\'étudiant'], 500);
    }
}

// Supprimer un étudiant
function deleteEtudiantAPI($cne) {
    // Vérifier d'abord si l'étudiant existe
    $etudiant = getEtudiantByCNE($cne);

    if (isset($etudiant['error'])) {
        jsonResponse(['error' => $etudiant['error']], 404);
    }

    $result = deleteEtudiantByCNE($cne);

    if ($result) {
        jsonResponse(['message' => 'Étudiant supprimé avec succès'], 200);
    } else {
        jsonResponse(['error' => 'Erreur lors de la suppression de l\'étudiant'], 500);
    }
}