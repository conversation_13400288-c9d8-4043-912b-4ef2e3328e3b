<?php
require_once __DIR__ . '/../controller/eventsController.php';
require_once __DIR__ . '/../utils/response.php';

// Récupérer la méthode HTTP et l'action
$method = $_SERVER['REQUEST_METHOD'];
$action = isset($_GET['action']) ? $_GET['action'] : '';

// Récupérer les données du corps de la requête pour POST et PUT
if ($method === 'POST' || $method === 'PUT') {
    $data = json_decode(file_get_contents('php://input'), true);
}

// Router pour les événements
switch ($method) {
    case 'GET':
        if ($action === 'get' && isset($_GET['id'])) {
            echo handleGetEvent($_GET['id']);
        } else {
            echo handleGetEvents();
        }
        break;

    case 'POST':
        if (!isset($data)) {
            jsonResponse(['success' => false, 'message' => 'Données invalides'], 400);
            break;
        }
        echo handleCreateEvent($data);
        break;

    case 'PUT':
        if (!isset($_GET['id']) || !isset($data)) {
            jsonResponse(['success' => false, 'message' => 'ID de l\'événement ou données manquantes'], 400);
            break;
        }
        echo handleUpdateEvent($_GET['id'], $data);
        break;

    case 'DELETE':
        if (!isset($_GET['id'])) {
            jsonResponse(['success' => false, 'message' => 'ID de l\'événement manquant'], 400);
            break;
        }
        echo handleDeleteEvent($_GET['id']);
        break;

    default:
        jsonResponse(['success' => false, 'message' => 'Méthode non supportée'], 405);
        break;
}
?>