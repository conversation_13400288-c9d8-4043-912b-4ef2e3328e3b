<?php
require_once __DIR__ . "/../model/passwordResetModel.php";
require_once __DIR__ . "/../utils/emailSender.php";
require_once __DIR__ . "/../utils/response.php";

/**
 * Traite la demande de réinitialisation de mot de passe
 *
 * @param string $identifier CNI ou CNE de l'utilisateur
 * @return array Résultat de la demande
 */
function requestPasswordReset($identifier) {
    // Validation de l'identifiant
    if (empty($identifier)) {
        return ['success' => false, 'error' => 'Veuillez fournir un identifiant (CNI ou CNE)'];
    }

    // Rechercher l'utilisateur
    $userInfo = findUserByIdentifier($identifier);

    if (!$userInfo['success']) {
        return ['success' => false, 'error' => 'Aucun utilisateur trouvé avec cet identifiant'];
    }

    // Générer un code de réinitialisation
    $code = generateResetCode();

    // Enregistrer le code dans la base de données
    $saveResult = saveResetCode($identifier, $userInfo['email'], $code);

    if (!$saveResult) {
        return ['success' => false, 'error' => 'Erreur lors de l\'enregistrement du code de réinitialisation'];
    }

    // Envoyer l'email avec le code
    $emailResult = sendPasswordResetEmail($userInfo['email'], '', $code);

    if (!$emailResult['success']) {
        return ['success' => false, 'error' => 'Erreur lors de l\'envoi de l\'email: ' . $emailResult['message']];
    }

    // Masquer partiellement l'email pour la réponse
    $email = $userInfo['email'];
    $atPos = strpos($email, '@');
    $maskedEmail = substr($email, 0, 2) . '***' . substr($email, $atPos - 2);

    $response = [
        'success' => true,
        'message' => 'Un code de réinitialisation a été envoyé à votre adresse email',
        'email' => $maskedEmail,
        'identifier' => $identifier
    ];

    // Ajouter le code de débogage en environnement de développement
    if ($_SERVER['SERVER_NAME'] == 'localhost' || strpos($_SERVER['SERVER_NAME'], '127.0.0.1') !== false) {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        if (isset($_SESSION['debug_reset_code'])) {
            $response['debug_code'] = $_SESSION['debug_reset_code'];
            error_log("Code de débogage inclus dans la réponse: " . $_SESSION['debug_reset_code']);
        }
    }

    return $response;
}

/**
 * Vérifie le code de réinitialisation
 *
 * @param string $identifier CNI ou CNE de l'utilisateur
 * @param string $code Code de réinitialisation
 * @return array Résultat de la vérification
 */
function verifyResetCodeAPI($identifier, $code) {
    // Validation des entrées
    if (empty($identifier) || empty($code)) {
        return ['success' => false, 'error' => 'Veuillez fournir un identifiant et un code'];
    }

    // Vérifier le code
    $result = verifyResetCode($identifier, $code);

    if (!$result['success']) {
        return $result;
    }

    // Générer un token de session pour la réinitialisation
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    $_SESSION['reset_token'] = bin2hex(random_bytes(32));
    $_SESSION['reset_identifier'] = $identifier;
    $_SESSION['reset_code_id'] = $result['resetCode']['id'];

    return [
        'success' => true,
        'message' => 'Code vérifié avec succès',
        'token' => $_SESSION['reset_token']
    ];
}

/**
 * Réinitialise le mot de passe
 *
 * @param string $token Token de session
 * @param string $newPassword Nouveau mot de passe
 * @param string $confirmPassword Confirmation du nouveau mot de passe
 * @return array Résultat de la réinitialisation
 */
function resetPasswordAPI($token, $newPassword, $confirmPassword) {
    // Vérifier la session
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Vérifier le token
    if (!isset($_SESSION['reset_token']) || $_SESSION['reset_token'] !== $token) {
        return ['success' => false, 'error' => 'Session invalide ou expirée'];
    }

    // Vérifier que l'identifiant est présent
    if (!isset($_SESSION['reset_identifier'])) {
        return ['success' => false, 'error' => 'Session invalide ou expirée'];
    }

    // Validation des mots de passe
    if (empty($newPassword) || empty($confirmPassword)) {
        return ['success' => false, 'error' => 'Veuillez remplir tous les champs'];
    }

    if ($newPassword !== $confirmPassword) {
        return ['success' => false, 'error' => 'Les mots de passe ne correspondent pas'];
    }

    if (strlen($newPassword) < 6) {
        return ['success' => false, 'error' => 'Le mot de passe doit contenir au moins 6 caractères'];
    }

    // Réinitialiser le mot de passe
    $result = resetUserPassword($_SESSION['reset_identifier'], $newPassword);

    if ($result['success']) {
        // Marquer le code comme utilisé
        markResetCodeAsUsed($_SESSION['reset_code_id']);

        // Nettoyer la session
        unset($_SESSION['reset_token']);
        unset($_SESSION['reset_identifier']);
        unset($_SESSION['reset_code_id']);

        return [
            'success' => true,
            'message' => 'Mot de passe réinitialisé avec succès'
        ];
    }

    return $result;
}
?>
