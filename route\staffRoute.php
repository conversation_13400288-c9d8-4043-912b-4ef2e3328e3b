<?php
require_once "../controller/staffController.php";

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        if (isset($_GET['roles'])) {
            // Route pour obtenir les rôles disponibles
            getAvailableRolesAPI();
        } else if (isset($_GET['cni'])) {
            // Route pour obtenir un membre du personnel par son CNI
            getStaffByCNIAPI($_GET['cni']);
        } else {
            // Route pour obtenir tous les membres du personnel
            getAllStaffAPI();
        }
        break;

    case 'POST':
        // Route pour créer un nouveau membre du personnel
        createStaffAPI();
        break;

    case 'PUT':
        if (isset($_GET['cni'])) {
            // Route pour mettre à jour un membre du personnel par son CNI
            updateStaffAPI($_GET['cni']);
        }
        break;

    case 'DELETE':
        if (isset($_GET['cni'])) {
            // Route pour supprimer un membre du personnel par son CNI
            deleteStaffAPI($_GET['cni']);
        }
        break;

    default:
        jsonResponse(['error' => 'Méthode HTTP non autorisée'], 405);
        break;
}
?>