<?php
require_once __DIR__ . '/../model/eventsModel.php';
require_once __DIR__ . '/../utils/response.php';

function handleCreateEvent($data) {
    if (!isset($data['title']) || !isset($data['description']) || 
        !isset($data['category']) || !isset($data['event_date']) || 
        !isset($data['event_time']) || !isset($data['location'])) {
        jsonResponse(['success' => false, 'message' => 'Tous les champs sont requis'], 400);
    }

    $send_notification = isset($data['send_notification']) ? $data['send_notification'] : 1;
    
    $event_id = createEvent(
        $data['title'],
        $data['description'],
        $data['category'],
        $data['event_date'],
        $data['event_time'],
        $data['location'],
        $send_notification
    );

    if (!$event_id) {
        jsonResponse(['success' => false, 'message' => 'Erreur lors de la création de l\'événement'], 500);
    }

    // Gérer les images si elles sont fournies
    if (isset($data['images']) && is_array($data['images'])) {
        foreach ($data['images'] as $image_url) {
            if (!addEventImage($event_id, $image_url)) {
                error_log("Erreur lors de l'ajout de l'image pour l'événement $event_id");
            }
        }
    }

    $event = getEventById($event_id);
    jsonResponse(['success' => true, 'message' => 'Événement créé avec succès', 'data' => $event], 201);
}

function handleGetEvents() {
    $category = isset($_GET['category']) ? $_GET['category'] : null;
    $events = getEvents($category);
    jsonResponse(['success' => true, 'message' => 'Événements récupérés avec succès', 'data' => $events]);
}

function handleGetEvent($event_id) {
    $event = getEventById($event_id);
    
    if (!$event) {
        jsonResponse(['success' => false, 'message' => 'Événement non trouvé'], 404);
    }

    jsonResponse(['success' => true, 'message' => 'Événement récupéré avec succès', 'data' => $event]);
}

function handleUpdateEvent($event_id, $data) {
    $event = getEventById($event_id);
    if (!$event) {
        jsonResponse(['success' => false, 'message' => 'Événement non trouvé'], 404);
    }

    if (!isset($data['title']) || !isset($data['description']) || 
        !isset($data['category']) || !isset($data['event_date']) || 
        !isset($data['event_time']) || !isset($data['location'])) {
        jsonResponse(['success' => false, 'message' => 'Tous les champs sont requis'], 400);
    }

    $success = updateEvent(
        $event_id,
        $data['title'],
        $data['description'],
        $data['category'],
        $data['event_date'],
        $data['event_time'],
        $data['location'],
        isset($data['send_notification']) ? $data['send_notification'] : 1
    );

    if (!$success) {
        jsonResponse(['success' => false, 'message' => 'Erreur lors de la mise à jour de l\'événement'], 500);
    }

    // Gérer les images si elles sont fournies
    if (isset($data['images']) && is_array($data['images'])) {
        // Supprimer les anciennes images
        foreach ($event['images'] as $old_image) {
            deleteEventImage($event_id, $old_image);
        }
        // Ajouter les nouvelles images
        foreach ($data['images'] as $image_url) {
            if (!addEventImage($event_id, $image_url)) {
                error_log("Erreur lors de l'ajout de l'image pour l'événement $event_id");
            }
        }
    }

    $updated_event = getEventById($event_id);
    jsonResponse(['success' => true, 'message' => 'Événement mis à jour avec succès', 'data' => $updated_event]);
}

function handleDeleteEvent($event_id) {
    $event = getEventById($event_id);
    if (!$event) {
        jsonResponse(['success' => false, 'message' => 'Événement non trouvé'], 404);
    }

    $success = deleteEvent($event_id);
    if (!$success) {
        jsonResponse(['success' => false, 'message' => 'Erreur lors de la suppression de l\'événement'], 500);
    }

    jsonResponse(['success' => true, 'message' => 'Événement supprimé avec succès']);
}
?>