.faculty-management-container {
  background-color: #f8f9fa;
  border-radius: 8px;
}

.search-filters {
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.faculty-table {
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  width: 100%;
  table-layout: fixed;
  overflow-x: hidden;
  height: auto;
}

.faculty-table th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  color: #495057;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 1;
}

.faculty-table td {
  vertical-align: middle;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  padding: 4px 6px;
  font-size: 0.9rem;
}

.faculty-actions .btn {
  padding: 0.2rem 0.4rem;
  margin: 0 0.15rem;
  font-size: 0.85rem;
}

.faculty-actions .btn i {
  font-size: 0.875rem;
}

.filter-group {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.search-box {
  position: relative;
}

.search-box .bi-search {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.search-box input {
  padding-left: 35px;
}

/* Modal Styles */
.modal-content {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  border-radius: 8px 8px 0 0;
}

.modal-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  border-radius: 0 0 8px 8px;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .filter-group {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .faculty-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .faculty-actions .btn {
    width: 100%;
    margin: 0;
  }
}