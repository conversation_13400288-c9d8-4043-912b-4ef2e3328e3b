<?php
require_once "../controller/userController.php";
require_once "../utils/response.php";

// Vérifier si le fichier est appelé directement
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    // Désactiver l'affichage des erreurs pour éviter de renvoyer du HTML
    ini_set('display_errors', 0);
    error_reporting(E_ALL);

    // Définir le type de contenu comme JSON pour les requêtes AJAX
    header('Content-Type: application/json');

    try {
        // Récupérer l'action demandée
        $action = '';

        // Vérifier si c'est une requête POST JSON
        $jsonData = json_decode(file_get_contents('php://input'), true);
        if ($jsonData && isset($jsonData['action'])) {
            $action = $jsonData['action'];
        } else {
            // Sinon, vérifier les paramètres GET ou POST traditionnels
            $action = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');
        }

        switch ($action) {
            case 'resetPassword':
                // Réinitialisation du mot de passe
                if ($jsonData && isset($jsonData['token']) && isset($jsonData['username']) && isset($jsonData['password']) && isset($jsonData['confirmPassword'])) {
                    // Requête JSON
                    $result = resetPasswordAPI($jsonData['token'], $jsonData['username'], $jsonData['password'], $jsonData['confirmPassword']);
                    jsonResponse($result);
                } else if (isset($_POST['token']) && isset($_POST['username']) && isset($_POST['password']) && isset($_POST['confirmPassword'])) {
                    // Requête formulaire traditionnelle
                    $result = resetPasswordAPI($_POST['token'], $_POST['username'], $_POST['password'], $_POST['confirmPassword']);

                    // Si c'est une requête de formulaire, rediriger vers la page de succès
                    if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
                        if ($result['success']) {
                            header('Location: ../view/initialize-password.php?success=1');
                        } else {
                            header('Location: ../view/initialize-password.php?token=' . urlencode($_POST['token']) . '&error=' . urlencode($result['error']));
                        }
                        exit;
                    } else {
                        jsonResponse($result);
                    }
                } else {
                    jsonResponse(['success' => false, 'error' => 'Paramètres manquants'], 400);
                }
                break;

            case 'getUnreadCount':
                // Récupérer le nombre de messages non lus
                getUserUnreadMessagesCountAPI();
                break;

            case 'getUsers':
                // Récupérer la liste des utilisateurs avec pagination et filtres
                $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
                $search = isset($_GET['search']) ? $_GET['search'] : '';
                $role = isset($_GET['role']) ? $_GET['role'] : '';
                $status = isset($_GET['status']) ? $_GET['status'] : '';

                require_once "../model/userModel.php";
                $result = getUsers($page, $search, $role, $status);
                jsonResponse($result);
                break;

            case 'getUserDetails':
                // Récupérer les détails d'un utilisateur
                if (isset($_GET['id'])) {
                    require_once "../model/userModel.php";
                    $result = getUserById($_GET['id']);
                    jsonResponse($result);
                } else {
                    jsonResponse(['success' => false, 'error' => 'ID utilisateur requis'], 400);
                }
                break;

            case 'toggleUserStatus':
                // Changer le statut d'un utilisateur (actif/inactif)
                $data = json_decode(file_get_contents('php://input'), true);
                if (isset($data['id']) && isset($data['status'])) {
                    require_once "../model/userModel.php";
                    $result = toggleUserStatus($data['id'], $data['status']);
                    jsonResponse($result);
                } else {
                    jsonResponse(['success' => false, 'error' => 'Paramètres manquants'], 400);
                }
                break;

            case 'deleteUser':
                // Supprimer un utilisateur
                $data = json_decode(file_get_contents('php://input'), true);
                if (isset($data['id'])) {
                    require_once "../model/userModel.php";
                    $result = deleteUser($data['id']);
                    jsonResponse($result);
                } else {
                    jsonResponse(['success' => false, 'error' => 'ID utilisateur requis'], 400);
                }
                break;

            default:
                jsonResponse(['success' => false, 'error' => 'Action non reconnue'], 400);
                break;
        }
    } catch (Exception $e) {
        // Journaliser l'erreur
        error_log("Erreur dans userRoute.php: " . $e->getMessage());

        // Renvoyer une réponse JSON avec l'erreur
        jsonResponse(['success' => false, 'error' => 'Erreur serveur: ' . $e->getMessage()], 500);
    }
}
?>
